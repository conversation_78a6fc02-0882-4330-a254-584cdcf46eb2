#set($pKeyName = "${context.pKeyName}")
#set($mastTableList = $context.mastTable)
#set($setPermission = "$setPermission")
#set($menuId = "menuId")
#set($useBtnPermission=${context.useBtnPermission})
#set($useColumnPermission=${context.useColumnPermission})
#macro(code8Index $formModel)
    #set($html = $formModel)
    #set($config = $html.config)
    #set($xhkey = "${config.xhKey}")
    #set($dateType = "")
    #set($model = "${html.vModel}")
    #set($label = $html.label)
    #set($mastModel="searchForm."+"${model}")
    #set($tag = "$xhkey")
    #set($type = "")
    #set($bh = "")
    #set($inputalign="")
    #if($xhkey=='input' || $xhkey=='textarea' || $xhkey=='billRule')
        #set($tag = "u-input")
        #set($inputalign="right")
    #elseif($xhkey=='inputNumber')
        #set($tag = "xh-num-range")
    #elseif($xhkey=='rate' || $xhkey=='slider' || $xhkey=='divider')
        #set($tag = "u-"+$xhkey)
    #elseif($xhkey=='switch' || $xhkey=='checkbox' || $xhkey=='radio' || $xhkey=='select' || $xhkey=='cascader')
        #set($tag = "xh-"+$xhkey)
        #if($xhkey == "checkbox" || $xhkey == "radio")
            #set($tag = "xh-select")
        #end
    #elseif($xhkey=='groupSelect')
        #set($tag = "xh-group-select")
    #elseif($xhkey=='autoComplete')
        #set($tag = "xh-auto-complete")
    #elseif($xhkey=='roleSelect')
        #set($tag = "xh-role-select")
    #elseif($xhkey=='areaSelect')
        #set($tag = "xh-city-select")
    #elseif($xhkey=='treeSelect')
        #set($tag = "xh-tree-select")
    #elseif($xhkey=='datePicker'||  $xhkey=='createTime' || $xhkey=='modifyTime' || $xhkey=='timePicker')
        #set($tag = "xh-date-range")
        #set($dateType = "date")
        #if($xhkey=='timePicker')
            #set($dateType = "time")
        #end
    #elseif($xhkey=='userSelect' || $xhkey=='createUser' ||  $xhkey=='modifyUser')
        #set($tag = "xh-user-select")
        #set($bh = "650")
    #elseif($xhkey=='usersSelect')
        #set($tag = "xh-user-choice")
    #elseif($xhkey=='organizeSelect' || $xhkey=='depSelect' || $xhkey=='posSelect' || $xhkey=='currOrganize' || $xhkey=='currDept' || $xhkey=='currPosition')
        #set($tag = "xh-postordep-select")
        #if($xhkey=='organizeSelect' || $xhkey=='currOrganize')
            #set($tag = "xh-com-select")
        #elseif($xhkey=='depSelect' || $xhkey=='currDept')
            #set($type = "department")
        #elseif($xhkey=='posSelect' || $xhkey=='currPosition')
            #set($type = "position")
        #end
    #end
    #set($placeholder="请选择")
    #if($xhkey=='input' || $xhkey=='textarea'||  $xhkey=='billRule')
        #set($placeholder="请输入")
    #end
    #set($modelprops="")
    #set($modeloptions="")
    #if($xhkey=='radio' || $xhkey=='checkbox' || $xhkey=='select' || $xhkey=='cascader' || $xhkey=='treeSelect')
        #set($modelprops="${model}Props")
        #set($modeloptions="${model}Options")
    #end
                                    <u-form-item label="${label}">
                                        #if($tag)
                                        <$tag v-model="${mastModel}"
                                            placeholder="${placeholder}${label}"
                                            :key="key"
                                            #if($dateType) xhKey="${dateType}" #end
                                            #if(${bh}) :bh="650" #end
                                            #if($inputalign) input-align='right' #end
                                            #if(${type}) type="${type}"#end
                                            #set($selectTypes = ${html.selectType})
                                            #if(${selectTypes}!='custom')
                                                #set($selectTypes = "all")
                                            #end
                                            #if($html.clearable) clearable #end
                                            #if($html.format) format="${html.format}" #end
                                            #if($html.interfaceId) interfaceId="${html.interfaceId}" #end
                                            #if($html.templateJson) :templateJson="interfaceRes.${model}" #end
                                            #if($html.relationField) relationField="${html.relationField}" #end
                                            #if($selectTypes) selectType="${selectTypes}" #end
                                            #if($html.searchMultiple) :multiple="${html.searchMultiple}"#end
                                            #if($html.ableIds) :ableIds="ableAll.${model}ableIds" #end
                                            #if($html.ableDepIds) :ableDepIds="ableAll.${model}ableDepIds" #end
                                            #if($html.ablePosIds) :ablePosIds="ableAll.${model}ablePosIds" #end
                                            #if($html.ableUserIds) :ableUserIds="ableAll.${model}ableUserIds" #end
                                            #if($html.ableRoleIds) :ableRoleIds="ableAll.${model}ableRoleIds" #end
                                            #if($html.ableGroupIds) :ableGroupIds="ableAll.${model}ableGroupIds" #end
                                            #if(${html.level}) :level="${html.level}" #end
                                            #if(${modelprops}) :props="${modelprops}" #end
                                            #if(${modeloptions}) :options="${modeloptions}" #end>
                                        </$tag>
                                        #end
                                    </u-form-item>
#end
#macro(code8List $formModel $prop $vModel)
    #set($html = $formModel)
    #set($activeTxt = "$html.activeTxt")
    #set($inactiveTxt = "$html.inactiveTxt")
    #set($config = $html.config)
    #set($hasModel = $html.vModel)
    #set($xhkey = "${config.xhKey}")
    #set($dataType = "$config.dataType")
    #set($label = "${config.label}")
                        <view class="item-cell u-line-1" #if(${useColumnPermission}==true) v-if="${setPermission}.hasP('${hasModel}',${menuId})"#end>
                            #set($text = "{{item."+$vModel+"_name}}")
                            #if($xhkey=='radio' || $xhkey=='checkbox' || $xhkey=='select' || $xhkey=='cascader' || $xhkey=='treeSelect')
                                #if($dataType=='static')
                                    #set($text = "{{item."+$vModel+" | dynamicText("+$prop+"Options)}}")
                                    #if($xhkey=='treeSelect')
                                        #set($text = "{{item."+$vModel+" | dynamicTreeText("+$prop+"Options)}}")
                                    #end
                                #end
                            #end
                            <text>$label：</text>
                            <text>$text</text>
                        </view>
#end
#macro(code8DataList $vmodel $formModel)
    #set($html = $formModel)
    #set($config = $html.config)
    #set($xhkey = "${config.xhKey}")
    #set($dataType = "$config.dataType")
    #if($xhkey=='cascader' || $xhkey=="checkbox")
        #if($dataType=='static')
                    _data.${vmodel} = JSON.parse(_data.${vmodel})
        #end
    #end
    #if($xhkey=='select' || $xhkey=='treeSelect')
        #if(${dataType}=='static' && ${html.multiple}=='true')
        _data.${vmodel} = JSON.parse(_data.${vmodel})
        #end
    #end
#end
#macro(searchAllForm $formModel)
    #set($html = $formModel)
    #set($config = $html.config)
    #set($xhkey = "${config.xhKey}")
    #set($defaultCurrent=${config.defaultCurrent})
    #set($multiple=${html.searchMultiple})
    #set($selectType=${html.selectType})
    #set($serchFiled=${html.vModel})
    #set($ableUserIds=${html.ableUserIds})
    #set($ableDepIds=${html.ableDepIds})
    #set($ableGroupIds=${html.ableGroupIds})
    #set($ableRoleIds=${html.ableRoleIds})
    #set($ablePosIds=${html.ablePosIds})
    #if($defaultCurrent)
        #if($xhkey=='datePicker')
##                this.searchForm.${serchFiled} = [startDateTime.getTime(), endDateTime.getTime()]
        #elseif($xhkey=='depSelect')
                if(this.userInfo.departmentId != null && this.userInfo.departmentId != '') {
            #if($selectType=='all')
                #if($multiple)
                    this.searchForm.${serchFiled} =[this.userInfo.departmentId]
                #else
                    this.searchForm.${serchFiled} = this.userInfo.departmentId
                #end
            #else
                    let ableDepIds = ${ableDepIds}
                    if(ableDepIds instanceof Array && ableDepIds.length > 0 && ableDepIds.includes(this.userInfo.departmentId)) {
                #if($multiple)
                        this.searchForm.${serchFiled} = [this.userInfo.departmentId]
                #else
                        this.searchForm.${serchFiled} = this.userInfo.departmentId
                #end
                    }else if(ableDepIds instanceof Array && ableDepIds.length > 0) {
                        let res = await getDefaultCurrentValueDepartmentIdAsync({departIds:ableDepIds})
                        if (res.data.departmentId != null && res.data.departmentId != '') {
                    #if($multiple)
                            this.searchForm.${serchFiled} = [this.userInfo.departmentId]
                    #else
                            this.searchForm.${serchFiled} = this.userInfo.departmentId
                    #end
                        }
                    }
            #end
                }
        #elseif($xhkey=='organizeSelect')
                if(this.userInfo.organizeIdList instanceof Array && this.userInfo.organizeIdList.length > 0) {
            #if($multiple)
                    this.searchForm.${serchFiled} = [this.userInfo.organizeIdList]
            #else
                    this.searchForm.${serchFiled} = this.userInfo.organizeIdList
            #end
                }
        #elseif($xhkey=='userSelect')
            #if($selectType=='all')
                #if($multiple)
                this.searchForm.${serchFiled} = [this.userInfo.userId]
                #else
                this.searchForm.${serchFiled} = this.userInfo.userId
                #end
            #elseif($selectType=='custom')
                if(this.userInfo.userId != null && this.userInfo.userId != '') {
                    let ableUserIds = ${ableUserIds}
                    let ableDepIds = ${ableDepIds}
                    let ableGroupIds = ${ableGroupIds}
                    let ableRoleIds = ${ableRoleIds}
                    let ablePosIds = ${ablePosIds}
                    if(ableUserIds instanceof Array && ableUserIds.length > 0 && ableUserIds.includes(this.userInfo.userId)) {
                #if($multiple)
                        this.searchForm.${serchFiled} = [this.userInfo.userId]
                #else
                        this.searchForm.${serchFiled} = this.userInfo.userId
                #end
                    }else if((ableUserIds instanceof Array && ableUserIds.length > 0)
                        || (ableDepIds instanceof Array && ableDepIds.length > 0)
                        || (ableGroupIds instanceof Array && ableGroupIds.length > 0)
                        || (ableRoleIds instanceof Array && ableRoleIds.length > 0)
                        || (ablePosIds instanceof Array && ablePosIds.length > 0)) {
                        let res = await getDefaultCurrentValueUserIdAsync({
                            departIds:ableDepIds,
                            groupIds:ableGroupIds,
                            roleIds:ableRoleIds,
                            userIds:ableUserIds,
                            positionIds:ablePosIds
                        })
                        if(res.data.userId != null && res.data.userId != '') {
                    #if($multiple)
                            this.searchForm.${serchFiled} = [this.userInfo.userId]
                    #else
                            this.searchForm.${serchFiled} = this.userInfo.userId
                    #end
                        }
                    }
                }
            #end
        #end
    #end
#end

<template>
    <view class="dynamicModel-list-v">
        <view class="head-warp com-dropdown">
            <u-dropdown class="u-dropdown" ref="uDropdown" @open="openData">
                <u-dropdown-item title="排序">
                    <view class="dropdown-slot-content">
                        <view class="dropdown-slot-content-main">
                            <u-cell-group>
                                <u-cell-item @click="cellClick(item)" :arrow="false" :title="item.label"
									 v-for="(item, index) in sortOptions" :key="index" :title-style="{color: sortValue == item.value ? '#2979ff' : '#606266' }">
                                    <u-icon v-if="sortValue == item.value" name="checkbox-mark" color="#2979ff" size="32"></u-icon>
                                </u-cell-item>
                            </u-cell-group>
                        </view>
                    </view>
                </u-dropdown-item>
                <u-dropdown-item title="筛选" >
                    <view class="dropdown-slot-content">
                        <view class="dropdown-slot-content-main search-main">
                            <scroll-view scroll-y="true" style="height: 1000rpx;">
                            <view class="u-p-l-20 u-p-r-20 search-form">
								<u-form :model="searchForm" ref="searchForm" :errorType="['toast']" label-position="left" label-width="150">
                                #foreach($searchAll in ${context.searchAll})
                                    #set($searckey = ${searchAll.key})
                                    #set($html = ${searchAll.html})
                                    #code8Index($html)
                                #end
								</u-form>
                            </view>
                            </scroll-view>
							<view class="buttom-actions">
							  <u-button class="buttom-btn" @click="reset">重置</u-button>
							  <u-button class="buttom-btn" type="primary" @click="closeDropdown">检索</u-button>
							</view>
							<view class="dropdown-slot-bg" @click="$refs.uDropdown.close()"></view>
                        </view>
                    </view>
                </u-dropdown-item>
            </u-dropdown>
        </view>
		<view class="list-warp">
            <mescroll-uni ref="mescrollRef" @init="mescrollInit" @down="downCallback" @up="upCallback" :up="upOption" top="100">
				<view class="list">
                    <view class="list-box">
                        <uni-swipe-action ref="swipeAction">
                            <uni-swipe-action-item v-for="(item, index) in list" :key="item.${pKeyName}" :threshold="0" :right-options="options">
                                <view class="item" @click="goDetail(item.${pKeyName},item.flowState)">
                                    <view class="u-line-1 item-cell" v-for="(column,i) in columnList" :key="i">
                                        <view v-if="column.xhKey != 'table'">
                                            <view class="" v-if="column.xhKey == 'relationForm'">
                                                <text class="listTit u-line-1">{{column.label}}:</text>
                                                <text class="listContent"
                                                      @click.stop="relationFormClick(item,column)"
                                                      style="color: blue;">{{item[column.prop]}}</text>
                                            </view>
                                            <view v-else class="u-line-1">
                                                <text class="listTit u-line-1">{{column.label}}:</text>
                                                <text class="listContent" v-if="['calculate','inputNumber'].includes(column.xhKey) && column.thousands">{{toThousands(item[column.prop],column)}}</text>
                                                <text class="listContent" v-else>{{item[column.prop] | dynamicTreeText(column.option)}}</text>
                                            </view>
                                        </view>
                                        <view class="collapse-box u-flex" v-else>
                                            <tableCell :label="column.label" :childList="item[column.prop]" @cRelationForm="relationFormClick" :children="column.children"
                                                       ref="tableCell" :pageLen="3"></tableCell>
                                        </view>
                                    </view>
                                </view>
                                <template v-slot:right>
                                    <view class="right-option-box">
                                        <view class="right-option" v-for="(it,i) in options" @click="handleClick(index)"
                                              :key="i">
                                            <text>{{it.text}}</text>
                                        </view>
                                    </view>
                                </template>
                            </uni-swipe-action-item>
                        </uni-swipe-action>
                    </view>
				</view>
			</mescroll-uni>
		</view>
		 #foreach($btns in ${context.columnBtnsList})
            #if(${btns.value}=='add')
        <view class="com-addBtn" @click="addPage()" #if(${useBtnPermission}==true) v-if="${setPermission}.hasBtnP('btn_add',${menuId})" #end>
            <u-icon name="plus" size="60" color="#fff" />
        </view>
            #end
		 #end
    </view>
</template>
#macro(code8AbleAll $fieLdsModel)
    #set($html = $fieLdsModel)
    #set($vModel = "${html.vModel}")
    #set($config = $html.config)
    #set($dataType = "${config.dataType}")
    #if(${html.ableIds})
        ${vModel}ableIds:${html.ableIds},
    #end
    #if(${html.ableDepIds})
        ${vModel}ableDepIds:${html.ableDepIds},
    #end
    #if(${html.ablePosIds})
        ${vModel}ablePosIds:${html.ablePosIds},
    #end
    #if(${html.ableUserIds})
        ${vModel}ableUserIds:${html.ableUserIds},
    #end
    #if(${html.ableRoleIds})
        ${vModel}ableRoleIds:${html.ableRoleIds},
    #end
    #if(${html.ableGroupIds})
        ${vModel}ableGroupIds:${html.ableGroupIds},
    #end
#end
#macro(code8FaceRes $fieLdsModel,$childList)
    #set($html = $fieLdsModel)
    #set($vModel = "${childList}${html.vModel}")
    #set($config = $html.config)
    #if(${vModel})
        ${vModel}:[
        #foreach($templateJson in ${config.templateJson})
        {
            fieldName:"${templateJson.fieldName}",
            field:"${templateJson.field}",
            defaultValue:"${templateJson.defaultValue}",
            xhKey:"$!{templateJson.xhKey}",
            dataType:"${templateJson.dataType}",
            id:"${templateJson.id}",
            required:"${templateJson.required}",
            relationField:"${templateJson.relationField}",
        },
        #end
    ],
    #end
#end
#macro(code8DataOptions $fieLdsModel)
    #set($html = $fieLdsModel)
    #set($vModel = "${html.vModel}")
    #set($config = $html.config)
    #if(${config.dataType} == "static")
        #if($html.slot.options)
            ${vModel}Options:${html.slot.options},
        #elseif($html.options)
            ${vModel}Options:${html.options},
        #end
    #end
#end
#macro(code8Options $fieLdsModel)
    #set($html = $fieLdsModel)
    #set($vModel = "${html.vModel}")
    #set($config = $html.config)
    #if(${config.dataType}=='dictionary'||${config.dataType}=='dynamic')
        ${vModel}Options:[],
    #elseif(${config.dataType} == "static")
        #if($html.slot.options)
            ${vModel}Options:${html.slot.options},
        #elseif($html.options)
            ${vModel}Options:${html.options},
        #end
    #end
    #if($html.props)
        #set($propsModel = ${html.props})
        ${vModel}Props:{"label":"${propsModel.label}","value":"${propsModel.value}","multiple":${propsModel.multiple},"children":"${propsModel.children}"},
    #end
    #if($config.props)
        ${vModel}Props:{"label":"${config.props.label}","value":"${config.props.value}"},
    #end
#end
#macro(code8DataAll $fieLdsModel)
    #set($html = $fieLdsModel)
    #set($vModel = "${html.vModel}")
    #set($config = $html.config)
    #set($dataType = "${config.dataType}")
    #if(${dataType}=='dictionary')
        this.get${vModel}Options()
    #elseif(${dataType}=='dynamic')
        this.get${vModel}Options()
    #end
#end
#macro(code8OptionsList $fieLdsModel)
    #set($html = $fieLdsModel)
    #set($vModel = "${html.vModel}")
    #set($config = $html.config)
    #set($dataType = "${config.dataType}")
    #if(${dataType}=='dictionary')
    get${vModel}Options() {
        getDictionaryDataSelector('${config.dictionaryType}').then(res => {
            this.${vModel}Options = res.data.list
        })
    },
    #elseif(${dataType}=='dynamic')
    get${vModel}Options() {
        getDataInterfaceRes('${config.propsUrl}').then(res => {
            let data = res.data
            this.${vModel}Options = data
        })
    },
    #end
#end
<script>
    import resources from '@/libs/resources.js'
    import MescrollMixin from "@/uni_modules/mescroll-uni/components/mescroll-uni/mescroll-mixins.js";
    import tableCell from '../dynamicModel/components/tableCell.vue'
    import request from '@/utils/request'

    export default {
		mixins: [MescrollMixin],
		components: {
            tableCell
        },
        data() {
            return {
                sortValue: '',
                searchForm: {
                    #foreach($html in ${context.mastsearchList})
                        #set($model = "${html.vModel}")
                    ${model}:undefined,
                    #end
                    #foreach($html in ${context.searchList})
                        #set($model = "${html.vModel}")
					${model}:undefined,
                    #end
                    #foreach($html in ${context.childSearch})
                        #set($model = "${html.vModel}")
                    ${model}:undefined,
                    #end
                },
                downOption: {
                    use: true,
                    auto: false
                },
                dataOptions:{
                    #foreach($fieLdsModel in ${context.fields})
                        #set($html = $fieLdsModel.formColumnModel.fieLdsModel)
                        #code8DataOptions($html)
                    #end
                    #foreach($masetkey in $mastTableList.entrySet())
                        #set($tableModel = $masetkey.key)
                        #set($fieldsAll = $masetkey.value)
                        #foreach($fieLdsModel in ${fieldsAll})
                            #set($html = $fieLdsModel.formMastTableModel.mastTable.fieLdsModel)
                            #code8DataOptions($html)
                        #end
                    #end
                    #foreach($child in ${context.children})
                        #set($tableModel = "${child.tableModel}")
                        ${tableModel}:{
                        #foreach($childList in ${child.childList})
                            #set($html = $childList.fieLdsModel)
                            #code8DataOptions($html)
                        #end
                    },
                    #end
                },
                upOption: {
                    page: {
                        num: 0,
                        size: ${context.pageSize},
                        time: null,
                    },
                    empty: {
                        use: true,
                        icon: resources.message.nodata,
                        tip: "暂无数据",
                        fixed: true,
                        top: "300rpx",
                        zIndex: 5,
                    },
                    textNoMore: '没有更多数据',
                    toTop: {
                        bottom: 250
                    }
                },
                #foreach($searchAll in ${context.searchAll})
                    #set($searckey = ${searchAll.key})
                    #set($html = ${searchAll.html})
                    #code8Options($html)
                #end
				list: [],
				#set($appColumnList='[]')
                #if(${context.AppColumnList})
                    #set($appColumnList=${context.AppColumnList})
                #end
				appColumnList:${appColumnList},
				listQuery: {
                    moduleId:'${context.moduleId}',
				    sort: '${context.sort}',
				    sidx: '${context.defaultSidx}',
				    keyword: '',
				    json: ''
				},
				options: [
				#foreach($btns in ${context.columnBtnsList})
                    #set($remove = "删除")
                    #if(${btns.value}=='remove')
                        #set($remove = "${btns.label}")
                    {
                        text: '删除',
                        style: {
                            backgroundColor: '#dd524d'
                        }
                    },
                    #end
                #end
				],
				sortOptions:[
				    #foreach($html in ${context.sortList})
				    {
				        label: '${html.label}降序',
				        sidx: '${html.prop}',
				        value: '${html.prop}desc',
				        sort: 'desc'
				    },
				    {
				        label: '${html.label}升序',
				        sidx: '${html.prop}',
				        value: '${html.prop}asc',
				        sort: 'asc'
				    },
				    #end
				],
                ableAll:{
                #foreach($searchAll in ${context.searchAll})
                    #set($searckey = ${searchAll.key})
                    #set($html = ${searchAll.html})
                    #code8AbleAll($html)
                #end
                },
                interfaceRes:{
                #foreach($searchAll in ${context.searchAll})
                    #set($searckey = ${searchAll.key})
                    #set($html = ${searchAll.html})
                    #code8FaceRes($html,'')
                #end
                },
                menuId:'',
                columnList:[],
                isTree:${context.groupTable},
                type:'${context.type}',
                key:new Date(),
                dataValue:{},
                userInfo:{},
                firstInitSearchData:false,
                thousandsField:${context.thousandsField},
                formatType:{"yyyy":"yyyy","yyyy-MM":"yyyy-mm","yyyy-MM-dd":"yyyy-mm-dd","yyyy-MM-dd HH:mm":"yyyy-mm-dd hh:MM","yyyy-MM-dd HH:mm:ss":"yyyy-mm-dd hh:MM:ss","HH:mm:ss":"hh:MM:ss","HH:mm":"hh:MM"},
            }
        },
        onLoad(e){
            this.userInfo = uni.getStorageSync('userInfo') || {}
			this.menuId = e.menuId
            uni.$on('refresh', () => {
                this.list = [];
                this.mescroll.resetUpScroll();
            })
            this.dataAll()
            this.getColumnList()
            this.dataValue = JSON.parse(JSON.stringify(this.searchForm))
        },
        onUnload() {
            uni.$off('refresh')
        },
        methods:{
            toThousands(val, column) {
                let num = Number(val)
                let newVal = this.thousandsField.includes(column.__vModel__) ? num.toLocaleString('zh', {maximumFractionDigits: '2',minimumFractionDigits: '2'}): num.toFixed(2);
                return newVal
            },
			dataAll(){
                #foreach($searchAll in ${context.searchAll})
                    #set($searckey = ${searchAll.key})
                    #set($html = ${searchAll.html})
                    #code8DataAll($html)
                #end
			},
            #foreach($searchAll in ${context.searchAll})
                #set($searckey = ${searchAll.key})
                #set($html = ${searchAll.html})
                #code8OptionsList($html)
            #end
			openData(e){
			    if(e==1){
			        // this.reset()
			    }
			},
            //初始化查询的默认数据
            async initSearchData() {
                let startDateTime = new Date()
                startDateTime.setHours(0,0,0)
                let endDateTime = new Date()
                endDateTime.setHours(23,59,59)
                #foreach($html in ${context.mastsearchList})
                    #set($formModel=$html)
                    #searchAllForm(${formModel})
                #end
                #foreach($html in ${context.searchList})
                    #set($formModel=$html)
                    #searchAllForm(${formModel})
                #end
                #foreach($html in ${context.childSearch})
                    #set($formModel=$html)
                    #searchAllForm(${formModel})
                #end
            },
            relationFormClick(item,column) {
                let vModel = column.__vModel__?column.__vModel__+"_id":column.__vModel__
                let id = item[vModel]
                if(vModel){
                    let [attr,attr1]=vModel.split("_xh_")
                    if(attr&&attr1){
                        attr = attr.replace('xh_','')
                        id = item[attr]&&item[attr][attr1]
                    }
                }
                let modelId = column.modelId
                if (!id || !modelId) return
                let config = {
                    modelId: modelId,
                    id: id,
                    formTitle: '详情',
                    noShowBtn: 1
                }
                this.$nextTick(() => {
                    const url ='/pages/apply/dynamicModel/detail?config=' + this.base64.encode(JSON.stringify(config),"UTF-8")
                    uni.navigateTo({
                        url: url
                    })
                })
            },
            async upCallback(page) {
                if(!this.firstInitSearchData) {
                  await this.initSearchData()
                  this.firstInitSearchData = true
                }
                const query = {
                    currentPage: page.num,
                    pageSize: page.size,
                    menuId : this.menuId,
                    ...this.listQuery,
                    ...this.searchForm,
                    #set($queryDataType="0")
                    #if(${context.page}=='1')
                        #set($queryDataType="1")
                    #end
                    dataType:${queryDataType},
                }
                request({
                    url: '/api/${context.module}/${context.className}/getList',
                    method: 'post',
                    data: query,
                }).then(res => {
                    let _list = this.columnList.length?res.data.list:[];
                    #set($list = "this.mescroll.endSuccess(_list.length);")
                    #if(${context.page}=='1')
                        #set($list = "this.mescroll.endSuccess(_list.length, false);")
                    #end
                    ${list}
                    if (page.num == 1) this.list = [];
                    const list = _list.map(o => ({
                        show: false,
                        ...o
                    }));
                    this.list = this.list.concat(_list);
                }).catch(() => {
                    this.mescroll.endErr();
                })
            },
            handleClick(index, index1) {
                #if(${useBtnPermission}==true)
                if (!this.${setPermission}.hasBtnP("btn_remove",this.${menuId})) return this.$u.toast("未开启删除权限")
                #end
				const item = this.list[index]
                request({
                    url: '/api/${context.module}/${context.className}/' + item.${pKeyName},
                    method: 'delete'
                }).then(res => {
                    uni.showToast({
                        title: res.msg,
                        complete: () => {
                            #set($toast='this.$u.toast(res.msg)')
                            ${toast}
                            this.mescroll.resetUpScroll()
                        }
                    })
                })
            },
            open(index) {
                this.list[index].show = true;
                this.list.map((val, idx) => {
                    if (index != idx) this.list[idx].show = false;
                })
            },
            search() {
                if (this.isPreview == '1') return
                this.searchTimer && clearTimeout(this.searchTimer)
                this.searchTimer = setTimeout(() => {
                    this.list = [];
                    this.mescroll.resetUpScroll();
                }, 300)
            },
            goDetail(id, status) {
                let btnType = ''
                let btnList = []
                #foreach($btns in ${context.columnBtnsList})
                    #if(${btns.value}=='edit')
                btnList.push('btn_edit')
                    #end
                    #if(${btns.value}=='detail')
                btnList.push('btn_detail')
                    #end
                #end
                #set($edit="")
                #set($detail="")
                #if(${useBtnPermission}==true)
                    #foreach($btns in ${context.columnBtnsList})
                        #if(${btns.value}=='edit')
                            #set($edit="btn_edit")
                        #end
                        #if(${btns.value}=='detail')
                            #set($detail="btn_detail")
                        #end
                    #end
                if(!this.${setPermission}.hasBtnP('${detail}',this.${menuId})){
                    btnList = btnList.filter(o=>{return o !== '${detail}'})
                }
                if(!this.${setPermission}.hasBtnP('${edit}',this.${menuId})){
                    btnList = btnList.filter(o=>{return o !== '${edit}'})
                }
                #end
                if(btnList.length==0) return
                this.jumPage(id, status,btnList)
            },
            addPage() {
				this.jumPage()
			},
            getColumnList() {
                let columnPermissionList = []
                let _appColumnList =this.appColumnList
                #if(${useColumnPermission}==true)
                let permissionList = uni.getStorageSync('permissionList')
                let list = permissionList.filter(o => o.modelId === this.menuId)
                let _columnList = list[0] && list[0].column ? list[0].column : []
                for (let i = 0; i < _appColumnList.length; i++) {
                    let _app = _appColumnList[i].prop
                    inner:  for (let j = 0; j < _columnList.length; j++) {
                        let _encode = _columnList[j].enCode
                        if(_app == _encode){
                            columnPermissionList.push(this.appColumnList[i])
                            break inner
                        }
                    }
                }
                #else
                for (let i = 0; i < _appColumnList.length; i++) {
                    columnPermissionList.push(_appColumnList[i])
                }
                #end
                this.columnList = this.transformColumnList(columnPermissionList, this.dataOptions)
            },
			transformColumnList(columnList, dataOptions) {
				let list = []
				for (let i = 0; i < columnList.length; i++) {
                    let e = columnList[i]
                    let columProp = e.prop
                    let label = e.label
					let option = null
					let options = columProp + "Options"
					if (!columProp.includes('-')) {
                        columProp = columProp #if($context.type=='4') +"_name" #end
                        if(label.length>4){
                            label = label.substring(0, 4)
                        }
                        e.label = label
                        e.prop = columProp
						e.option = option
						list.push(e)
					} else {
						e.vModel = columProp.split('-')[1]
						e.childLabel = e.label.split('-')[1]
						options = e.vModel + "Options"
						let prop = columProp.split('-')[0]
						let label = e.label.split('-')[0]
						let newItem = {
							align: "center",
							xhKey: "table",
							prop,
							label,
							children: []
						}
						if (!list.some(o => o.prop === prop)) list.push(newItem)
						for (let i = 0; i < list.length; i++) {
							if (list[i].prop === prop) {
								e.option = option
                                e.vModel= e.vModel#if($context.type=='4') +"_name" #end
								list[i].children.push(e)
								break
							}
						}
					}
				}
				return list
			},
            jumPage(id, status, btnList) {
                let idVal = id ? "&id=" + id : ''
                let idList = []
                for(let i=0;i<this.list.length;i++){
                    idList.push(this.list[i].${pKeyName})
                }
                let idListVal = "&idList="+idList
                if (!id && !status){
                    uni.navigateTo({
                        url: "./form?menuId=" + this.menuId + "&jurisdictionType=btn_add"
                    })
                } else if(btnList.includes('btn_detail')){
                    uni.navigateTo({
                        url: "./detail?menuId=" + this.menuId + "&btnList=" + btnList + idVal+idListVal
                    })
                } else if(btnList.includes('btn_edit')){
                    uni.navigateTo({
                        url: "./form?menuId=" + this.menuId + "&jurisdictionType=btn_edit&btnList=" + btnList + idVal+idListVal
                    })
                }
            },
            cellClick(item) {
                if (this.sortValue === item.value) return
                this.listQuery.sort = item.sort
                this.listQuery.sidx = item.sidx
                this.sortValue = item.value
                this.$refs.uDropdown.close();
                this.$nextTick(() => {
                    this.list = [];
                    this.mescroll.resetUpScroll();
                })
            },
            reset() {
                this.searchForm = JSON.parse(JSON.stringify(this.dataValue))
                this.key = new Date()
            },
            closeDropdown() {
                this.$refs.uDropdown.close();
                this.$nextTick(() => {
                    this.list = [];
                    this.mescroll.resetUpScroll();
                })
            },
			dataList(data){
				let _list =[]
                if(this.isTree){
                    data = this.treeToList(data)
                }
				for(let i=0;i<data.list.length;i++){
					let _data = data.list[i]
                    #foreach($fieLdsModel in ${context.fields})
                        #set($formModel = $fieLdsModel.formColumnModel.fieLdsModel)
                        #set($vModel = "${formModel.vModel}"+"_name")
                        #code8DataList(${vModel},$formModel)
                    #end
                    #foreach($masetkey in $mastTableList.entrySet())
                        #set($fieldsAll = $masetkey.value)
                        #set($tableModel = $masetkey.key)
                        #foreach($fieLdsModel in ${fieldsAll})
                            #set($mastTableModel = $fieLdsModel.formMastTableModel)
                            #set($formModel = $mastTableModel.mastTable.fieLdsModel)
                            #set($field = "${mastTableModel.field}")
                            #set($vModel ="${tableModel}"+"."+"${field}")
                            #set($mastVmodel ="xh_"+"${tableModel}"+"_xh_"+"${field}"+"_name")
                            #set($dataVmodel ="${vModel}"+"_name")
                    #code8DataList($dataVmodel,$formModel)
                    _data.${mastVmodel} = _data.${dataVmodel}
                        #end
                    #end
                    #foreach($child in ${context.children})
                        #set($tableModel = ${child.tableModel})
                    for(let i=0;i<_data.${tableModel}.length;i++){
                        #foreach($childList in ${child.childList})
                            #set($formModel = $childList.fieLdsModel)
                            #set($field = "${formModel.vModel}")
                            #set($vModel ="${tableModel}"+"[i]."+${field})
                            #set($config = $formModel.config)
                            #set($xhkey = "${config.xhKey}")
                            #set($format = ${formModel.format})
                            #if($xhkey=='datePicker')
                                #set($timeFormat = "this."+'$'+"u.timeFormat")
                    _data.${vModel} =  _data.${vModel}?  ${timeFormat}(Number(_data.${vModel}), this.formatType['${format}']) : ''
                            #end
                    #code8DataList($vModel,$formModel)
                        #end
                    }
                    #end
					_list.push(_data)
				}
				return _list;
			},
            treeToList(data) {
                let treeList = []
                if(this.type == '3'){
                    for(let i=0;i<data.length;i++){
                        if(data[i].children){
                            treeList = treeList.concat(data[i].children);
                        }
                    }
                    data = treeList
                }else{
                    for(let i=0;i<data.list.length;i++){
                        treeList = treeList.concat(data.list[i]);
                    }
                    data = treeList
                }
                let listAll = this.treeToArray(data)
                let _list = {
                    list: listAll
                }
                return _list;
            },
            treeToArray(data) {
                let options = data
                let list = []
                const loop = (options) => {
                    for (let i = 0; i < options.length; i++) {
                        const item = options[i]
                        list.push(item)
                        if (item.children && Array.isArray(item.children)) {
                            loop(item.children)
                        }
                    }
                }
                loop(options)
                return list
            },
        },
    }
</script>

<style lang="scss">
    page {
        background-color: #f0f2f6;
        height: 100%;
        /* #ifdef MP-ALIPAY */
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        /* #endif */
    }
    .right-option-box {
        display: flex;
        width: max-content;

        .right-option {
            width: 144rpx;
            height: 100%;
            font-size: 16px;
            color: #fff;
            background-color: #dd524d;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .more-option {
            background-color: #1890ff;
        }
    }
</style>
