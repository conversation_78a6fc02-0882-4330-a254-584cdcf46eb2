package com.xinghuo.card.report.controller;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 报表查询请求参数
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2024-01-01
 */
@Data
@Schema(description = "报表查询请求参数")
public class ReportQueryRequest {

    /**
     * 开始日期
     */
    @Schema(description = "开始日期")
    private Date startDate;

    /**
     * 结束日期
     */
    @Schema(description = "结束日期")
    private Date endDate;

    /**
     * 账户ID列表
     */
    @Schema(description = "账户ID列表")
    private List<String> accIds;

    /**
     * 分类ID列表
     */
    @Schema(description = "分类ID列表")
    private List<String> flowTypeIds;

    /**
     * 交易类型列表：1-收入，2-支出，3-转账，0-全部
     */
    @Schema(description = "交易类型列表：1-收入，2-支出，3-转账，0-全部")
    private List<Integer> transType;

    /**
     * 关键词（用于备注搜索）
     */
    @Schema(description = "关键词（用于备注搜索）")
    private String keywords;

    /**
     * 最小金额
     */
    @Schema(description = "最小金额")
    private BigDecimal minAmount;

    /**
     * 最大金额
     */
    @Schema(description = "最大金额")
    private BigDecimal maxAmount;

    /**
     * 分组方式：day-按日，month-按月，year-按年
     */
    @Schema(description = "分组方式：day-按日，month-按月，year-按年")
    private String groupBy;

    /**
     * 限制数量
     */
    @Schema(description = "限制数量")
    private Integer limit;
}
