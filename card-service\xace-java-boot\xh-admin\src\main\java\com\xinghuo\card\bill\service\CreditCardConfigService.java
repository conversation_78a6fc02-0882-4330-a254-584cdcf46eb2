package com.xinghuo.card.bill.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xinghuo.card.bill.entity.CreditCardConfigEntity;

import java.util.List;

/**
 * 信用卡配置服务接口
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2024-01-01
 */
public interface CreditCardConfigService extends IService<CreditCardConfigEntity> {

    /**
     * 根据用户ID获取信用卡配置列表
     *
     * @param userId 用户ID
     * @return 信用卡配置列表
     */
    List<CreditCardConfigEntity> getByUserId(String userId);

    /**
     * 根据账户ID获取信用卡配置
     *
     * @param accId 账户ID
     * @return 信用卡配置
     */
    CreditCardConfigEntity getByAccId(String accId);

    /**
     * 获取启用自动生成账单的信用卡配置列表
     *
     * @return 配置列表
     */
    List<CreditCardConfigEntity> getAutoGenerateConfigs();

    /**
     * 验证信用卡配置的有效性
     *
     * @param config 信用卡配置
     * @return 验证结果
     */
    boolean validateConfig(CreditCardConfigEntity config);

    /**
     * 创建信用卡配置
     *
     * @param config 信用卡配置
     * @return 创建结果
     */
    boolean createConfig(CreditCardConfigEntity config);

    /**
     * 更新信用卡配置
     *
     * @param config 信用卡配置
     * @return 更新结果
     */
    boolean updateConfig(CreditCardConfigEntity config);

    /**
     * 删除信用卡配置
     *
     * @param configId 配置ID
     * @return 删除结果
     */
    boolean deleteConfig(String configId);

    /**
     * 启用/禁用自动生成账单
     *
     * @param configId 配置ID
     * @param enable 是否启用
     * @return 操作结果
     */
    boolean toggleAutoGenerate(String configId, boolean enable);
}
