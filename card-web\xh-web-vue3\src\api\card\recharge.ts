import { defHttp } from '/@/utils/http/axios';

/**
 * 充值记录相关API接口
 */

enum Api {
  Prefix = '/api/card/flow/recharge',
}

/**
 * 充值记录分页查询参数
 */
export interface RechargeListParams {
  chargeType?: string;
  chargeDate?: string[];
  keyword?: string;
  current?: number;
  size?: number;
}

/**
 * 充值记录表单数据
 */
export interface RechargeFormData {
  id?: string;
  chargeType: string;
  chargeDate: string;
  outAccId: string;
  inAccId: string;
  amount: number;
  addAmount?: number;
  note?: string;
}

/**
 * 获取充值记录列表（分页）
 */
export function getRechargeList(data: RechargeListParams) {
  return defHttp.post({ url: Api.Prefix + '/getList', data });
}

/**
 * 创建充值记录
 */
export function createRecharge(data: RechargeFormData) {
  return defHttp.post({ url: Api.Prefix, data });
}

/**
 * 修改充值记录
 */
export function updateRecharge(data: RechargeFormData) {
  return defHttp.put({ url: Api.Prefix + '/' + data.id, data });
}

/**
 * 获取充值记录详情
 */
export function getRechargeInfo(id: string) {
  return defHttp.get({ url: Api.Prefix + '/' + id });
}

/**
 * 删除充值记录
 */
export function delRecharge(id: string) {
  return defHttp.delete({ url: Api.Prefix + '/' + id });
}
