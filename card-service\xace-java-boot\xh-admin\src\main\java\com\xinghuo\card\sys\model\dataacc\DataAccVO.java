package com.xinghuo.card.sys.model.dataacc;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * 个人账号
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2022-12-02
 */
@Data
public class DataAccVO {

    private String id;
    private String parentId;
    private Integer favMark;
    private Boolean hasChildren;
    private Integer isTree;
    private List<DataAccVO> children;
    @JsonProperty("fullName")
    private String name;

}
