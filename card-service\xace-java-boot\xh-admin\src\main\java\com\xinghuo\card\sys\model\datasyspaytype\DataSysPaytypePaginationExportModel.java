package com.xinghuo.card.sys.model.datasyspaytype;

import com.xinghuo.common.base.model.Pagination;
import lombok.Data;

/**
 * 收支类型管理
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2022-11-14
 */
@Data
public class DataSysPaytypePaginationExportModel extends Pagination {

    /**
     * 选择的key
     */
    private String selectKey;

    /**
     * json
     */
    private String json;

    /**
     * 数据类型
     */
    private String dataType;


    /**
     * 类型
     */
    private String type;

    /**
     * 名称
     */
    private String name;
}
