package ${package.ServiceImpl};

import ${package.Entity}.*;
import ${package.Mapper}.${table.mapperName};
import ${package.Service}.*;
import ${superServiceImplClassPackage};
import com.xinghuo.common.hutool.*;
import cn.hutool.core.util.ObjectUtil;
import com.xinghuo.permission.model.authorize.AuthorizeConditionModel;
import com.xinghuo.visualdev.base.model.filter.RuleInfo;
import com.xinghuo.example.util.GenUtil;

import com.xinghuo.example.util.GeneraterSwapUtil;
#set($peimaryKeyName="${pKeyName.substring(0,1).toUpperCase()}${pKeyName.substring(1)}")
#if($superQuery)
import com.xinghuo.common.database.model.superQuery.SuperQueryJsonModel;
import com.xinghuo.common.database.model.superQuery.ConditionJsonModel;
import com.xinghuo.common.database.model.superQuery.SuperQueryConditionModel;
#end

#if(${DS})
import com.baomidou.dynamic.datasource.annotation.DS;
import com.xinghuo.common.database.util.DataSourceUtil;
import com.xinghuo.common.database.model.entity.DbLinkEntity;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.xinghuo.common.database.plugins.DynamicSourceGeneratorInterface;
#end

#set($Authorize = 'AuthorizeService')
#set($authorize = 'authorizeService')
#if(${isCloud}=="cloud")
    #set($Authorize = 'AuthorizeUtil')
    #set($authorize = 'authorizeUtil')
import com.xinghuo.permission.util.AuthorizeUtil;
#else
import com.xinghuo.pub.provider.permission.service.AuthorizeService;
#end


#if(${main})
#set($Name = "${genInfo.className.substring(0,1).toUpperCase()}${genInfo.className.substring(1)}")
#set($name = "${genInfo.className.substring(0,1).toLowerCase()}${genInfo.className.substring(1)}")
#set($QueryWrapper = "${name}QueryWrapper")
#set($packName = "${name.toLowerCase()}")
import com.baomidou.mybatisplus.annotation.TableField;
#else
#set($packName = "${mainModelName.toLowerCase()}")
#end${packName}.*;

#macro(CreateWrapper)
    #foreach($child in ${allTableNameList})
        #set($ModelName= "${child.table.substring(0,1).toUpperCase()}${child.table.substring(1)}")
        #set($modelName ="${child.table.substring(0,1).toLowerCase()}${child.table.substring(1)}")
        #set($modelQueryWrapper = "${modelName}QueryWrapper")
        #set($modelNum = "${modelName}Num")
		int ${modelNum} =0;
        QueryWrapper<${ModelName}Entity> ${modelQueryWrapper}=new QueryWrapper<>();
    #end
#end
#macro(treeTableLazyLoad)
    #if($treeTable == true&&$treeLazyType==false)
            if (! ${name}Pagination.isHasParam()) {
            if (StringUtil.isEmpty( ${name}Pagination.getTreeParentValue())) {
            sxmbybQueryWrapper.lambda().and(t -> t.eq(${Name}Entity::get${parentField}, "[]" )
            .or().isNull(${Name}Entity::get${parentField})
            .or().eq(${Name}Entity::get${parentField}, "" ));
            } else {
        ${name}QueryWrapper.lambda().eq(${Name}Entity::get${parentField},  ${name}Pagination.getTreeParentValue());
            }
            }
    #end
#end
#macro(countTableSize)
    #foreach($child in ${allTableNameList})
        #set($modelName ="${child.table.substring(0,1).toLowerCase()}${child.table.substring(1)}")
        #if(${child.tableField})
            #set($relationField = "${child.tableField.substring(0,1).toUpperCase()}${child.tableField.substring(1)}")
            long ${modelName}count = ${modelName}Service.count();
        #end
    #end
#end
#macro(listWrapper)
    #foreach($child in ${allTableNameList})
        #set($ModelName= "${child.table.substring(0,1).toUpperCase()}${child.table.substring(1)}")
        #set($modelName ="${child.table.substring(0,1).toLowerCase()}${child.table.substring(1)}")
        #set($modelQueryWrapper = "${modelName}QueryWrapper")
        #set($modelNum = "${modelName}Num")
        #if(${child.tableField})
            #set($relationField = "${child.tableField.substring(0,1).toUpperCase()}${child.tableField.substring(1)}")
            if(${modelNum}>0){
            List<String> ${modelName}IdList = ${modelName}Service.list($modelQueryWrapper).stream().filter(t->StringUtil.isNotEmpty(t.get${relationField}())).map(t->t.get${relationField}()).collect(Collectors.toList());
            long count = ${modelName}Service.count();
            if (count>0){
                intersectionList.add(${modelName}IdList);
            }
            AllIdList.addAll(${modelName}IdList);
            }
            total+=${modelNum};
        #end


    #end
#end
#macro(CreateDataPermission $menuIdModel)
    boolean pcPermission = ${pcDataPermisson};
    boolean appPermission = ${appDataPermisson};
    boolean	isPc = "pc".equals(ServletUtil.getHeader("xh-origin"));
    if(isPc && pcPermission){
		if (!userProvider.get().getIsAdministrator()){
        #foreach($child in ${allTableNameList})
            #set($ModelName= "${child.table.substring(0,1).toUpperCase()}${child.table.substring(1)}")
            #set($modelName ="${child.table.substring(0,1).toLowerCase()}${child.table.substring(1)}")
            #set($modelNum = "${modelName}Num")
            #set($initName = "${child.initName}")
            #set($modelQueryWrapper = "${modelName}QueryWrapper")
			Object ${modelName}Obj=${authorize}.getCondition(new AuthorizeConditionModel(${modelQueryWrapper},${menuIdModel}.getMenuId(),"$child.initName"));
            if (ObjectUtil.isEmpty(${modelName}Obj)){
                return new ArrayList<>();
            } else {
                ${modelQueryWrapper} = (QueryWrapper<${ModelName}Entity>)${modelName}Obj;
                if( ${modelQueryWrapper}.getExpression().getNormal().size()>0){
                    ${modelNum}++;
                }
            }
        #end
        }
    }
    if(!isPc && appPermission){
		if (!userProvider.get().getIsAdministrator()){
    #foreach($child in ${allTableNameList})
        #set($ModelName= "${child.table.substring(0,1).toUpperCase()}${child.table.substring(1)}")
        #set($modelName ="${child.table.substring(0,1).toLowerCase()}${child.table.substring(1)}")
        #set($modelNum = "${modelName}Num")
        #set($modelQueryWrapper = "${modelName}QueryWrapper")
			Object ${modelName}Obj=${authorize}.getCondition(new AuthorizeConditionModel(${modelQueryWrapper},${menuIdModel}.getMenuId(),"$child.initName"));
            if (ObjectUtil.isEmpty(${modelName}Obj)){
                return new ArrayList<>();
            } else {
            ${modelQueryWrapper} = (QueryWrapper<${ModelName}Entity>)${modelName}Obj;
				if( ${modelQueryWrapper}.getExpression().getNormal().size()>0){
                     ${modelNum}++;
				}
			}
    #end
		}
    }
#end

#macro(PaginationSerach)
    #if($groupModels.size()>0)
    if(isPc){
        #foreach($Group in ${groupModels})
            #set($ModelName= "${Group.modelName.substring(0,1).toUpperCase()}${Group.modelName.substring(1)}")
            #set($ModelNameEntity = "${ModelName}Entity")
            #set($modelName ="${Group.modelName.substring(0,1).toLowerCase()}${Group.modelName.substring(1)}")
            #set($modelQueryWrapper ="${modelName}QueryWrapper")
            #set($modelNum = "${modelName}Num")
            #set($ForeignKey =${Group.ForeignKey})
            #PaginationQuery(${Group.searchTypeModelList},${modelQueryWrapper},${ModelNameEntity},${modelNum})
        #end
        }
    #end
    #if($groupAppModels.size()>0)
    if(!isPc){
        #foreach($appGroup in ${groupAppModels})
            #set($ModelName= "${appGroup.modelName.substring(0,1).toUpperCase()}${appGroup.modelName.substring(1)}")
            #set($ModelNameEntity = "${ModelName}Entity")
            #set($modelName ="${appGroup.modelName.substring(0,1).toLowerCase()}${appGroup.modelName.substring(1)}")
            #set($modelQueryWrapper ="${modelName}QueryWrapper")
            #set($modelNum = "${modelName}Num")
            #set($ForeignKey =${appGroup.ForeignKey})
            #PaginationQuery(${appGroup.searchTypeModelList},${modelQueryWrapper},${ModelNameEntity},${modelNum})
        #end
    }
    #end
#end

#macro(childPaginationSerach $tableName)
    boolean pcPermission = ${pcDataPermisson};
    boolean appPermission = ${appDataPermisson};
    boolean	isPc = "pc".equals(ServletUtil.getHeader("xh-origin"));
    #if($groupModels.size()>0)
    if(isPc){
        #foreach($Group in ${groupModels})
            #if($tableName.toLowerCase() == "${Group.modelName.toLowerCase()}")
                #set($ModelName= "${Group.modelName.substring(0,1).toUpperCase()}${Group.modelName.substring(1)}")
                #set($ModelNameEntity = "${ModelName}Entity")
                #set($modelName ="${Group.modelName.substring(0,1).toLowerCase()}${Group.modelName.substring(1)}")
                #set($modelQueryWrapper ="${modelName}QueryWrapper")
                #set($modelNum = "${modelName}Num")
                int ${modelName}Num = 0;
                #childPaginationSearch(${Group.searchTypeModelList},${modelQueryWrapper},${ModelNameEntity})
                #ChildSuperQuery($Group.modelName)
                #ChildDataPermission($Group.modelName)
            #end
        #end
        }
    #end

    #if($groupAppModels.size()>0)
    if(!isPc){
        #foreach($appGroup in ${groupAppModels})
            #if($tableName == "${appGroup.modelName.toLowerCase()}")
                #set($ModelName= "${appGroup.modelName.substring(0,1).toUpperCase()}${appGroup.modelName.substring(1)}")
                #set($ModelNameEntity = "${ModelName}Entity")
                #set($modelName ="${appGroup.modelName.substring(0,1).toLowerCase()}${appGroup.modelName.substring(1)}")
                #set($modelQueryWrapper ="${modelName}QueryWrapper")
                #set($modelNum = "${modelName}Num")
								int ${modelName}Num = 0;
                #childPaginationSearch(${appGroup.searchTypeModelList},${modelQueryWrapper},${ModelNameEntity})
                #ChildSuperQuery($appGroup.modelName)
                #ChildDataPermission($appGroup.modelName)
            #end
        #end
        }
    #end
#end

#macro(childPaginationSearch $searchListSizes,$queryWrapper,$ModelNameEntity)
    #if($searchListSizes)
        #foreach($SearchTypeModel in ${searchListSizes})
            #if($SearchTypeModel.afterVModel)
                #set($vModelName = $SearchTypeModel.afterVModel)
            #else
                #set($vModelName = $SearchTypeModel.vModel)
            #end
            #set($fieldName = "${vModelName.substring(0,1).toUpperCase()}${vModelName.substring(1)}")
            #set($paginationName ="${SearchTypeModel.vModel.substring(0,1).toUpperCase()}${SearchTypeModel.vModel.substring(1)}")
						if(ObjectUtil.isNotEmpty(pagination.get${paginationName}())){
            #if(${SearchTypeModel.searchType}==1)
                #set($xhKey=${SearchTypeModel.xhKey})
                #if(${xhKey}=='select' || ${xhKey}=='depSelect' || ${xhKey} =='roleSelect' || ${xhKey} =='userSelect' || ${xhKey} =='treeSelect'
                || ${xhKey}=='usersSelect' || ${xhKey} =='comSelect' || ${xhKey} =='posSelect' || ${xhKey} =='groupSelect'
                || ${xhKey}=='address' || ${xhKey} =='cascader' || ${xhKey} =='currOrganize'  || ${xhKey} =='checkbox')
                        List<String> idList = new ArrayList<>();
                        try {
                            String[][] ${vModelName} = JsonXhUtil.toBean(pagination.get${paginationName}(),String[][].class);
                            for(int i=0;i<${vModelName}.length;i++){
                                if(${vModelName}[i].length>0){
                                    idList.add(JsonXhUtil.toJSONString(Arrays.asList(${vModelName}[i])));
                                }
                            }
                        }catch (Exception e1){
                            try {
                                List<String> ${vModelName} = JsonXhUtil.getJsonToList(pagination.get${paginationName}(),String.class);
                                if(${vModelName}.size()>0){
                                    #if(${xhKey}=='address' || ${xhKey} =='cascader' || ${xhKey} =='comSelect')
                                    idList.add(JsonXhUtil.toJSONString(${vModelName}));
                                    #elseif(${xhKey} =='currOrganize')
                                    idList.add(${vModelName}.get(${vModelName}.size()-1));
                                    #else
                                    idList.addAll(${vModelName});
                                    #end
                                }
                                }catch (Exception e2){
                                    idList.add(String.valueOf(pagination.get${paginationName}()));
                                }
                        }
                        ${queryWrapper}.lambda().and(t->{
                            idList.forEach(tt->{
                                t.like(${ModelNameEntity}::get${fieldName}, tt).or();
                            });
                        });
##                        List ${vModelName}List = pagination.get${paginationName}();
##                        String ${vModelName} = String.valueOf(${vModelName}List.get(${vModelName}List.size()-1));
##                    ${queryWrapper}.lambda().like(${ModelNameEntity}::get${fieldName}, ${vModelName});
##                    ${queryWrapper}.lambda().and(t->
##                        {
##                        for(Object ${vModelName} : ${vModelName}List){
##                        t.or((wrapper->{
##                        String ${vModelName}_str=${vModelName} instanceof List ?JsonUtil.getListToJsonArray((List)${vModelName}).toJSONString():${vModelName}.toString();
##                        wrapper.like(${ModelNameEntity}::get${fieldName}, ${vModelName}_str);
##                        } ));
##                        } });
                #else
                    ${queryWrapper}.lambda().eq(${ModelNameEntity}::get${fieldName},pagination.get${paginationName}());
                #end
            #elseif(${SearchTypeModel.searchType}==2)
                ${queryWrapper}.lambda().like(${ModelNameEntity}::get${fieldName},pagination.get${paginationName}());
            #elseif(${SearchTypeModel.searchType}==3)
								List ${fieldName}List = JsonXhUtil.getJsonToList(pagination.get${paginationName}(), String.class);
                #if(${SearchTypeModel.xhKey}=="dateTime" || ${SearchTypeModel.xhKey}=="date" || ${SearchTypeModel.xhKey}=="createTime" || ${SearchTypeModel.xhKey}=="modifyTime")
										Long fir = Long.valueOf(String.valueOf(${fieldName}List.get(0)));
										Long sec = Long.valueOf(String.valueOf(${fieldName}List.get(1)));

                    ${queryWrapper}.lambda().ge(${ModelNameEntity}::get${fieldName}, new Date(fir))
										.le(${ModelNameEntity}::get${fieldName}, DateUtil.stringToDate(DateUtil.daFormatYmd(sec) + " 23:59:59"));
                #elseif(${SearchTypeModel.xhKey}=="time")
										String fir = String.valueOf(${fieldName}List.get(0));
										String sec = String.valueOf(${fieldName}List.get(1));
                    ${queryWrapper}.lambda().ge(${ModelNameEntity}::get${fieldName}, fir)
										.le(${ModelNameEntity}::get${fieldName}, sec);
                #elseif(${SearchTypeModel.xhKey}=="numInput" || ${SearchTypeModel.xhKey}=="calculate")
										for(int i=0;i<${fieldName}List.size();i++){
										String id = String.valueOf(${fieldName}List.get(i));
										boolean idAll = StringUtil.isNotEmpty(id) && !id.equals("null");
										if(idAll){
										BigDecimal b= new BigDecimal(id);
										if(i==0){
                    ${queryWrapper}.lambda().ge(${ModelNameEntity}::get${fieldName},b);
										}else{
                    ${queryWrapper}.lambda().le(${ModelNameEntity}::get${fieldName},b);
										}
										}
										}
                #else
										String fir = String.valueOf(${fieldName}List.get(0));
										String sec = String.valueOf(${fieldName}List.get(1));
                    ${queryWrapper}.lambda().ge(${ModelNameEntity}::get${fieldName}, fir)
										.le(${ModelNameEntity}::get${fieldName}, sec);
                #end

            #end
						}

        #end
    #end

#end


#macro(PaginationQuery $searchListSizes $queryWrapper $ModelNameEntity,$modelNum)
#if($searchListSizes)
    #foreach($SearchTypeModel in ${searchListSizes})
        #if($SearchTypeModel.afterVModel)
            #set($vModelName = $SearchTypeModel.afterVModel)
         #else
            #set($vModelName = $SearchTypeModel.vModel)
        #end
        #set($fieldName = "${vModelName.substring(0,1).toUpperCase()}${vModelName.substring(1)}")
        #set($paginationName ="${SearchTypeModel.vModel.substring(0,1).toUpperCase()}${SearchTypeModel.vModel.substring(1)}")
			if(ObjectUtil.isNotEmpty(${name}Pagination.get${paginationName}())){
            ${modelNum}++;

                #if(${SearchTypeModel.searchType}==1)
                    #set($xhKey=${SearchTypeModel.xhKey})
                    #if(${xhKey}=='select' || ${xhKey}=='depSelect' || ${xhKey} =='roleSelect' || ${xhKey} =='userSelect' || ${xhKey} =='treeSelect'
                    || ${xhKey}=='usersSelect' || ${xhKey} =='comSelect' || ${xhKey} =='posSelect' || ${xhKey} =='groupSelect'
                    || ${xhKey}=='address' || ${xhKey} =='cascader' || ${xhKey} =='currOrganize' || ${xhKey} =='checkbox')
                            List<String> idList = new ArrayList<>();
                            try {
                                String[][] ${vModelName} = JsonXhUtil.toBean(${name}Pagination.get${paginationName}(),String[][].class);
                                for(int i=0;i<${vModelName}.length;i++){
                                    if(${vModelName}[i].length>0){
                                        idList.add(JsonXhUtil.toJSONString(Arrays.asList(${vModelName}[i])));
                                    }
                                }
                            }catch (Exception e1){
                                try {
                                    List<String> ${vModelName} = JsonXhUtil.getJsonToList(${name}Pagination.get${paginationName}(),String.class);
                                    if(${vModelName}.size()>0){
                                        #if(${xhKey}=='address' || ${xhKey} =='cascader' || ${xhKey} =='comSelect')
                                        idList.add(JsonXhUtil.toJSONString(${vModelName}));
                                        #elseif(${xhKey} =='currOrganize')
                                        idList.add(${vModelName}.get(${vModelName}.size()-1));
                                        #else
                                        idList.addAll(${vModelName});
                                        #end
                                    }
                                }catch (Exception e2){
                                    idList.add(String.valueOf(${name}Pagination.get${paginationName}()));
                                }
                            }
                        ${queryWrapper}.lambda().and(t->{
                                idList.forEach(tt->{
                                    t.like(${ModelNameEntity}::get${fieldName}, tt).or();
                                });
                            });
##                            List ${vModelName}List = ${name}Pagination.get${paginationName}();
##                            String ${vModelName} = String.valueOf(${vModelName}List.get(${vModelName}List.size()-1));
##                        ${queryWrapper}.lambda().like(${ModelNameEntity}::get${fieldName}, ${vModelName});
##                        ${queryWrapper}.lambda().and(t->
##                            {
##                            for(Object ${vModelName} : ${vModelName}List){
##                            t.or((wrapper->{
##                            String ${vModelName}_str=${vModelName} instanceof List ?JsonUtil.getListToJsonArray((List)${vModelName}).toJSONString():${vModelName}.toString();
##                            wrapper.like(${ModelNameEntity}::get${fieldName}, ${vModelName}_str);
##                            } ));
##                            } });
                    #else
                        ${queryWrapper}.lambda().eq(${ModelNameEntity}::get${fieldName},${name}Pagination.get${paginationName}());
                    #end
                #elseif(${SearchTypeModel.searchType}==2)
                    ${queryWrapper}.lambda().like(${ModelNameEntity}::get${fieldName},${name}Pagination.get${paginationName}());
                #elseif(${SearchTypeModel.searchType}==3)
										List ${fieldName}List =  JsonXhUtil.getJsonToList(${name}Pagination.get${paginationName}(),String.class);
                    #if(${SearchTypeModel.xhKey}=="dateTime" || ${SearchTypeModel.xhKey}=="date" || ${SearchTypeModel.xhKey}=="createTime" || ${SearchTypeModel.xhKey}=="modifyTime")
												Long fir = Long.valueOf(String.valueOf(${fieldName}List.get(0)));
												Long sec = Long.valueOf(String.valueOf(${fieldName}List.get(1)));

                ${queryWrapper}.lambda().ge(${ModelNameEntity}::get${fieldName}, new Date(fir))
            .le(${ModelNameEntity}::get${fieldName}, DateUtil.stringToDate(DateUtil.daFormatYmd(sec) + " 23:59:59"));
        #elseif(${SearchTypeModel.xhKey}=="time")
            String fir = String.valueOf(${fieldName}List.get(0));
            String sec =  String.valueOf(${fieldName}List.get(1));
            ${queryWrapper}.lambda().ge(${ModelNameEntity}::get${fieldName}, fir)
            .le(${ModelNameEntity}::get${fieldName}, sec);
        #elseif(${SearchTypeModel.xhKey}=="numInput" || ${SearchTypeModel.xhKey}=="calculate")
            for(int i=0;i<${fieldName}List.size();i++){
                String id = String.valueOf(${fieldName}List.get(i));
                boolean idAll = StringUtil.isNotEmpty(id) && !id.equals("null");
            if(idAll){
                BigDecimal b= new BigDecimal(id);
                if(i==0){
                ${queryWrapper}.lambda().ge(${ModelNameEntity}::get${fieldName},b);
                }else{
                ${queryWrapper}.lambda().le(${ModelNameEntity}::get${fieldName},b);
                 }
                }
            }
            #else
            String fir = String.valueOf(${fieldName}List.get(0));
            String sec = String.valueOf(${fieldName}List.get(1));
                ${queryWrapper}.lambda().ge(${ModelNameEntity}::get${fieldName}, fir)
            .le(${ModelNameEntity}::get${fieldName}, sec);
            #end

                #end

                    }

    #end
#end
#end

#macro(SuperQuery $tableName)
	if (ObjectUtil.isNotEmpty(${tableName}Pagination.getSuperQueryJson())){
		SuperQueryJsonModel superQueryJsonModel = JsonXhUtil.toBean(${tableName}Pagination.getSuperQueryJson(), SuperQueryJsonModel.class);
		String matchLogic = superQueryJsonModel.getMatchLogic();
		List<ConditionJsonModel> superQueryList = JsonXhUtil.getJsonToList(superQueryJsonModel.getConditionJson(), ConditionJsonModel.class);
		for (ConditionJsonModel conditionjson : superQueryList){
            Map<String, Object> map = JsonXhUtil.stringToMap(conditionjson.getAttr());
            Map<String, Object> configMap = JsonXhUtil.stringToMap(map.get("__config__").toString());
            String tableName = configMap.get("relationTable")!=null ? String.valueOf(configMap.get("relationTable")) : String.valueOf(configMap.get("tableName"));
		    if (map.get("multiple") != null) {
		        if (Boolean.valueOf(String.valueOf(map.get("multiple"))) && ObjectUtil.isNull(conditionjson.getFieldValue())) {
		            conditionjson.setFieldValue("[]");
		        }
		    }
            conditionjson.setTableName(tableName);
		}
		List<String> allSuperList = new ArrayList<>();
		List<List<String>> intersectionSuperList  = new ArrayList<>();
    #foreach($child in ${allTableNameList})
        #set($ModelName= "${child.table.substring(0,1).toUpperCase()}${child.table.substring(1)}")
        #set($modelName ="${child.table.substring(0,1).toLowerCase()}${child.table.substring(1)}")
        #set($modelQueryWrapper = "${modelName}SuperWrapper")
        #set($modelNum = "${modelName}Num1")
        int $modelNum = 0;
        QueryWrapper<${ModelName}Entity> $modelQueryWrapper = new QueryWrapper<>();
        $modelNum = generaterSwapUtil.getCondition(new SuperQueryConditionModel(${modelQueryWrapper},superQueryList,matchLogic,"$child.initName")
        ,new ${ModelName}Entity(),$modelNum);
        #if(${child.tableField})
            #set($relationField = "${child.tableField.substring(0,1).toUpperCase()}${child.tableField.substring(1)}")
            if ($modelNum>0 && ${modelName}count>0){
            List<String> ${modelName}List =${modelName}Service.list($modelQueryWrapper).stream().map(${ModelName}Entity::get${relationField}).collect(Collectors.toList());
            allSuperList.addAll(${modelName}List);
            intersectionSuperList.add(${modelName}List);
            }
        #else
            if ($modelNum>0){
            #if($snowflake)
            List<String> ${modelName}List =this.list($modelQueryWrapper).stream().map(${ModelName}Entity::get${peimaryKeyName}).collect(Collectors.toList());
            #else
            List<String> ${modelName}List =this.list($modelQueryWrapper).stream().map(${ModelName}Entity::getFlowtaskid).collect(Collectors.toList());
            #end
            allSuperList.addAll(${modelName}List);
            intersectionSuperList.add(${modelName}List);
            }
        #end
    #end
		superOp = matchLogic;
		//and or
		if(matchLogic.equalsIgnoreCase("and")){
		    allSuperIDlist = generaterSwapUtil.getIntersection(intersectionSuperList);
		}else{
		    allSuperIDlist = allSuperList;
		}
    }
#end

#macro(ChildSuperQuery $childModelName)
		if (ObjectUtil.isNotEmpty(pagination.getSuperQueryJson())){
		SuperQueryJsonModel superQueryJsonModel = JsonXhUtil.toBean(pagination.getSuperQueryJson(), SuperQueryJsonModel.class);
		String matchLogic = superQueryJsonModel.getMatchLogic();
		List<ConditionJsonModel> superQueryList = JsonXhUtil.getJsonToList(superQueryJsonModel.getConditionJson(), ConditionJsonModel.class);
		for (ConditionJsonModel conditionjson : superQueryList){
		Map<String, Object> map = JsonXhUtil.stringToMap(conditionjson.getAttr());
		Map<String, Object> configMap = JsonXhUtil.stringToMap(map.get("__config__").toString());
		String tableName = configMap.get("relationTable")!=null ? String.valueOf(configMap.get("relationTable")) : String.valueOf(configMap.get("tableName"));
		if (map.get("multiple") != null) {
		    if (Boolean.valueOf(String.valueOf(map.get("multiple"))) && ObjectUtil.isNull(conditionjson.getFieldValue())) {
		        conditionjson.setFieldValue("[]");
		    }
		}
		conditionjson.setTableName(tableName);
		}
    #foreach($child in ${allTableNameList})
        #if(${child.table} == $childModelName)
        #set($ModelName= "${child.table.substring(0,1).toUpperCase()}${child.table.substring(1)}")
        #set($modelName ="${child.table.substring(0,1).toLowerCase()}${child.table.substring(1)}")
        #set($modelQueryWrapper = "${modelName}QueryWrapper")
         $modelNum = generaterSwapUtil.getCondition(new SuperQueryConditionModel(${modelQueryWrapper},superQueryList,matchLogic,"$child.initName")
				,new ${ModelName}Entity(),$modelNum);
        #end
    #end
		}
#end

#macro(ChildDataPermission $menuIdModel)
		if(isPc && pcPermission){
		if (!userProvider.get().getIsAdministrator()){
    #foreach($child in ${allTableNameList})
        #if($menuIdModel == $child.table)
        #set($ModelName= "${child.table.substring(0,1).toUpperCase()}${child.table.substring(1)}")
        #set($modelName ="${child.table.substring(0,1).toLowerCase()}${child.table.substring(1)}")
        #set($modelNum = "${modelName}Num")
        #set($initName = "${child.initName}")
        #set($modelQueryWrapper = "${modelName}QueryWrapper")

				Object ${modelName}Obj=${authorize}.getCondition(new AuthorizeConditionModel(${modelQueryWrapper},pagination.getMenuId(),"$child.initName"));
				if (ObjectUtil.isEmpty(${modelName}Obj)){

				} else {
                     ${modelQueryWrapper} = (QueryWrapper<${ModelName}Entity>)${modelName}Obj;
                    if( ${modelQueryWrapper}.getExpression().getNormal().size()>0){
                         ${modelNum}++;
                    }
				}
        #end
    #end
		}
		}
		if(!isPc && appPermission){
		if (!userProvider.get().getIsAdministrator()){
    #foreach($child in ${allTableNameList})
        #if($menuIdModel == $child.table)
        #set($ModelName= "${child.table.substring(0,1).toUpperCase()}${child.table.substring(1)}")
        #set($modelName ="${child.table.substring(0,1).toLowerCase()}${child.table.substring(1)}")
        #set($modelNum = "${modelName}Num")
        #set($modelQueryWrapper = "${modelName}QueryWrapper")
						Object ${modelName}Obj=${authorize}.getCondition(new AuthorizeConditionModel(${modelQueryWrapper},pagination.getMenuId(),"$child.initName"));
				if (ObjectUtil.isEmpty(${modelName}Obj)){

				} else {
            ${modelQueryWrapper} = (QueryWrapper<${ModelName}Entity>)${modelName}Obj;
						if( ${modelQueryWrapper}.getExpression().getNormal().size()>0){
                        ${modelNum}++;
						}
				}
        #end

    #end
		}
		}
#end

import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import java.text.SimpleDateFormat;
import com.xinghuo.common.util.*;
import com.xinghuo.common.hutool.*;
import java.util.*;

/**
 *
 * ${genInfo.description}
 * 版本： ${genInfo.version}
 * 版权： ${genInfo.copyright}
 * 作者： ${genInfo.createUser}
 * 日期： ${genInfo.createDate}
 */
@Service
#if(${DS})
@DS("${DS}")
#end
public class ${table.serviceImplName} extends ${superServiceImplClass}<${table.mapperName}, ${table.entityName}> implements ${table.serviceName}#if(${DS}),DynamicSourceGeneratorInterface #end  {

    #if(${DS})
##        @Autowired
##        private DbLinkService dblinkService;
    #end
        @Autowired
        private GeneraterSwapUtil generaterSwapUtil;

        @Autowired
        private UserProvider userProvider;

        @Autowired
        private ${Authorize} ${authorize};
#if(${main})

    #foreach($subfield in ${childTableNameList})
        #set($ChildName="${subfield.table.substring(0,1).toUpperCase()}${subfield.table.substring(1)}")
        #set($childName="${subfield.table.substring(0,1).toLowerCase()}${subfield.table.substring(1)}")
    @Autowired
    private ${ChildName}Service ${childName}Service;
    #end

    #set($serviceName = "${table.serviceName.substring(0,1).toLowerCase()}${table.serviceName.substring(1)}")

    #set($Entity = "${table.entityName}")


    #set($searchListSize =$!{searchList})
    @Override
    public List<${Entity}> getList(${Name}Pagination ${name}Pagination){
        return getTypeList(${name}Pagination,${name}Pagination.getDataType());
    }
    @Override
    public List<${Entity}> getTypeList(${Name}Pagination ${name}Pagination,String dataType){
        String userId=userProvider.get().getUserId();
        List<String> AllIdList =new ArrayList();
		List<List<String>> intersectionList =new ArrayList<>();
		int total=0;
#CreateWrapper()
//树形，异步父级查询条件
#treeTableLazyLoad()
#countTableSize()
#if($superQuery)
		List<String> allSuperIDlist = new ArrayList<>();
		String superOp ="";
#SuperQuery(${name})
#end
#CreateDataPermission("${name}Pagination")

    #if (${hasSub} ==true && ( ${hasFilter} == true ||${hasAppFilter} == true ) )
            // 副表过滤条件
            List<String> filterIds = new ArrayList<>();
        #foreach($child in ${allTableNameList})
            #set($ModelName= "${child.table.substring(0,1).toUpperCase()}${child.table.substring(1)}")
            #set($modelName ="${child.table.substring(0,1).toLowerCase()}${child.table.substring(1)}")
            #set($modelNum = "${modelName}Num1")
            #if(${child.tableField})
                #set($relationField = "${child.tableField.substring(0,1).toUpperCase()}${child.tableField.substring(1)}")
                #if(${child.tableTag}!="main")
                        this.wrapperHandle(${modelName}QueryWrapper, ${name}Pagination.getModuleId(), ${ModelName}Entity.class, "${child.tableTag}","${modelName}");
                #end

                    List<String> ${modelName}FilterList = ${modelName}Service.list(${modelName}QueryWrapper).stream().map(${ModelName}Entity::get${relationField}).collect(Collectors.toList());

                #if(${foreach.index}==1)
                        filterIds = ${modelName}FilterList;
                #else
                        filterIds.retainAll(${modelName}FilterList);
                #end

            #end
        #end
            if(filterIds.size()==0 && !this.onlyMainFilter(${name}Pagination.getModuleId())){
            return new ArrayList<>();
            }
            List<String> finalFilterIds = filterIds;
        ${name}QueryWrapper.lambda().and(t -> t.in(${Entity}::get${peimaryKeyName}, finalFilterIds ));

    #end

#PaginationSerach()
    #listWrapper()
		List<String> intersection = generaterSwapUtil.getIntersection(intersectionList);
		if (total>0){
		if (intersection.size()==0){
		    intersection.add("xhNullList");
		}
		#if($snowflake)
         ${QueryWrapper}.lambda().in(${Entity}::get${peimaryKeyName}, intersection);
         #else
         ${QueryWrapper}.lambda().in(${Entity}::getFlowtaskid, intersection);
        #end
		}
        //是否有高级查询
    #if($superQuery)
		if (StringUtil.isNotEmpty(superOp)){
		    if (allSuperIDlist.size()==0){
		        allSuperIDlist.add("xhNullList");
		    }
		List<String> finalAllSuperIDlist = allSuperIDlist;
        #if($snowflake)
            ${QueryWrapper}.lambda().and(t->t.in(${Entity}::get${peimaryKeyName}, finalAllSuperIDlist));
        #else
            ${QueryWrapper}.lambda().and(t->t.in(${Entity}::getFlowtaskid, finalAllSuperIDlist));
        #end
		}
    #end

    #if($logicalDelete)
            //假删除标志
        ${name}QueryWrapper.lambda().isNull(${Name}Entity::getDeletemark);
    #end

        //排序
        if(StringUtil.isEmpty(${name}Pagination.getSidx())){
    #if(${defaultSidx})
    #set($model = "${defaultSidx.substring(0,1).toUpperCase()}${defaultSidx.substring(1)}")
    #set($Sort = "${sort.substring(0,1).toUpperCase()}${sort.substring(1)}")
        ${QueryWrapper}.lambda().orderBy${Sort}(${Entity}::get${model});
    #else
        ${QueryWrapper}.lambda().orderByDesc(${Entity}::get${peimaryKeyName});
    #end
        }else{
            try {
            	String sidx = ${name}Pagination.getSidx();
                String[] strs= sidx.split("_name");
                ${Entity} ${name}Entity = new ${Entity}();
                Field declaredField = ${name}Entity.getClass().getDeclaredField(strs[0]);
				declaredField.setAccessible(true);
				String value = declaredField.getAnnotation(TableField.class).value();
                 ${QueryWrapper}="asc".equals(${name}Pagination.getSort().toLowerCase())?${QueryWrapper}.orderByAsc(value):${QueryWrapper}.orderByDesc(value);
            } catch (NoSuchFieldException e) {
                e.printStackTrace();
            }
        }


    #if ( ( ${hasFilter} == true ||${hasAppFilter} == true ) )
            // 过滤条件
            if(${QueryWrapper}.getSqlSegment().startsWith(" ORDER")){
                ${QueryWrapper}.apply(" 1=1 ");
            }
        ${QueryWrapper}.and(t->{
            this.wrapperHandle(t,${name}Pagination.getModuleId(), getEntityClass(), "main","${name}");
            });
    #end


        if("0".equals(dataType)){
            if((total>0 && AllIdList.size()>0) || total==0){
                Page<${Entity}> page=new Page<>(${name}Pagination.getCurrentPage(), ${name}Pagination.getPageSize());
                IPage<${Entity}> userIPage=this.page(page, ${QueryWrapper});
                return ${name}Pagination.setData(userIPage.getRecords(),userIPage.getTotal());
            }else{
                List<${Entity}> list = new ArrayList();
                return ${name}Pagination.setData(list, list.size());
            }
        }else{
            return this.list(${QueryWrapper});
        }
    }
    private boolean onlyMainFilter(String moduleId) {
        List<RuleInfo> ruleInfos = generaterSwapUtil.getFilterCondition(moduleId);
        for (RuleInfo info : ruleInfos){
            if(info.getField().contains("_xh_") || info.getField().contains("-")){
                return  false;
            }
        }
        return true;
    }
    public QueryWrapper wrapperHandle(QueryWrapper<?> wrapper, String id, Class<?> aClass, String type,String tableName) {
        try{
            // 避免空and
            wrapper.apply(" 1=1 ");
            List<RuleInfo> ruleInfos = generaterSwapUtil.getFilterCondition(id);
            for (RuleInfo info : ruleInfos){
                String field=info.getField();
                if ("main".equals(type) && field.contains("-")) {
                    continue;
                }
                if ("main".equals(type) && field.contains("_xh_" )) {
                    continue;
                }
                if ("sub".equals(type) && !field.contains("-")) {
                    continue;
                }
                if("sub-xh".equals(type) && !field.contains("_xh_" )){
                    continue;
                }
                String fieldName = field ;
                String table = "";
                if(field.contains("-")){
                    fieldName= field.split("-")[1];
                    if(!tableName.equals(field.split("-")[0])){
                        continue;
                    }
                }
                if(field.contains("_xh_" )){
                    fieldName = field.split("_xh_" )[1];
                    table = field.split("_xh_" )[0];
                    table = table.replace("xh_","");
                }
                if("sub-xh".equals(type) && !tableName.equals(table)){
                    continue;
                }
                Field declaredField=aClass.getDeclaredField(fieldName);
                declaredField.setAccessible(true);
                String fieldDb=declaredField.getAnnotation(TableField.class).value();
                GenUtil genUtil=JsonXhUtil.toBean(info,GenUtil.class);
                genUtil.setDbType("${dbType}");
                genUtil.solveValue(wrapper, fieldDb);
            }
            return wrapper;
        }catch (Exception e){
            return  wrapper;
        }
    }

    @Override
    public ${Entity} getInfo(String ${peimaryKeyname}){
        QueryWrapper<${Entity}> queryWrapper=new QueryWrapper<>();
        #if($snowflake)
            queryWrapper.lambda().eq(${Entity}::get${peimaryKeyName},${peimaryKeyname});
        #else
        queryWrapper.lambda().and(
        t->t.eq(${Entity}::get${peimaryKeyName},${peimaryKeyname})
        .or().eq(${Entity}::getFlowtaskid, ${peimaryKeyname})
        );
        #end
        return this.getOne(queryWrapper);
    }

    @Override
    public void create(${Entity} entity){
        this.save(entity);
    }

    @Override
    public boolean update(String ${peimaryKeyname}, ${Entity} entity){
##        entity.set${peimaryKeyName}(${peimaryKeyname});
        return this.updateById(entity);
    }
    @Override
    public void delete(${Entity} entity){
        if(entity!=null){
            this.removeById(entity.get${peimaryKeyName}());
        }
    }
    //子表方法
    #foreach($grid in ${child})
		@Override
		public List<${grid.className}Entity> get${grid.className}List(String id,${Name}Pagination ${name}Pagination){
        #set($serviceName = "${grid.className.substring(0,1).toLowerCase()}${grid.className.substring(1)}")
        #set($tableField = "${grid.tableField.substring(0,1).toUpperCase()}${grid.tableField.substring(1)}")
				QueryWrapper<${grid.className}Entity> queryWrapper = new QueryWrapper<>();
				queryWrapper = ${serviceName}Service.getChild(${name}Pagination,queryWrapper);
				queryWrapper.lambda().eq(${grid.className}Entity::get${tableField}, id);
				return ${serviceName}Service.list(queryWrapper);
				}

		@Override
		public List<${grid.className}Entity> get${grid.className}List(String id){
        #set($serviceName = "${grid.className.substring(0,1).toLowerCase()}${grid.className.substring(1)}")
        #set($tableField = "${grid.tableField.substring(0,1).toUpperCase()}${grid.tableField.substring(1)}")
				QueryWrapper<${grid.className}Entity> queryWrapper = new QueryWrapper<>();
				queryWrapper.lambda().eq(${grid.className}Entity::get${tableField}, id);
				return ${serviceName}Service.list(queryWrapper);
				}
    #end

    //列表子表数据方法
    #foreach($child in ${columnChildren})
	@Override
    public ${child.modelUpName}Entity get${child.modelUpName}(String id){
    	QueryWrapper<${child.modelUpName}Entity> queryWrapper = new QueryWrapper<>();
		queryWrapper.lambda().eq(${child.modelUpName}Entity::get${child.relationUpField}, id);
        return ${child.modelLowName}Service.getOne(queryWrapper);
        }
    #end



    //验证表单唯一字段
    @Override
    public String checkForm(${Name}Form form,int i) {
        int total = 0;
		boolean isUp =StringUtil.isNotEmpty(form.get${peimaryKeyName}()) && !form.get${peimaryKeyName}().equals("0");
		String id="";
		String countRecover = "";
		if (isUp){
			#if($snowflake)
			 id = form.get${peimaryKeyName}();
             #else
             id = this.getInfo(form.getId()).getFlowtaskid();
            #end
		}
    #foreach($mastField in $mast)
            #set($Field = $mastField.formColumnModel.fieLdsModel)
            #set($config = $Field.config)
            #set($unique = $config.unique)
            #set($xhKey = $config.xhKey)
            #set($vModel = ${Field.vModel})
            #if($xhKey == 'comInput' && $unique ==true)
                #set($upName = "${vModel.substring(0,1).toUpperCase()}${vModel.substring(1)}")
                if(ObjectUtil.isNotEmpty(form.get${upName}())){
                    form.set${upName}(form.get${upName}().trim());
                    QueryWrapper<${Name}Entity> ${vModel}Wrapper=new QueryWrapper<>();
                    ${vModel}Wrapper.lambda().eq(${Name}Entity::get${upName},form.get${upName}());
                #if($logicalDelete)
                        //假删除标志
                    ${vModel}Wrapper.lambda().isNull(${Name}Entity::getDeletemark);
                #end
                    if (isUp){
                    #if($snowflake)
                        ${vModel}Wrapper.lambda().ne(${Name}Entity::get${peimaryKeyName}, id);
                    #else
                        ${vModel}Wrapper.lambda().ne(${Name}Entity::getFlowtaskid, id);
                    #end
                    }
                    if((int) this.count(${vModel}Wrapper)>0){
						total ++;
                        countRecover = "${config.label}";
                     }
                }
            #end
        #end
     #foreach($grid in ${child})
    #set($list = "${grid.className.substring(0,1).toUpperCase()}${grid.className.substring(1).toLowerCase()}")
    #set($serviceName = "${grid.className.substring(0,1).toLowerCase()}${grid.className.substring(1)}")
         #set($tableField = "${grid.tableField.substring(0,1).toUpperCase()}${grid.tableField.substring(1)}")
				 if (form.get${list}List()!=null){
         #foreach($xhkey in ${grid.childList})
             #if(${xhkey.fieLdsModel.vModel} != '')
                 #set($key = ${xhkey.fieLdsModel.config.xhKey})
                 #set($model = "${xhkey.fieLdsModel.vModel.substring(0,1).toUpperCase()}${xhkey.fieLdsModel.vModel.substring(1)}")
                 #set($unique = $xhkey.fieLdsModel.config.unique)
                 #if($key =="comInput" && $unique ==true)
            form.get${list}List().stream().forEach(t->{
                if(StringUtil.isNotEmpty(t.get${model}())){
                    t.set${model}(t.get${model}().trim());
                }
            });
			QueryWrapper<${grid.className}Entity> ${grid.className}${model}Wrapper = new QueryWrapper<>();
            List<String> ${model}List = form.get${list}List().stream().filter(f->StringUtil.isNotEmpty(f.get${model}())).map(f -> f.get${model}()).collect(Collectors.toList());
            HashSet<String> ${model}Set = new HashSet<>(${model}List);
            if(${model}Set.size() != ${model}List.size()){
                 countRecover = "${xhkey.fieLdsModel.config.label}";
             }
            if(${model}List.size()>0){
                 for (String ${model} : ${model}List) {
                     ${grid.className}${model}Wrapper.lambda().eq(${grid.className}Entity::get${model}, ${model});
                     if(isUp){
                         ${grid.className}${model}Wrapper.lambda().ne(${grid.className}Entity::get${tableField},id);
                     }
                     if((int) ${serviceName}Service.count(${grid.className}${model}Wrapper) > 0){
                        countRecover = "${xhkey.fieLdsModel.config.label}";
                        total ++;
                     }
                 }
             }
                 #end
             #end
         #end
         }
        #end
#if(${columnChildren.size()}>0)
    #foreach($cl in  ${columnChildren})
        #set($oracleName = "${cl.TableName.substring(0,1).toUpperCase()}${cl.TableName.substring(1).toLowerCase()}")
		if(ObjectUtil.isNotEmpty(form.get${oracleName}())){
        #foreach($clModel in ${cl.fieLdsModelList})
            #set($model = "${clModel.field.substring(0,1).toUpperCase()}${clModel.field.substring(1).toLowerCase()}")
            #set($key =  ${clModel.mastTable.fieLdsModel.config.xhKey})
            #set($unique = $clModel.mastTable.fieLdsModel.config.unique)
            #if($key =="comInput" && $unique ==true)
                    if(ObjectUtil.isNotEmpty(form.get${oracleName}().get${model}())){
                    form.get${oracleName}().set${model}(form.get${oracleName}().get${model}().trim());
                    QueryWrapper<${oracleName}Entity> ${oracleName}${model}Wrapper=new QueryWrapper<>();
                ${oracleName}${model}Wrapper.lambda().eq(${oracleName}Entity::get${model},form.get${oracleName}().get${model}());
                    if (isUp){
                    ${oracleName}${model}Wrapper.lambda().ne(${oracleName}Entity::get${cl.relationUpField}, id);
                    }
                    if((int) ${cl.modelLowName}Service.count(${oracleName}${model}Wrapper)>i){
                        countRecover = "${clModel.mastTable.fieLdsModel.config.label}";
						total ++;
                    }
                    }
             #end
        #end
		}
    #end
#end

        return countRecover;
    }

#else
    #set($childWrapperName = "${modelName.substring(0,1).toLowerCase()}${modelName.substring(1)}")
@Override
public QueryWrapper<${table.entityName}> getChild(${mainModelName}Pagination pagination, QueryWrapper<${table.entityName}> ${childWrapperName}QueryWrapper){
         #childPaginationSerach(${table.name})
        return ${childWrapperName}QueryWrapper;
		}
#end
#if(${DS})
        @Override
        public DataSourceUtil getDataSource() {
            return generaterSwapUtil.getDataSource(this.getClass().getAnnotation(DS.class).value());
		}
#end
}
