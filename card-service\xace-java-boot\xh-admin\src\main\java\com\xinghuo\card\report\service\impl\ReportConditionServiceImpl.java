package com.xinghuo.card.report.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xinghuo.card.report.dao.ReportQueryConditionMapper;
import com.xinghuo.card.report.entity.ReportQueryConditionEntity;
import com.xinghuo.card.report.service.ReportConditionService;
import com.xinghuo.common.util.core.RandomUtil;
import com.xinghuo.common.util.json.JsonXhUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;

/**
 * 报表查询条件服务实现类
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2024-01-01
 */
@Slf4j
@Service
public class ReportConditionServiceImpl extends ServiceImpl<ReportQueryConditionMapper, ReportQueryConditionEntity> 
        implements ReportConditionService {

    @Override
    public List<ReportQueryConditionEntity> getByUserId(String userId, Integer reportType) {
        QueryWrapper<ReportQueryConditionEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(ReportQueryConditionEntity::getCreatedBy, userId)
                .orderByDesc(ReportQueryConditionEntity::getLastUseTime)
                .orderByDesc(ReportQueryConditionEntity::getUseCount);

        if (reportType != null) {
            queryWrapper.lambda().eq(ReportQueryConditionEntity::getReportType, reportType);
        }

        return this.list(queryWrapper);
    }

    @Override
    public List<ReportQueryConditionEntity> getPublicConditions(Integer reportType) {
        QueryWrapper<ReportQueryConditionEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(ReportQueryConditionEntity::getIsPublic, true)
                .orderByDesc(ReportQueryConditionEntity::getUseCount);

        if (reportType != null) {
            queryWrapper.lambda().eq(ReportQueryConditionEntity::getReportType, reportType);
        }

        return this.list(queryWrapper);
    }

    @Override
    public List<ReportQueryConditionEntity> getFavoriteConditions(String userId) {
        QueryWrapper<ReportQueryConditionEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(ReportQueryConditionEntity::getCreatedBy, userId)
                .eq(ReportQueryConditionEntity::getIsFavorite, true)
                .orderByAsc(ReportQueryConditionEntity::getListOrder)
                .orderByDesc(ReportQueryConditionEntity::getLastUseTime);

        return this.list(queryWrapper);
    }

    @Override
    public boolean saveCondition(ReportQueryConditionEntity condition) {
        try {
            if (StringUtils.isBlank(condition.getId())) {
                condition.setId(RandomUtil.snowId());
                condition.setUseCount(0);

                if (condition.getIsFavorite() == null) {
                    condition.setIsFavorite(false);
                }
                if (condition.getIsPublic() == null) {
                    condition.setIsPublic(false);
                }
            }

            return this.saveOrUpdate(condition);
        } catch (Exception e) {
            log.error("保存查询条件失败", e);
            return false;
        }
    }

    @Override
    public boolean incrementUseCount(String conditionId) {
        try {
            ReportQueryConditionEntity condition = this.getById(conditionId);
            if (condition != null) {
                condition.setUseCount((condition.getUseCount() != null ? condition.getUseCount() : 0) + 1);
                condition.setLastUseTime(new Date());
                return this.updateById(condition);
            }
            return false;
        } catch (Exception e) {
            log.error("增加使用次数失败", e);
            return false;
        }
    }

    @Override
    public boolean toggleFavorite(String conditionId, boolean favorite) {
        try {
            ReportQueryConditionEntity condition = this.getById(conditionId);
            if (condition != null) {
                condition.setIsFavorite(favorite);
                return this.updateById(condition);
            }
            return false;
        } catch (Exception e) {
            log.error("切换收藏状态失败", e);
            return false;
        }
    }

    @Override
    public Map<String, Object> convertToQueryParams(ReportQueryConditionEntity condition) {
        Map<String, Object> params = new HashMap<>();

        // 处理动态时间范围
        Date[] dateRange = calculateDateRange(condition);
        params.put("startDate", dateRange[0]);
        params.put("endDate", dateRange[1]);

        // 将单个 transType 转换为列表格式
        if (condition.getTransType() != null) {
            params.put("transType", Arrays.asList(condition.getTransType()));
        }
        params.put("keywords", condition.getKeywords());
        params.put("minAmount", condition.getMinAmount());
        params.put("maxAmount", condition.getMaxAmount());

        // 解析JSON字段
        if (StringUtils.isNotBlank(condition.getAccIds())) {
            try {
                List<String> accIds = JsonXhUtil.jsonToList(condition.getAccIds(), String.class);
                params.put("accIds", accIds);
            } catch (Exception e) {
                log.warn("解析账户ID列表失败", e);
            }
        }

        if (StringUtils.isNotBlank(condition.getFlowTypes())) {
            try {
                List<String> flowTypeIds = JsonXhUtil.jsonToList(condition.getFlowTypes(), String.class);
                params.put("flowTypeIds", flowTypeIds);
            } catch (Exception e) {
                log.warn("解析分类ID列表失败", e);
            }
        }

        // 解析额外的查询条件，但排除时间相关的配置（已在上面处理）
        if (StringUtils.isNotBlank(condition.getQueryConditions())) {
            try {
                Map<String, Object> extraParams = JsonXhUtil.stringToMap(condition.getQueryConditions());
                // 移除时间相关的配置，避免重复
                extraParams.remove("dateType");
                extraParams.remove("period");
                params.putAll(extraParams);
            } catch (Exception e) {
                log.warn("解析查询条件JSON失败", e);
            }
        }

        return params;
    }

    @Override
    public ReportQueryConditionEntity createFromParams(Map<String, Object> params, String userId, String conditionName) {
        ReportQueryConditionEntity condition = new ReportQueryConditionEntity();

        condition.setCreatedBy(userId);
        condition.setConditionName(conditionName);
        condition.setStartDate(parseDate(params.get("startDate")));
        condition.setEndDate(parseDate(params.get("endDate")));
        // 处理 transType，可能是单个值或列表
        Object transTypeObj = params.get("transType");
        if (transTypeObj instanceof List) {
            List<Integer> transTypeList = (List<Integer>) transTypeObj;
            if (!transTypeList.isEmpty()) {
                condition.setTransType(transTypeList.get(0)); // 取第一个值作为主要类型
            }
        } else if (transTypeObj instanceof Integer) {
            condition.setTransType((Integer) transTypeObj);
        }
        condition.setKeywords((String) params.get("keywords"));
        condition.setMinAmount((java.math.BigDecimal) params.get("minAmount"));
        condition.setMaxAmount((java.math.BigDecimal) params.get("maxAmount"));

        // 处理列表参数
        List<String> accIds = (List<String>) params.get("accIds");
        if (accIds != null && !accIds.isEmpty()) {
            try {
                condition.setAccIds(JsonXhUtil.getObjectToString(accIds));
            } catch (Exception e) {
                log.warn("序列化账户ID列表失败", e);
            }
        }

        List<String> flowTypeIds = (List<String>) params.get("flowTypeIds");
        if (flowTypeIds != null && !flowTypeIds.isEmpty()) {
            try {
                condition.setFlowTypes(JsonXhUtil.getObjectToString(flowTypeIds));
            } catch (Exception e) {
                log.warn("序列化分类ID列表失败", e);
            }
        }

        // 处理其他参数
        Map<String, Object> extraParams = new HashMap<>();
        for (Map.Entry<String, Object> entry : params.entrySet()) {
            String key = entry.getKey();
            if (!Arrays.asList("startDate", "endDate", "transType", "keywords", "minAmount", "maxAmount", "accIds", "flowTypeIds").contains(key)) {
                extraParams.put(key, entry.getValue());
            }
        }

        if (!extraParams.isEmpty()) {
            try {
                condition.setQueryConditions(JsonXhUtil.getObjectToString(extraParams));
            } catch (Exception e) {
                log.warn("序列化额外查询条件失败", e);
            }
        }

        return condition;
    }

    /**
     * 安全地将对象转换为Date
     * 支持Date对象、ISO 8601字符串格式
     */
    private Date parseDate(Object dateObj) {
        if (dateObj == null) {
            return null;
        }

        if (dateObj instanceof Date) {
            return (Date) dateObj;
        }

        if (dateObj instanceof String) {
            String dateStr = (String) dateObj;
            if (StringUtils.isBlank(dateStr)) {
                return null;
            }

            try {
                // 尝试解析ISO 8601格式 (如: 2025-05-31T16:00:00.000Z)
                if (dateStr.contains("T") && dateStr.endsWith("Z")) {
                    return Date.from(Instant.parse(dateStr));
                }

                // 尝试解析标准日期格式
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                return sdf.parse(dateStr);
            } catch (Exception e) {
                log.warn("解析日期字符串失败: {}", dateStr, e);
                return null;
            }
        }

        log.warn("不支持的日期类型: {}", dateObj.getClass().getName());
        return null;
    }

    /**
     * 计算动态时间范围
     * 支持相对时间概念，如"本月"、"本年"、"近30天"等
     */
    private Date[] calculateDateRange(ReportQueryConditionEntity condition) {
        // 如果有固定的开始和结束时间，直接使用
        if (condition.getStartDate() != null && condition.getEndDate() != null) {
            return new Date[]{condition.getStartDate(), condition.getEndDate()};
        }

        // 解析动态时间配置
        if (StringUtils.isNotBlank(condition.getQueryConditions())) {
            try {
                Map<String, Object> queryConfig = JsonXhUtil.stringToMap(condition.getQueryConditions());
                String dateType = (String) queryConfig.get("dateType");
                Object period = queryConfig.get("period");

                if (dateType != null) {
                    return calculateDynamicDateRange(dateType, period);
                }
            } catch (Exception e) {
                log.warn("解析动态时间配置失败: {}", condition.getQueryConditions(), e);
            }
        }

        // 默认返回本月时间范围
        return calculateDynamicDateRange("month", "current");
    }

    /**
     * 根据时间类型和周期计算具体的时间范围
     */
    private Date[] calculateDynamicDateRange(String dateType, Object period) {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime startTime;
        LocalDateTime endTime;

        switch (dateType) {
            case "month":
                if ("current".equals(period)) {
                    // 本月
                    startTime = now.withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0).withNano(0);
                    endTime = now.withDayOfMonth(now.toLocalDate().lengthOfMonth()).withHour(23).withMinute(59).withSecond(59).withNano(999999999);
                } else if ("last6months".equals(period)) {
                    // 最近6个月
                    startTime = now.minusMonths(6).withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0).withNano(0);
                    endTime = now.withHour(23).withMinute(59).withSecond(59).withNano(999999999);
                } else {
                    // 默认本月
                    startTime = now.withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0).withNano(0);
                    endTime = now.withDayOfMonth(now.toLocalDate().lengthOfMonth()).withHour(23).withMinute(59).withSecond(59).withNano(999999999);
                }
                break;

            case "year":
                if ("current".equals(period)) {
                    // 本年
                    startTime = now.withDayOfYear(1).withHour(0).withMinute(0).withSecond(0).withNano(0);
                    endTime = now.withDayOfYear(now.toLocalDate().lengthOfYear()).withHour(23).withMinute(59).withSecond(59).withNano(999999999);
                } else {
                    // 默认本年
                    startTime = now.withDayOfYear(1).withHour(0).withMinute(0).withSecond(0).withNano(0);
                    endTime = now.withDayOfYear(now.toLocalDate().lengthOfYear()).withHour(23).withMinute(59).withSecond(59).withNano(999999999);
                }
                break;

            case "days":
                int days = 30; // 默认30天
                if (period instanceof Number) {
                    days = ((Number) period).intValue();
                }
                // 近N天
                startTime = now.minusDays(days).withHour(0).withMinute(0).withSecond(0).withNano(0);
                endTime = now.withHour(23).withMinute(59).withSecond(59).withNano(999999999);
                break;

            case "week":
                if ("current".equals(period)) {
                    // 本周
                    startTime = now.with(java.time.DayOfWeek.MONDAY).withHour(0).withMinute(0).withSecond(0).withNano(0);
                    endTime = now.with(java.time.DayOfWeek.SUNDAY).withHour(23).withMinute(59).withSecond(59).withNano(999999999);
                } else {
                    // 默认本周
                    startTime = now.with(java.time.DayOfWeek.MONDAY).withHour(0).withMinute(0).withSecond(0).withNano(0);
                    endTime = now.with(java.time.DayOfWeek.SUNDAY).withHour(23).withMinute(59).withSecond(59).withNano(999999999);
                }
                break;

            default:
                // 默认本月
                startTime = now.withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0).withNano(0);
                endTime = now.withDayOfMonth(now.toLocalDate().lengthOfMonth()).withHour(23).withMinute(59).withSecond(59).withNano(999999999);
                break;
        }

        // 转换为Date对象
        Date startDate = Date.from(startTime.atZone(ZoneId.systemDefault()).toInstant());
        Date endDate = Date.from(endTime.atZone(ZoneId.systemDefault()).toInstant());

        return new Date[]{startDate, endDate};
    }

    @Override
    public IPage<ReportQueryConditionEntity> getConditionListForAdmin(Page<ReportQueryConditionEntity> page,
                                                                      String keyword,
                                                                      Integer reportType,
                                                                      Boolean isPublic) {
        QueryWrapper<ReportQueryConditionEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .orderByDesc(ReportQueryConditionEntity::getUseCount);

        // 关键词搜索
        if (StringUtils.isNotBlank(keyword)) {
            queryWrapper.lambda().and(wrapper ->
                wrapper.like(ReportQueryConditionEntity::getConditionName, keyword)
                       .or()
                       .like(ReportQueryConditionEntity::getConditionDesc, keyword)
                       .or()
                       .like(ReportQueryConditionEntity::getKeywords, keyword)
            );
        }

        // 报表类型过滤
        if (reportType != null) {
            queryWrapper.lambda().eq(ReportQueryConditionEntity::getReportType, reportType);
        }

        // 公共状态过滤
        if (isPublic != null) {
            queryWrapper.lambda().eq(ReportQueryConditionEntity::getIsPublic, isPublic);
        }

        return this.page(page, queryWrapper);
    }

    @Override
    public boolean batchSetPublic(List<String> conditionIds, Boolean isPublic) {
        try {
            if (conditionIds == null || conditionIds.isEmpty()) {
                return false;
            }

            List<ReportQueryConditionEntity> conditions = this.listByIds(conditionIds);
            for (ReportQueryConditionEntity condition : conditions) {
                condition.setIsPublic(isPublic);
            }

            return this.updateBatchById(conditions);
        } catch (Exception e) {
            log.error("批量设置公共状态失败", e);
            return false;
        }
    }
}
