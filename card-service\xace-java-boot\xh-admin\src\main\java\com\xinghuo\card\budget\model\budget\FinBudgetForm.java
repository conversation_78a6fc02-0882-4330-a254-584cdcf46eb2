package com.xinghuo.card.budget.model.budget;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 预算表单模型
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2024-06-28
 */
@Data
public class FinBudgetForm {

    /**
     * 主键ID
     */
    private String id;

    /**
     * 预算名称
     */
    @NotBlank(message = "预算名称不能为空")
    private String name;

    /**
     * 周期类型：MONTHLY-月度, YEARLY-年度
     */
    @NotBlank(message = "周期类型不能为空")
    private String periodType;

    /**
     * 开始日期
     */
    @NotNull(message = "开始日期不能为空")
    private Date startDate;

    /**
     * 结束日期
     */
    @NotNull(message = "结束日期不能为空")
    private Date endDate;

    /**
     * 总预算金额
     */
    private BigDecimal totalBudgetAmount;

    /**
     * 状态：ACTIVE-激活, ARCHIVED-归档
     */
    private String status;
}