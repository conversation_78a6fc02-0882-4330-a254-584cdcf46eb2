package com.xinghuo.card.budget.model.budgetitem;

import com.xinghuo.common.base.PaginationForm;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 预算子项分页查询参数
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2024-06-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FinBudgetItemPagination extends PaginationForm {

    /**
     * 关联预算ID
     */
    private String budgetId;

    /**
     * 预算项类型: CATEGORY-分类, ACCOUNT-账户
     */
    private String itemType;

    /**
     * 目标名称（模糊查询）
     */
    private String targetName;

    /**
     * 是否已预警
     */
    private Boolean isAlerted;

    /**
     * 用户ID
     */
    private String userId;
}