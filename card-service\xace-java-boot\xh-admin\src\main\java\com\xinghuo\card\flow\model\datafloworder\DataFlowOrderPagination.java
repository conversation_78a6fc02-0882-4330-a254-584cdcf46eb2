package com.xinghuo.card.flow.model.datafloworder;

import com.xinghuo.common.base.model.Pagination;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 订单管理
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2022-11-27
 */
@Data
public class DataFlowOrderPagination extends Pagination {

    @Schema(description = "店铺 ")
    private String accId;

    @Schema(description = "订单日期 ")
    private List<String> orderDate;

    @Schema(description = "状态 ")
    private String formStatus;

    @Schema(description = "订单编号 ")
    private String orderNo;

    @Schema(description = "菜单id")
    private String menuId;
}
