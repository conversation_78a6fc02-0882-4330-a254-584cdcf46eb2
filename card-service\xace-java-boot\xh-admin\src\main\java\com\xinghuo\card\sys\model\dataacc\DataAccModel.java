package com.xinghuo.card.sys.model.dataacc;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.xinghuo.common.util.tree.SumTree;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;


/**
 * 个人账号
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2022-12-02
 */
@Data
public class DataAccModel extends SumTree {

    @Schema(description = "名称")
    @JsonProperty("fullName")
    private String name;

    @Schema(description = "常用标记")
    private Integer favMark;

}
