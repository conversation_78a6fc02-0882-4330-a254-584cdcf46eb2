import { defHttp } from '/@/utils/http/axios';

enum Api {
  Prefix = '/api/card/report/condition',
}

// 获取用户查询条件列表
export function getConditionList(reportType?: number) {
  return defHttp.get({ url: Api.Prefix + '/list', params: { reportType } });
}

// 获取公共查询条件
export function getPublicConditions(reportType?: number) {
  return defHttp.get({ url: Api.Prefix + '/public', params: { reportType } });
}

// 获取收藏的查询条件
export function getFavoriteConditions() {
  return defHttp.get({ url: Api.Prefix + '/favorites' });
}

// 获取查询条件详情
export function getConditionDetail(conditionId: string) {
  return defHttp.get({ url: Api.Prefix + `/${conditionId}` });
}

// 保存查询条件
export function saveCondition(data: any) {
  return defHttp.post({ url: Api.Prefix + '/save', data });
}

// 从查询参数创建查询条件
export function createFromParams(data: any) {
  return defHttp.post({ url: Api.Prefix + '/create-from-params', data });
}

// 更新查询条件
export function updateCondition(conditionId: string, data: any) {
  return defHttp.put({ url: Api.Prefix + `/${conditionId}`, data });
}

// 删除查询条件
export function deleteCondition(conditionId: string) {
  return defHttp.delete({ url: Api.Prefix + `/${conditionId}` });
}

// 收藏/取消收藏查询条件
export function toggleFavorite(conditionId: string, favorite: boolean) {
  return defHttp.post({
    url: Api.Prefix + `/${conditionId}/toggle-favorite`,
    params: { favorite },
  });
}

// 使用查询条件
export function useCondition(conditionId: string) {
  return defHttp.post({ url: Api.Prefix + `/${conditionId}/use` });
}

// ==================== 管理端接口 ====================

// 管理端：分页获取查询条件列表
export function getConditionListForAdmin(params: any) {
  return defHttp.get({ url: Api.Prefix + '/admin/list', params });
}

// 管理端：批量设置公共状态
export function batchSetPublic(data: { conditionIds: string[]; isPublic: boolean }) {
  return defHttp.post({ url: Api.Prefix + '/admin/batch-public', data });
}

// 管理端：批量删除查询条件
export function batchDeleteConditions(data: { conditionIds: string[] }) {
  return defHttp.delete({ url: Api.Prefix + '/admin/batch-delete', data });
}
