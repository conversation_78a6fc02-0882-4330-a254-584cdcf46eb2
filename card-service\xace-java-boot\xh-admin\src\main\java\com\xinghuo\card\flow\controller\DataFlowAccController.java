package com.xinghuo.card.flow.controller;

import com.xinghuo.admin.util.GeneraterSwapUtil;
import com.xinghuo.card.flow.entity.DataFlowAccEntity;
import com.xinghuo.card.flow.model.dataflowacc.DataFlowAccForm;
import com.xinghuo.card.flow.model.dataflowacc.DataFlowAccPagination;
import com.xinghuo.card.flow.model.dataflowacc.DataFlowAccVO;
import com.xinghuo.card.flow.service.DataFlowAccService;
import com.xinghuo.card.sys.entity.DataAccEntity;
import com.xinghuo.card.sys.service.DataAccService;
import com.xinghuo.card.sys.service.DataSysPaytypeService;
import com.xinghuo.common.base.ActionResult;
import com.xinghuo.common.base.UserInfo;
import com.xinghuo.common.base.vo.PaginationVO;
import com.xinghuo.common.exception.DataException;
import com.xinghuo.common.util.UserProvider;
import com.xinghuo.common.util.core.BeanCopierUtils;
import com.xinghuo.common.util.core.PageXhUtil;
import com.xinghuo.common.util.core.RandomUtil;
import com.xinghuo.common.util.core.StrXhUtil;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 账号明细
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2022-11-27
 */
@Slf4j
@RestController
@RequestMapping("/api/card/flow/flowAcc")
public class DataFlowAccController {

    @Autowired
    private GeneraterSwapUtil generaterSwapUtil;

    @Autowired
    private UserProvider userProvider;

    @Autowired
    private DataFlowAccService dataFlowAccService;
    @Autowired
    private DataAccService dataAccService;

    @Autowired
    private DataSysPaytypeService dataSysPaytypeService;


    /**
     * 列表
     *
     * @param dataFlowAccPagination
     * @return
     */
    @Operation(summary = "列表")
    @PostMapping("/getList")
    public ActionResult list(@RequestBody DataFlowAccPagination dataFlowAccPagination) throws Exception {
        List<DataFlowAccEntity> list = dataFlowAccService.getList(dataFlowAccPagination);
        List<DataFlowAccVO> listVO = BeanCopierUtils.copyList(list, DataFlowAccVO.class);

        //处理id字段转名称，若无需转或者为空可删除
        for (DataFlowAccVO vo : listVO) {
            try {
                // 处理账户名称转换
                if(StrXhUtil.isNotEmpty(vo.getAccId())){
                    DataAccEntity acc = dataAccService.getInfo(vo.getAccId());
                    if(acc != null && StrXhUtil.isNotEmpty(acc.getName())) {
                        vo.setAccName(acc.getName());
                    }
                }

                // 处理转出账户名称转换
                if(StrXhUtil.isNotEmpty(vo.getOutAccId())){
                    DataAccEntity acc = dataAccService.getInfo(vo.getOutAccId());
                    if(acc != null && StrXhUtil.isNotEmpty(acc.getName())) {
                        vo.setOutAccName(acc.getName());
                    }
                }

                // 处理收支类型名称转换，增加空值检查
                String typeName = "";
                if(StrXhUtil.isNotEmpty(vo.getType())) {
                    var paytype = dataSysPaytypeService.getInfo(vo.getType());
                    if(paytype != null && StrXhUtil.isNotEmpty(paytype.getName())) {
                        typeName = paytype.getName();
                    } else {
                        typeName = "未知类型";
                    }
                } else {
                    typeName = "未分类";
                }

                // 组合显示类型信息
                if(StringUtils.isNotBlank(vo.getOutAccId())){
                    vo.setTypeDesc(typeName + " | " + vo.getOutAccName());
                } else {
                    vo.setTypeDesc(typeName);
                }

            } catch (Exception e) {
                log.error("处理流水记录数据转换时发生错误，记录ID：{}", vo.getId(), e);
                // 设置默认值，避免显示异常
                if(StringUtils.isBlank(vo.getType())) {
                    vo.setType("数据异常");
                }
            }
        }
        PaginationVO page = PageXhUtil.getPaginationVO(dataFlowAccPagination);
        return ActionResult.page(listVO, page);
    }

    /**
     * 创建
     *
     * @param dataFlowAccForm
     * @return
     */
    @Operation(summary = "新增")
    @PostMapping
    @Transactional
    public ActionResult create(@RequestBody @Valid DataFlowAccForm dataFlowAccForm) throws DataException {
        try {
            // 参数验证
            if (dataFlowAccForm == null) {
                return ActionResult.fail("请求参数不能为空");
            }

            // 业务逻辑验证
            if (dataFlowAccForm.getFlowDate() == null) {
                return ActionResult.fail("流水日期不能为空");
            }

            if (StrXhUtil.isBlank(dataFlowAccForm.getAccId())) {
                return ActionResult.fail("账户不能为空");
            }

            // 验证金额
            if (dataFlowAccForm.getIncome() == null && dataFlowAccForm.getPay() == null) {
                return ActionResult.fail("收入金额或支出金额至少填写一项");
            }

            String mainId = RandomUtil.snowId();
            UserInfo userInfo = userProvider.get();

            DataFlowAccEntity entity = BeanCopierUtils.copy(dataFlowAccForm, DataFlowAccEntity.class);
            entity.setId(mainId);

            // 使用服务层的create方法，包含业务逻辑处理
            dataFlowAccService.create(entity);

            log.info("用户{}创建流水记录成功，记录ID：{}", userInfo.getUserId(), mainId);
            return ActionResult.success("创建成功");

        } catch (Exception e) {
            log.error("创建流水记录失败", e);
            throw new DataException("创建失败：" + e.getMessage());
        }
    }

    /**
     * 信息
     *
     * @param id
     * @return
     */
    @Operation(summary = "查询单条")
    @GetMapping("/{id}")
    public ActionResult<DataFlowAccVO> info(@PathVariable("id") String id) {
        DataFlowAccEntity entity = dataFlowAccService.getInfo(id);
        DataFlowAccVO vo = BeanCopierUtils.copy(entity, DataFlowAccVO.class);
        return ActionResult.success(vo);
    }

    /**
     * 表单信息(详情页)
     *
     * @param id
     * @return
     */
    @Operation(summary = "表单信息(详情页)")
    @GetMapping("/detail/{id}")
    public ActionResult<DataFlowAccVO> detailInfo(@PathVariable("id") String id) {
        DataFlowAccEntity entity = dataFlowAccService.getInfo(id);
        DataFlowAccVO vo = BeanCopierUtils.copy(entity, DataFlowAccVO.class);
        //添加到详情表单对象中
//        vo.setAccId(generaterSwapUtil.getDynName("1056694166801408" ,"fullName" ,"id","" ,vo.getAccId()));
//        vo.setOutAccId(generaterSwapUtil.getDynName("1056694166801408" ,"fullName" ,"id","" ,vo.getOutAccId()));
        return ActionResult.success(vo);
    }

    /**
     * 更新
     *
     * @param id
     * @param dataFlowAccForm
     * @return
     */
    @Operation(summary = "修改")
    @PutMapping("/{id}")
    @Transactional
    public ActionResult update(@PathVariable("id") String id, @RequestBody @Valid DataFlowAccForm dataFlowAccForm) throws DataException {
        try {
            // 参数验证
            if (StrXhUtil.isBlank(id)) {
                return ActionResult.fail("记录ID不能为空");
            }

            if (dataFlowAccForm == null) {
                return ActionResult.fail("请求参数不能为空");
            }

            UserInfo userInfo = userProvider.get();
            DataFlowAccEntity entity = dataFlowAccService.getInfo(id);

            if (entity == null) {
                return ActionResult.fail("更新失败，数据不存在");
            }

            // 业务逻辑验证
            if (dataFlowAccForm.getFlowDate() == null) {
                return ActionResult.fail("流水日期不能为空");
            }

            if (StrXhUtil.isBlank(dataFlowAccForm.getAccId())) {
                return ActionResult.fail("账户不能为空");
            }

            // 验证金额
            if (dataFlowAccForm.getIncome() == null && dataFlowAccForm.getPay() == null) {
                return ActionResult.fail("收入金额或支出金额至少填写一项");
            }

            DataFlowAccEntity updateEntity = BeanCopierUtils.copy(dataFlowAccForm, DataFlowAccEntity.class);

            boolean success = dataFlowAccService.update(id, updateEntity);
            if (success) {
                log.info("用户{}更新流水记录成功，记录ID：{}", userInfo.getUserId(), id);
                return ActionResult.success("更新成功");
            } else {
                return ActionResult.fail("更新失败");
            }

        } catch (Exception e) {
            log.error("更新流水记录失败，记录ID：{}", id, e);
            throw new DataException("更新失败：" + e.getMessage());
        }
    }


    /*
     * @param id
     * @return
     */
    @Operation(summary = "清算")
    @GetMapping("/Q/{id}")
    public ActionResult q(@PathVariable("id") String id) throws DataException {
        DataFlowAccEntity entity = dataFlowAccService.getInfo(id);
        if (entity != null) {
            if (!StringUtils.contains(entity.getNote(), "【Q")) {
                entity.setNote(StrXhUtil.emptyIfNull(entity.getNote()) + "【Q-" + entity.getBalance() + "】");
            }
            dataFlowAccService.update(id, entity);
            return ActionResult.success("更新成功");
        } else {
            return ActionResult.fail("更新失败，数据不存在");
        }
    }

    /**
     * 删除
     *
     * @param id
     * @return
     */
    @Operation(summary = "删除")
    @DeleteMapping("/{id}")
    @Transactional
    public ActionResult delete(@PathVariable("id") String id) {
        DataFlowAccEntity entity = dataFlowAccService.getInfo(id);
        if (entity != null) {
            dataFlowAccService.delete(entity);
        }
        return ActionResult.success("删除成功");
    }


    /**
     * 更新
     *
     * @param id
     * @return
     */
    @Operation(summary = "设置清算账号明细")
    @PutMapping("/editQ/{id}")
    @Transactional
    public ActionResult editQ(@PathVariable("id") String id) throws DataException {
        UserInfo userInfo = userProvider.get();
        DataFlowAccEntity entity = dataFlowAccService.getInfo(id);
        if (entity != null) {
            if (!StringUtils.contains(entity.getNote(), "【Q")) {
                entity.setNote(entity.getNote().trim() + "【Q-" + entity.getBalance() + "】");
            }
            dataFlowAccService.update(id, entity);
            return ActionResult.success("设置清算账号明细成功");
        } else {
            return ActionResult.fail("设置清算账号明细失败，数据不存在");
        }
    }
}
