package ${package.Service};

#set($moduleName = "${mainModelName.substring(0,1).toLowerCase()}${mainModelName.substring(1).toLowerCase()}")
import ${modulePackageName}.model.${moduleName}.*;
import ${package.Entity}.*;
import java.util.*;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

/**
 * ${genInfo.description}
 * 版本： ${genInfo.version}
 * 版权： ${genInfo.copyright}
 * 作者： ${genInfo.createUser}
 * 日期： ${genInfo.createDate}
 */
public interface ${table.serviceName} extends ${superServiceClass}<${table.entityName}> {
#if(${main})
    #if(${pKeyName.toLowerCase().startsWith('f_')})
        #set($peimaryKeyname = "${pKeyName.substring(2,3).toLowerCase()}${pKeyName.substring(3)}")
    #else
        #set($peimaryKeyname = "${pKeyName.substring(0,1).toLowerCase()}${pKeyName.substring(1).toLowerCase()}")
    #end
    #if($isList)
    List<${table.entityName}> getList(${Name}Pagination ${name}Pagination);

    List<${table.entityName}> getTypeList(${Name}Pagination ${name}Pagination,String dataType);

    #end
    ${table.entityName} getInfo(String ${peimaryKeyname});

    void delete(${table.entityName} entity);

    void create(${table.entityName} entity);

    boolean update(String ${peimaryKeyname}, ${table.entityName} entity);

    //子表方法
    #foreach($grid in ${childTableHandle})
    #if($isList)
    List<${grid.aliasUpName}Entity> get${grid.aliasUpName}List(String id,${Name}Pagination ${name}Pagination);

    #end
    List<${grid.aliasUpName}Entity> get${grid.aliasUpName}List(String id);

    #end
    //副表数据方法
    #foreach($child in ${columnTableHandle})
    ${child.modelUpName}Entity get${child.modelUpName}(String id);

    #end
	String checkForm(${Name}Form form,int i);

#else
#if($isList)
    #set($childWrapperName = "${modelName.substring(0,1).toLowerCase()}${modelName.substring(1)}")
    QueryWrapper<${table.entityName}> getChild(${mainModelName}Pagination pagination,QueryWrapper<${table.entityName}> ${childWrapperName}QueryWrapper);
#end
#end
}
