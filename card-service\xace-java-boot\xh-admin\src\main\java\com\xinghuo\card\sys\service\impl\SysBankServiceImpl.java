package com.xinghuo.card.sys.service.impl;

import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.xinghuo.card.sys.entity.SysBankEntity;
import com.xinghuo.card.sys.dao.SysBankMapper;
import com.xinghuo.card.sys.model.sysbank.SysBankPagination;
import com.xinghuo.card.sys.service.SysBankService;
import com.xinghuo.common.base.service.impl.ExtendedBaseServiceImpl;
import com.xinghuo.common.util.UserProvider;
import com.xinghuo.common.util.core.StrXhUtil;
import com.xinghuo.common.util.core.RandomUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 银行信息管理服务实现类
 * 用于管理银行的基本信息，包括银行名称、图标、网站等
 * 支持银行分类和信用卡账号属性配置
 *
 * <AUTHOR> Assistant
 * @version V1.0.0
 * @date 2024-12-29
 */
@Slf4j
@Service
public class SysBankServiceImpl extends ExtendedBaseServiceImpl<SysBankMapper, SysBankEntity> implements SysBankService {

    @Autowired
    private UserProvider userProvider;

    @Override
    public List<SysBankEntity> getList(SysBankPagination sysBankPagination) {
        return getListByType(sysBankPagination, "0");
    }

    @Override
    public List<SysBankEntity> getTypeList(SysBankPagination sysBankPagination, String dataType) {
        return getListByType(sysBankPagination, dataType);
    }

    @Override
    public List<SysBankEntity> getSelectList() {
        QueryWrapper<SysBankEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .orderByAsc(SysBankEntity::getBankType)
                .orderByAsc(SysBankEntity::getBankName);
        return this.list(queryWrapper);
    }

    @Override
    public List<SysBankEntity> getListByType(String bankType) {
        Assert.notEmpty(bankType, "银行类型不能为空");
        
        QueryWrapper<SysBankEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(SysBankEntity::getBankType, bankType)
                .orderByAsc(SysBankEntity::getBankName);
        return this.list(queryWrapper);
    }

    /**
     * 根据类型获取列表数据
     *
     * @param sysBankPagination 查询参数
     * @param dataType          数据类型 0:分页 1:不分页
     * @return 查询结果
     */
    private List<SysBankEntity> getListByType(SysBankPagination sysBankPagination, String dataType) {
        QueryWrapper<SysBankEntity> queryWrapper = new QueryWrapper<>();

        // 构建查询条件
        if (StrXhUtil.isNotEmpty(sysBankPagination.getBankName())) {
            queryWrapper.lambda().like(SysBankEntity::getBankName, sysBankPagination.getBankName());
        }
        if (StrXhUtil.isNotEmpty(sysBankPagination.getBankType())) {
            queryWrapper.lambda().eq(SysBankEntity::getBankType, sysBankPagination.getBankType());
        }
        if (StrXhUtil.isNotEmpty(sysBankPagination.getBankKey())) {
            queryWrapper.lambda().like(SysBankEntity::getBankKey, sysBankPagination.getBankKey());
        }
        if (StrXhUtil.isNotEmpty(sysBankPagination.getCreditType())) {
            queryWrapper.lambda().eq(SysBankEntity::getCreditType, sysBankPagination.getCreditType());
        }
        if (StrXhUtil.isNotEmpty(sysBankPagination.getKeyword())) {
            queryWrapper.lambda().and(wrapper -> wrapper
                    .like(SysBankEntity::getBankName, sysBankPagination.getKeyword())
                    .or()
                    .like(SysBankEntity::getBankKey, sysBankPagination.getKeyword()));
        }

        if (Boolean.TRUE.equals(sysBankPagination.getOnlyWithIcon())) {
            queryWrapper.lambda().isNotNull(SysBankEntity::getBankIcon)
                    .ne(SysBankEntity::getBankIcon, "");
        }
        if (Boolean.TRUE.equals(sysBankPagination.getOnlyWithUrl())) {
            queryWrapper.lambda().isNotNull(SysBankEntity::getBankUrl)
                    .ne(SysBankEntity::getBankUrl, "");
        }

        // 排序
        if (StrXhUtil.isNotEmpty(sysBankPagination.getSortField()) && StrXhUtil.isNotEmpty(sysBankPagination.getSortOrder())) {
            boolean isAsc = "ASC".equalsIgnoreCase(sysBankPagination.getSortOrder());
            switch (sysBankPagination.getSortField()) {
                case "bankName":
                    queryWrapper.lambda().orderBy(true, isAsc, SysBankEntity::getBankName);
                    break;
                case "bankKey":
                    queryWrapper.lambda().orderBy(true, isAsc, SysBankEntity::getBankKey);
                    break;
                case "bankType":
                    queryWrapper.lambda().orderBy(true, isAsc, SysBankEntity::getBankType);
                    break;
                case "creditType":
                    queryWrapper.lambda().orderBy(true, isAsc, SysBankEntity::getCreditType);
                    break;

                default:
                    queryWrapper.lambda().orderByAsc(SysBankEntity::getBankType)
                            .orderByAsc(SysBankEntity::getBankName);
                    break;
            }
        } else {
            queryWrapper.lambda()
                    .orderByAsc(SysBankEntity::getBankType)
                    .orderByAsc(SysBankEntity::getBankName);
        }

        return this.list(queryWrapper);
    }

    @Override
    public SysBankEntity getInfo(String id) {
        Assert.notEmpty(id, "id不能为空");
        QueryWrapper<SysBankEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(SysBankEntity::getId, id);
        return this.getOne(queryWrapper);
    }

    @Override
    public SysBankEntity getInfoByBankKey(String bankKey) {
        Assert.notEmpty(bankKey, "银行简称不能为空");
        QueryWrapper<SysBankEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(SysBankEntity::getBankKey, bankKey);
        return this.getOne(queryWrapper);
    }

    @Override
    public SysBankEntity getInfoByBankName(String bankName) {
        Assert.notEmpty(bankName, "银行名称不能为空");
        QueryWrapper<SysBankEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(SysBankEntity::getBankName, bankName);
        return this.getOne(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(SysBankEntity entity) {
        // 检查银行名称唯一性
        if (!checkBankNameUnique(entity.getBankName(), null)) {
            throw new RuntimeException("银行名称已存在");
        }
        
        // 检查银行简称唯一性
        if (StrXhUtil.isNotEmpty(entity.getBankKey()) && !checkBankKeyUnique(entity.getBankKey(), null)) {
            throw new RuntimeException("银行简称已存在");
        }
        
        this.save(entity);
        log.info("创建银行成功，ID: {}, 银行名称: {}", entity.getId(), entity.getBankName());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean update(String id, SysBankEntity entity) {
        Assert.notEmpty(id, "id不能为空");
        
        // 检查银行名称唯一性
        if (!checkBankNameUnique(entity.getBankName(), id)) {
            throw new RuntimeException("银行名称已存在");
        }
        
        // 检查银行简称唯一性
        if (StrXhUtil.isNotEmpty(entity.getBankKey()) && !checkBankKeyUnique(entity.getBankKey(), id)) {
            throw new RuntimeException("银行简称已存在");
        }
        
        entity.setId(id);
        boolean result = this.updateById(entity);
        
        if (result) {
            log.info("更新银行成功，ID: {}, 银行名称: {}", id, entity.getBankName());
        }
        
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(SysBankEntity entity) {
        if (entity != null) {
            this.removeById(entity.getId());
            log.info("删除银行成功，ID: {}, 银行名称: {}", entity.getId(), entity.getBankName());
        }
    }

    @Override
    public boolean checkBankNameUnique(String bankName, String id) {
        Assert.notEmpty(bankName, "银行名称不能为空");
        
        QueryWrapper<SysBankEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(SysBankEntity::getBankName, bankName);
        
        if (StrXhUtil.isNotEmpty(id)) {
            queryWrapper.lambda().ne(SysBankEntity::getId, id);
        }
        
        return this.count(queryWrapper) == 0;
    }

    @Override
    public boolean checkBankKeyUnique(String bankKey, String id) {
        Assert.notEmpty(bankKey, "银行简称不能为空");
        
        QueryWrapper<SysBankEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(SysBankEntity::getBankKey, bankKey);
        
        if (StrXhUtil.isNotEmpty(id)) {
            queryWrapper.lambda().ne(SysBankEntity::getId, id);
        }
        
        return this.count(queryWrapper) == 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchDelete(List<String> ids) {
        Assert.notEmpty(ids, "删除ID列表不能为空");
        
        int count = this.removeBatchByIds(ids) ? ids.size() : 0;
        log.info("批量删除银行完成，删除数量: {}", count);
        
        return count;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateStatus(String id, String status) {
        Assert.notEmpty(id, "id不能为空");
        Assert.notEmpty(status, "状态不能为空");
        
        SysBankEntity entity = new SysBankEntity();
        entity.setId(id);
        // 注意：这里假设实体有status字段，如果没有需要根据实际情况调整
        
        boolean result = this.updateById(entity);
        
        if (result) {
            log.info("更新银行状态成功，ID: {}, 状态: {}", id, status);
        }
        
        return result;
    }

    @Override
    public Map<String, Object> getBankStatistics() {
        Map<String, Object> statistics = new HashMap<>();

        // 总银行数量
        long totalCount = this.count();
        statistics.put("totalCount", totalCount);

        // 按类型统计
        QueryWrapper<SysBankEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("BANK_TYPE, COUNT(*) as count")
                .groupBy("BANK_TYPE");

        List<Map<String, Object>> typeStatistics = this.listMaps(queryWrapper);
        statistics.put("typeStatistics", typeStatistics);

        // 按信用卡类型统计
        QueryWrapper<SysBankEntity> creditQueryWrapper = new QueryWrapper<>();
        creditQueryWrapper.select("CREDIT_TYPE, COUNT(*) as count")
                .groupBy("CREDIT_TYPE");

        List<Map<String, Object>> creditTypeStatistics = this.listMaps(creditQueryWrapper);
        statistics.put("creditTypeStatistics", creditTypeStatistics);

        // 有图标的银行数量
        QueryWrapper<SysBankEntity> iconQueryWrapper = new QueryWrapper<>();
        iconQueryWrapper.lambda().isNotNull(SysBankEntity::getBankIcon)
                .ne(SysBankEntity::getBankIcon, "");
        long iconCount = this.count(iconQueryWrapper);
        statistics.put("iconCount", iconCount);

        // 有网站的银行数量
        QueryWrapper<SysBankEntity> urlQueryWrapper = new QueryWrapper<>();
        urlQueryWrapper.lambda().isNotNull(SysBankEntity::getBankUrl)
                .ne(SysBankEntity::getBankUrl, "");
        long urlCount = this.count(urlQueryWrapper);
        statistics.put("urlCount", urlCount);

        return statistics;
    }

    @Override
    public List<Map<String, Object>> getBankTypeDistribution() {
        QueryWrapper<SysBankEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("BANK_TYPE, COUNT(*) as count")
                .groupBy("BANK_TYPE")
                .orderBy(true, false, "count");

        List<Map<String, Object>> result = this.listMaps(queryWrapper);

        // 添加类型名称
        for (Map<String, Object> item : result) {
            String bankType = (String) item.get("BANK_TYPE");
            String typeName = getBankTypeName(bankType);
            item.put("typeName", typeName);
        }

        return result;
    }

    @Override
    public List<Map<String, Object>> getCreditTypeDistribution() {
        QueryWrapper<SysBankEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("CREDIT_TYPE, COUNT(*) as count")
                .groupBy("CREDIT_TYPE")
                .orderBy(true, false, "count");

        List<Map<String, Object>> result = this.listMaps(queryWrapper);

        // 添加类型名称
        for (Map<String, Object> item : result) {
            String creditType = (String) item.get("CREDIT_TYPE");
            String typeName = getCreditTypeName(creditType);
            item.put("typeName", typeName);
        }

        return result;
    }

    @Override
    public Map<String, Object> validateCanDelete(String id) {
        Assert.notEmpty(id, "银行ID不能为空");

        Map<String, Object> result = new HashMap<>();
        result.put("canDelete", true);
        result.put("reason", "");

        // TODO: 检查是否有关联的用户银行信息
        // 这里需要根据实际的关联表来实现

        // TODO: 检查是否有关联的信用卡账户
        // 这里需要根据实际的关联表来实现

        return result;
    }

    @Override
    public Map<String, Object> batchValidateCanDelete(List<String> ids) {
        Assert.notEmpty(ids, "银行ID列表不能为空");

        Map<String, Object> result = new HashMap<>();
        List<String> canDeleteIds = new ArrayList<>();
        List<Map<String, Object>> cannotDeleteList = new ArrayList<>();

        for (String id : ids) {
            Map<String, Object> validateResult = validateCanDelete(id);
            if ((Boolean) validateResult.get("canDelete")) {
                canDeleteIds.add(id);
            } else {
                Map<String, Object> item = new HashMap<>();
                item.put("id", id);
                item.put("reason", validateResult.get("reason"));
                cannotDeleteList.add(item);
            }
        }

        result.put("canDeleteIds", canDeleteIds);
        result.put("cannotDeleteList", cannotDeleteList);
        result.put("canDeleteCount", canDeleteIds.size());
        result.put("cannotDeleteCount", cannotDeleteList.size());

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> importBankData(List<SysBankEntity> bankList) {
        Assert.notEmpty(bankList, "银行数据列表不能为空");

        Map<String, Object> result = new HashMap<>();
        int successCount = 0;
        int failCount = 0;
        List<String> errorMessages = new ArrayList<>();

        for (SysBankEntity bank : bankList) {
            try {
                // 设置ID
                bank.setId(RandomUtil.snowId());


                // 验证数据
                if (StrXhUtil.isEmpty(bank.getBankName())) {
                    errorMessages.add("银行名称不能为空");
                    failCount++;
                    continue;
                }

                if (!checkBankNameUnique(bank.getBankName(), null)) {
                    errorMessages.add("银行名称已存在: " + bank.getBankName());
                    failCount++;
                    continue;
                }

                if (StrXhUtil.isNotEmpty(bank.getBankKey()) && !checkBankKeyUnique(bank.getBankKey(), null)) {
                    errorMessages.add("银行简称已存在: " + bank.getBankKey());
                    failCount++;
                    continue;
                }

                this.save(bank);
                successCount++;

            } catch (Exception e) {
                errorMessages.add("导入失败: " + e.getMessage());
                failCount++;
                log.error("导入银行数据失败", e);
            }
        }

        result.put("successCount", successCount);
        result.put("failCount", failCount);
        result.put("errorMessages", errorMessages);

        log.info("导入银行数据完成，成功: {}, 失败: {}", successCount, failCount);

        return result;
    }

    @Override
    public List<Map<String, Object>> exportBankData(SysBankPagination sysBankPagination) {
        List<SysBankEntity> bankList = getList(sysBankPagination);

        return bankList.stream().map(bank -> {
            Map<String, Object> item = new HashMap<>();
            item.put("id", bank.getId());
            item.put("bankName", bank.getBankName());
            item.put("bankKey", bank.getBankKey());
            item.put("bankType", bank.getBankType());
            item.put("bankTypeName", getBankTypeName(bank.getBankType()));
            item.put("bankIcon", bank.getBankIcon());
            item.put("bankUrl", bank.getBankUrl());
            item.put("creditType", bank.getCreditType());
            item.put("creditTypeName", getCreditTypeName(bank.getCreditType()));
            item.put("note", bank.getNote());

            return item;
        }).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SysBankEntity copyBank(String id, String newName) {
        Assert.notEmpty(id, "源银行ID不能为空");
        Assert.notEmpty(newName, "新银行名称不能为空");

        SysBankEntity sourceBank = getInfo(id);
        if (sourceBank == null) {
            throw new RuntimeException("源银行不存在");
        }

        if (!checkBankNameUnique(newName, null)) {
            throw new RuntimeException("银行名称已存在");
        }

        SysBankEntity newBank = new SysBankEntity();
        newBank.setId(RandomUtil.snowId());
        newBank.setBankName(newName);
        newBank.setBankKey(sourceBank.getBankKey() + "_COPY");
        newBank.setBankType(sourceBank.getBankType());
        newBank.setBankIcon(sourceBank.getBankIcon());
        newBank.setBankUrl(sourceBank.getBankUrl());
        newBank.setCreditType(sourceBank.getCreditType());
        newBank.setNote("复制自: " + sourceBank.getBankName());


        this.save(newBank);

        log.info("复制银行成功，源银行: {}, 新银行: {}", sourceBank.getBankName(), newName);

        return newBank;
    }

    @Override
    public Map<String, Object> getBankDetailWithRelated(String id) {
        Assert.notEmpty(id, "银行ID不能为空");

        SysBankEntity bank = getInfo(id);
        if (bank == null) {
            return null;
        }

        Map<String, Object> result = new HashMap<>();
        result.put("bank", bank);

        // TODO: 添加关联信息
        // result.put("userBankCount", getUserBankCount(id));
        // result.put("creditCardCount", getCreditCardCount(id));

        return result;
    }

    /**
     * 获取银行类型名称
     */
    private String getBankTypeName(String bankType) {
        if (StrXhUtil.isEmpty(bankType)) {
            return "未知";
        }

        switch (bankType) {
            case SysBankEntity.BankType.STATE_OWNED:
                return "国有银行";
            case SysBankEntity.BankType.JOINT_STOCK:
                return "股份制银行";
            case SysBankEntity.BankType.CITY_COMMERCIAL:
                return "城市商业银行";
            case SysBankEntity.BankType.RURAL_COMMERCIAL:
                return "农村商业银行";
            case SysBankEntity.BankType.FOREIGN:
                return "外资银行";
            case SysBankEntity.BankType.OTHER:
                return "其他";
            default:
                return "未知";
        }
    }

    /**
     * 获取信用卡类型名称
     */
    private String getCreditTypeName(String creditType) {
        if (StrXhUtil.isEmpty(creditType)) {
            return "未设置";
        }

        switch (creditType) {
            case SysBankEntity.CreditType.STANDARD:
                return "标准信用卡";
            case SysBankEntity.CreditType.SPECIAL:
                return "特殊信用卡";
            default:
                return "未知";
        }
    }
}
