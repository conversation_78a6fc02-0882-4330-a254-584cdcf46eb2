package com.xinghuo.card.flow.model.datafloworder;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.DecimalMin;
import java.math.BigDecimal;

/**
 * 订单管理表单数据
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2022-11-27
 */
@Data
public class DataFlowOrderForm {

    @Schema(description = "主键ID")
    private String orderId;

    @Schema(description = "店铺ID", required = true)
    @JsonProperty("accId")
    @NotBlank(message = "店铺不能为空")
    private String accId;

    @Schema(description = "采购细目", required = true)
    @JsonProperty("orderItem")
    @NotBlank(message = "采购细目不能为空")
    private String orderItem;

    @Schema(description = "金额", required = true)
    @JsonProperty("amount")
    @NotNull(message = "金额不能为空")
    @DecimalMin(value = "0.01", message = "金额必须大于0")
    private BigDecimal amount;

    @Schema(description = "订单编号")
    @JsonProperty("orderNo")
    private String orderNo;

    @Schema(description = "订单日期", required = true)
    @JsonProperty("orderDate")
    @NotNull(message = "订单日期不能为空")
    private Long orderDate;

    @Schema(description = "快递编号")
    @JsonProperty("expressNo")
    private String expressNo;

    @Schema(description = "目的地")
    @JsonProperty("dest")
    private String dest;

    @Schema(description = "状态", required = true)
    @JsonProperty("formStatus")
    @NotBlank(message = "状态不能为空")
    private String formStatus;

    @Schema(description = "签收日期")
    @JsonProperty("recDate")
    private Long recDate;


    @Schema(description = "预定日期")
    @JsonProperty("prePayDate")
    private Long prePayDate;


    @Schema(description = "定金账户")
    @JsonProperty("prePayAccId")
    private String prePayAccId;


    @Schema(description = "定金金额")
    @JsonProperty("prePayAmount")
    private String prePayAmount;


    @Schema(description = "支付方式")
    @JsonProperty("payType")
    private String payType;


    @Schema(description = "支付卡片")
    @JsonProperty("payAccId")
    private String payAccId;


    @Schema(description = "支付费用")
    @JsonProperty("payAmount")
    private String payAmount;


    @Schema(description = "苏宁卡")
    @JsonProperty("payLp1")
    private String payLp1;


    @Schema(description = "云钻")
    @JsonProperty("payLp2")
    private String payLp2;


    @Schema(description = "铜板")
    @JsonProperty("payLp3")
    private String payLp3;


    @Schema(description = "银行支付立减")
    @JsonProperty("payYh")
    private String payYh;


    @Schema(description = "返钻")
    @JsonProperty("backLp2")
    private String backLp2;


    @Schema(description = "支付备注")
    @JsonProperty("payNote")
    private String payNote;


    @Schema(description = "备注")
    @JsonProperty("note")
    private String note;


    @Schema(description = "结算账户")
    @JsonProperty("backAccId")
    private String backAccId;


    @Schema(description = "回款金额")
    @JsonProperty("backAmount")
    private String backAmount;


    @Schema(description = "回款日期")
    @JsonProperty("backDate")
    private Long backDate;


    @Schema(description = "最后创建人")
    @JsonProperty("updateBy")
    private String updateBy;


    @Schema(description = "最后修改时间")
    @JsonProperty("updateTime")
    private String updateTime;


    @Schema(description = "创建人员")
    @JsonProperty("createBy")
    private String createBy;


    @Schema(description = "创建时间")
    @JsonProperty("createTime")
    private String createTime;


}
