package com.xinghuo.card.budget.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xinghuo.card.budget.entity.FinBudgetEntity;
import com.xinghuo.card.budget.model.budget.FinBudgetPagination;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 预算主表
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2024-06-28
 */
@Mapper
public interface FinBudgetMapper extends BaseMapper<FinBudgetEntity> {

    /**
     * 列表查询
     *
     * @param page 分页参数
     * @param finBudgetPagination 查询参数
     * @return 预算列表
     */
    List<FinBudgetEntity> getList(Page<FinBudgetEntity> page, @Param("finBudgetPagination") FinBudgetPagination finBudgetPagination);

    /**
     * 根据用户ID获取当前激活的预算
     *
     * @param userId 用户ID
     * @return 激活的预算列表
     */
    List<FinBudgetEntity> getActiveBudgetsByUserId(@Param("userId") String userId);

    /**
     * 根据用户ID和状态获取预算列表
     *
     * @param userId 用户ID
     * @param status 状态
     * @return 预算列表
     */
    List<FinBudgetEntity> getBudgetsByUserIdAndStatus(@Param("userId") String userId, @Param("status") String status);
}