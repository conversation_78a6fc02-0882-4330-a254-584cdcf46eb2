package com.xinghuo.card.flow.controller;

import com.xinghuo.admin.util.GeneraterSwapUtil;
import com.xinghuo.card.flow.entity.DataFlowEntity;
import com.xinghuo.card.flow.entity.DataWoolWoolEntity;
import com.xinghuo.card.flow.model.datawoolwool.DataWoolWoolForm;
import com.xinghuo.card.flow.model.datawoolwool.DataWoolWoolPagination;
import com.xinghuo.card.flow.model.datawoolwool.DataWoolWoolVO;
import com.xinghuo.card.flow.service.DataFlowService;
import com.xinghuo.card.flow.service.DataWoolWoolService;
import com.xinghuo.card.sys.entity.DataAccEntity;
import com.xinghuo.card.sys.service.DataAccService;
import com.xinghuo.common.base.ActionResult;
import com.xinghuo.common.base.UserInfo;
import com.xinghuo.common.base.vo.PageListVO;
import com.xinghuo.common.base.vo.PaginationVO;
import com.xinghuo.common.exception.DataException;
import com.xinghuo.common.util.UserProvider;
import com.xinghuo.common.util.core.BeanCopierUtils;
import com.xinghuo.common.util.core.DateXhUtil;
import com.xinghuo.common.util.core.RandomUtil;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.*;
import java.util.Date;

/**
 * 羊毛记录表
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2022-12-12
 */
@Slf4j
@RestController
@RequestMapping("/api/card/flow/wool")
public class DataWoolWoolController {

    @Autowired
    private GeneraterSwapUtil generaterSwapUtil;

    @Autowired
    private UserProvider userProvider;

    @Autowired
    private DataWoolWoolService dataWoolWoolService;

    @Autowired
    private DataFlowService dataFlowService;

    @Autowired
    private DataAccService dataAccService;

    /**
     * 获取羊毛记录列表
     *
     * @param dataWoolWoolPagination 分页查询参数
     * @return 羊毛记录列表
     */
    @Operation(summary = "获取羊毛记录列表")
    @PostMapping("/getList")
    public ActionResult list(@RequestBody DataWoolWoolPagination dataWoolWoolPagination) throws IOException {
        List<DataWoolWoolEntity> list = dataWoolWoolService.getList(dataWoolWoolPagination);
        List<DataWoolWoolVO> listVO = BeanCopierUtils.copyList(list, DataWoolWoolVO.class);

        // 批量获取账户信息，避免重复查询
        Set<String> accIds = new HashSet<>();
        for (DataWoolWoolVO vo : listVO) {
            if (StringUtils.isNotBlank(vo.getSrcAccId())) {
                accIds.add(vo.getSrcAccId());
            }
            if (StringUtils.isNotBlank(vo.getInAccId())) {
                accIds.add(vo.getInAccId());
            }
        }

        // 批量查询账户信息
        Map<String, String> accNameMap = new HashMap<>();
        for (String accId : accIds) {
            DataAccEntity acc = dataAccService.getInfo(accId);
            if (acc != null) {
                accNameMap.put(accId, acc.getName());
            }
        }

        // 处理数据转换和补充
        for (DataWoolWoolVO vo : listVO) {
            // 账户名称转换
            if (StringUtils.isNotBlank(vo.getSrcAccId())) {
                vo.setSrcAccId(accNameMap.getOrDefault(vo.getSrcAccId(), vo.getSrcAccId()));
            }
            if (StringUtils.isNotBlank(vo.getInAccId())) {
                vo.setInAccId(accNameMap.getOrDefault(vo.getInAccId(), vo.getInAccId()));
            }

            // 用户名称转换
            vo.setCreateBy(generaterSwapUtil.userSelectValue(vo.getCreateBy()));
            vo.setUpdateBy(generaterSwapUtil.userSelectValue(vo.getUpdateBy()));
        }

        PaginationVO page = BeanCopierUtils.copy(dataWoolWoolPagination, PaginationVO.class);
        return ActionResult.page(listVO, page);
    }

    /**
     * 创建
     *
     * @param dataWoolWoolForm
     * @return
     */
    @PostMapping
    public ActionResult create(@RequestBody @Valid DataWoolWoolForm dataWoolWoolForm) throws DataException {
        String mainId = RandomUtil.snowId();
        UserInfo userInfo = userProvider.get();
//        dataWoolWoolForm.setCreateBy(userInfo.getUserId());
//        dataWoolWoolForm.setCreateTime(DateXhUtil.now());
        DataWoolWoolEntity entity = BeanCopierUtils.copy(dataWoolWoolForm, DataWoolWoolEntity.class);
        entity.setId(mainId);
        dataWoolWoolService.save(entity);
        syncNewDataFlow(entity);
        return ActionResult.success("创建成功");
    }

    /**
     * 信息
     *
     * @param id
     * @return
     */
    @GetMapping("/{id}")
    public ActionResult<DataWoolWoolVO> info(@PathVariable("id") String id) {
        DataWoolWoolEntity entity = dataWoolWoolService.getInfo(id);
        DataWoolWoolVO vo = BeanCopierUtils.copy(entity, DataWoolWoolVO.class);
        vo.setCreateBy(generaterSwapUtil.userSelectValue(vo.getCreateBy()));
        vo.setUpdateBy(generaterSwapUtil.userSelectValue(vo.getUpdateBy()));
        return ActionResult.success(vo);
    }

    /**
     * 更新
     *
     * @param id
     * @return
     */
    @PutMapping("/{id}")
    public ActionResult update(@PathVariable("id") String id, @RequestBody @Valid DataWoolWoolForm dataWoolWoolForm) throws DataException {
        UserInfo userInfo = userProvider.get();
        DataWoolWoolEntity entity = dataWoolWoolService.getInfo(id);
        if (entity != null) {
//            dataWoolWoolForm.setUpdateBy(userInfo.getUserId());
//            dataWoolWoolForm.setUpdateTime(DateXhUtil.date());
            DataWoolWoolEntity subentity = BeanCopierUtils.copy(dataWoolWoolForm, DataWoolWoolEntity.class);
            subentity.setCreateBy(entity.getCreateBy());
            subentity.setCreateTime(entity.getCreateTime());
            dataWoolWoolService.update(id, subentity);
            syncNewDataFlow(subentity);
            return ActionResult.success("更新成功");
        } else {
            return ActionResult.fail("更新失败，数据不存在");
        }
    }

    /**
     * 删除
     *
     * @param id
     * @return
     */
    @DeleteMapping("/{id}")
    public ActionResult delete(@PathVariable("id") String id) {
        DataWoolWoolEntity entity = dataWoolWoolService.getInfo(id);
        if (entity != null) {
            dataWoolWoolService.delete(entity);
            dataFlowService.deleteDataFlowByRelatedIds(id);
        }
        return ActionResult.success("删除成功");
    }


    private void syncNewDataFlow(DataWoolWoolEntity dataWoolWool) {

        //第一步删除数据
        String relatedId = dataWoolWool.getId();
        dataFlowService.deleteDataFlowByRelatedIds(relatedId);

        //如果存在定金数据，则需要进行定金计算
        if (StringUtils.isNotBlank(dataWoolWool.getInAccId()) && dataWoolWool.getFlowAmount() != null && dataWoolWool.getFlowAmount().doubleValue() > 0) {
            DataFlowEntity dataFlow = new DataFlowEntity();
            dataFlow.setRelatedId(relatedId);
            dataFlow.setFlowDate(dataWoolWool.getInDate());
            dataFlow.setAccId(dataWoolWool.getInAccId());
            dataFlow.setAmout(dataWoolWool.getFlowAmount());
            dataFlow.setTransType("1");
            dataFlow.setType("JFDHSR");
            dataFlow.setNote("【羊毛】" + StringUtils.stripToEmpty(dataWoolWool.getAcitivity()) + StringUtils.stripToEmpty(dataWoolWool.getNote()));
            dataFlowService.create(dataFlow);
        }
    }
}
