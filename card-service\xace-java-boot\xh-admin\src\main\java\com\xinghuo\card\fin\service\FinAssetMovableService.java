package com.xinghuo.card.fin.service;

import com.xinghuo.card.fin.entity.FinAssetMovableEntity;
import com.xinghuo.card.fin.model.finAssetMovable.FinAssetMovablePagination;
import com.xinghuo.common.base.service.BaseService;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 动产资产服务接口
 *
 * <AUTHOR> Assistant
 * @version V1.0.0
 * @date 2024-12-29
 */
public interface FinAssetMovableService extends BaseService<FinAssetMovableEntity> {

    /**
     * 分页查询动产资产
     *
     * @param pagination 查询条件
     * @return 分页数据
     */
    List<FinAssetMovableEntity> getList(FinAssetMovablePagination pagination);

    /**
     * 根据ID查询动产资产
     *
     * @param id 资产ID
     * @return 动产资产
     */
    FinAssetMovableEntity getInfo(String id);

    /**
     * 创建动产资产
     *
     * @param entity 资产信息
     * @return 创建结果
     */
    boolean saveInfo(FinAssetMovableEntity entity);

    /**
     * 更新动产资产
     *
     * @param entity 更新信息
     * @return 更新结果
     */
    boolean updateInfo(FinAssetMovableEntity entity);

    /**
     * 删除动产资产
     *
     * @param id 资产ID
     * @return 删除结果
     */
    boolean deleteById(String id);

    /**
     * 批量删除动产资产
     *
     * @param ids 资产ID列表
     * @return 删除结果
     */
    boolean deleteBatch(List<String> ids);

    /**
     * 根据持卡人ID获取动产资产列表
     *
     * @param manId 持卡人ID
     * @return 动产资产列表
     */
    List<FinAssetMovableEntity> getListByManId(String manId);

    /**
     * 获取动产资产统计信息
     *
     * @return 统计信息
     */
    Map<String, Object> getAssetStatistics();

    /**
     * 获取动产类型分布统计
     *
     * @return 类型分布
     */
    List<Map<String, Object>> getAssetTypeDistribution();

    /**
     * 获取动产品牌分布统计
     *
     * @return 品牌分布
     */
    List<Map<String, Object>> getBrandDistribution();

    /**
     * 更新动产估值
     *
     * @param id 资产ID
     * @param currentValue 当前估值
     * @param valuationMethod 估值方法
     * @return 更新结果
     */
    boolean updateValuation(String id, BigDecimal currentValue, String valuationMethod);

    /**
     * 批量更新动产估值
     *
     * @param valuationList 估值列表
     * @return 更新成功的数量
     */
    int batchUpdateValuation(List<Map<String, Object>> valuationList);

    /**
     * 获取动产估值历史
     *
     * @param id 资产ID
     * @return 估值历史
     */
    List<Map<String, Object>> getValuationHistory(String id);

    /**
     * 计算动产总价值
     *
     * @param manId 持卡人ID
     * @return 总价值
     */
    BigDecimal calculateTotalValue(String manId);

    /**
     * 获取动产价值趋势
     *
     * @param manId 持卡人ID
     * @param months 月份数
     * @return 价值趋势
     */
    List<Map<String, Object>> getValueTrend(String manId, Integer months);

    /**
     * 检查动产名称是否重复
     *
     * @param name 资产名称
     * @param manId 持卡人ID
     * @param excludeId 排除ID
     * @return 是否重复
     */
    boolean checkNameDuplicate(String name, String manId, String excludeId);

    /**
     * 导入动产资产数据
     *
     * @param assetList 资产数据列表
     * @return 导入结果
     */
    Map<String, Object> importAssetData(List<FinAssetMovableEntity> assetList);

    /**
     * 导出动产资产数据
     *
     * @param pagination 查询条件
     * @return 导出数据
     */
    List<Map<String, Object>> exportAssetData(FinAssetMovablePagination pagination);

    /**
     * 复制动产资产
     *
     * @param id 源资产ID
     * @param newName 新资产名称
     * @return 复制的资产信息
     */
    FinAssetMovableEntity copyAsset(String id, String newName);

    /**
     * 获取动产资产详细信息（包含估值历史）
     *
     * @param id 资产ID
     * @return 详细信息
     */
    Map<String, Object> getAssetDetailWithHistory(String id);

    /**
     * 根据动产类型获取资产列表
     *
     * @param assetType 动产类型
     * @return 资产列表
     */
    List<FinAssetMovableEntity> getListByAssetType(String assetType);

    /**
     * 根据品牌获取资产列表
     *
     * @param brand 品牌
     * @return 资产列表
     */
    List<FinAssetMovableEntity> getListByBrand(String brand);

    /**
     * 获取高价值动产列表
     *
     * @param minValue 最小价值
     * @return 高价值资产列表
     */
    List<FinAssetMovableEntity> getHighValueAssets(BigDecimal minValue);

    /**
     * 计算动产折旧
     *
     * @param id 资产ID
     * @return 折旧信息
     */
    Map<String, Object> calculateDepreciation(String id);

    /**
     * 获取动产使用年限统计
     *
     * @param manId 持卡人ID
     * @return 使用年限统计
     */
    Map<String, Object> getUsageYearStats(String manId);

    /**
     * 获取即将到期的动产列表
     *
     * @param manId 持卡人ID
     * @param months 提前月数
     * @return 即将到期的动产列表
     */
    List<FinAssetMovableEntity> getExpiringAssets(String manId, Integer months);

    /**
     * 更新动产状态
     *
     * @param id 资产ID
     * @param status 新状态
     * @return 更新结果
     */
    boolean updateAssetStatus(String id, String status);

    /**
     * 批量更新动产状态
     *
     * @param ids 资产ID列表
     * @param status 新状态
     * @return 更新成功的数量
     */
    int batchUpdateAssetStatus(List<String> ids, String status);

    /**
     * 获取动产保险到期提醒
     *
     * @param manId 持卡人ID
     * @param days 提前天数
     * @return 保险到期列表
     */
    List<FinAssetMovableEntity> getInsuranceExpiringAssets(String manId, Integer days);

    /**
     * 计算动产投资回报率
     *
     * @param id 资产ID
     * @return 投资回报率
     */
    Map<String, Object> calculateROI(String id);

    /**
     * 获取动产维护记录
     *
     * @param id 资产ID
     * @return 维护记录
     */
    List<Map<String, Object>> getMaintenanceRecords(String id);

    /**
     * 添加动产维护记录
     *
     * @param id 资产ID
     * @param maintenanceInfo 维护信息
     * @return 添加结果
     */
    boolean addMaintenanceRecord(String id, Map<String, Object> maintenanceInfo);

    /**
     * 获取动产持有时长统计
     *
     * @param manId 持卡人ID
     * @return 持有时长统计
     */
    Map<String, Object> getHoldingPeriodStats(String manId);

    /**
     * 获取动产折旧信息
     *
     * @param id 资产ID
     * @return 折旧信息
     */
    Map<String, Object> getDepreciationInfo(String id);

    /**
     * 批量更新动产状态
     *
     * @param ids 资产ID列表
     * @param status 新状态
     * @return 更新成功的数量
     */
    int batchUpdateStatus(List<String> ids, String status);

    /**
     * 获取即将到期的动产列表（按天数）
     *
     * @param days 提前天数
     * @return 即将到期的动产列表
     */
    List<FinAssetMovableEntity> getExpiringAssets(int days);

    /**
     * 获取动产保险信息
     *
     * @param id 资产ID
     * @return 保险信息
     */
    Map<String, Object> getInsuranceInfo(String id);

    /**
     * 更新动产保险信息
     *
     * @param id 资产ID
     * @param insuranceInfo 保险信息
     * @return 更新结果
     */
    boolean updateInsuranceInfo(String id, Map<String, Object> insuranceInfo);
}
