package com.xinghuo.card.flow.model.dataflowacc;

import com.xinghuo.common.base.model.Pagination;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 账号明细
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2022-11-27
 */
@Data
public class DataFlowAccPagination extends Pagination {

    @Schema(description = "收支类型 ")
    private String type;

    @Schema(description = "日期 ")
    private List<String> flowDate;

    @Schema(description = "菜单id")
    private String menuId;

    @Schema(description = "账号ID")
    private String accId;
}
