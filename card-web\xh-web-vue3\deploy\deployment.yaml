apiVersion: apps/v1
kind: Deployment
metadata:
  name: xh-web-vue3
  namespace: xh-staging
  labels:
    app: xh-web-vue3
  annotations:
      kubesphere.io/description: vue3测试环境
spec:
  replicas: 1
  selector:
    matchLabels:
      app: xh-web-vue3
  strategy:
    rollingUpdate:
      maxSurge: 50%
      maxUnavailable: 50%
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: xh-web-vue3
    spec:
      imagePullSecrets:
        - name: harbor-id
      containers:
        - name: containers-xh-web-vue3
          image: $REGISTRY/$HARBOR_NAMESPACE/$APP_NAME:SNAPSHOT-$BRANCH_NAME-$BUILD_NUMBER
          imagePullPolicy: Always
          ports:
            - name: http-80
              containerPort: 80
              protocol: TCP
          resources:
            limits:
              cpu: 300m
              memory: 600Mi
          volumeMounts:
            - name: timezone
              mountPath: /etc/localtime
            - name: volume-web-vue3-cm
              readOnly: true
              mountPath: /etc/nginx/conf.d/default.conf
              subPath: default.conf
      volumes:
        - name: timezone
          hostPath:
            path: /etc/localtime
            type: ''
        - name: volume-web-vue3-cm
          configMap:
            name: xh-web-vue3-cm
            items:
              - key: default.conf
                path: default.conf
      restartPolicy: Always
