package com.xinghuo.card.flow.model.dataflowacc;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;

/**
 * 账号明细
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2022-11-27
 */
@Data
public class DataFlowAccVO {

    @Schema(description = "主键")
    private String id;

    @Schema(description = "收支类型")
    @JsonProperty("type")
    private String type;

    @Schema(description = "收支类型中文描述")
    private String typeDesc;

    @Schema(description = "日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @JsonProperty("flowDate")
    private Date flowDate;

    @Schema(description = "收入")
    @JsonProperty("income")
    private BigDecimal income;

    @Schema(description = "支出")
    @JsonProperty("pay")
    private BigDecimal pay;

    @Schema(description = "流水ID")
    @JsonProperty("flowId")
    private String flowId;

    @Schema(description = "账号")
    @JsonProperty("accId")
    private String accId;

    private String accName;

    @Schema(description = "关联账号")
    @JsonProperty("outAccId")
    private String outAccId;

    private String outAccName;

    @Schema(description = "标签")
    private String manId;

    @Schema(description = "余额")
    @JsonProperty("balance")
    private BigDecimal balance;

    @Schema(description = "排序")
    @JsonProperty("listOrder")
    private Integer listOrder;

    @Schema(description = "账单ID")
    @JsonProperty("billId")
    private String billId;

    @Schema(description = "备注")
    @JsonProperty("note")
    private String note;

    @Schema(description = "排序2")
    @JsonProperty("listOrder2")
    private Integer listOrder2;

}
