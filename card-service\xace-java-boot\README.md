> 特别说明：源码、JDK、MySQL、Redis等存放路径禁止包含中文、空格、特殊字符等

## 一 技术栈

- 主框架：`Spring Boot` + `Spring Framework`
- 持久层框架：`MyBatis-Plus`
- JSON序列化: `<PERSON>`&`<PERSON><PERSON><PERSON>`
- 缓存：`Redis`
- 数据库： `MySQL`(默认)、`SQLServer`、`Oracle`、`PostgreSQL`、`达梦数据库`、`人大金仓数据库`
- 项目构建：`Maven`
- 安全框架：`spring-cloud-security-oauth2`+`jwt`
- 模板引擎：`Velocity`
- 任务调度：`XXL-JOB`
- 即时通讯：`spring-boot-starter-websocket`
- AOP：`spring-boot-starter-aop`

## 二 环境要求

> 官方建议：JDK版本不低于 `1.8.0_281`版本，可使用`OpenJDK 8/11`、`Alibaba Dragonwell 8/11`、`BiShengJDK 8/11`

> `Alibaba Dragonwell 8/11`下载地址：https://dragonwell-jdk.io

> `BiShengJDK 8/11`下载地址：https://www.hikunpeng.com/developer/devkit/compiler/jdk

> 在使用JDK11时需要把项目根目录下的`pom.xml`中的<java.version>改成`11`

| 项目                    | 推荐版本 | 说明                                     |
|-----------------------| --- |----------------------------------------|
| IDEA                  | IDEA2020及以上版本 |                                        |
| JDK                   | 1.8.0_281及以上版本 | JAVA环境依赖(需配置环境变量)                      |
| Maven                 | 3.6.3及以上版本 | 项目构建(需配置环境变量)                          |
| Redis                 | 3.2.100(Windows)/4.0.x+(Linux,Mac) | 缓存                                     |
| MySQL                 | 5.7.x+ | 数据库任选一(默认)                             |
| SQLServer             | 2012+ | 数据库任选一                                 |
| Oracle                | 11g+ | 数据库任选一                                 |
| PostgreSQL            | 12+ | 数据库任选一                                 |
| 达梦数据库                 | DM8 | 数据库任选一                                 |
| 人大金库                  | KingbaseES V8 R6 | 数据库任选一                                 |

## 三 IDEA插件

- `Lombok`(必须)
- `Alibaba Java Coding Guidelines`
- `MybatisX`

## 四 Maven私服配置

> 因部分依赖在阿里云Maven私服、Maven官方无法下载

**依赖包差异**

- com.sqlserver:sqljdbc4:4.0
- com.oracle:ojdbc6:11.2.0
- com.dm:DmJdbcDriver18:1.8.0
- com.kingbase8:kingbase8-jdbc:2.0
- dingtalk-sdk-java:taobao-sdk-java-source:1.0
- dingtalk-sdk-java:taobao-sdk-java:1.0
- yozo:signclient:3.0.1


## 五 开发环境

### 5.1 环境准备

> 开发环境以Windows 10为例

#### 5.1.1 基础环境
| 项目 | 说明 |
| --- | --- |
| IDEA | 使用IDEA 2020及以上版本，也可以用eclipse等其他JAVA开发工具 |
| JDK | 1.8.0_281及以上版本 |
| Maven | 3.6.3及以上版本 |
| Redis | 3.2.100 |
| Navicat(可选) | 数据库管理工具 |
| RDM(可选) | Redis管理工具 |

#### 5.1.2 关联项目
| 项目                     | 说明                 |
|------------------------|--------------------|
| xace-db          | 数据库脚本文件            |
| xace-scheduletask      | 任务调度服务端(XXL-JOB引擎) |
| xace-file-core-starter | 文件服务核心工具包          |
| xace-resourecs         | 静态资源               |

### 5.2 初始化数据库

> 以`MySQL`数据库为例

#### 5.2.1 创建数据库

  创建平台数据库

#### 5.2.2 导入数据库脚本

  将`/xace-db/MySQL/<版本>/xh_init.sql`导入至上述创建的数据库中

### 5.3 准备依赖项目

#### 5.3.1 导入系统调度服务端

  详见`xace-scheduletask`项目中的`README.md`文档说明

#### 5.3.2 导入文件服务核心工具包

  详见`xace-file-core-starter`项目中的`README.md`文档说明

### 5.4 环境配置

#### 5.4.1 主配置文件

  打开编辑`xh-admin/src/main/resources/application.yml`

**指定环境配置**

``` yml
  # application.yml第5行,可选值：dev(默认)|test|pro|preview
  active: dev
```
其他配置参考`application.yml`文件中的说明

#### 5.4.2 环境配置文件

项目中环境配置文件说明

- `application-dev.yml`  开发环境(默认)
- `application-test.yml`  测试环境
- `application-preview.yml` 预发布环境
- `application-pro.yml` 生产环境

> 以开发环境以例

 打开编辑`application-dev.yml`,需配置以下相关信息
  - 应用端口(`port`)
  - 数据源配置
  - Redis配置
  - 系统调度服务端配置
  - 静态资源配置
  - 第三方登录配置(可选)
  - 任务调度配置

### 5.5 启动项目

  找到`xh-admin/src/main/java/XhAdminApplication.java`，右击运行即可。

## 六 项目发布

- 在`IDEA`右侧`Maven`-`xace-java-boot(root)`-`Lifecycle`中双击`clean`清理下项目
- 双击`package`打包项目
- 打开项目目录，依次打开`xace-java-boot\xh-admin\target`，将`xh-admin-{version}-RELEASE.jar`上传至服务器

## 七 swagger接口文档
- `http://localhost:30000/swagger-ui/`
