spring:
  application:
    name: xace-java-boot
  profiles:
    # 指定环境配置 dev(开发环境-默认)、test(测试环境)、pro(生产环境)
    active: vpn
  servlet:
    multipart: #文件传输配置
      max-file-size: 100MB #单个数据大小限制
      max-request-size: 100MB #请求总数据大小限制
      enabled: true  #是否启用分段上传支持
  mvc:
    hiddenmethod: #隐式方法过滤器
      filter:
        enabled: true #默认开启。开启以支持：PUT,DELETE表单提交方法
    pathmatch:
      matching-strategy: ant_path_matcher
  jackson: #序列化和反序列化json框架
    serialization:
      write-dates-as-timestamps: true #是否写入日期时间时间戳格式
    time-zone: GMT+8 #指定日期格式化时区
  main:
    allow-bean-definition-overriding: true #允许同名bean后者覆盖,默认：true
    allow-circular-references: true #允许Bean相互引用,默认：false

mybatis-plus:
  global-config:
    db-config:
      # 设置需要排除的表名
      tenant-exclude-tables: zz_allegro_categories_parameters,base_tenant,zz_sync_time,zz_allegro_api_client,zz_collect_offer_parse_tmp,zz_express,zz_express_product

config:
  # ===============静态资源目录映射==================
  WebAnnexFilePath: WebAnnexFile
  DataBackupFilePath: DataBackupFile
  TemporaryFilePath: TemporaryFile
  SystemFilePath: SystemFile
  TemplateFilePath: TemplateFile
  EmailFilePath: EmailFile
  DocumentFilePath: DocumentFile
  DocumentPreviewPath: DocumentPreview
  UserAvatarFilePath: UserAvatar
  IMContentFilePath: IMContentFile
  MPMaterialFilePath: MPMaterial
  TemplateCodePath: TemplateCode
  BiVisualPath: BiVisualPath
  # ===============功能格式限制==================
  MPUploadFileType: bmp,png,jpeg,jpg,gif,mp3,wma,wav,amr,mp4
  WeChatUploadFileType: jpg,png,doc,docx,ppt,pptx,xls,xlsx,pdf,txt,rar,zip,csv,amr,mp4

  AllowUploadImageType: jpg,gif,png,bmp,jpeg,tiff,psd,swf,svg,pcx,dxf,wmf,emf,lic,eps,tga #允许上传图片类型
  AllowUploadFileType: jpg,gif,png,bmp,jpeg,doc,docx,ppt,pptx,xls,xlsx,pdf,txt,rar,zip,csv,mp3 #允许上传文件类型
  AllowPreviewFileType: doc,docx,xls,xlsx,ppt,pptx,pdf,jpg,gif,png,bmp,jpeg #允许预览文件类型
  PreviewType: kkfile #文件预览方式 （1.yozo 2.kkfile）默认使用kkfile
  kkFileUrl: http://127.0.0.1:30090/FileServer/ #kkfile文件预览服务地址
  ApiDomain: http://127.0.0.1:32000 #后端域名(文档预览中使用)
  FrontDomain: http://127.0.0.1:3000 #前端域名(文档预览中使用)
  AppDomain: http://127.0.0.1:8080 #app/h5端域名配置(文档预览中使用)
  allgeroUrl: http://127.0.0.1:3100/staticImage/
  alldata: 1059,569205217342521733,576024611603351045,577405585251634117,597710600079940549,admin

  CodeAreasName: example #代码生成器模块命名


  #===================== unipush =====================
  AppPushUrl: https://8e84eea8-6922-4033-8e86-67ad7442e692.bspapp.com/unipush

  #===================== 多租户 =====================
  MultiTenancy: false #是否开启
  MultiTenancyUrl: http://**************:30006/api/tenant/DbName/ #多租户项目地址
  #COLUMN、SCHEMA模式
  MultiTenantType: COLUMN
  ignoreTables:
    - zz_allegro_categories_parameters
    - base_tenant
    - zz_sync_time
    - zz_allegro_api_client

  #===================== 系统及错误报告反馈相关 =====================
  SoftName: xace-java-boot #项目名
  SoftFullName: XH快速开发平台 #项目全名
  SoftVersion: V2.0.0 #版本号

  RecordLog: true #系统日志启用
  ErrorReport: false #软件错误报告
  ErrorReportTo: <EMAIL> #软件错误报告接收者
  IgexinEnabled: true #推送启动

  #===================== APP =====================
  AppVersion: V2.0.0 #APP版本号
  IgexinAppid: HLFY9T2d1z7MySY8hwGwh4 #APPID：应用的唯一标识
  IgexinAppkey: 6Uiduugq648YDChhCjAt59 #APPKEY：公匙（相当于账号）
  IgexinMastersecret: pEyQm156SJ9iS7PbyjLCZ6 #Mastersecret：私匙（相当于密码）
  AppUpdateContent: ; #APP更新内容

  #===================== 永中office在线预览配置 =====================
  YozoDomain: //dcsapi.com/ #永中api域名
  YozoDomainKey: 57462250284462899305150 #域名key
  YozoCloudDomain: //dmc.yozocloud.cn #云预览
  YozoAppId: yozoAgR41jgC0062 #appid
  YozoAppKey: fc3134a9ba8bc6f4c69d635f9adf #app秘钥
  YozoEditDomain: //eic.yozocloud.cn #云编辑

  #===================== 系统功能配置 =====================
  EnablePreAuth: true #是否开启接口鉴权
  EnableLogicDelete: false #是否开启逻辑删除
  #===================== 是否开启magic接口编辑权限，默认为开启。可自行关闭，关闭后所有用户都有权限访问接口=====================
  EnableMagicEditAuth: true
#日志配置
logging:
  config: classpath:logback-spring.xml
  level: #自定义第三方包名日志等级
    #    org.springframework.cache: trace
    org.springframework: info
    com.alicp.jetcache: info
    org.redisson: info
    io: info
    com.alibaba.nacos: ERROR
    com.alibaba.cloud.nacos: ERROR
    io.seata: ERROR
    org.apache.dubbo: ERROR
    # 解除注释后Druid连接池打印SQL语句
    druid.sql.Statement: debug
    #    druid.sql.DataSource: debug
    #    druid.sql.Connection: debug
    druid.sql.ResultSet: info
log:
  level:
    root: info
  path: log/${spring.application.name}
#knife4j账号密码配置
knife4j:
  enable: true
  basic:
    enable: true
    username: xinghuo
    password: XH12345@
  #magic-api:
  #  #配置web页面入口
  #  web: /magic/web
  #  prefix: /magic
  #  resource:
  #    type: database
  #  #magic-api界面配置
  #  editor-config: classpath:./magic-editor-config.js
  backup:
    #是否启用备份
    enable: true
    #备份保留天数,-1为永久保存
    max-history: -1
    table-name: magic_api_backup
  swagger:
    version: 1.0
    description: MagicAPI接口信息
    title: MagicAPI Swagger Docs
    name: MagicAPI 接口
    location: /v2/api-docs/magic-api/swagger2.json
  cluster:
    channel: magic-api:notify:channel #redis通道，此值为默认值
  page:
    size: pageSize #页大小的请求参数名称
    page: currentPage #页码的请求参数名称
    default-page: 1 #自定义默认首页
    default-size: 20 #自定义页码
fanyi:
  appid: 20240923002158398
  secret: z0Tj11UPmBv74fQNr6C8
webhook:
  enable: true
  url: https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=fb338d7b-c798-4172-8cdd-0e996fe16d19
  erpurl: https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=be91eb0f-aa1b-4058-9f79-7e521c61f292

