package com.xinghuo.card.flow.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xinghuo.card.flow.entity.DataFlowAccEntity;
import com.xinghuo.card.flow.model.dataflowacc.DataFlowAccPagination;

import java.util.List;


/**
 * data_flow_acc
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2022-11-26
 */
public interface DataFlowAccService extends IService<DataFlowAccEntity> {
    /**
     * 查询分页数据
     *
     * @param dataFlowAccPagination 查询对象
     * @return 查询结果
     */
    List<DataFlowAccEntity> getList(DataFlowAccPagination dataFlowAccPagination);

    /**
     * 查询分页或者不分页列表
     *
     * @param dataFlowAccPagination 查询对象
     * @param dataType              0:分页 1:不分页
     * @return 查询结果
     */
    List<DataFlowAccEntity> getTypeList(DataFlowAccPagination dataFlowAccPagination, int dataType);

    /**
     * 获取DataFlowAccEntity详细信息
     *
     * @param id   主键
     */
    DataFlowAccEntity getInfo(String id);

    /**
     * 删除
     *
     * @param entity 删除的对象
     */
    void delete(DataFlowAccEntity entity);

    /**
     * 新增保存
     *
     * @param entity 新增的对象
     */
    void create(DataFlowAccEntity entity);

    /**
     * 修改保存
     *
     * @param id     主键
     * @param entity 修改的对象
     */
    boolean update(String id, DataFlowAccEntity entity);


    public int selectMaxListOrder(DataFlowAccEntity dataFlowAcc);


    public int updateDataFlowAccBalance(DataFlowAccEntity entity);

    public DataFlowAccEntity getDataFlowAccByFlowId(String accId, String flowId);

    public int deleteDataFlowAccByFlowId(String flowId);

}
