-- ===================================================================
-- 个人财务收支管理系统第一阶段功能数据库脚本
-- 包含：预算管理模块、智能提醒功能所需的表结构
-- 执行时间：请在测试环境先行验证后再在生产环境执行
-- ===================================================================

-- -------------------------------------------------
-- 1. 预算管理模块相关表
-- -------------------------------------------------

-- 预算主表
CREATE TABLE `fin_budget` (
  `ID` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '主键ID',
  `USER_ID` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '用户ID, 关联至现有用户体系',
  `NAME` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '预算名称',
  `PERIOD_TYPE` enum('MONTHLY', 'YEARLY') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '周期类型',
  `START_DATE` date NOT NULL COMMENT '开始日期',
  `END_DATE` date NOT NULL COMMENT '结束日期',
  `TOTAL_BUDGET_AMOUNT` decimal(12,2) DEFAULT NULL COMMENT '总预算金额 (由子项汇总)',
  `STATUS` enum('ACTIVE', 'ARCHIVED') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT 'ACTIVE' COMMENT '状态：激活/归档',
  `CREATE_BY` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人',
  `CREATE_TIME` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATE_BY` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '更新人',
  `UPDATE_TIME` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `f_tenantid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '租户id',
  `f_flowid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '流程id',
  PRIMARY KEY (`ID`) USING BTREE,
  KEY `idx_user_date` (`USER_ID`, `START_DATE`) USING BTREE,
  KEY `idx_status` (`STATUS`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='预算主表';

-- 预算子项表
CREATE TABLE `fin_budget_item` (
  `ID` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '主键ID',
  `BUDGET_ID` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '关联 fin_budget.ID',
  `ITEM_TYPE` enum('CATEGORY', 'ACCOUNT') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '预算项类型: 分类/账户',
  `TARGET_ID` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '目标ID (关联 data_sys_paytype.ID 或 data_acc.ID)',
  `TARGET_NAME` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '目标名称（冗余字段，便于查询）',
  `BUDGETED_AMOUNT` decimal(12,2) NOT NULL COMMENT '预算金额',
  `CURRENT_SPENT` decimal(12,2) DEFAULT 0.00 COMMENT '当前已支出金额（可通过计算得出，也可存储以提高性能）',
  `ALERT_THRESHOLD` decimal(5,2) DEFAULT 80.00 COMMENT '预警阈值百分比（默认80%）',
  `IS_ALERTED` tinyint(1) DEFAULT 0 COMMENT '是否已预警（避免重复提醒）',
  `CREATE_BY` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人',
  `CREATE_TIME` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATE_BY` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '更新人',
  `UPDATE_TIME` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE,
  KEY `idx_budget_id` (`BUDGET_ID`) USING BTREE,
  KEY `idx_target` (`ITEM_TYPE`, `TARGET_ID`) USING BTREE,
  CONSTRAINT `fk_budget_item_budget` FOREIGN KEY (`BUDGET_ID`) REFERENCES `fin_budget` (`ID`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='预算子项表';

-- 预算执行历史记录表（可选，用于历史分析）
CREATE TABLE `fin_budget_history` (
  `ID` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '主键ID',
  `BUDGET_ITEM_ID` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '预算项ID',
  `RECORD_DATE` date NOT NULL COMMENT '记录日期',
  `SPENT_AMOUNT` decimal(12,2) NOT NULL COMMENT '当日支出金额',
  `CUMULATIVE_SPENT` decimal(12,2) NOT NULL COMMENT '累计支出金额',
  `CREATE_TIME` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`ID`) USING BTREE,
  KEY `idx_item_date` (`BUDGET_ITEM_ID`, `RECORD_DATE`) USING BTREE,
  CONSTRAINT `fk_budget_history_item` FOREIGN KEY (`BUDGET_ITEM_ID`) REFERENCES `fin_budget_item` (`ID`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='预算执行历史记录表';

-- -------------------------------------------------
-- 2. 智能提醒功能相关表
-- -------------------------------------------------

-- 系统通知表
CREATE TABLE `sys_notification` (
  `ID` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '主键ID',
  `USER_ID` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '用户ID',
  `TITLE` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '通知标题',
  `CONTENT` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '通知内容',
  `NOTIF_TYPE` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '提醒类型: BILL_REPAY, BUDGET_ALERT, CARD_EXPIRE, CUSTOM等',
  `RELATED_ID` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '关联对象ID (如账单ID, 预算项ID)',
  `RELATED_TYPE` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '关联对象类型',
  `PRIORITY` enum('LOW', 'NORMAL', 'HIGH', 'URGENT') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT 'NORMAL' COMMENT '优先级',
  `IS_READ` tinyint(1) DEFAULT 0 COMMENT '0-未读, 1-已读',
  `IS_PUSHED` tinyint(1) DEFAULT 0 COMMENT '0-未推送, 1-已推送',
  `PUSH_TIME` datetime DEFAULT NULL COMMENT '推送时间',
  `EXPIRE_TIME` datetime DEFAULT NULL COMMENT '过期时间（过期后可自动清理）',
  `CREATE_BY` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人',
  `CREATE_TIME` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATE_BY` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '更新人',
  `UPDATE_TIME` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `f_tenantid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`ID`) USING BTREE,
  KEY `idx_user_read` (`USER_ID`, `IS_READ`) USING BTREE,
  KEY `idx_user_type` (`USER_ID`, `NOTIF_TYPE`) USING BTREE,
  KEY `idx_create_time` (`CREATE_TIME`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='系统通知表';

-- 提醒规则配置表
CREATE TABLE `sys_notification_rule` (
  `ID` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '主键ID',
  `USER_ID` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '用户ID',
  `RULE_TYPE` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '规则类型',
  `RULE_NAME` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '规则名称',
  `IS_ENABLED` tinyint(1) DEFAULT 1 COMMENT '是否启用',
  `RULE_CONFIG` json DEFAULT NULL COMMENT '规则配置（JSON格式）',
  `LAST_EXECUTED` datetime DEFAULT NULL COMMENT '最后执行时间',
  `CREATE_BY` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人',
  `CREATE_TIME` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATE_BY` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '更新人',
  `UPDATE_TIME` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE,
  KEY `idx_user_type` (`USER_ID`, `RULE_TYPE`) USING BTREE,
  KEY `idx_enabled` (`IS_ENABLED`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='提醒规则配置表';

-- -------------------------------------------------
-- 3. 索引优化建议（针对现有表）
-- -------------------------------------------------

-- 为了提高预算统计查询性能，建议为data_flow表添加以下索引（如果不存在）
-- ALTER TABLE `data_flow` ADD INDEX `idx_type_date` (`TYPE`, `FLOW_DATE`);
-- ALTER TABLE `data_flow` ADD INDEX `idx_acc_date` (`ACC_ID`, `FLOW_DATE`);
-- ALTER TABLE `data_flow` ADD INDEX `idx_trans_type_date` (`TRANS_TYPE`, `FLOW_DATE`);

-- 为了提高账单提醒查询性能，建议为data_credit_bill表添加以下索引（如果不存在）
-- ALTER TABLE `data_credit_bill` ADD INDEX `idx_repay_status_date` (`REPAY_STATUS`, `REPAY_DATE`);

-- -------------------------------------------------
-- 4. 初始化数据（提醒规则默认配置）
-- -------------------------------------------------

-- 插入默认的提醒规则模板（用户可以基于这些模板创建自己的规则）
INSERT INTO `sys_notification_rule` (`ID`, `USER_ID`, `RULE_TYPE`, `RULE_NAME`, `IS_ENABLED`, `RULE_CONFIG`, `CREATE_TIME`) 
VALUES 
('default-bill-repay-rule', 'SYSTEM', 'BILL_REPAY', '信用卡还款提醒', 1, 
 '{"advance_days": 3, "time": "09:00", "enabled_days": [1,2,3,4,5,6,7]}', 
 NOW()),
('default-budget-alert-rule', 'SYSTEM', 'BUDGET_ALERT', '预算超支提醒', 1, 
 '{"thresholds": [80, 90, 100], "time": "20:00"}', 
 NOW()),
('default-card-expire-rule', 'SYSTEM', 'CARD_EXPIRE', '信用卡到期提醒', 1, 
 '{"advance_months": 2, "time": "10:00"}', 
 NOW());

-- -------------------------------------------------
-- 5. 视图创建（便于复杂查询）
-- -------------------------------------------------

-- 预算执行情况视图
CREATE VIEW `v_budget_execution` AS
SELECT 
    b.ID as BUDGET_ID,
    b.NAME as BUDGET_NAME,
    b.PERIOD_TYPE,
    b.START_DATE,
    b.END_DATE,
    b.STATUS,
    bi.ID as ITEM_ID,
    bi.ITEM_TYPE,
    bi.TARGET_ID,
    bi.TARGET_NAME,
    bi.BUDGETED_AMOUNT,
    bi.CURRENT_SPENT,
    bi.ALERT_THRESHOLD,
    bi.IS_ALERTED,
    CASE 
        WHEN bi.BUDGETED_AMOUNT > 0 THEN (bi.CURRENT_SPENT / bi.BUDGETED_AMOUNT * 100)
        ELSE 0 
    END as SPENT_PERCENTAGE,
    (bi.BUDGETED_AMOUNT - bi.CURRENT_SPENT) as REMAINING_AMOUNT
FROM `fin_budget` b
LEFT JOIN `fin_budget_item` bi ON b.ID = bi.BUDGET_ID
WHERE b.STATUS = 'ACTIVE';

-- 用户未读通知统计视图
CREATE VIEW `v_user_notification_summary` AS
SELECT 
    USER_ID,
    COUNT(*) as TOTAL_NOTIFICATIONS,
    SUM(CASE WHEN IS_READ = 0 THEN 1 ELSE 0 END) as UNREAD_COUNT,
    SUM(CASE WHEN NOTIF_TYPE = 'BILL_REPAY' AND IS_READ = 0 THEN 1 ELSE 0 END) as UNREAD_BILL_COUNT,
    SUM(CASE WHEN NOTIF_TYPE = 'BUDGET_ALERT' AND IS_READ = 0 THEN 1 ELSE 0 END) as UNREAD_BUDGET_COUNT,
    MAX(CREATE_TIME) as LATEST_NOTIFICATION_TIME
FROM `sys_notification`
GROUP BY USER_ID;

-- -------------------------------------------------
-- 说明：
-- 1. 所有表都包含了与现有系统一致的字段规范（如f_tenantid, CREATE_BY等）
-- 2. 使用了适当的索引来确保查询性能
-- 3. 预算相关的计算逻辑将在应用层实现，数据库主要负责存储
-- 4. 通知系统设计为可扩展的，支持多种类型的提醒规则
-- 5. 建议在执行前在测试环境验证，特别是索引创建部分
-- -------------------------------------------------