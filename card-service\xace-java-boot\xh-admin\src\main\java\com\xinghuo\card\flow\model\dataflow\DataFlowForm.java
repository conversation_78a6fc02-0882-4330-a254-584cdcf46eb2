package com.xinghuo.card.flow.model.dataflow;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;

/**
 * 流水表
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2022-11-26
 */
@Data
public class DataFlowForm {

    @Schema(description = "主键")
    private String id;


    @Schema(description = "类型")
    @JsonProperty("transType")
    private String transType;


    @Schema(description = "收支类型")
    @JsonProperty("type")
    private String type;


    @Schema(description = "金额")
    @JsonProperty("amout")
    private BigDecimal amout;


    @Schema(description = "日期")
    @JsonProperty("flowDate")
    private Date flowDate;


    @Schema(description = "收支账户")
    @JsonProperty("accId")
    private String accId;


    @Schema(description = "备注")
    @JsonProperty("note")
    private String note;


    @Schema(description = "转入账户")
    @JsonProperty("inAccId")
    private String inAccId;

    @Schema(description = "标签")
    private String manId;

    @Schema(description = "转出账户")
    @JsonProperty("outAccId")
    private String outAccId;


    @Schema(description = "创建人员")
    @JsonProperty("createBy")
    private String createBy;


    @Schema(description = "创建时间")
    @JsonProperty("createTime")
    private Date createTime;


    @Schema(description = "修改人员")
    @JsonProperty("updateBy")
    private String updateBy;


    @Schema(description = "最后修改时间")
    @JsonProperty("updateTime")
    private Date updateTime;


}
