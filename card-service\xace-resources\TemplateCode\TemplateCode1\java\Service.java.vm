package ${package.Service};

import ${package.Entity}.${table.entityName};
import ${superServiceClassPackage};
import com.xinghuo.common.exception.WorkFlowException;
import com.xinghuo.common.base.*;
#if(${typeId}=='1')
#set($modelPath = ${modulePackageName}+".model."+${modelPathName})
import ${modelPath}.${genInfo.className}Form;
#set($modelClassName ="${genInfo.className.substring(0,1).toLowerCase()}${genInfo.className.substring(1)}")
#foreach($grid in ${child})
import ${package.Entity}.${grid.className}Entity;
import ${package.Service}.${grid.className}Service;
#end
#foreach($grid in ${tableNameAll})
    #set($mastTableService = "import ${package.Service}.${grid.className}Service;")
    #set($mastTableEntity = "import ${package.Entity}.${grid.className}Entity;")
${mastTableService}
${mastTableEntity}
#end
#end

import java.util.*;

/**
 *
 * ${genInfo.description}
 * 版本： ${genInfo.version}
 * 版权： ${genInfo.copyright}
 * 作者： ${genInfo.createUser}
 * 日期： ${genInfo.createDate}
 */
public interface ${table.serviceName} extends ${superServiceClass}<${table.entityName}> {

    #if(${typeId}=='1')
    #foreach($grid in ${child})
    List<${grid.className}Entity> Get${grid.className}List(String id);

    #end
    #foreach($grid in ${tableNameAll})
    ${grid.className}Entity  get${grid.className}(String id);

    #end
    ${table.entityName} getInfo(String id);

    void create(${table.entityName} entity#foreach($grid in ${child}), List<${grid.className}Entity> ${grid.className}List#end#foreach($grid in ${tableNameAll}), ${grid.className}Entity ${grid.className}#end,String status,String flowId,Map<String, List<String>> candidateList,Map<String,Object> data)throws WorkFlowException;

    void update(String id, ${table.entityName} entity#foreach($grid in ${child}), List<${grid.className}Entity> ${grid.className}List#end#foreach($grid in ${tableNameAll}), ${grid.className}Entity ${grid.className}#end,String status,String flowId,Map<String, List<String>> candidateList,Map<String,Object> data)throws WorkFlowException;

    void data(String id, String data)throws WorkFlowException;

    ${genInfo.className}Form uniqueAll(Map<String,Object> data,int num)throws WorkFlowException;

    void delete(String id);
    #end
}
