package com.xinghuo.card.flow.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xinghuo.card.flow.dao.DataFlowRechargeMapper;
import com.xinghuo.card.flow.entity.DataFlowRechargeEntity;
import com.xinghuo.card.flow.model.dataflowrecharge.DataFlowRechargePagination;
import com.xinghuo.card.flow.service.DataFlowRechargeService;
import com.xinghuo.common.util.UserProvider;
import com.xinghuo.common.util.core.DateXhUtil;
import com.xinghuo.common.util.core.StrXhUtil;
import com.xinghuo.common.util.extra.ServletUtil;
import com.xinghuo.permission.model.authorize.AuthorizeConditionModel;
import com.xinghuo.permission.service.AuthorizeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 充值记录服务实现类
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2022-11-27
 */
@Service
public class DataFlowRechargeServiceImpl extends ServiceImpl<DataFlowRechargeMapper, DataFlowRechargeEntity> implements DataFlowRechargeService {

    @Autowired
    private UserProvider userProvider;

    @Autowired
    private AuthorizeService authorizeService;

    @Override
    public List<DataFlowRechargeEntity> getList(DataFlowRechargePagination dataFlowRechargePagination) {
        return getListByType(dataFlowRechargePagination, 0);
    }

    @Override
    public List<DataFlowRechargeEntity> getTypeList(DataFlowRechargePagination dataFlowRechargePagination, int dataType) {
        return getListByType(dataFlowRechargePagination, dataType);
    }

    private List<DataFlowRechargeEntity> getListByType(DataFlowRechargePagination dataFlowRechargePagination, int dataType) {
        List<String> allIdList = new ArrayList();
        int total = 0;
        int dataFlowRechargeNum = 0;
        QueryWrapper<DataFlowRechargeEntity> dataFlowRechargeQueryWrapper = new QueryWrapper<>();
        boolean pcPermission = false;
        boolean appPermission = false;
        boolean isPc = "pc".equals(ServletUtil.getHeader("xh-origin"));
        if (isPc && pcPermission) {
            if (!userProvider.get().getIsAdministrator()) {
                Object dataFlowRechargeObj = authorizeService.getCondition(new AuthorizeConditionModel(dataFlowRechargeQueryWrapper, dataFlowRechargePagination.getMenuId(), "data_flow_recharge"));
                if (ObjectUtil.isEmpty(dataFlowRechargeObj)) {
                    return new ArrayList<>();
                } else {
                    dataFlowRechargeQueryWrapper = (QueryWrapper<DataFlowRechargeEntity>) dataFlowRechargeObj;
                    dataFlowRechargeNum++;
                }
            }
        }
        if (!isPc && appPermission) {
            if (!userProvider.get().getIsAdministrator()) {
                Object dataFlowRechargeObj = authorizeService.getCondition(new AuthorizeConditionModel(dataFlowRechargeQueryWrapper, dataFlowRechargePagination.getMenuId(), "data_flow_recharge"));
                if (ObjectUtil.isEmpty(dataFlowRechargeObj)) {
                    return new ArrayList<>();
                } else {
                    dataFlowRechargeQueryWrapper = (QueryWrapper<DataFlowRechargeEntity>) dataFlowRechargeObj;
                    dataFlowRechargeNum++;
                }
            }
        }
        if (StrXhUtil.isNotEmpty(dataFlowRechargePagination.getChargeType())) {
            dataFlowRechargeNum++;
            dataFlowRechargeQueryWrapper.lambda().eq(DataFlowRechargeEntity::getChargeType, dataFlowRechargePagination.getChargeType());
        }
        if (CollUtil.isNotEmpty(dataFlowRechargePagination.getChargeDate())) {
            dataFlowRechargeNum++;
            List<String> ChargeDateList = dataFlowRechargePagination.getChargeDate();
            Long fir = Long.valueOf(ChargeDateList.get(0));
            Long sec = Long.valueOf(ChargeDateList.get(1));
            dataFlowRechargeQueryWrapper.lambda().ge(DataFlowRechargeEntity::getChargeDate, new Date(fir))
                    .le(DataFlowRechargeEntity::getChargeDate, DateXhUtil.endOfDay(sec));
        }
        if (StrXhUtil.isNotEmpty(dataFlowRechargePagination.getNote())) {
            dataFlowRechargeNum++;
            dataFlowRechargeQueryWrapper.lambda().like(DataFlowRechargeEntity::getNote, dataFlowRechargePagination.getNote());
        }
        if (allIdList.size() > 0) {
            dataFlowRechargeQueryWrapper.lambda().in(DataFlowRechargeEntity::getId, allIdList);
        }
        //排序
        if (StrXhUtil.isEmpty(dataFlowRechargePagination.getSidx())) {
            dataFlowRechargeQueryWrapper.lambda().orderByDesc(DataFlowRechargeEntity::getId);
        } else {
            try {
                DataFlowRechargeEntity dataFlowRechargeEntity = new DataFlowRechargeEntity();
                Field declaredField = dataFlowRechargeEntity.getClass().getDeclaredField(dataFlowRechargePagination.getSidx());
                declaredField.setAccessible(true);
                String value = declaredField.getAnnotation(TableField.class).value();
                dataFlowRechargeQueryWrapper = "asc".equals(dataFlowRechargePagination.getSort().toLowerCase()) ? dataFlowRechargeQueryWrapper.orderByAsc(value) : dataFlowRechargeQueryWrapper.orderByDesc(value);
            } catch (NoSuchFieldException e) {
                e.printStackTrace();
            }
        }

            return this.list(dataFlowRechargeQueryWrapper);

    }

    @Override
    public DataFlowRechargeEntity getInfo(String id) {
        QueryWrapper<DataFlowRechargeEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(DataFlowRechargeEntity::getId, id);
        return this.getOne(queryWrapper);
    }

    @Override
    public void create(DataFlowRechargeEntity entity) {
        this.save(entity);
    }

    @Override
    public boolean update(String id, DataFlowRechargeEntity entity) {
        entity.setId(id);
        return this.updateById(entity);
    }

    @Override
    public void delete(DataFlowRechargeEntity entity) {
        if (entity != null) {
            this.removeById(entity.getId());
        }
    }


}
