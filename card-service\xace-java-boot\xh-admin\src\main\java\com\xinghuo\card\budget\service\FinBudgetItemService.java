package com.xinghuo.card.budget.service;

import com.xinghuo.card.budget.entity.FinBudgetItemEntity;
import com.xinghuo.card.budget.model.budgetitem.FinBudgetItemForm;
import com.xinghuo.card.budget.model.budgetitem.FinBudgetItemPagination;
import com.xinghuo.common.base.vo.PageListVO;

import java.util.List;

/**
 * 预算子项管理Service接口
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2024-06-28
 */
public interface FinBudgetItemService {

    /**
     * 分页查询预算子项列表
     *
     * @param finBudgetItemPagination 查询参数
     * @return 分页结果
     */
    PageListVO<FinBudgetItemEntity> getList(FinBudgetItemPagination finBudgetItemPagination);

    /**
     * 获取预算子项详情
     *
     * @param id 预算子项ID
     * @return 预算子项详情
     */
    FinBudgetItemEntity getInfo(String id);

    /**
     * 创建预算子项
     *
     * @param finBudgetItemForm 预算子项表单
     */
    void create(FinBudgetItemForm finBudgetItemForm);

    /**
     * 更新预算子项
     *
     * @param id 预算子项ID
     * @param finBudgetItemForm 预算子项表单
     */
    void update(String id, FinBudgetItemForm finBudgetItemForm);

    /**
     * 删除预算子项
     *
     * @param id 预算子项ID
     */
    void delete(String id);

    /**
     * 根据预算ID获取子项列表
     *
     * @param budgetId 预算ID
     * @return 预算子项列表
     */
    List<FinBudgetItemEntity> getItemsByBudgetId(String budgetId);

    /**
     * 获取需要预警的预算项
     *
     * @param userId 用户ID
     * @return 需要预警的预算项列表
     */
    List<FinBudgetItemEntity> getItemsNeedAlert(String userId);

    /**
     * 重新计算预算项的当前支出
     *
     * @param id 预算项ID
     */
    void recalculateCurrentSpent(String id);

    /**
     * 批量更新预算的所有子项支出
     *
     * @param budgetId 预算ID
     */
    void batchUpdateCurrentSpent(String budgetId);

    /**
     * 检查预算项是否需要预警
     *
     * @param id 预算项ID
     * @return 是否需要预警
     */
    boolean checkNeedAlert(String id);

    /**
     * 批量创建预算子项
     *
     * @param budgetItemForms 预算子项表单列表
     */
    void batchCreate(List<FinBudgetItemForm> budgetItemForms);
}