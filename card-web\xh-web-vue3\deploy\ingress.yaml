apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: xh-web-vue3-ingress
  namespace: xh-staging
  annotations:
    kubernetes.io/ingress.class: nginx
    kubesphere.io/description: xh-web-vue3前端访问
spec:
  rules:
    - host: vue3.devops.xh.team
      http:
        paths:
          - path: /
            pathType: ImplementationSpecific
            backend:
              service:
                name: xh-web-vue3-external
                port:
                  number: 80
