#macro(GetStartAndEndTimeEdit $mastKey,$config,$html,$startTime,$endTime)
    #set($startRelationField="''")
    #if($config.startRelationField)
        //行内编辑的都是取row
        #set($startRelationField="scope.row.${config.startRelationField}")
    #end
    #set($startTimeValue="#if(${config.startTimeValue})'${config.startTimeValue}'#else''#end")
    #set($startTimeType="#if(${config.startTimeType})${config.startTimeType}#else''#end")
    #set($startTimeTarget="#if(${config.startTimeTarget})${config.startTimeTarget}#else''#end")
    #set($endRelationField="''")
    #if($config.endRelationField)
        //行内编辑的都是取row
        #set($endRelationField="scope.row.${config.startRelationField}")
    #end
    #set($endTimeValue="#if(${config.endTimeValue})'${config.endTimeValue}'#else''#end")
    #set($endTimeType="#if(${config.endTimeType})${config.endTimeType}#else''#end")
    #set($endTimeTarget="#if(${config.endTimeTarget})${config.endTimeTarget}#else''#end")

    #set($startTime="dateTime(${config.startTimeRule},${startTimeType},${startTimeTarget},${startTimeValue},${startRelationField})")
    #set($endTime="dateTime(${config.endTimeRule},${endTimeType},${endTimeTarget},${endTimeValue},${endRelationField})")
    #if($mastKey=='time')
        #set($startTime="time(${config.startTimeRule},${startTimeType},${startTimeTarget},${startTimeValue},'${html.format}',${startRelationField})")
        #set($endTime="time(${config.endTimeRule},${endTimeType},${endTimeTarget},${endTimeValue},'${html.format}',${endRelationField})")
    #end
#end
#parse("macro/batchPrint.vm")
<template>
    #set($sign='$')
    #set($doc='.')
    <div class="XH-common-layout">
        #if(${context.columnData.type}==2)
                <div class="XH-common-layout-left">
                    #if(${context.columnData.treeTitle})
                        <div class="XH-common-title">
                            <h2>${context.columnData.treeTitle}</h2>
                        </div>
                    #end
                   <el-tree :data="treeData" :props="treeProps" default-expand-all highlight-current
                             ref="treeBox" :expand-on-click-node="false" @node-click="handleNodeClick"
                             class="XH-common-el-tree" node-key="${context.columnData.treePropsValue}">
        <span class="custom-tree-node" slot-scope="{ node, data }">
          <i :class="data.icon"></i>
          <span class="text">{{node.label}}</span>
        </span>
                    </el-tree>
                </div>
            #end

        <div class="XH-common-layout-center">
            #set($searchSize=$!{context.searchList})
            #if($searchSize.size()>0)
            <el-row class="XH-common-search-box" :gutter="16">
                <el-form @submit.native.prevent>
                    #foreach($se in ${context.searchList})
                        #set($serchFiled=${se.vModel})
                        #if(${foreach.index}<3 && $searchSize.size()>0)
                        <el-col :span="6">
                                <el-form-item label="$se.config.label">
                                    #set($jk=${se.config.xhKey})
                                    #set($name="${se.props.props.label}")
                                    #set($id="${se.props.props.value}")
                                    #if($jk=='checkbox'||$jk=='select'||$jk=='radio')
                                        #set($placeholder = '请选择')
                                        #if($!{se.placeholder})
                                            #set($placeholder = ${se.placeholder})
                                        #end
                                        <el-select v-model="query.${serchFiled}" placeholder="${placeholder}" #if(${se.searchMultiple} =='true') multiple #end
                                                   clearable>
                                            <el-option v-for="(item, index) in ${serchFiled}Options" :key="index"
                                                       :label="item.${name}" :value="item.${id}"
                                                       :disabled="item.disabled"></el-option>
                                        </el-select>
                                    #elseif($jk=='date')
                                        #set($datetypeStr= "datetimerange")
                                        #if($se.type=="year")
                                            #set($datetypeStr= "monthrange")
                                        #else
                                            #set($datetypeStr= "$!{se.type}range")
                                        #end
                                        <el-date-picker v-model="query.${serchFiled}" type="$datetypeStr"
                                                        value-format="$!{se.valueformat}" format="$!{se.format}" start-placeholder="开始日期"
                                                        end-placeholder="结束日期" >
                                        </el-date-picker>
                                    #elseif($jk=='time')
                                        <el-time-picker v-model="query.${serchFiled}" start-placeholder="开始时间" end-placeholder="结束时间"
                                                        clearable value-format="$!{se.valueformat}" format="$!{se.format}" is-range
                                        />
                                    #elseif($jk=='numInput'||$jk=='calculate')
                                        <num-range v-model="query.${serchFiled}"></num-range>
                                    #elseif($jk=='createUser'||$jk=='modifyUser')
                                        <userSelect v-model="query.${serchFiled}" placeholder="请选择" />
                                    #elseif($jk=='currDept')
                                        <depSelect v-model="query.${serchFiled}" placeholder="请选择"  :lastLevel="false"/>
                                    #elseif($jk=='currOrganize')
                                        <comSelect v-model="query.${serchFiled}" placeholder="请选择"  :lastLevel="false" #if(${se.searchMultiple} =='true') multiple #end/>
                                    #elseif($jk=='currPosition')
                                        <posSelect v-model="query.${serchFiled}" placeholder="请选择" #if(${se.searchMultiple} =='true') multiple #end/>
                                    #elseif($jk=='comInput' || $jk=='textarea' || $jk=='XHText' || $jk=='billRule')
                                        <el-input v-model="query.${serchFiled}" placeholder="请输入" clearable>  </el-input>
                                    #elseif($jk=='createTime'||$jk=='modifyTime')
                                        <el-date-picker v-model="query.${serchFiled}" value-format="timestamp" format="yyyy-MM-dd"
                                                        start-placeholder="开始日期" end-placeholder="结束日期"  type="daterange" />
                                    #elseif($jk=='treeSelect'||$jk=='cascader')
                                        <${se.config.tag}  v-model="query.${serchFiled}" placeholder="$!{se.placeholder}" :options ="${serchFiled}Options" :props="${serchFiled}Props"
                                        clearable/>
                                     #elseif($jk=='address')
                                        <XH-Address  v-model="query.${serchFiled}" placeholder="$!{se.placeholder}" :level="$!{se.level}"
                                        clearable/>
                                    #else
                                        <${se.config.tag}
                                        #if($se.selectType == 'custom') selectType="custom" #end
                                        #if($jk == 'userSelect')
                                            #if($se.selectType == 'custom')
                                                :ableDepIds = '$se.ableDepIds'
                                                :ablePosIds = '$se.ablePosIds'
                                                :ableUserIds = '$se.ableUserIds'
                                                :ableRoleIds = '$se.ableRoleIds'
                                                :ableGroupIds = '$se.ableGroupIds'
                                            #end
                                        #end
                                        #if($jk == 'usersSelect')
                                            #if($se.selectType == 'custom')
                                                :ableIds = '$se.ableIds'
                                            #end
                                        #end
                                        #if($jk == 'posSelect')
                                            #if($se.selectType == 'custom')
                                                :ableDepIds = '$se.ableDepIds'
                                                :ablePosIds = '$se.ablePosIds'
                                            #end
                                        #end
                                        #if($jk == 'depSelect')
                                            #if($se.selectType == 'custom')
                                                :ableDepIds = '$se.ableDepIds'
                                            #end
                                        #end
                                        v-model="query.${serchFiled}" placeholder="$!{se.placeholder}
                                        " clearable #if(${se.searchMultiple} =='true') multiple #end />
                                    #end
                                </el-form-item>
                            </el-col>
                        #end
                    #end
                    #if($searchSize.size()>3)
                        <template v-if="showAll">
                            #foreach($se in ${context.searchList})
                                #set($serchFiled=${se.vModel})
                                #if(${foreach.index}>=3)
                                    <el-col :span="6">
                                        <el-form-item label="$se.config.label">
                                            #set($jk=${se.config.xhKey})
                                            #set($name="${se.props.props.label}")
                                            #set($id="${se.props.props.value}")
                                            #if($jk=='checkbox'||$jk=='select'||$jk=='radio')
                                                #set($placeholder = '请选择')
                                                #if($!{se.placeholder})
                                                    #set($placeholder = ${se.placeholder})
                                                #end
                                                <el-select v-model="query.${serchFiled}" placeholder="${placeholder}" #if(${se.searchMultiple} =='true') multiple #end
                                                           clearable>
                                                    <el-option v-for="(item, index) in ${serchFiled}Options" :key="index"
                                                               :label="item.${name}" :value="item.${id}"
                                                               :disabled="item.disabled"></el-option>
                                                </el-select>
                                            #elseif($jk=='date')
                                                #set($datetypeStr= "datetimerange")
                                                #if($se.type=="year")
                                                    #set($datetypeStr= "monthrange")
                                                #else
                                                    #set($datetypeStr= "$!{se.type}range")
                                                #end
                                                <el-date-picker v-model="query.${serchFiled}" type="$datetypeStr"
                                                                value-format="$!{se.valueformat}" format="$!{se.format}" start-placeholder="开始日期"
                                                                end-placeholder="结束日期" >
                                                </el-date-picker>
                                            #elseif($jk=='time')
                                                <el-time-picker v-model="query.${serchFiled}" start-placeholder="开始时间" end-placeholder="结束时间"
                                                                clearable value-format="$!{se.valueformat}" format="$!{se.format}" is-range
                                                />
                                            #elseif($jk=='numInput'||$jk=='calculate')
                                                <num-range v-model="query.${serchFiled}"></num-range>
                                            #elseif($jk=='createUser'||$jk=='modifyUser')
                                                <userSelect v-model="query.${serchFiled}" placeholder="请选择" />
                                            #elseif($jk=='currDept')
                                                <depSelect v-model="query.${serchFiled}" placeholder="请选择"  :lastLevel="false"/>
                                            #elseif($jk=='currOrganize')
                                                <comSelect v-model="query.${serchFiled}" placeholder="请选择"  :lastLevel="false" #if(${se.searchMultiple} =='true') multiple #end/>
                                            #elseif($jk=='currPosition')
                                                <posSelect v-model="query.${serchFiled}" placeholder="请选择" #if(${se.searchMultiple} =='true') multiple #end/>
                                            #elseif($jk=='comInput' || $jk=='textarea' || $jk=='XHText' || $jk=='billRule')
                                                <el-input v-model="query.${serchFiled}" placeholder="请输入" clearable>  </el-input>
                                            #elseif($jk=='createTime'||$jk=='modifyTime')
                                                <el-date-picker v-model="query.${serchFiled}" value-format="timestamp" format="yyyy-MM-dd"
                                                                start-placeholder="开始日期" end-placeholder="结束日期"  type="daterange" />
                                            #elseif($jk=='treeSelect'||$jk=='cascader')
                                                <${se.config.tag}  v-model="query.${serchFiled}" placeholder="$!{se.placeholder}" :options ="${serchFiled}Options" :props="${serchFiled}Props"
                                                clearable/>
                                            #elseif($jk=='address')
                                                <XH-Address  v-model="query.${serchFiled}" placeholder="$!{se.placeholder}" :level="$!{se.level}"
                                                               clearable/>
                                            #else
                                                <${se.config.tag}
                                                #if($se.selectType == 'custom') selectType="custom" #end
                                                #if($jk == 'userSelect')
                                                    #if($se.selectType == 'custom')
                                                        :ableDepIds = '$se.ableDepIds'
                                                        :ablePosIds = '$se.ablePosIds'
                                                        :ableUserIds = '$se.ableUserIds'
                                                        :ableRoleIds = '$se.ableRoleIds'
                                                        :ableGroupIds = '$se.ableGroupIds'
                                                    #end
                                                #end
                                                #if($jk == 'usersSelect')
                                                    #if($se.selectType == 'custom')
                                                        :ableIds = '$se.ableIds'
                                                    #end
                                                #end
                                                #if($jk == 'posSelect')
                                                    #if($se.selectType == 'custom')
                                                        :ableDepIds = '$se.ableDepIds'
                                                        :ablePosIds = '$se.ablePosIds'
                                                    #end
                                                #end
                                                #if($jk == 'depSelect')
                                                    #if($se.selectType == 'custom')
                                                        :ableDepIds = '$se.ableDepIds'
                                                    #end
                                                #end
                                                v-model="query.${serchFiled}" placeholder="$!{se.placeholder}" clearable
                                                #if(${se.searchMultiple} =='true') multiple #end
                                                #if($jk == 'autoComplete')
                                                    relationField="${se.relationField}"
                                                    interfaceId="${se.interfaceId}"
                                                    :templateJson="interfaceRes.${serchFiled}"
                                                    :total="${se.total}"
                                                    :formData="query.${serchFiled}"
                                                #end/>
                                            #end
                                        </el-form-item>
                                    </el-col>
                                #end
                            #end
                        </template>
                    #end
                    <el-col :span="6">
                        <el-form-item>
                            <el-button type="primary" icon="el-icon-search" @click="search()">查询</el-button>
                            <el-button icon="el-icon-refresh-right" @click="reset()">重置</el-button>
                            #if($searchSize.size()>3)
                                <el-button type="text" icon="el-icon-arrow-down" @click="showAll=true" v-if="!showAll">
                                    展开
                                </el-button>
                                <el-button type="text" icon="el-icon-arrow-up" @click="showAll=false" v-else>
                                    收起
                                </el-button>
                            #end
                        </el-form-item>
                    </el-col>
                </el-form>
            </el-row>
            #end
            <div class="XH-common-layout-main XH-flex-main">
                <div class="XH-common-head">
                    <div>
                        #set($btsSize=$!{context.columnData.btnsList})
                        #if($btsSize.size()>0)
                            #foreach($bts in ${context.columnData.btnsList})
                                #if(${bts.value}=='add')
                                    <el-button type="primary" icon="${bts.icon}" #if(${context.columnData.useBtnPermission}) v-has="'btn_${bts.value}'" #end @click="addOrUpdateHandle()">${bts.label}
                                    </el-button>
                                #elseif(${bts.value}=='print')
                                    <el-button type="text" icon="${bts.icon}" @click="print()" #if(${context.columnData.useBtnPermission}) v-has="'btn_${bts.value}'" #end>${bts.label}
                                    </el-button>
                                #elseif(${bts.value}=='upload')
                                    <el-button type="text" icon="${bts.icon}" @click="handelUpload()" #if(${context.columnData.useBtnPermission}) v-has="'btn_${bts.value}'" #end>${bts.label}
                                    </el-button>
                                #elseif(${bts.value}=='download')
                                    <el-button type="text" icon="${bts.icon}" @click="exportData()" #if(${context.columnData.useBtnPermission}) v-has="'btn_${bts.value}'" #end>${bts.label}
                                    </el-button>
                                #elseif(${bts.value}=='batchRemove')
                                    <el-button type="text" icon="${bts.icon}" @click="handleBatchRemoveDel()" #if(${context.columnData.useBtnPermission}) v-has="'btn_${bts.value}'" #end>${bts.label}
                                    </el-button>
                                #elseif(${bts.value}=='batchPrint')
                                  <el-button type="text" icon="${bts.icon}" @click="printDialog()" #if(${context.columnData.useBtnPermission}) v-has="'btn_${bts.value}'" #end>${bts.label}
                                  </el-button>
                                #end

                            #end
                        #end
                    </div>
                    <div class="XH-common-head-right">
                      #if($context.superQuery)
                        <el-tooltip content="高级查询" placement="top" v-if="true">
                            <el-link icon="icon-ym icon-ym-filter XH-common-head-icon" :underline="false"
                                     @click="openSuperQuery()" />
                        </el-tooltip>
                        <el-tooltip effect="dark" :content="$t('common.refresh')" placement="top">
                            <el-link icon="icon-ym icon-ym-Refresh XH-common-head-icon" :underline="false"
                                     @click="initData()" />
                        </el-tooltip>
                       #else
                       <el-tooltip effect="dark" content="刷新" placement="top">
                           <el-link icon="icon-ym icon-ym-Refresh XH-common-head-icon" :underline="false"
                                    @click="reset()"/>
                       </el-tooltip>
                      #end
                    </div>
                </div>
                #set($batchRemove=false)
                #set($batchPrint=false)
                #foreach($bts in ${context.columnData.btnsList})
                    #if(${bts.value}=='batchRemove')
                        #set($batchRemove=true)
                    #end
                    #if(${bts.value}=='batchPrint')
                        #set($batchPrint=true)
                    #end
                    #end
                <XH-table v-loading="listLoading" :data="list" @sort-change='sortChange' #if(${batchRemove}==true) has-c @selection-change="handleSelectionChange"#end :hasNO="false"
                    #if(${context.configurationTotal} == true) show-summary :summary-method="getTableSummaries" #end>
                    #set($columnListSize=$!{context.columnDataListFiled})
                    <el-table-column width="50" align="center" label="序号" fixed="left">
                        <template slot-scope="scope">
                            <div class="row-action">
                                <span class="index">{{ scope.$index + 1 }}</span>
                                <i class="ym-custom ym-custom-arrow-expand"
                                   @click="handleRowForm(scope.$index)"></i>
                            </div>
                        </template>
                    </el-table-column>
                    #if($columnListSize.size()>0)
                        #foreach($columnField in ${context.columnDataListFiled})
                            #if($columnField.label)
                            #set($columnFieldProp=${columnField.prop})
                            #if(${columnField.newProp})
                                #set($columnFieldProp = "${columnField.newProp}")
                            #end
                            #set($func ="dynamicText")
                            #set($mul = ${columnField.multiple})
                            #if(${columnField.xhKey} == 'checkbox')
                                #set($mul = true)
                            #end
                            #if(${columnField.xhKey} == 'treeSelect' || ${columnField.xhKey} == 'cascader')
                                #set($func ="dynamicTreeText")
                            #end
                                <el-table-column prop="${columnFieldProp}" label="${columnField.label}" #if(${columnField.width})width="${columnField.width}"#end align="${columnField.align}"
                                    #if(${columnField.fixed} != 'none') fixed="${columnField.fixed}" #end #if(${columnField.sortable}) sortable="custom" #end
                                    #if(${context.columnData.useColumnPermission}) v-if="xh.hasP('${columnField.prop}')" #end
                                >
                                #if(${columnField.xhKey} == 'createUser' || ${columnField.xhKey} == 'createTime' || ${columnField.xhKey} == 'modifyUser' || ${columnField.xhKey} == 'modifyTime' || ${columnField.xhKey} == 'currOrganize'
                                || ${columnField.xhKey} == 'currPosition' || ${columnField.xhKey} == 'billRule')
                                        <template slot-scope="scope">
                                            {{scope.row.${columnFieldProp}_name}}
                                        </template>
                                    </el-table-column>
                                 #elseif(${columnField.xhKey} == 'checkbox' || ${columnField.xhKey}=='select'|| ${columnField.xhKey}=='radio')
                                     #set($propsValue =$columnField.propsModel.value)
                                     #set($propsLabel =$columnField.propsModel.label)
                                     <template slot-scope="scope">
                                        <template v-if="scope.row.rowEdit">
                                         <el-select v-model="scope.row.${columnFieldProp}" placeholder="请选择" :multiple =${mul} #if(${columnField.filterable}) filterable #end
                                             #if($columnField.readonly == true ) readonly #end
                                             #if($columnField.clearable == true ) clearable #else :clearable="false" #end
                                             #if($columnField.disabled == true ) disabled #end
                                                    >
                                             <el-option v-for="(item, index) in ${columnField.vModel}Options" :key="index"
                                                        :label="item.$propsLabel" :value="item.$propsValue"
                                                        :disabled="item.disabled"></el-option>
                                         </el-select>
                                         </template>
                                    <template v-else>
                                         #if(${columnField.dataType}=='static')
                                    {{ scope.row.${columnFieldProp} | ${func}($!{columnField.vModel}Options) }}
                                          #else
                                     {{scope.row.${columnFieldProp}_name}}
                                         #end
                                    </template>
                                     </template>
                                     </el-table-column>
                                #else
                                    <template slot-scope="scope">
                                        <template v-if="scope.row.rowEdit">
##                                            #BuildCondition(${columnField})
                                                #set($html = $columnField)
                                                #set($vModel = "${html.vModel}")
                                                #set($config = $html.config)
                                                #set($mastKey = "${columnField.xhKey}")
                                                #set($show = $config.noShow)
                                                #set($pcshow = $config.pc)
                                                #set($pop = $html.prop)
                                                #if($html.newProp)
                                                    #set($pop = $html.newProp)
                                                #end
                                                #set($startTime=${html.startTime})
                                                #set($endTime=${html.endTime})
                                                #if(${mastKey}=='date'||${mastKey}=='time')
                                                    #GetStartAndEndTimeEdit($mastKey,$config,$html,$startTime,$endTime)
                                                #end
                                                #if($show == false && $pcshow == true && $mastKey != 'relationFormAttr' && $mastKey != 'popupAttr')
                                                    #set($mastModel="scope.row.${vModel}")
                                                    <${config.tag}  #if($pop)  v-model="scope.row.$pop"#end
                                                    #if($mastKey!='XHText')
                                                        #if($html.placeholder) placeholder="${html.placeholder}" #end
                                                    #else
                                                        #if($mastKey == 'relationFormAttr' || $mastKey == 'popupAttr')
                                                            #if($config.defaultValue) defaultValue="${config.defaultValue}"#end
                                                        #else
                                                            #if($config.defaultValue) value="${config.defaultValue}"#end
                                                        #end
                                                    #end
                                                    #if($mastKey== 'cascader')
                                                        #if($html.showAllLevels)
                                                            show-all-levels
                                                        #else
                                                            :show-all-levels="false"
                                                        #end
                                                    #end
                                                    #if($mastKey== 'uploadFz' || $mastKey== 'uploadImg')
                                                        #if(${html.fileSize}) :fileSize="${html.fileSize}" #else :fileSize="null" #end  #end
                                                    #if($html.maxlength) :maxlength="${html.maxlength}" #end
                                                    #if($html.readonly == true ) readonly #end
                                                    #if($html.clearable == true ) clearable #else :clearable="false" #end
                                                    #if($html.prefixicon) prefix-icon='${html.prefixicon}' #end
                                                    #if($html.suffixicon) suffix-icon='${html.suffixicon}' #end
                                                    #if($html.style) :style='${html.style}'#end
                                                    #if($html.showWordLimit == true ) ${html.showWordLimit} #end
                                                    #if($html.size) size="${html.size}" #end
                                                    #if($html.min) :min="${html.min}" #end
                                                    #if($html.max) :max="${html.max}" #end
                                                    #if($mastKey!='textarea')
                                                        #if($html.type) type="${html.type}" #end
                                                        #if($html.autosize) :autosize='${html.autosize}' #end
                                                    #end
                                                    #if($html.step) :step="${html.step}" #end
                                                    #if($html.precision) :precision="${html.precision}" #end
                                                    #if($html.stepstrictly==true) stepstrictly #end
                                                    #if($html.textStyle) :textStyle='${html.textStyle}' #end
                                                    #if($html.disabled == true ) disabled #end
                                                    #if($html.lineHeight) :lineHeight="${html.lineHeight}" #end
                                                    #if($html.fontSize) :fontSize="${html.fontSize}" #end
                                                    #if($html.controlsposition) controls-position='${html.controlsposition}' #end
                                                    #if($html.showChinese) :showChinese="${html.showChinese}" #end
                                                    #if($html.showPassword) show-password #end
                                                    #if($html.filterable==true) filterable #else :filterable="false" #end
                                                    #if($html.multiple) :multiple="${html.multiple}" #end
                                                    #if($html.separator) separator="${html.separator}" #end
                                                    #if($html.isrange==true) is-range #end
                                                    #if($html.rangeseparator) range-separator="${html.rangeseparator}" #end
                                                    #if($html.startplaceholder) start-placeholder="${html.startplaceholder}" #end
                                                    #if($html.endplaceholder) end-placeholder="${html.endplaceholder}" #end
                                                    #if($html.format) format="${html.format}" #end
                                                    #if($html.colorformat) color-format="${html.colorformat}" #end
                                                    #if($html.valueformat) value-format="${html.valueformat}" #end
                                                    #if($html.activetext) active-text="${html.activetext}" #end
                                                    #if($html.inactivetext) inactive-text="${html.inactivetext}" #end
                                                    #if($html.activecolor) active-color="${html.activecolor}" #end
                                                    #if($html.inactivecolor) inactive-color="${html.inactivecolor}" #end
                                                    #if($html.activevalue) :active-value="${html.activevalue}" #end
                                                    #if($html.inactivevalue) :inactive-value="${html.inactivevalue}" #end
                                                    #if($html.pickeroptions) :picker-options='${html.pickeroptions}'#end
                                                    #if($html.showScore == true ) show-score #end
                                                    #if($html.showText == true ) show-text #end
                                                    #if($html.allowhalf == true ) allow-half #end
                                                    #if($html.showAlpha == true ) show-alpha #end
                                                    #if($html.showStops == true ) show-stops #end
                                                    #if($html.range == true ) range #end
                                                    #if($html.showTip == true ) :showTip="${html.showTip}" #end
                                                    #if($html.accept) accept="${html.accept}" #end
                                                    #if($html.sizeUnit) sizeUnit="${html.sizeUnit}" #end
                                                    #if($html.limit) :limit="${html.limit}" #end
                                                    #if($html.pathType) pathType="${html.pathType}" #end
                                                    #if($html.isAccount) :isAccount="${html.isAccount}" #end
                                                    #if($html.folder) folder="${html.folder}" #end
                                                    #if($html.buttonText) buttonText="${html.buttonText}" #end
                                                    #if($html.contentposition) content-position="${html.contentposition}" #end
                                                    #if($!html.level || $html.level=='0') :level=${html.level} #end
                                                    #if($startTime) :startTime="${startTime}" #end
                                                    #if($endTime) :endTime="${endTime}" #end
                                                    #if($mastKey == 'relationFormAttr' || $mastKey == 'popupAttr')
                                                        #if($html.relationField) relationField="${html.relationField}" #end
                                                        #if($html.showField) showField="${html.showField}" #end
                                                        #if($config.isStorage) isStorage=$config.isStorage #end
                                                    #end
                                                    #if($html.selectType) selectType="$html.selectType" #end
                                                    #if($html.selectType == 'custom')
                                                        #if($html.ableDepIds) :ableDepIds = '$html.ableDepIds' #end
                                                        #if($html.ablePosIds) :ablePosIds = '$html.ablePosIds' #end
                                                        #if($html.ableUserIds) :ableUserIds = '$html.ableUserIds' #end
                                                        #if($html.ableRoleIds) :ableRoleIds = '$html.ableRoleIds' #end
                                                        #if($html.ableGroupIds) :ableGroupIds = '$html.ableGroupIds' #end
                                                        #if($html.ableIds) :ableIds = '$html.ableIds' #end
##                                                    #elseif($html.selectType == 'dep' || $html.selectType == 'pos' || $html.selectType == 'role' || $html.selectType == 'group')
##                                                        #if($html.relationField)
##                                                            :ableRelationIds=" Array.isArray(dataForm.${html.relationField}) ? dataForm.${html.relationField} : [dataForm.${html.relationField}]"
##                                                        #end
                                                    #end
                                                    #if($mastKey == 'relationForm') field="${vModel}" modelId ="${html.modelId}" :columnOptions="${vModel}columnOptions" relationField="${html.relationField}" popupWidth="${html.popupWidth}" #if($html.hasPage) hasPage :pageSize="$html.pageSize" #end  #end
                                                    #if($mastKey == 'popupSelect' || $mastKey =='popupTableSelect') field="${vModel}" interfaceId="${html.interfaceId}" :columnOptions="${vModel}columnOptions" propsValue="${html.propsValue}" relationField="${html.relationField}" popupType="${html.popupType}"
                                                        #if(${html.popupTitle}) popupTitle="${html.popupTitle}" #end popupWidth="${html.popupWidth}"
                                                        #if($html.hasPage) hasPage :pageSize="$html.pageSize" #end  #end
                                                    #if($mastKey=='cascader' || $mastKey=='treeSelect') :options="${vModel}Options" :props="${vModel}Props" #end
                                                    #if($mastKey == 'autoComplete')
                                                      relationField="${html.relationField}"
                                                      interfaceId="${html.interfaceId}"
                                                      :templateJson="interfaceRes.${vModel}"
                                                      :total="${html.total}"
                                                      :formData="scope.row"
                                                    #end>
                                                    #if($mastKey=='checkbox'||$mastKey=='radio'||$mastKey=='select')
                                                        #set($name="${config.props.label}")
                                                        #set($id="${config.props.value}")
                                                        #if($mastKey=='select')
                                                            <el-option v-for="(item, index) in ${html.vModel}Options" :key="index" :label="item.${name}" :value="item.${id}" :disabled="item.disabled" #if($config.border == true )border #end></el-option>
                                                        #else
                                                            <el-${mastKey}#if($config.optionType=="button")-button #end v-for="(item, index) in ${html.vModel}Options" :key="index" :label="item.${id}" :disabled="item.disabled" #if($config.border == true )border #end>
                                                                {{item.${name}}}
                                                            </el-${mastKey}#if($config.optionType=="button")-button #end>
                                                        #end
                                                    #end
                                                    #if($mastKey!='checkbox' && $mastKey!='radio' && $mastKey!='select')
                                                        #if($html.slot.prepend)
                                                            <template slot="prepend">${html.slot.prepend}</template>
                                                        #end
                                                        #if($html.slot.append)
                                                            <template slot="append">${html.slot.append}</template>
                                                        #end
                                                    #end
                                                </${config.tag}>
                                                #end
                                            ##行内编辑没有关联属性
                                                #if($mastKey == 'relationFormAttr' || $mastKey == 'popupAttr')
                                                    {{scope.row.${columnFieldProp}_name}}
                                                #end
                                        </template>
                                        <template v-else>
                                            #if(${columnField.xhKey} =='relationForm')
                                              <a  style="color:#1890ff"
                                                  #if(${columnField.newProp})
                                                  v-if = "scope.row.${columnField.columnTableName}"
                                                  #end
                                                  @click="toDetail(scope.row.${columnFieldProp},'${columnField.modelId}')">
                                                {{scope.row.${columnFieldProp}_name}}</a>
                                            #elseif(${columnField.xhKey} =='cascader' || ${columnField.xhKey} =='treeSelect')
                                                #if($columnField.dataType == "static")
                                                    {{scope.row.${columnFieldProp} | ${func}(${vModel}Options) }}
                                                #else
                                                    {{scope.row.${columnFieldProp}_name}}
                                                #end
                                            #else
                                              {{scope.row.${columnFieldProp}_name}}
                                            #end
                                        </template>
                                    </template>
                                    </el-table-column>
                                #end
                            #end
                     #end
                    #end
                    #set($columnBtnsSize=$!{context.columnData.columnBtnsList})
                    #if($columnBtnsSize.size()>0)
                        <el-table-column label="操作" fixed="right"
                                         #if($columnBtnsSize.size()==3)width="150" #elseif($columnBtnsSize.size()==2)
                                         width="100" #else width="50" #end>
                            <template slot-scope="scope">
                                #foreach($columnBtns in ${context.columnData.columnBtnsList})
                                    #if(${columnBtns.value}=='edit')
                                        <template v-if="scope.row.rowEdit">
                                            <el-button size="mini" type="text" @click="saveForRowEdit(scope.row)">保存</el-button>
                                            <el-button size="mini" type="text" class="XH-table-delBtn"
                                                       @click="cancelRowEdit(scope.row,scope.$index)">取消</el-button>
                                        </template>
                                        <template v-else>
                                            <el-button type="text" @click="scope.row.rowEdit=true" #if(${context.columnData.useBtnPermission}) v-has="'btn_${columnBtns.value}'" #end>${columnBtns.label}</el-button>
                                    #elseif(${columnBtns.value}=='remove')
                                            <el-button type="text" class="XH-table-delBtn" #if(${context.columnData.useBtnPermission}) v-has="'btn_${columnBtns.value}'" #end @click="handleDel(scope.row.${context.pKeyName})">${columnBtns.label}
                                            </el-button>
                                    #elseif(${columnBtns.value}=='detail')
                                        <el-button type="text"  #if(${context.columnData.useBtnPermission}) v-has="'btn_${columnBtns.value}'" #end
                                                   @click="goDetail(scope.row.${context.pKeyName})">${columnBtns.label}
                                        </el-button>
                                        </template>
                                    #end
                                #end
                            </template>
                        </el-table-column>
                    #end
                </XH-table>
                #if(${context.columnData.hasPage}==true)
                    <pagination :total="total" :page.sync="listQuery.currentPage" :limit.sync="listQuery.pageSize" @pagination="initData"/>
                #end
            </div>
        </div>
        <extraForm v-show="extraFormVisible" ref="Form" @refreshDataList="refresh" />
        <ExportBox v-if="exportBoxVisible" ref="ExportBox" @download="download"/>
        #handlePrintCom(${batchPrint})
        <ImportBox v-if="uploadBoxVisible" ref="UploadBox" @refresh="initData" />
        <Detail v-if="detailVisible" ref="Detail" @refresh="detailVisible=false"/>
        <ToFormDetail v-if="toFormDetailVisible" ref="toFormDetail" @close="toFormDetailVisible = false" />
#if($context.superQuery)
        <SuperQuery v-if="superQueryVisible" ref="SuperQuery" :columnOptions="superQueryJson"
                @superQuery="superQuery" />
#end
    </div>
</template>

<script>
    import extraForm from './extraForm'
    import request from '@/utils/request'
    import {getDictionaryDataSelector} from '@/api/systemData/dictionary'
    import ExportBox from './ExportBox'
    import {getDataInterfaceRes} from '@/api/systemData/dataInterface'
    import Detail from './Detail'
    import ToFormDetail from '@/views/basic/dynamicModel/list/detail'
    import {getConfigData} from '@/api/onlineDev/visualDev'
    import {mapGetters} from "vuex"
    import {getDefaultCurrentValueUserId, getDefaultCurrentValueUserIdAsync} from '@/api/permission/user'
    import {
        getDefaultCurrentValueDepartmentId,
        getDefaultCurrentValueDepartmentIdAsync
    } from '@/api/permission/organize'
    import {
        getBeforeData,
        getBeforeTime,
        getDateDay,
        getLaterData,
        getLaterTime
    } from '@/components/Generator/utils/index.js'
    import {thousandsFormat} from "@/components/Generator/utils/index"
    import SuperQuery from '@/components/SuperQuery'
    import superQueryJson from './superQueryJson'
    import PrintBrowse from "@/components/PrintBrowse/batch";
    import PrintDialog from "@/components/PrintDialog";
        #if($context.superQuery)
        #end
#if( ${batchPrint}==true)
#end
    export default {
        components: {extraForm,#if( ${batchPrint}==true)PrintBrowse,PrintDialog,#end ExportBox,Detail,ToFormDetail #if($context.superQuery) ,SuperQuery #end},
        data() {
            return {
                #handlePrintData(${batchPrint}, $!{context.printId})
                extraFormVisible:false,
                toFormDetailVisible:false,
                #if($searchSize.size()>3)
                    showAll: false,
                #end
            #if($context.superQuery)
                superQueryVisible: false,
                superQueryJson,
            #end
                uploadBoxVisible: false,
                detailVisible: false,
                query: {
                    #if($searchSize.size()>0)
                        #foreach($keyword in ${context.searchList})
                            #set($serchFiled=${keyword.vModel})
                            #set($maohao=':')
                                ${serchFiled}${maohao}undefined,
                        #end
                    #end
                },
                treeProps: {
                    children: '${context.columnData.treePropsChildren}',
                    label: '${context.columnData.treePropsLabel}',
                    value: '${context.columnData.treePropsValue}'
                },
                list: [],
                listLoading: true,
                #if(${batchRemove}==true) multipleSelection: [],#end
                #if(${context.columnData.hasPage}==true)
                    total: 0,
                #end
                listQuery: {
                    superQueryJson: '',
                    #if(${context.columnData.hasPage}==true)
                        currentPage: 1,
                        pageSize: ${context.columnData.pageSize},
                    #end
                    sort: "${context.columnData.sort}",
                    sidx: "${context.columnData.defaultSidx}",
                },
                formVisible: false,
                exportBoxVisible: false,
                columnList: [
                #foreach($columnField in ${context.columnDataListFiled})
                    #set($columnFieldProp=${columnField.prop})
                    #if(${columnField.newProp})
                        #set($columnFieldProp = ${columnField.newProp})
                    #end
                    #if(${columnField.label})
                        {prop: '${columnFieldProp}', label: '${columnField.label}'},
                    #end
                    #end
                ],
                #if(${context.columnData.type}==2)
                    treeData: [],
                    treeActiveId: '',
                #end
                #foreach($fieLdsModel in ${context.fields})
                    #set($html = $fieLdsModel.formColumnModel.fieLdsModel)
                    #set($vModel = "${html.vModel}")
                    #set($config = $html.config)
                    #set($xhKey = ${config.xhKey})
                    #if(${config.dataType}=='dictionary'||${config.dataType}=='dynamic')
                            ${vModel}Options:[],
                    #elseif(${config.dataType} == "static")
                        #if($html.slot.options)
                                ${vModel}Options:${html.slot.options},
                        #elseif($html.options)
                                ${vModel}Options:${html.options},
                        #end
                    #end

                    #if($xhKey == "relationForm" || $xhKey == "popupSelect" || $xhKey =="popupTableSelect")
                            ${vModel}columnOptions:[#foreach($options in ${html.columnOptions}) {"label":"${options.label}","value":"${options.value}"},#end],
                    #end
                    #if($html.config.props)
                            $!{vModel}Props:{"label":"${html.config.props.label}","value":"${html.config.props.value}"},
                    #elseif($html.props.propsModel)
                        #set($propsModel =${html.props.propsModel})
                        #set($multiple = ${propsModel.multiple})
                            $!{vModel}Props:{"label":"${propsModel.label}","value":"${propsModel.value}" #if($!{multiple}) ,"multiple":$multiple #end #if($!{propsModel.children}),"children":"${propsModel.children}" #end},
                    #elseif($html.props.props)
                        #set($propsModel = ${html.props.props})
                        #set($multiple = ${propsModel.multiple})
                            $!{vModel}Props:{"label":"${propsModel.label}","value":"${propsModel.value}" #if($!{multiple}) ,"multiple":$multiple #end #if($!{propsModel.children}),"children":"${propsModel.children}" #end},
                    #end
                #end
                #foreach($ChildField in ${context.columnChildren})
                    #foreach($FormMastTableModel in ${ChildField.fieLdsModelList})
                        #set($html = ${FormMastTableModel.mastTable.fieLdsModel})
                        #set($ChildVmodel =${FormMastTableModel.vModel})
                        #set($ClDataType = ${html.config.dataType})
                        #set($xhKey = ${html.config.xhKey})
                        #if(${ClDataType}=='dictionary'||${ClDataType}=='dynamic')
                                ${ChildVmodel}Options:[],
                        #elseif(${ClDataType} == "static")
                            #if($html.slot.options)
                                    ${ChildVmodel}Options:${html.slot.options},
                            #elseif($html.options)
                                    ${ChildVmodel}Options:${html.options},
                            #end
                        #end
                        #if(${xhKey} == "relationForm" || ${xhKey} == "popupSelect" || $xhKey =="popupTableSelect")
                                ${ChildVmodel}columnOptions:[#foreach($options in ${html.columnOptions}) {"label":"${options.label}","value":"${options.value}"},#end],
                        #end
                        #if($html.config.props)
                                $!{ChildVmodel}Props:{"label":"${html.config.props.label}","value":"${html.config.props.value}"},
                        #elseif($html.props.propsModel)
                            #set($propsModel =${html.props.propsModel})
                            #set($multiple = ${propsModel.multiple})
                                $!{ChildVmodel}Props:{"label":"${propsModel.label}","value":"${propsModel.value}" #if($!{multiple}) ,"multiple":$multiple #end #if($!{propsModel.children}),"children":"${propsModel.children}" #end},
                        #elseif($html.props.props)
                            #set($propsModel = ${html.props.props})
                            #set($multiple = ${propsModel.multiple})
                                $!{ChildVmodel}Props:{"label":"${propsModel.label}","value":"${propsModel.value}" #if($!{multiple}) ,"multiple":$multiple #end #if($!{propsModel.children}),"children":"${propsModel.children}" #end},
                        #end
                    #end
                #end
                #foreach($child in ${context.children})
                    #set($className = "${child.tableModel}")
                    #foreach($fieLdsModel in ${child.childList})
                        #set($html = $fieLdsModel.fieLdsModel)
                        #set($vModel = "${html.vModel}")
                        #set($config = $html.config)
                        #set($xhkey = $config.xhKey)
                        #if(${config.dataType}=='dictionary'||${config.dataType}=='dynamic')
                                ${className}_${vModel}Options:[],
                        #elseif(${config.dataType} == "static")
                            #if($html.slot.options)
                                    ${className}_${vModel}Options:${html.slot.options},
                            #elseif($html.options)
                                    ${className}_${vModel}Options:${html.options},
                            #end
                        #end
                        #if($xhkey == "relationForm" || $xhkey == "popupSelect" || $xhkey == "popupTableSelect")
                                ${className}_${vModel}columnOptions:[#foreach($options in ${html.columnOptions}) {"label":"${options.label}","value":"${options.value}"},#end],
                        #end
                        #if($html.props)
                            #set($propsModel = ${html.props.props})
                                ${className}_$!{vModel}Props:{"label":"${propsModel.label}","value":"${propsModel.value}" #if($!{propsModel.multiple}) ,"multiple":$propsModel.multiple #end #if($!{propsModel.children}),"children":"${propsModel.children}" #end},
                        #end
                    #end
                #end
                cacheList: [],
              interfaceRes: {
                  #foreach($fieLdsModel in ${context.fields})
                      #set($html = $fieLdsModel.formColumnModel.fieLdsModel)
                      #set($vModel = "${html.vModel}")
                      #set($config = $html.config)
                      #set($mastKey = "${config.xhKey}")
                      #if(${vModel})
                          ${vModel}:#if($!{html.templateJson})${html.templateJson} #else [] #end,
                      #end
                  #end
                  #foreach($MastfieLds in ${context.mastTable})
                      #set($BeforeVmodel = $MastfieLds.formMastTableModel.vModel)
                      #set($tableName = $MastfieLds.formMastTableModel.table)
                      #set($lowTableName = "${tableName.toLowerCase()}")
                      #set($html = $MastfieLds.formMastTableModel.mastTable.fieLdsModel)
                      #set($vModel = "${html.vModel}")
                      #set($config = $html.config)
                      #if( ${vModel})
                          ${BeforeVmodel}:#if($!{html.templateJson})${html.templateJson} #else [] #end,
                      #end
                  #end
                  #foreach($child in ${context.children})
                      #set($className = "${child.className.substring(0,1).toLowerCase()}${child.className.substring(1).toLowerCase()}")
                      #foreach($fieLdsModel in ${child.childList})
                          #set($html = $fieLdsModel.fieLdsModel)
                          #set($vModel = "${html.vModel}")
                          #set($config = $html.config)
                          #if( ${vModel} && $!{html.interfaceId})
                              ${config.parentVModel}_${vModel}:#if($!{config.templateJson})${config.templateJson} #else [] #end,
                          #end
                      #end
                  #end
              },
            }
        },
        computed: {
            ...mapGetters(['userInfo']),
            menuId() {
                return this.$route.meta.modelId || ''
            }
        },
        created() {
            #if(${context.columnData.type}==2)
                this.getTreeView();
            #else
                this.initSearchDataAndListData();
            #end

            #foreach($fieLdsModel in ${context.fields})
                #set($html = $fieLdsModel.formColumnModel.fieLdsModel)
                #set($vModel = "${html.vModel}")
                #set($config = $html.config)
                #if(${config.dataType}=='dictionary')
                    this.get${vModel}Options();
                #elseif(${config.dataType}=='dynamic')
                    this.get${vModel}Options();
                #end
            #end

            #foreach($ChildField in ${context.columnChildren})
                #foreach($FormMastTableModel in ${ChildField.fieLdsModelList})
                    #set($html = ${FormMastTableModel.mastTable.fieLdsModel})
                    #set($ChildVmodel =${FormMastTableModel.vModel})
                    #set($ClDataType = ${html.config.dataType})
                    #set($config = ${html.config})
                    #if(${ClDataType}=='dictionary')
                        this.get${ChildVmodel}Options();
                    #elseif(${ClDataType}=='dynamic')
                        this.get${ChildVmodel}Options();
                    #end
                #end
            #end
        },
        methods: {
            #handlePrint(${batchPrint})
            #if(${context.configurationTotal} == true)
                getTableSummaries(param) {
                    const { columns, data } = param;
                    const sums = [];
                    columns.forEach((column, index) => {
                        if (index === 0) {
                            sums[index] = '合计';
                            return;
                        } else if (${context.fieldsTotal}.includes(column.property)) {
                            const values = data.map(item => {
                                if (column.property.includes('.')) {
                                    const [attr1, attr2] = column.property.split('.')
                                    return Number(item[attr1][attr2])
                                }
                                return Number(item[column.property])
                            });
                            if (!values.every(value => isNaN(value))) {
                                sums[index] = values.reduce((prev, curr) => {
                                    const value = Number(curr);
                                    if (!isNaN(value)) {
                                        return prev + curr;
                                    } else {
                                        return prev;
                                    }
                                }, 0).toFixed(2);
                                if(${context.thousandsField}.includes(column.property)){
                                    sums[index] = thousandsFormat(sums[index]);
                                }
                            } else {
                                sums[index] = '';
                            }
                        }
                    })
                    return sums;
                },
            #end
            toDetail(defaultValue, modelId) {
                if (!defaultValue) return
                getConfigData(modelId).then(res => {
                    if (!res.data || !res.data.formData) return
                    let formData = JSON.parse(res.data.formData)
                    formData.popupType = 'general'
                    this.toFormDetailVisible = true
                    this.$nextTick(() => {
                        this.$refs.toFormDetail.init(formData, modelId, defaultValue)
                    })
                })
            },
            handleRowForm(index) {
                this.extraFormVisible = true
                #set($indexString  ="this.list[index]")
                #set($Restring = '$' +'refs')
                this.$nextTick(() => {
                    this.${Restring}.Form.init(${indexString})
                })
            },
            #foreach($fieLdsModel in ${context.fields})
            #set($html = $fieLdsModel.formColumnModel.fieLdsModel)
            #set($vModel = "${html.vModel}")
            #set($config = $html.config)
            #set($xhKey = ${config.xhKey})
            #if(${config.dataType}=='dictionary')
                get${vModel}Options() {
                    getDictionaryDataSelector('${config.dictionaryType}').then(res => {
                        this.${vModel}Options = res.data.list
                    })
                },
            #elseif(${config.dataType}=='dynamic')
                get${vModel}Options() {
                    getDataInterfaceRes('${config.propsUrl}').then(res => {
                        let data = res.data
                        this.${vModel}Options = data
                    })
                },
            #end
        #end

        #foreach($ChildField in ${context.columnChildren})
            #foreach($FormMastTableModel in ${ChildField.fieLdsModelList})
                #set($html = ${FormMastTableModel.mastTable.fieLdsModel})
                #set($ChildVmodel =${FormMastTableModel.vModel})
                #set($ClDataType = ${html.config.dataType})
                #set($config = ${html.config})
                #if(${ClDataType}=='dictionary')
                    get${ChildVmodel}Options() {
                        getDictionaryDataSelector('${config.dictionaryType}').then(res => {
                            this.${ChildVmodel}Options = res.data.list
                        })
                    },
                #elseif(${ClDataType}=='dynamic')
                    get${ChildVmodel}Options() {
                        getDataInterfaceRes('${config.propsUrl}').then(res => {
                            let data = res.data
                            this.${ChildVmodel}Options = data
                        })
                    },
                #end
            #end
        #end
            goDetail(id){
                this.detailVisible = true
                this.$nextTick(() => {
                    this.$refs.Detail.init(id)
                })
            },
            sortChange({column, prop, order}) {
                this.listQuery.sort = order == 'ascending' ? 'asc' : 'desc'
                this.listQuery.sidx = !order ? '' : prop
                this.initData()
            },
            #set($datasourse = ${context.columnData.treeDataSource})
            #if(${context.columnData.type}==2)
                getTreeView() {
                    #if(${datasourse} =='dictionary')
                        getDictionaryDataSelector('${context.columnData.treeDictionary}').then(res => {
                            this.treeData = res.data.list
                            this.initSearchDataAndListData()
                        })
                    #elseif(${datasourse} == 'api')
                        getDataInterfaceRes('${context.columnData.treePropsUrl}').then(res => {
                        let data = res.data
                            this.treeData = data
                            this.initSearchDataAndListData()
                        })
                    #elseif(${datasourse}=='organize')
                        this.${sign}store.dispatch('generator/getDepTree').then(res => {
                            this.treeData = res
                            this.initSearchDataAndListData()
                        })
                    #elseif(${datasourse} == 'department')
                        this.${sign}store.dispatch('generator/getDepTree').then(res => {
                            this.treeData = res
                            this.initSearchDataAndListData()
                        })
                    #end
                },
                getNodePath(node) {
                    let fullPath = []
                    const loop = (node) => {
                        if (node.level) fullPath.unshift(node.data)
                        if (node.parent) loop(node.parent)
                    }
                    loop(node)
                    return fullPath
                },
                handleNodeClick(data,node) {
                    this.treeActiveId = data.${context.columnData.treePropsValue}
                    for(let
                    key in this.query
                )
                    {
                        this.query[key] = undefined
                    }
                    #if(${datasourse}=='organize')
                        const nodePath = this.getNodePath(node)
                        const currValue = nodePath.map(o => o.${context.columnData.treePropsValue})
                        this.query.${context.columnData.treeRelation} = currValue
                     #else
                         this.query.${context.columnData.treeRelation} = data.${context.columnData.treePropsValue}
                    #end
                    this.listQuery = {
                        #if(${context.columnData.hasPage}==true)
                            currentPage: 1,
                            pageSize: ${context.columnData.pageSize},
                        #end
                        sort: "${context.columnData.sort}",
                        sidx: "${context.columnData.defaultSidx}",
                    }
                    this.initData()
                },
            #end
            async initSearchDataAndListData() {
                await this.initSearchData()
                this.initData()
            },
            //初始化查询的默认数据
            async initSearchData() {
                #set($searchDataHavaDateFeild=0)
                #foreach($searchItem in ${context.searchList})
                #set($serchFiled=${searchItem.vModel})
                #set($jk=${searchItem.config.xhKey})
                #set($defaultCurrent=${searchItem.config.defaultCurrent})
                #set($multiple=${searchItem.searchMultiple})
                #set($selectType=${searchItem.selectType})
                #set($ableUserIds=${searchItem.ableUserIds})
                #set($ableDepIds=${searchItem.ableDepIds})
                #set($ableGroupIds=${searchItem.ableGroupIds})
                #set($ableRoleIds=${searchItem.ableRoleIds})
                #set($ablePosIds=${searchItem.ablePosIds})
                #if($jk=='date' && $defaultCurrent == true)
##                #if($searchDataHavaDateFeild==0)
##                let startDateTime = new Date()
##                startDateTime.setHours(0,0,0,0)
##                let endDateTime = new Date()
##                endDateTime.setHours(23,59,59,999)
##                #set($searchDataHavaDateFeild=1)
##                #end
##                this.query.${serchFiled} = [startDateTime.getTime(), endDateTime.getTime()]
                #elseif($jk=='depSelect' && $defaultCurrent == true)
                if(this.userInfo.departmentId != null && this.userInfo.departmentId != '') {
                    #if($selectType=='all')
                    #if($multiple == true)
                    this.query.${serchFiled} = [this.userInfo.departmentId]
                    #else
                    this.query.${serchFiled} = this.userInfo.departmentId
                    #end
                    #else
                    let ableDepIds = ${ableDepIds}
                    if(ableDepIds instanceof Array && ableDepIds.length > 0 && ableDepIds.includes(this.userInfo.departmentId)) {
                        #if($multiple == true)
                        this.query.${serchFiled} = [this.userInfo.departmentId]
                        #else
                        this.query.${serchFiled} = this.userInfo.departmentId
                        #end
                    } else if(ableDepIds instanceof Array && ableDepIds.length > 0) {
                        let res = await getDefaultCurrentValueDepartmentIdAsync({departIds:ableDepIds})
                        if (res.data.departmentId != null && res.data.departmentId != '') {
                            #if($multiple == true)
                            this.query.${serchFiled} = [this.userInfo.departmentId]
                            #else
                            this.query.${serchFiled} = this.userInfo.departmentId
                            #end
                        }
                    } else {}
                #end
                }
                #elseif($jk=='comSelect' && $defaultCurrent == true)
                if(this.userInfo.organizeIdList instanceof Array && this.userInfo.organizeIdList.length > 0) {
                    #if($multiple == true)
                    this.query.${serchFiled} = [this.userInfo.organizeIdList]
                    #else
                    this.query.${serchFiled} = this.userInfo.organizeIdList
                    #end
                }
                #elseif($jk=='userSelect' && $defaultCurrent == true && ($selectType=='all' || $selectType == 'custom'))
                #if($selectType=='all')
                #if($multiple == true)
                this.query.${serchFiled} = [this.userInfo.userId]
                #else
                this.query.${serchFiled} = this.userInfo.userId
                #end
                #elseif($selectType=='custom')
                if(this.userInfo.userId != null && this.userInfo.userId != '') {
                    let ableUserIds = ${ableUserIds}
                    let ableDepIds = ${ableDepIds}
                    let ableGroupIds = ${ableGroupIds}
                    let ableRoleIds = ${ableRoleIds}
                    let ablePosIds = ${ablePosIds}
                    if (ableUserIds instanceof Array && ableUserIds.length > 0 && ableUserIds.includes(this.userInfo.userId)) {
                        #if($multiple == true)
                        this.query.${serchFiled} = [this.userInfo.userId]
                        #else
                        this.query.${serchFiled} = this.userInfo.userId
                        #end
                    } else if((ableUserIds instanceof Array && ableUserIds.length > 0)
                        || (ableDepIds instanceof Array && ableDepIds.length > 0)
                        || (ableGroupIds instanceof Array && ableGroupIds.length > 0)
                        || (ableRoleIds instanceof Array && ableRoleIds.length > 0)
                        || (ablePosIds instanceof Array && ablePosIds.length > 0)) {
                        let res = await getDefaultCurrentValueUserIdAsync({
                            departIds:ableDepIds,
                            groupIds:ableGroupIds,
                            roleIds:ableRoleIds,
                            userIds:ableUserIds,
                            positionIds:ablePosIds
                        })
                        if (res.data.userId != null && res.data.userId != '') {
                            #if($multiple == true)
                            this.query.${serchFiled} = [this.userInfo.userId]
                            #else
                            this.query.${serchFiled} = this.userInfo.userId
                            #end
                        }
                    } else {}
                }
                #end
                #end
                #end
            },
            initData() {
                this.listLoading = true;
                let _query = {
                    ...this.listQuery,
                    ...this.query,
                    dataType: ${context.dataType},
                    menuId:this.menuId,
                    moduleId:'${context.moduleId}'
                };
                request({
                    url: `/api/${context.module}/${context.className}/getList`,
                    method: 'post',
                    data: _query
                }).then(res => {
                    var _list =[];
                    for(let i=0;i<res.data.list.length;i++){
                        let _data = this.dataInfo(res.data.list[i]);
                        _list.push(_data)
                    }
                    let list  = _list.map(o => ({
                        ...o,
                        rowEdit: false
                    }))
                    this.list = list
                    this.cacheList = JSON.parse(JSON.stringify(this.list))
                    #if(${context.columnData.hasPage}==true)
                        this.total = res.data.pagination.total
                    #end

                    this.listLoading = false
                })
            },
            #set($d='${id}')
            handleDel(id) {
                this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
                    type: 'warning'
                }).then(() => {
                    request({
                        url: `/api/${context.module}/${context.className}/${d}`,
                        method: 'DELETE'
                    }).then(res => {
                        this.$message({
                            type: 'success',
                            message: res.msg,
                            onClose: () => {
                                this.initData()
                            }
                        });
                    })
                }).catch(() => {
                });
            },
            handelUpload(){
                this.uploadBoxVisible = true
                #set($refstring = '$refs')
                this.$nextTick(() => {
                    this.${refstring}.UploadBox.init("","${context.module}/${context.className}")
                })
            },
            #if(${batchRemove}==true)
                handleSelectionChange(val) {
                    const res = val.map(item => item.${context.pKeyName})
                    this.multipleSelection = res
                },
                handleBatchRemoveDel() {
                    if (!this.multipleSelection.length) {
                        this.$message({
                            type: 'error',
                            message: '请选择一条数据',
                            duration: 1500,
                        })
                        return
                    }
                    const ids = this.multipleSelection.join()
                    #set($ids='${ids}')
                    this.$confirm('您确定要删除这些数据吗, 是否继续？', '提示', {
                        type: 'warning'
                    }).then(() => {
                        request({
                            url: `/api/${context.module}/${context.className}/batchRemove`,
                            data: ids,
                            method: 'DELETE'
                        }).then(res => {
                            this.$message({
                                type: 'success',
                                message: res.msg,
                                onClose: () => {
                                    this.initData()
                                }
                            });
                        })
                    }).catch(() => {
                    })
                },
            #end
            addOrUpdateHandle() {
                let item = {
                    rowEdit: true,
                #foreach($fieLdsModel in ${context.fields})
                    #set($html = $fieLdsModel.formColumnModel.fieLdsModel)
                    #set($vModel = "${html.vModel}")
                    #if($vModel !='')
                        #set($config = $html.config)
                        #set($mastKey = "${config.xhKey}")
                        #if($mastKey!='XHText' && $mastKey!='divider')
                            #if($!config.valueType=='String')
                                $!{vModel} : "$!{config.defaultValue}",
                            #elseif($!config.valueType=='undefined')
                                $!{vModel} : '',
                            #else
                                $!{vModel} : $!{config.defaultValue},
                            #end
                        #end
                    #end
                #end
                #foreach($clum in ${context.columnChildren})
                    #set($clumLowName = "${clum.tableName.toLowerCase()}")
                    ${clumLowName}:
                    {
                        #foreach($field in  ${clum.fieLdsModelList})
                            #set($html  = $field.mastTable.fieLdsModel)
                            #set($BeforeVmodel = "${lowTableName}.${vModel}")
                            #set($vModel = "${html.vModel}")
                            #set($mastKey = "$html.config.xhKey")
                            #if($mastKey!='XHText' && $mastKey!='divider' && $!{html.config.defaultValue})
                                #if($!html.config.valueType=='String')
                                        ${vModel} : "$!{html.config.defaultValue}",
                                #elseif($!html.config.valueType=='undefined')
                                        ${vModel} : '',
                                #else
                                        ${vModel} : $!{html.config.defaultValue},
                                #end
                            #elseif(${mastKey}=='checkbox' || ${mastKey}=='timeRange' || ${mastKey}=='dateRange' || ${mastKey}=='address' || ${mastKey}=='cascader')
                                $vModel : [],
                            #elseif(${mastKey}=="uploadFz" || ${mastKey}=="uploadImg")
                                $vModel : [],
                            #elseif(${mastKey}=='select' || ${mastKey}=='userSelect' || ${mastKey}=='depSelect' || ${mastKey}=='posSelect')
                                #if(${html.multiple}=='true')
                                    $vModel : [],
                                #end
                            #elseif(${mastKey}=='comSelect')
                                $vModel : [],
                            #elseif(${mastKey}=='switch'||${mastKey}=='slider')
                                $vModel : [],
                            #else
                                $vModel : '',
                            #end
                        #end
                    },
                #end
                    #if($context.version)
                        version:0,
                    #end
                }
                this.list.unshift(item)
                this.initDefaultData()
            },
            //初始化默认数据
            initDefaultData() {
                #foreach($eachItem in ${context.fields})
                #set($eachfieLdsModel=${eachItem.formColumnModel.fieLdsModel})
                #set($eachFiled=${eachfieLdsModel.vModel})
                #set($jk=${eachfieLdsModel.config.xhKey})
                #set($multiple=${eachfieLdsModel.multiple})
                #set($selectType=${eachfieLdsModel.selectType})
                #set($defaultCurrent=${eachfieLdsModel.config.defaultCurrent})
                #set($ableUserIds=${eachfieLdsModel.ableUserIds})
                #set($ableDepIds=${eachfieLdsModel.ableDepIds})
                #set($ableGroupIds=${eachfieLdsModel.ableGroupIds})
                #set($ableRoleIds=${eachfieLdsModel.ableRoleIds})
                #set($ablePosIds=${eachfieLdsModel.ablePosIds})
                #if($jk=='date' && $defaultCurrent == true)
                    this.list[0].${eachFiled} = new Date().getTime()
                #elseif($jk=='time' && $defaultCurrent == true)
                    this.list[0].${eachFiled} =this.xh.toDate(new Date().getTime(), '${eachfieLdsModel.format}')
                #elseif($jk=='depSelect' && $defaultCurrent == true)
                if(this.userInfo.departmentId != null && this.userInfo.departmentId != '') {
                    #if($selectType=='all')
                    #if($multiple == true)
                    this.list[0].${eachFiled} = [this.userInfo.departmentId]
                    #else
                    this.list[0].${eachFiled} = this.userInfo.departmentId
                    #end
                    #else
                    let ableDepIds = ${ableDepIds}
                    if(ableDepIds instanceof Array && ableDepIds.length > 0 && ableDepIds.includes(this.userInfo.departmentId)) {
                        #if($multiple == true)
                        this.list[0].${eachFiled} = [this.userInfo.departmentId]
                        #else
                        this.list[0].${eachFiled} = this.userInfo.departmentId
                        #end
                    } else if(ableDepIds instanceof Array && ableDepIds.length > 0) {
                        getDefaultCurrentValueDepartmentId({departIds:ableDepIds}).then(res => {
                            if (res.data.departmentId != null && res.data.departmentId != '') {
                                #if($multiple == true)
                                this.list[0].${eachFiled} = [this.userInfo.departmentId]
                                #else
                                this.list[0].${eachFiled} = this.userInfo.departmentId
                                #end
                            }
                        })
                    } else {}
                #end
                }
                #elseif($jk=='comSelect' && $defaultCurrent == true)
                if(this.userInfo.organizeIdList instanceof Array && this.userInfo.organizeIdList.length > 0) {
                    #if($multiple == true)
                    this.list[0].${eachFiled} = [this.userInfo.organizeIdList]
                    #else
                    this.list[0].${eachFiled} = this.userInfo.organizeIdList
                    #end
                }
                #elseif($jk=='userSelect' && $defaultCurrent == true && ($selectType=='all' || $selectType == 'custom'))
                #if($selectType == 'all')
                #if($multiple == true)
                this.list[0].${eachFiled} = [this.userInfo.userId]
                #else
                this.list[0].${eachFiled} = this.userInfo.userId
                #end
                #elseif($selectType=='custom')
                if(this.userInfo.userId != null && this.userInfo.userId != '') {
                    let ableUserIds = ${ableUserIds}
                    let ableDepIds = ${ableDepIds}
                    let ableGroupIds = ${ableGroupIds}
                    let ableRoleIds = ${ableRoleIds}
                    let ablePosIds = ${ablePosIds}
                    if (ableUserIds instanceof Array && ableUserIds.length > 0 && ableUserIds.includes(this.userInfo.userId)) {
                        #if($multiple == true)
                        this.list[0].${eachFiled} = [this.userInfo.userId]
                        #else
                        this.list[0].${eachFiled} = this.userInfo.userId
                        #end
                    } else if((ableUserIds instanceof Array && ableUserIds.length > 0)
                        || (ableDepIds instanceof Array && ableDepIds.length > 0)
                        || (ableGroupIds instanceof Array && ableGroupIds.length > 0)
                        || (ableRoleIds instanceof Array && ableRoleIds.length > 0)
                        || (ablePosIds instanceof Array && ablePosIds.length > 0)) {
                        getDefaultCurrentValueUserId({
                            departIds:ableDepIds,
                            groupIds:ableGroupIds,
                            roleIds:ableRoleIds,
                            userIds:ableUserIds,
                            positionIds:ablePosIds
                        }).then(res => {
                            if (res.data.userId != null && res.data.userId != '') {
                                #if($multiple == true)
                                this.list[0].${eachFiled} = [this.userInfo.userId]
                                #else
                                this.list[0].${eachFiled} = this.userInfo.userId
                                #end
                            }
                        })
                } else {}
            }
            #end
            #end
            #end

            #foreach($eachChildren in ${context.columnChildren})
            #set($tableClumLowName = "${eachChildren.tableName.toLowerCase()}")
            #foreach($eachItem in ${eachChildren.fieLdsModelList})
            #set($eachFiled=${eachItem.mastTable.fieLdsModel.vModel})
            #set($jk=${eachItem.mastTable.fieLdsModel.config.xhKey})
            #set($multiple=${eachItem.mastTable.fieLdsModel.multiple})
            #set($selectType=${eachItem.mastTable.fieLdsModel.selectType})
            #set($defaultCurrent=${eachItem.mastTable.fieLdsModel.config.defaultCurrent})
            #set($ableUserIds=${eachItem.mastTable.fieLdsModel.ableUserIds})
            #set($ableDepIds=${eachItem.mastTable.fieLdsModel.ableDepIds})
            #set($ableGroupIds=${eachItem.mastTable.fieLdsModel.ableGroupIds})
            #set($ableRoleIds=${eachItem.mastTable.fieLdsModel.ableRoleIds})
            #set($ablePosIds=${eachItem.mastTable.fieLdsModel.ablePosIds})
            #if($jk=='date' && $defaultCurrent == true)
            this.list[0].${tableClumLowName}.${eachFiled} = new Date().getTime()
            #elseif($jk=='depSelect' && $defaultCurrent == true)
            if(this.userInfo.departmentId != null && this.userInfo.departmentId != '') {
                #if($selectType=='all')
                #if($multiple == true)
                this.list[0].${tableClumLowName}.${eachFiled} = [this.userInfo.departmentId]
                #else
                this.list[0].${tableClumLowName}.${eachFiled} = this.userInfo.departmentId
                #end
                #else
                let ableDepIds = JSON.parse(${ableDepIds})
                if(ableDepIds instanceof Array && ableDepIds.length > 0 && ableDepIds.includes(this.userInfo.departmentId)) {
                    #if($multiple == true)
                    this.list[0].${tableClumLowName}.${eachFiled} = [this.userInfo.departmentId]
                    #else
                    this.list[0].${tableClumLowName}.${eachFiled} = this.userInfo.departmentId
                    #end
                } else if(ableDepIds instanceof Array && ableDepIds.length > 0) {
                    getDefaultCurrentValueDepartmentId({departIds:ableDepIds}).then(res => {
                        if (res.data.departmentId != null && res.data.departmentId != '') {
                            #if($multiple == true)
                            this.list[0].${tableClumLowName}.${eachFiled} = [this.userInfo.departmentId]
                            #else
                            this.list[0].${tableClumLowName}.${eachFiled} = this.userInfo.departmentId
                            #end
                        }
                    })
                } else { }
            #end
            }
            #elseif($jk=='comSelect' && $defaultCurrent == true)
            if(this.userInfo.organizeIdList instanceof Array && this.userInfo.organizeIdList.length > 0) {
                #if($multiple == true)
                this.list[0].${tableClumLowName}.${eachFiled} = [this.userInfo.organizeIdList]
                #else
                this.list[0].${tableClumLowName}.${eachFiled} = this.userInfo.organizeIdList
                #end
            }
            #elseif($jk=='userSelect' && $defaultCurrent == true && ($selectType=='all' || $selectType == 'custom'))
                #if($selectType == 'all')
                #if($multiple == true)
                this.list[0].${tableClumLowName}.${eachFiled} = [this.userInfo.userId]
                #else
                this.list[0].${tableClumLowName}.${eachFiled} = this.userInfo.userId
                #end
            #elseif($selectType=='custom')
            if(this.userInfo.userId != null && this.userInfo.userId != '') {
                let ableUserIds = JSON.parse(${ableUserIds})
                let ableDepIds = JSON.parse(${ableDepIds})
                let ableGroupIds = JSON.parse(${ableGroupIds})
                let ableRoleIds = JSON.parse(${ableRoleIds})
                let ablePosIds = JSON.parse(${ablePosIds})
                if (ableUserIds instanceof Array && ableUserIds.length > 0 && ableUserIds.includes(this.userInfo.userId)) {
                #if($multiple == true)
                this.list[0].${tableClumLowName}.${eachFiled} = [this.userInfo.userId]
                #else
                this.list[0].${tableClumLowName}.${eachFiled} = this.userInfo.userId
                #end
                } else if((ableUserIds instanceof Array && ableUserIds.length > 0)
                    || (ableDepIds instanceof Array && ableDepIds.length > 0)
                    || (ableGroupIds instanceof Array && ableGroupIds.length > 0)
                    || (ableRoleIds instanceof Array && ableRoleIds.length > 0)
                    || (ablePosIds instanceof Array && ablePosIds.length > 0)) {
                    getDefaultCurrentValueUserId({
                        departIds:ableDepIds,
                        groupIds:ableGroupIds,
                        roleIds:ableRoleIds,
                        userIds:ableUserIds,
                        positionIds:ablePosIds
                    }).then(res => {
                        if (res.data.userId != null && res.data.userId != '') {
                            #if($multiple == true)
                            this.list[0].${tableClumLowName}.${eachFiled} = [this.userInfo.userId]
                            #else
                            this.list[0].${tableClumLowName}.${eachFiled} = this.userInfo.userId
                            #end
                        }
                    })
                } else {}
            }
            #end
            #end
            #end
            #end
            },
            cancelRowEdit(row, index) {
                if (!row.${context.pKeyName}) return this.list.splice(index, 1)
                row.rowEdit = false
                let item = JSON.parse(JSON.stringify(this.cacheList[index]))
                this.$set(this.list, index, item)
            },
            saveForRowEdit(rowEdit) {
                var row =this.dataList(rowEdit)
                if (!row.${context.pKeyName}) {
                    request({
                        url: '/api/${context.module}/${context.className}',
                        method: 'post',
                        data: row
                    }).then((res) => {
                        this.$message({
                            message: res.msg,
                            type: 'success',
                            duration: 1000,
                            onClose: () => {
                                this.initData()
                            }
                        })
                    })
                }else{
                    request({
                        url: '/api/${context.module}/${context.className}/'+row.${context.pKeyName},
                        method: 'PUT',
                        data: row
                    }).then((res) => {
                        this.$message({
                            message: res.msg,
                            type: 'success',
                            duration: 1000,
                            onClose: () => {
                                this.initData()
                            }
                        })
                    })
                }
            },
            exportData() {
                this.exportBoxVisible = true
                this.$nextTick(() => {
                    this.$refs${doc}ExportBox.init(this.columnList)
                })
            },
            download(data) {
                let query = {...data, ...this.listQuery, ...this.query,menuId:this.menuId}
                request({
                    url: `/api/${context.module}/${context.className}/Actions/Export`,
                    method: 'post',
                    data: query
                }).then(res => {
                    if (!res.data.url) return
                    this.xh.downloadFile(res.data.url)
                    this.$refs.ExportBox.visible = false
                    this.exportBoxVisible = false
                })
            },
            search() {
            #if(${context.columnData.hasPage}==true)
                this.listQuery.currentPage=1
                this.listQuery.pageSize=${context.columnData.pageSize}
           #end
                this.listQuery.sort="${context.columnData.sort}"
                this.listQuery.sidx="${context.columnData.defaultSidx}"
                this.initData()
            },
            refresh(isrRefresh) {
                this.formVisible = false
                if (isrRefresh) this.reset()
            },
            reset() {
                for (let key in this.query) {
                    this.query[key] = undefined
                }
                this.search()
            },
            #if($context.superQuery)
                openSuperQuery() {
                    this.superQueryVisible = true
                    this.$nextTick(() => {
                        this.$refs.SuperQuery.init()
                    })
                },
                superQuery(queryJson) {
                    this.listQuery.superQueryJson = queryJson
                    this.listQuery.currentPage = 1
                    this.initData()
                },
            #end
            dataList(fromData){
                var _data = JSON.parse(JSON.stringify(fromData));
                #foreach($fieLdsModel in ${context.fields})
                    #set($html = $fieLdsModel.formColumnModel.fieLdsModel)
                    #set($vModel = "${html.vModel}")
                    #set($config = $html.config)
                    #set($mastKey = "${config.xhKey}")
                    #if($vModel)
                        #StringifytoString($html,"_data.${vModel}","_data.${vModel}","")
                    #end
                #end
                #foreach($MastfieLds in ${context.mastTable})
                    #set($BeforeVmodel = $MastfieLds.formMastTableModel.vModel)
                    #set($tableName = $MastfieLds.formMastTableModel.table)
                    #set($lowTableName = "${tableName.toLowerCase()}")
                    #set($html = $MastfieLds.formMastTableModel.mastTable.fieLdsModel)
                    #set($vModel = "${html.vModel}")
                    #set($config = $html.config)
                    #set($mastKey = "${config.xhKey}")
                    #if(${vModel})
                        #StringifytoString($html,"_data.${BeforeVmodel}","_data.${lowTableName}.${vModel}","true")
                        #StringifytoString($html,"_data.${lowTableName}.${vModel}","_data.${lowTableName}.${vModel}","true")
                    #end
                #end
                return _data;
            },
            dateTime(timeRule, timeType, timeTarget, timeValueData, dataValue) {
                let timeDataValue = null;
                let timeValue = Number(timeValueData)
                if (timeRule) {
                    if (timeType == 1) {
                        timeDataValue = timeValue
                    } else if (timeType == 2) {
                        timeDataValue = dataValue
                    } else if (timeType == 3) {
                        timeDataValue = new Date().getTime()
                    } else if (timeType == 4) {
                        let previousDate = '';
                        if (timeTarget == 1 || timeTarget == 2) {
                            previousDate = getDateDay(timeTarget, timeType, timeValue)
                            timeDataValue = new Date(previousDate).getTime()
                        } else if (timeTarget == 3) {
                            previousDate = getBeforeData(timeValue)
                            timeDataValue = new Date(previousDate).getTime()
                        } else {
                            timeDataValue = getBeforeTime(timeTarget, timeValue).getTime()
                        }
                    } else if (timeType == 5) {
                        let previousDate = '';
                        if (timeTarget == 1 || timeTarget == 2) {
                            previousDate = getDateDay(timeTarget, timeType, timeValue)
                            timeDataValue = new Date(previousDate).getTime()
                        } else if (timeTarget == 3) {
                            previousDate = getLaterData(timeValue)
                            timeDataValue = new Date(previousDate).getTime()
                        } else {
                            timeDataValue = getLaterTime(timeTarget, timeValue).getTime()
                        }
                    }
                }
                return timeDataValue;
            },
            time(timeRule, timeType, timeTarget, timeValue, formatType, dataValue) {
                let format = formatType == 'HH:mm' ? 'HH:mm:00' : formatType
                let timeDataValue = null
                if (timeRule) {
                    if (timeType == 1) {
                        timeDataValue = timeValue || '00:00:00'
                        if (timeDataValue.split(':').length == 3) {
                            timeDataValue = timeDataValue
                        } else {
                            timeDataValue = timeDataValue + ':00'
                        }
                    } else if (timeType == 2) {
                        timeDataValue = dataValue
                    } else if (timeType == 3) {
                        timeDataValue = this.xh.toDate(new Date(), format)
                    } else if (timeType == 4) {
                        let previousDate = '';
                        previousDate = getBeforeTime(timeTarget, timeValue)
                        timeDataValue = this.xh.toDate(previousDate, format)
                    } else if (timeType == 5) {
                        let previousDate = '';
                        previousDate = getLaterTime(timeTarget, timeValue)
                        timeDataValue = this.xh.toDate(previousDate, format)
                    }
                }
                return timeDataValue;
            },
            dataInfo(dataAll){
                let _dataAll =dataAll
                #foreach($fieLdsModel in ${context.fields})
                    #set($html = $fieLdsModel.formColumnModel.fieLdsModel)
                    #set($vModel = "${html.vModel}")
                    #set($config = $html.config)
                    #set($mastKey = "${config.xhKey}")
                    #ParsetoArray($html,"_dataAll.${vModel}","_dataAll.${vModel}","")
                #end
                #foreach($MastfieLds in ${context.mastTable})
                    #set($table =  ${MastfieLds.formMastTableModel.table})
                    #set($lowTableName = "${table.toLowerCase()}")
                    #set($BeforeVmodel = $MastfieLds.formMastTableModel.vModel)
                    #set($html = $MastfieLds.formMastTableModel.mastTable.fieLdsModel)
                    #set($vModel = "${html.vModel}")
                    #set($config = $html.config)
                    #set($mastKey = "${config.xhKey}")
                    #ParsetoArray($html,"_dataAll.${BeforeVmodel}","_dataAll.${lowTableName}.${vModel}","true")
                    #ParsetoArray($html,"_dataAll.${lowTableName}.${vModel}","_dataAll.${lowTableName}.${vModel}","true")
                #end
                return _dataAll
            },
        }
    }
</script>
#macro(ParsetoArray $html,$parm1,$parm2,$isFailure)
    #set($config = $html.config)
    #set($mastKey = "${config.xhKey}")
    #if(${mastKey}=='checkbox' || ${mastKey}=='timeRange' || ${mastKey}=='dateRange' || ${mastKey}=='address' || ${mastKey}=='cascader')
        $parm1 = ${parm2} ?JSON.parse($parm2):[]
    #elseif(${mastKey}=="uploadFz" || ${mastKey}=="uploadImg" || ${mastKey}=='comSelect')
        $parm1 = ${parm2} ?JSON.parse($parm2):[]
    #elseif(${mastKey}=='select' || ${mastKey}=='userSelect'|| ${mastKey}=='depSelect' || ${mastKey}=='posSelect' || ${mastKey}=='treeSelect'|| ${mastKey}=='roleSelect'|| ${mastKey}=='groupSelect'|| ${mastKey}=='popupTableSelect' || ${mastKey}=='usersSelect')
        ##        #if($!{config.defaultValue} == '[]')
        ##            $parm1 = ${parm2} ?JSON.parse($parm2):[]
        ##        #else
        #if(${html.multiple}=='true')
            $parm1 = ${parm2} ?JSON.parse($parm2):[]
        #else
            #if($isFailure)
                $parm1 = $parm2
            #end
        #end
        ##        #end
    #elseif(${mastKey}=="switch" || ${mastKey}=="slider")
        $parm1 = parseInt($parm2)
    #elseif($isFailure)
        $parm1 = $parm2
    #end
#end
#macro(StringifytoString $html,$parm1,$parm2,$isFailure)
    #set($config = $html.config)
    #set($mastKey = "${config.xhKey}")
    #if(${mastKey}=='checkbox' || ${mastKey}=='timeRange' || ${mastKey}=='dateRange' || ${mastKey}=='address' || ${mastKey}=='cascader')
        $parm1 = Array.isArray(${parm2})? JSON.stringify(${parm2}):'[]'
    #elseif(${mastKey}=="uploadFz" || ${mastKey}=="uploadImg"  || ${mastKey}=='comSelect')
        $parm1 = Array.isArray(${parm2})? JSON.stringify(${parm2}):'[]'
    #elseif(${mastKey}=='select' || ${mastKey}=='userSelect'|| ${mastKey}=='depSelect' || ${mastKey}=='posSelect' || ${mastKey}=='treeSelect'|| ${mastKey}=='roleSelect'|| ${mastKey}=='groupSelect'|| ${mastKey}=='popupTableSelect' || ${mastKey}=='usersSelect')
        ##        #if($!{config.defaultValue} == '[]')
        ##            $parm1 = Array.isArray(${parm2})? JSON.stringify(${parm2}):'[]'
        ##        #else
        #if(${html.multiple}=='true')
            $parm1 = Array.isArray(${parm2})? JSON.stringify(${parm2}):'[]'
        #else
            #if($isFailure)
                $parm1 = $parm2
            #end
        #end
        ##        #end
    #elseif(${mastKey}=="switch" || ${mastKey}=="slider")
        $parm1 = parseInt($parm2)
    #elseif($isFailure)
        $parm1 = $parm2
    #end
#end
