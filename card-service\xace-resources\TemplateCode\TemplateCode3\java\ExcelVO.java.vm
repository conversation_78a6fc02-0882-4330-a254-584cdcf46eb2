#set($name = "${context.className.substring(0,1).toLowerCase()}${context.className.substring(1)}")
#set($pKeyName =${context.pKeyName})

#set($peimaryKeyName = "${pKeyName.substring(0,1).toUpperCase()}${pKeyName.substring(1)}")
#set($peimaryKeyname = "${pKeyName.substring(0,1).toLowerCase()}${pKeyName.substring(1)}")

package ${context.package}.model.$!{name.toLowerCase()};

import lombok.Data;
import java.sql.Time;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.alibaba.fastjson.annotation.JSONField;
import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelCollection;
import com.xinghuo.common.annotation.XhField;
import java.math.BigDecimal;
import java.util.List;
/**
 *
 * ${context.genInfo.description}
 * @版本： ${context.genInfo.version}
 * @版权： ${context.genInfo.copyright}
 * @作者： ${context.genInfo.createUser}
 * @日期： ${context.genInfo.createDate}
 */
@Data
public class $!{context.className}ExcelVO{
    #foreach($fieLdsModel in ${context.importFields})
        #set($html = $fieLdsModel)
        #set($vModel = "${html.vModel.toLowerCase()}")
        #set($VModel = "${html.vModel}")
        #if($vModel != '')
            #set($config = $html.config)
            #set($xhkey = "${config.xhKey}")
            #set($tableType = $html.tableType)
            #set($fieldName=${config.label})
            #set($relationTable = ${config.tableName})
            #set($tableName = "${config.aliasClassName.substring(0,1).toUpperCase()}${config.aliasClassName.substring(1)}")
                /** ${fieldName} **/
				@XhField(#if(${fieldName}) label = "${fieldName}", #end
                    #if(${xhkey}) xhKey = "${xhkey}", #end
                    #if($fieLdsModel.multiple) multiple =${fieLdsModel.multiple},#end
                    #if($fieLdsModel.showLevel) showLevel = "${fieLdsModel.showLevel}",#end
                    #if($fieLdsModel.level) level = "${fieLdsModel.level}",#end
                    #if($config.rule) rule="${config.rule}", #end
                    #if(${xhkey} == "switch")
                        #if($fieLdsModel.activeTxt) activeTxt="${fieLdsModel.activeTxt}", #end
                        #if($fieLdsModel.inactiveTxt) inactiveTxt="${fieLdsModel.inactiveTxt}", #end
                    #end
                    #if($fieLdsModel.min) min=${fieLdsModel.min}, #end
                    #if($fieLdsModel.max) max=${fieLdsModel.max}, #end
                    #if(${context.importIsUpdate} == true) isUpdate = true, #end
                    #if($config.unique) unique = ${config.unique},#end
                    #if($config.regList) regex = ${config.reg},#end
                    #if($config.relationTable) relationTable = "${config.relationTable}",#end
                    #if($config.tableName) tableName = "${config.tableName}",#end
                    #if($fieLdsModel.format) format = "${fieLdsModel.format}",#end
                    #if($config.dataType) dataType = "${config.dataType}",#end
                    #if($config.propsUrl) propsUrl = "${config.propsUrl}",#end
                    #if($config.dictionaryType) dictionaryType = "${config.dictionaryType}",#end
                    #if($html.props.props.label) dataLabel = "${html.props.props.label}",#end
                    #if($html.props.props.value) dataValue = "${html.props.props.value}",#end
                    #if($config.options) #if(${config.dataType}=="static") options = ${config.options},
                    #end #end
                    #if($fieLdsModel.selectType) selectType = "${fieLdsModel.selectType}",#end
                    #if($fieLdsModel.ableDepIds) ableDepIds = ${fieLdsModel.ableDepIds},#end
                    #if($fieLdsModel.ablePosIds) ablePosIds = ${fieLdsModel.ablePosIds},#end
                    #if($fieLdsModel.ableUserIds) ableUserIds = ${fieLdsModel.ableUserIds},#end
                    #if($fieLdsModel.ableRoleIds) ableRoleIds = ${fieLdsModel.ableRoleIds},#end
                    #if($fieLdsModel.ableGroupIds) ableGroupIds = ${fieLdsModel.ableGroupIds},#end
                    #if($fieLdsModel.ableIds) ableIds = ${fieLdsModel.ableIds},#end
                    vModel = "#if(${VModel})${VModel}#end")
            #if(${tableType} == 1)
                #set($VModel = $html.beforeVmodel)
            #else
                #set($VModel = $html.vModel)
            #end
				@JSONField(name = "${VModel}")
            #if(${tableType} == 2)
                @JsonProperty("${VModel}")
                @ExcelCollection(name="${fieldName}",orderNum = "${fieLdsModel.childrenSize}")
                private List<${tableName}Model> ${config.aliasClassName}List;
            #else
				@Excel(name = "${fieldName}",orderNum = "1", isImportField = "true" #if(${context.nameAgain.contains($VModel)}) ,fixedIndex =${foreach.index} #end)
            #if(${xhkey}!='XHText' && ${xhkey}!='divider')
                #if(${xhkey}=='numInput' || ${xhkey}=='calculate' )
                 #if($!{fieLdsModel.formColumnModel.fieLdsModel.precision})
                private BigDecimal ${VModel};
                    #else
                private Integer ${VModel};
                    #end
                #elseif(${xhkey}=='slider'|| ${xhkey}=='rate')
                private Integer ${VModel};

                #elseif(${xhkey}=='modifyTime' || ${xhkey}=='createTime')
                @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
                private Date ${VModel};

                #elseif(${xhkey}=='date')
                    #set($pattern = "yyyy-MM-dd")
                    #if(${html.type}=='datetime')
                        #set($pattern = "yyyy-MM-dd HH:mm:ss")
                    #end
                @JsonFormat(pattern = "$pattern",timezone = "GMT+8")
                private String ${VModel};

                #else
                private String ${VModel};
                #end
            #end

            #end
        #end
    #end
}
