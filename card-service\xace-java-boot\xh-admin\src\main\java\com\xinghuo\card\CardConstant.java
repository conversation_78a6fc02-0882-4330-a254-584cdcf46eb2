package com.xinghuo.card;

import java.util.HashMap;
import java.util.LinkedHashMap;

public class CardConstant {


    public static final int SYNC_ADD = 1;
    public static final int SYNC_UPDATE = 2;
    public static final int SYNC_DELETED = 3;
    public static final String CARD_BILLTYPE_FIXED = "FIXED";
    public static final String CARD_BILLTYPE_LAST = "LAST";
    public static final String CARD_REPAYTYPE_FIXED = "FIXED";
    public static final String CARD_REPAYTYPE_LATER = "LATER";
    public static final String CARD_BILLDAYTYPE_THIS = "THIS";
    public static final String CARD_BILLDAYTYPE_NEXT = "NEXT";
    public static String DATA_ACC_TYPE = "data_acc_type";
    public static HashMap<String, String> ADDRTYPE_MAP = new LinkedHashMap<String, String>();
    public static HashMap<Integer, String> BANKCREDITTYPE_MAP = new LinkedHashMap<Integer, String>();
    public static HashMap<String, String> BANKTYPE_MAP = new LinkedHashMap<String, String>();
    public static HashMap<String, String> CARDZONE_MAP = new LinkedHashMap<String, String>();
    public static HashMap<String, String> CARDSTATUS_MAP = new LinkedHashMap<String, String>();
    public static HashMap<String, String> CARDBILLTYPE_MAP = new LinkedHashMap<String, String>();
    public static HashMap<String, String> CARDREPAYTYPE_MAP = new LinkedHashMap<String, String>();
    public static HashMap<String, String> CARDBILLDAYTYPE_MAP = new LinkedHashMap<String, String>();


    static {


        CARDBILLTYPE_MAP.put(CARD_BILLTYPE_FIXED, "固定账单日");
        CARDBILLTYPE_MAP.put(CARD_BILLTYPE_LAST, "每月最后一天");

        CARDREPAYTYPE_MAP.put(CARD_REPAYTYPE_FIXED, "固定还款日");
        CARDREPAYTYPE_MAP.put(CARD_REPAYTYPE_LATER, "账单日之后");

        CARDBILLDAYTYPE_MAP.put(CARD_BILLDAYTYPE_THIS, "计入本期");
        CARDBILLDAYTYPE_MAP.put(CARD_BILLDAYTYPE_NEXT, "计入下期");


    }
}
