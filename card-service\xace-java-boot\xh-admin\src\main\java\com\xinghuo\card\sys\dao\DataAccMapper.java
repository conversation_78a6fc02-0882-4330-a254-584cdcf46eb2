package com.xinghuo.card.sys.dao;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xinghuo.card.sys.entity.DataAccEntity;
import org.apache.ibatis.annotations.Update;

/**
 * 个人账号	苏宁，京东 存在子账号表。在电商账号中处理
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2022-12-02
 */
public interface DataAccMapper extends BaseMapper<DataAccEntity> {


    @Update("CALL p1()")
    void updateAccBalance();
}
