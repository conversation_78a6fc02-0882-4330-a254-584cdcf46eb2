package com.xinghuo.card.flow.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xinghuo.card.flow.dao.DataWoolWoolMapper;
import com.xinghuo.card.flow.entity.DataWoolWoolEntity;
import com.xinghuo.card.flow.model.datawoolwool.DataWoolWoolPagination;
import com.xinghuo.card.flow.service.DataWoolWoolService;
import com.xinghuo.common.util.UserProvider;
import com.xinghuo.common.util.core.StrXhUtil;
import com.xinghuo.common.util.extra.ServletUtil;
import com.xinghuo.permission.model.authorize.AuthorizeConditionModel;
import com.xinghuo.permission.service.AuthorizeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;

/**
 * 羊毛记录表
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2022-12-12
 */
@Service
public class DataWoolWoolServiceImpl extends ServiceImpl<DataWoolWoolMapper, DataWoolWoolEntity> implements DataWoolWoolService {

    @Autowired
    private UserProvider userProvider;

    @Autowired
    private AuthorizeService authorizeService;

    @Override
    public List<DataWoolWoolEntity> getList(DataWoolWoolPagination dataWoolWoolPagination) {
        return getListByType(dataWoolWoolPagination, 0);
    }

    @Override
    public List<DataWoolWoolEntity> getTypeList(DataWoolWoolPagination dataWoolWoolPagination, int dataType) {
        return getListByType(dataWoolWoolPagination, dataType);
    }

    private List<DataWoolWoolEntity> getListByType(DataWoolWoolPagination dataWoolWoolPagination, int dataType) {
        List<String> allIdList = new ArrayList();
        int total = 0;
        int dataWoolWoolNum = 0;
        QueryWrapper<DataWoolWoolEntity> dataWoolWoolQueryWrapper = new QueryWrapper<>();
        boolean pcPermission = false;
        boolean appPermission = false;
        boolean isPc = "pc".equals(ServletUtil.getHeader("xh-origin"));
        if (isPc && pcPermission) {
            if (!userProvider.get().getIsAdministrator()) {
                Object dataWoolWoolObj = authorizeService.getCondition(new AuthorizeConditionModel(dataWoolWoolQueryWrapper, dataWoolWoolPagination.getMenuId(), "data_wool_wool"));
                if (ObjectUtil.isEmpty(dataWoolWoolObj)) {
                    return new ArrayList<>();
                } else {
                    dataWoolWoolQueryWrapper = (QueryWrapper<DataWoolWoolEntity>) dataWoolWoolObj;
                    dataWoolWoolNum++;
                }
            }
        }
        if (!isPc && appPermission) {
            if (!userProvider.get().getIsAdministrator()) {
                Object dataWoolWoolObj = authorizeService.getCondition(new AuthorizeConditionModel(dataWoolWoolQueryWrapper, dataWoolWoolPagination.getMenuId(), "data_wool_wool"));
                if (ObjectUtil.isEmpty(dataWoolWoolObj)) {
                    return new ArrayList<>();
                } else {
                    dataWoolWoolQueryWrapper = (QueryWrapper<DataWoolWoolEntity>) dataWoolWoolObj;
                    dataWoolWoolNum++;
                }
            }
        }
        if (StrXhUtil.isNotEmpty(dataWoolWoolPagination.getSrcAccId())) {
            dataWoolWoolNum++;
            dataWoolWoolQueryWrapper.lambda().eq(DataWoolWoolEntity::getSrcAccId, dataWoolWoolPagination.getSrcAccId());
        }
        if (StrXhUtil.isNotEmpty(dataWoolWoolPagination.getAcitivity())) {
            dataWoolWoolNum++;
            dataWoolWoolQueryWrapper.lambda().like(DataWoolWoolEntity::getAcitivity, dataWoolWoolPagination.getAcitivity());
        }
        if (StrXhUtil.isNotEmpty(dataWoolWoolPagination.getGoods())) {
            dataWoolWoolNum++;
            dataWoolWoolQueryWrapper.lambda().like(DataWoolWoolEntity::getGoods, dataWoolWoolPagination.getGoods());
        }
        if (allIdList.size() > 0) {
            dataWoolWoolQueryWrapper.lambda().in(DataWoolWoolEntity::getId, allIdList);
        }
        //排序
        if (StrXhUtil.isEmpty(dataWoolWoolPagination.getSidx())) {
            dataWoolWoolQueryWrapper.lambda().orderByDesc(DataWoolWoolEntity::getId);
        } else {
            try {
                DataWoolWoolEntity dataWoolWoolEntity = new DataWoolWoolEntity();
                Field declaredField = dataWoolWoolEntity.getClass().getDeclaredField(dataWoolWoolPagination.getSidx());
                declaredField.setAccessible(true);
                String value = declaredField.getAnnotation(TableField.class).value();
                dataWoolWoolQueryWrapper = "asc".equals(dataWoolWoolPagination.getSort().toLowerCase()) ? dataWoolWoolQueryWrapper.orderByAsc(value) : dataWoolWoolQueryWrapper.orderByDesc(value);
            } catch (NoSuchFieldException e) {
                e.printStackTrace();
            }
        }

            return this.list(dataWoolWoolQueryWrapper);

    }

    @Override
    public DataWoolWoolEntity getInfo(String id) {
        QueryWrapper<DataWoolWoolEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(DataWoolWoolEntity::getId, id);
        return this.getOne(queryWrapper);
    }

    @Override
    public void create(DataWoolWoolEntity entity) {
        this.save(entity);
    }

    @Override
    public boolean update(String id, DataWoolWoolEntity entity) {
        entity.setId(id);
        return this.updateById(entity);
    }

    @Override
    public void delete(DataWoolWoolEntity entity) {
        if (entity != null) {
            this.removeById(entity.getId());
        }
    }


}
