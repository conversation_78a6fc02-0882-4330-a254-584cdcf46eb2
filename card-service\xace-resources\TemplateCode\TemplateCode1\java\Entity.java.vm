package ${package.Entity};

#if(${entityLombokModel})
import com.baomidou.mybatisplus.annotation.*;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
#end

import java.util.Date;
import java.math.BigDecimal;
/**
 *
 * ${genInfo.description}
 * 版本： ${genInfo.version}
 * 版权： ${genInfo.copyright}
 * 作者： ${genInfo.createUser}
 * 日期： ${genInfo.createDate}
 */
#if(${entityLombokModel})
@Data
#end
@TableName("${table.name}")
public class ${table.entityName}  {

#foreach($field in ${table.fields})
    #set($tableField = "${field.name}")
    #set($Property = "${field.propertyName}")
    #if(${field.keyFlag}=='true')
        #if(${primaryKeyPolicy}!='1')
    @TableId(value="${tableField.toUpperCase()}",type = IdType.AUTO)
        #else
    @TableId("${tableField.toUpperCase()}")
        #end
    #else
    @TableField("${tableField.toUpperCase()}")
    #end
    #if(${field.propertyType} =='LocalDateTime' || ${field.propertyType} =='Date')
    private Date ${Property};
    #elseif(${field.type} =='datetime')
    private Date ${Property};
    #else
    #set($types = "String")
    #if(${concurrencyLock}==true && ${Property}=='version')
    #set($types = "Integer")
    @Version
    #end
    private ${types} ${Property};
    #end

#end

}
