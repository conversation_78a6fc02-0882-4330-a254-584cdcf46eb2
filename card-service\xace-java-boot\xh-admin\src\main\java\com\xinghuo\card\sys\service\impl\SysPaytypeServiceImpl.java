package com.xinghuo.card.sys.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xinghuo.card.sys.dao.SysPaytypeMapper;
import com.xinghuo.card.sys.entity.SysPaytypeEntity;
import com.xinghuo.card.sys.model.syspaytype.DataSysPaytypePagination;
import com.xinghuo.card.sys.service.SysPaytypeService;
import com.xinghuo.common.base.service.impl.ExtendedBaseServiceImpl;
import com.xinghuo.common.util.UserProvider;
import com.xinghuo.common.util.core.StrXhUtil;
import com.xinghuo.common.util.extra.ServletUtil;
import com.xinghuo.permission.model.authorize.AuthorizeConditionModel;
import com.xinghuo.permission.service.AuthorizeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 收支类型管理
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2022-11-14
 */
@Service
public class SysPaytypeServiceImpl extends ExtendedBaseServiceImpl<SysPaytypeMapper, SysPaytypeEntity> implements SysPaytypeService {

    @Autowired
    private UserProvider userProvider;

    @Autowired
    private AuthorizeService authorizeService;

    @Override
    public List<SysPaytypeEntity> getList(DataSysPaytypePagination dataSysPaytypePagination) {
        return getListByType(dataSysPaytypePagination, "0");
    }

    @Override
    public List<SysPaytypeEntity> getTypeList(DataSysPaytypePagination dataSysPaytypePagination, String dataType) {
        return getListByType(dataSysPaytypePagination, dataType);
    }

    private List<SysPaytypeEntity> getListByType(DataSysPaytypePagination dataSysPaytypePagination, String dataType) {
        List<String> allIdList = new ArrayList();
        int total = 0;
        int dataSysPaytypeNum = 0;
        QueryWrapper<SysPaytypeEntity> dataSysPaytypeQueryWrapper = new QueryWrapper<>();
        boolean pcPermission = false;
        boolean appPermission = false;
        boolean isPc = "pc".equals(ServletUtil.getHeader("xh-origin"));
        if (isPc && pcPermission) {
            if (!userProvider.get().getIsAdministrator()) {
                Object dataSysPaytypeObj = authorizeService.getCondition(new AuthorizeConditionModel(dataSysPaytypeQueryWrapper, dataSysPaytypePagination.getMenuId(), "data_sys_paytype"));
                if (ObjectUtil.isEmpty(dataSysPaytypeObj)) {
                    return new ArrayList<>();
                } else {
                    dataSysPaytypeQueryWrapper = (QueryWrapper<SysPaytypeEntity>) dataSysPaytypeObj;
                    dataSysPaytypeNum++;
                }
            }
        }
        if (!isPc && appPermission) {
            if (!userProvider.get().getIsAdministrator()) {
                Object dataSysPaytypeObj = authorizeService.getCondition(new AuthorizeConditionModel(dataSysPaytypeQueryWrapper, dataSysPaytypePagination.getMenuId(), "data_sys_paytype"));
                if (ObjectUtil.isEmpty(dataSysPaytypeObj)) {
                    return new ArrayList<>();
                } else {
                    dataSysPaytypeQueryWrapper = (QueryWrapper<SysPaytypeEntity>) dataSysPaytypeObj;
                    dataSysPaytypeNum++;
                }
            }
        }
        if (StrXhUtil.isNotEmpty(dataSysPaytypePagination.getType())) {
            dataSysPaytypeNum++;
            dataSysPaytypeQueryWrapper.lambda().eq(SysPaytypeEntity::getType, dataSysPaytypePagination.getType());
        }
        if (StrXhUtil.isNotEmpty(dataSysPaytypePagination.getName())) {
            dataSysPaytypeNum++;
            dataSysPaytypeQueryWrapper.lambda().like(SysPaytypeEntity::getName, dataSysPaytypePagination.getName());
        }
        if (allIdList.size() > 0) {
            dataSysPaytypeQueryWrapper.lambda().in(SysPaytypeEntity::getId, allIdList);
        }

        dataSysPaytypeQueryWrapper.lambda().orderByAsc(SysPaytypeEntity::getListOrder);


        return this.list(dataSysPaytypeQueryWrapper);

    }

    @Override
    public SysPaytypeEntity getInfo(String id) {
        QueryWrapper<SysPaytypeEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(SysPaytypeEntity::getId, id);
        return this.getOne(queryWrapper);
    }

    @Override
    public void create(SysPaytypeEntity entity) {
        this.save(entity);
    }

    @Override
    public boolean update(String id, SysPaytypeEntity entity) {
        entity.setId(id);
        return this.updateById(entity);
    }

    @Override
    public void delete(SysPaytypeEntity entity) {
        if (entity != null) {
            this.removeById(entity.getId());
        }
    }


}
