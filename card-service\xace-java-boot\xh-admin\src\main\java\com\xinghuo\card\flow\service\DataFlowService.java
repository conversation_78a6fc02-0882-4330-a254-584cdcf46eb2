package com.xinghuo.card.flow.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xinghuo.card.flow.entity.DataFlowEntity;
import com.xinghuo.card.flow.model.dataflow.DataFlowPagination;

import java.util.List;

/**
 * 流水表
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2022-11-26
 */
public interface DataFlowService extends IService<DataFlowEntity> {

    /**
     * 查询分页数据
     *
     * @param dataFlowPagination 查询对象
     * @param dataType           0:分页 1:不分页
     * @return 查询结果
     */
    List<DataFlowEntity> getList(DataFlowPagination dataFlowPagination);

    /**
     * 查询分页或者不分页列表
     *
     * @param dataFlowPagination 查询对象
     * @param dataType           0:分页 1:不分页
     * @return 查询结果
     */
    List<DataFlowEntity> getTypeList(DataFlowPagination dataFlowPagination, int dataType);

    /**
     * 获取DataFlowEntity详细信息
     *
     * @param id   主键
     * @param 查询结果
     */
    DataFlowEntity getInfo(String id);

    /**
     * 删除
     *
     * @param entity 删除的对象
     */
    void delete(DataFlowEntity entity);

    /**
     * 新增保存
     *
     * @param entity 新增的对象
     */
    void create(DataFlowEntity entity);

    /**
     * 修改保存
     *
     * @param id     主键
     * @param entity 修改的对象
     */
    boolean update(String id, DataFlowEntity entity);

    /**
     * 删除关联的数据
     *
     * @param relatedId
     */
    public void deleteDataFlowByRelatedIds(String relatedId);

    public void deleteDataFlowByRelatedIds(String relatedId, String note);

}
