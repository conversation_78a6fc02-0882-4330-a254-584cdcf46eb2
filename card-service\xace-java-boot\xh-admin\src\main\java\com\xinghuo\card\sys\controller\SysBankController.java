package com.xinghuo.card.sys.controller;

import com.xinghuo.admin.util.GeneraterSwapUtil;
import com.xinghuo.card.sys.entity.SysBankEntity;
import com.xinghuo.card.sys.model.sysbank.SysBankForm;
import com.xinghuo.card.sys.model.sysbank.SysBankPagination;
import com.xinghuo.card.sys.model.sysbank.SysBankVO;
import com.xinghuo.card.sys.service.SysBankService;
import com.xinghuo.common.base.ActionResult;
import com.xinghuo.common.base.UserInfo;
import com.xinghuo.common.base.vo.ListVO;
import com.xinghuo.common.base.vo.PaginationVO;
import com.xinghuo.common.exception.DataException;
import com.xinghuo.common.util.UserProvider;
import com.xinghuo.common.util.core.BeanCopierUtils;
import com.xinghuo.common.util.core.DateXhUtil;
import com.xinghuo.common.util.core.RandomUtil;
import com.xinghuo.common.util.core.StrXhUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 银行信息管理控制器
 * 用于管理银行的基本信息，包括银行名称、图标、网站等
 * 支持银行分类和信用卡账号属性配置
 *
 * <AUTHOR> Assistant
 * @version V1.0.0
 * @date 2024-12-29
 */
@Slf4j
@RestController
@Tag(name = "银行信息管理", description = "银行信息管理")
@RequestMapping("/api/card/sys/bank")
public class SysBankController {

    @Autowired
    private SysBankService sysBankService;

    @Autowired
    private UserProvider userProvider;

    @Autowired
    private GeneraterSwapUtil generaterSwapUtil;

    /**
     * 获取银行列表
     *
     * @param sysBankPagination 分页查询参数
     * @return 银行列表
     */
    @Operation(summary = "获取银行列表")
    @PostMapping("/getList")
    public ActionResult list(@RequestBody SysBankPagination sysBankPagination) throws IOException {
        List<SysBankEntity> data = sysBankService.getList(sysBankPagination);
        List<SysBankVO> listVOs = BeanCopierUtils.copyList(data, SysBankVO.class);

        // 处理数据转换和补充
        for (SysBankVO vo : listVOs) {
            enrichBankVO(vo);
        }

        PaginationVO page = BeanCopierUtils.copy(sysBankPagination, PaginationVO.class);
        return ActionResult.page(listVOs, page);
    }

    /**
     * 获取银行选择器列表
     *
     * @return 银行选择器列表
     */
    @Operation(summary = "获取银行选择器列表")
    @GetMapping("/selector")
    public ActionResult<ListVO<SysBankVO>> selector() {
        List<SysBankEntity> data = sysBankService.getSelectList();
        List<SysBankVO> listVOs = BeanCopierUtils.copyList(data, SysBankVO.class);

        // 处理数据转换
        for (SysBankVO vo : listVOs) {
            enrichBankVO(vo);
        }

        ListVO<SysBankVO> vo = new ListVO<>();
        vo.setList(listVOs);
        return ActionResult.success(vo);
    }

    /**
     * 根据银行类型获取银行列表
     *
     * @param bankType 银行类型
     * @return 银行列表
     */
    @Operation(summary = "根据银行类型获取银行列表")
    @GetMapping("/type/{bankType}")
    public ActionResult<ListVO<SysBankVO>> getListByType(@PathVariable("bankType") String bankType) {
        List<SysBankEntity> data = sysBankService.getListByType(bankType);
        List<SysBankVO> listVOs = BeanCopierUtils.copyList(data, SysBankVO.class);

        // 处理数据转换
        for (SysBankVO vo : listVOs) {
            enrichBankVO(vo);
        }

        ListVO<SysBankVO> vo = new ListVO<>();
        vo.setList(listVOs);
        return ActionResult.success(vo);
    }

    /**
     * 获取银行详情
     *
     * @param id 银行ID
     * @return 银行详情
     */
    @GetMapping("/{id}")
    @Operation(summary = "获取银行详情")
    public ActionResult<SysBankVO> info(@PathVariable("id") String id) {
        SysBankEntity entity = sysBankService.getInfo(id);
        if (entity == null) {
            return ActionResult.fail("银行不存在");
        }

        SysBankVO vo = BeanCopierUtils.copy(entity, SysBankVO.class);
        enrichBankVO(vo);

        return ActionResult.success(vo);
    }

    /**
     * 表单信息(详情页)
     *
     * @param id 银行ID
     * @return 银行详情
     */
    @Operation(summary = "表单信息(详情页)")
    @GetMapping("/detail/{id}")
    public ActionResult<SysBankVO> detailInfo(@PathVariable("id") String id) {
        SysBankEntity entity = sysBankService.getInfo(id);
        if (entity == null) {
            return ActionResult.fail("银行不存在");
        }

        SysBankVO vo = BeanCopierUtils.copy(entity, SysBankVO.class);
        enrichBankVO(vo);

        return ActionResult.success(vo);
    }

    /**
     * 创建银行
     *
     * @param sysBankForm 银行表单
     * @return 操作结果
     */
    @PostMapping
    @Operation(summary = "创建银行")
    public ActionResult create(@RequestBody @Valid SysBankForm sysBankForm) throws DataException {
        String mainId = RandomUtil.snowId();
        UserInfo userInfo = userProvider.get();

        sysBankForm.setCreateBy(userInfo.getUserId());
        sysBankForm.setCreateTime(DateXhUtil.date());

        SysBankEntity entity = BeanCopierUtils.copy(sysBankForm, SysBankEntity.class);
        entity.setId(mainId);

        sysBankService.create(entity);
        return ActionResult.success("创建成功");
    }

    /**
     * 更新银行
     *
     * @param id          银行ID
     * @param sysBankForm 银行表单
     * @return 操作结果
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新银行")
    public ActionResult update(@PathVariable("id") String id, @RequestBody @Valid SysBankForm sysBankForm) throws DataException {
        UserInfo userInfo = userProvider.get();
        SysBankEntity entity = sysBankService.getInfo(id);

        if (entity != null) {
            SysBankEntity updateEntity = BeanCopierUtils.copy(sysBankForm, SysBankEntity.class);


            sysBankService.update(id, updateEntity);
            return ActionResult.success("更新成功");
        } else {
            return ActionResult.fail("更新失败，数据不存在");
        }
    }

    /**
     * 删除银行
     *
     * @param id 银行ID
     * @return 操作结果
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除银行")
    public ActionResult delete(@PathVariable("id") String id) {
        SysBankEntity entity = sysBankService.getInfo(id);
        if (entity != null) {
            sysBankService.delete(entity);
            return ActionResult.success("删除成功");
        } else {
            return ActionResult.fail("删除失败，数据不存在");
        }
    }

    /**
     * 批量删除银行
     *
     * @param idList 银行ID列表
     * @return 操作结果
     */
    @DeleteMapping("/batch")
    @Operation(summary = "批量删除银行")
    public ActionResult batchDelete(@RequestBody @NotEmpty List<String> idList) {
        int count = sysBankService.batchDelete(idList);
        return ActionResult.success("批量删除成功，共删除" + count + "条记录");
    }

    /**
     * 检查银行名称是否唯一
     *
     * @param bankName 银行名称
     * @param id       排除的ID
     * @return 检查结果
     */
    @GetMapping("/checkBankName")
    @Operation(summary = "检查银行名称是否唯一")
    public ActionResult<Boolean> checkBankNameUnique(
            @Parameter(description = "银行名称") @RequestParam String bankName,
            @Parameter(description = "排除的ID") @RequestParam(required = false) String id) {
        boolean isUnique = sysBankService.checkBankNameUnique(bankName, id);
        return ActionResult.success(isUnique);
    }

    /**
     * 检查银行简称是否唯一
     *
     * @param bankKey 银行简称
     * @param id      排除的ID
     * @return 检查结果
     */
    @GetMapping("/checkBankKey")
    @Operation(summary = "检查银行简称是否唯一")
    public ActionResult<Boolean> checkBankKeyUnique(
            @Parameter(description = "银行简称") @RequestParam String bankKey,
            @Parameter(description = "排除的ID") @RequestParam(required = false) String id) {
        boolean isUnique = sysBankService.checkBankKeyUnique(bankKey, id);
        return ActionResult.success(isUnique);
    }

    /**
     * 获取银行统计信息
     *
     * @return 统计信息
     */
    @GetMapping("/statistics")
    @Operation(summary = "获取银行统计信息")
    public ActionResult<Map<String, Object>> getBankStatistics() {
        Map<String, Object> statistics = sysBankService.getBankStatistics();
        return ActionResult.success(statistics);
    }

    /**
     * 获取银行类型分布统计
     *
     * @return 银行类型分布
     */
    @GetMapping("/typeDistribution")
    @Operation(summary = "获取银行类型分布统计")
    public ActionResult<List<Map<String, Object>>> getBankTypeDistribution() {
        List<Map<String, Object>> distribution = sysBankService.getBankTypeDistribution();
        return ActionResult.success(distribution);
    }

    /**
     * 获取信用卡类型分布统计
     *
     * @return 信用卡类型分布
     */
    @GetMapping("/creditTypeDistribution")
    @Operation(summary = "获取信用卡类型分布统计")
    public ActionResult<List<Map<String, Object>>> getCreditTypeDistribution() {
        List<Map<String, Object>> distribution = sysBankService.getCreditTypeDistribution();
        return ActionResult.success(distribution);
    }

    /**
     * 验证银行是否可以删除
     *
     * @param id 银行ID
     * @return 验证结果
     */
    @GetMapping("/validateDelete/{id}")
    @Operation(summary = "验证银行是否可以删除")
    public ActionResult<Map<String, Object>> validateCanDelete(@PathVariable("id") String id) {
        Map<String, Object> result = sysBankService.validateCanDelete(id);
        return ActionResult.success(result);
    }

    /**
     * 批量验证银行是否可以删除
     *
     * @param idList 银行ID列表
     * @return 验证结果
     */
    @PostMapping("/batchValidateDelete")
    @Operation(summary = "批量验证银行是否可以删除")
    public ActionResult<Map<String, Object>> batchValidateCanDelete(@RequestBody List<String> idList) {
        Map<String, Object> result = sysBankService.batchValidateCanDelete(idList);
        return ActionResult.success(result);
    }

    /**
     * 导入银行数据
     *
     * @param file 导入文件
     * @return 导入结果
     */
    @PostMapping("/import")
    @Operation(summary = "导入银行数据")
    public ActionResult<Map<String, Object>> importBankData(@RequestParam("file") MultipartFile file) {
        try {
            // TODO: 解析Excel文件并转换为银行实体列表
            List<SysBankEntity> bankList = new ArrayList<>();

            Map<String, Object> result = sysBankService.importBankData(bankList);
            return ActionResult.success(result);
        } catch (Exception e) {
            log.error("导入银行数据失败", e);
            return ActionResult.fail("导入失败: " + e.getMessage());
        }
    }

    /**
     * 导出银行数据
     *
     * @param sysBankPagination 查询条件
     * @return 导出数据
     */
    @PostMapping("/export")
    @Operation(summary = "导出银行数据")
    public ActionResult<List<Map<String, Object>>> exportBankData(@RequestBody SysBankPagination sysBankPagination) {
        List<Map<String, Object>> data = sysBankService.exportBankData(sysBankPagination);
        return ActionResult.success(data);
    }

    /**
     * 复制银行信息
     *
     * @param id      源银行ID
     * @param newName 新银行名称
     * @return 复制的银行信息
     */
    @PostMapping("/copy/{id}")
    @Operation(summary = "复制银行信息")
    public ActionResult<SysBankVO> copyBank(@PathVariable("id") String id, @RequestParam String newName) {
        try {
            SysBankEntity entity = sysBankService.copyBank(id, newName);
            SysBankVO vo = BeanCopierUtils.copy(entity, SysBankVO.class);
            enrichBankVO(vo);
            return ActionResult.success(vo);
        } catch (Exception e) {
            log.error("复制银行失败", e);
            return ActionResult.fail("复制失败: " + e.getMessage());
        }
    }

    /**
     * 获取银行详细信息（包含关联信息）
     *
     * @param id 银行ID
     * @return 详细信息
     */
    @GetMapping("/detailWithRelated/{id}")
    @Operation(summary = "获取银行详细信息（包含关联信息）")
    public ActionResult<Map<String, Object>> getBankDetailWithRelated(@PathVariable("id") String id) {
        Map<String, Object> result = sysBankService.getBankDetailWithRelated(id);
        if (result == null) {
            return ActionResult.fail("银行不存在");
        }
        return ActionResult.success(result);
    }

    /**
     * 丰富银行VO对象
     * 添加银行类型名称、信用卡账号属性名称、创建人名称等信息
     *
     * @param vo 银行VO对象
     */
    private void enrichBankVO(SysBankVO vo) {
        // 银行类型名称转换
        if (SysBankEntity.BankType.STATE_OWNED.equals(vo.getBankType())) {
            vo.setBankTypeName("国有银行");
        } else if (SysBankEntity.BankType.JOINT_STOCK.equals(vo.getBankType())) {
            vo.setBankTypeName("股份制银行");
        } else if (SysBankEntity.BankType.CITY_COMMERCIAL.equals(vo.getBankType())) {
            vo.setBankTypeName("城市商业银行");
        } else if (SysBankEntity.BankType.RURAL_COMMERCIAL.equals(vo.getBankType())) {
            vo.setBankTypeName("农村商业银行");
        } else if (SysBankEntity.BankType.FOREIGN.equals(vo.getBankType())) {
            vo.setBankTypeName("外资银行");
        } else if (SysBankEntity.BankType.OTHER.equals(vo.getBankType())) {
            vo.setBankTypeName("其他");
        } else {
            vo.setBankTypeName("未知类型");
        }

        // 信用卡账号属性名称转换
        if (SysBankEntity.CreditType.STANDARD.equals(vo.getCreditType())) {
            vo.setCreditTypeName("标准信用卡");
        } else if (SysBankEntity.CreditType.SPECIAL.equals(vo.getCreditType())) {
            vo.setCreditTypeName("特殊信用卡");
        } else {
            vo.setCreditTypeName("未设置");
        }

        // 判断是否有图标和网站
        vo.setHasIcon(StrXhUtil.isNotEmpty(vo.getBankIcon()));
        vo.setHasUrl(StrXhUtil.isNotEmpty(vo.getBankUrl()));

        // TODO: 获取关联数据数量
        // vo.setUserBankCount(getUserBankCount(vo.getId()));
        // vo.setCreditCardCount(getCreditCardCount(vo.getId()));

        // 判断是否可以删除
        Map<String, Object> validateResult = sysBankService.validateCanDelete(vo.getId());
        vo.setCanDelete((Boolean) validateResult.get("canDelete"));
        vo.setDeleteRestrictionReason((String) validateResult.get("reason"));

        // 创建人和更新人名称转换
        vo.setCreateByName(generaterSwapUtil.userSelectValue(vo.getCreateBy()));
        vo.setUpdateByName(generaterSwapUtil.userSelectValue(vo.getUpdateBy()));
    }
}
