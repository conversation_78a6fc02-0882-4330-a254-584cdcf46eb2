package com.xinghuo.card.report.controller;

import com.xinghuo.card.report.model.ReportStatisticsModel;
import com.xinghuo.card.report.service.FlowReportService;
import com.xinghuo.common.base.ActionResult;
import com.xinghuo.common.base.UserInfo;
import com.xinghuo.common.util.UserProvider;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 流水报表统计控制器
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2024-01-01
 */
@Slf4j
@RestController
@Tag(name = "流水报表统计", description = "FlowReport")
@RequestMapping("/api/card/report")
public class FlowReportController {

    @Resource
    private FlowReportService flowReportService;

    @Resource
    private UserProvider userProvider;

    /**
     * 获取统计概要
     */
    @Operation(summary = "获取统计概要")
    @PostMapping("/summary")
    public ActionResult<ReportStatisticsModel.StatisticsSummary> getStatisticsSummary(
            @RequestBody @Valid ReportQueryRequest request) {
        
        UserInfo userInfo = userProvider.get();
        ReportStatisticsModel.StatisticsSummary summary = flowReportService.getStatisticsSummary(
                userInfo.getUserId(), request.getStartDate(), request.getEndDate(),
                request.getAccIds(), request.getFlowTypeIds(), request.getTransType(),
                request.getKeywords(), request.getMinAmount(), request.getMaxAmount());
        
        return ActionResult.success(summary);
    }

    /**
     * 获取收支统计
     */
    @Operation(summary = "获取收支统计")
    @PostMapping("/income-expense")
    public ActionResult<List<ReportStatisticsModel.IncomeExpenseItem>> getIncomeExpenseStatistics(
            @RequestBody @Valid ReportQueryRequest request) {
        
        UserInfo userInfo = userProvider.get();
        String groupBy = request.getGroupBy() != null ? request.getGroupBy() : "month";
        
        List<ReportStatisticsModel.IncomeExpenseItem> list = flowReportService.getIncomeExpenseStatistics(
                userInfo.getUserId(), request.getStartDate(), request.getEndDate(),
                groupBy, request.getAccIds(), request.getFlowTypeIds());
        
        return ActionResult.success(list);
    }

    /**
     * 获取分类统计
     */
    @Operation(summary = "获取分类统计")
    @PostMapping("/category")
    public ActionResult<List<ReportStatisticsModel.CategoryStatItem>> getCategoryStatistics(
            @RequestBody @Valid ReportQueryRequest request) {
        
        UserInfo userInfo = userProvider.get();
        List<ReportStatisticsModel.CategoryStatItem> list = flowReportService.getCategoryStatistics(
                userInfo.getUserId(), request.getStartDate(), request.getEndDate(),
                request.getTransType(), request.getAccIds(), request.getKeywords());
        
        return ActionResult.success(list);
    }

    /**
     * 获取趋势分析
     */
    @Operation(summary = "获取趋势分析")
    @PostMapping("/trend")
    public ActionResult<List<ReportStatisticsModel.TrendAnalysisItem>> getTrendAnalysis(
            @RequestBody @Valid ReportQueryRequest request) {
        
        UserInfo userInfo = userProvider.get();
        String groupBy = request.getGroupBy() != null ? request.getGroupBy() : "month";
        
        List<ReportStatisticsModel.TrendAnalysisItem> list = flowReportService.getTrendAnalysis(
                userInfo.getUserId(), request.getStartDate(), request.getEndDate(),
                groupBy, request.getAccIds(), request.getTransType());
        
        return ActionResult.success(list);
    }

    /**
     * 获取卡片统计
     */
    @Operation(summary = "获取卡片统计")
    @PostMapping("/card")
    public ActionResult<List<ReportStatisticsModel.CardStatItem>> getCardStatistics(
            @RequestBody @Valid ReportQueryRequest request) {
        
        UserInfo userInfo = userProvider.get();
        List<ReportStatisticsModel.CardStatItem> list = flowReportService.getCardStatistics(
                userInfo.getUserId(), request.getStartDate(), request.getEndDate(),
                request.getAccIds());
        
        return ActionResult.success(list);
    }

    /**
     * 获取流水明细
     */
    @Operation(summary = "获取流水明细")
    @PostMapping("/details")
    public ActionResult<List<ReportStatisticsModel.FlowDetailItem>> getFlowDetails(
            @RequestBody @Valid ReportDetailRequest request) {
        
        UserInfo userInfo = userProvider.get();
        int pageNum = request.getPageNum() != null ? request.getPageNum() : 1;
        int pageSize = request.getPageSize() != null ? request.getPageSize() : 20;
        
        List<ReportStatisticsModel.FlowDetailItem> list = flowReportService.getFlowDetails(
                userInfo.getUserId(), request.getStartDate(), request.getEndDate(),
                request.getAccIds(), request.getFlowTypeIds(), request.getTransType(),
                request.getKeywords(), request.getMinAmount(), request.getMaxAmount(),
                pageNum, pageSize);
        
        return ActionResult.success(list);
    }

    /**
     * 获取完整报表数据
     */
    @Operation(summary = "获取完整报表数据")
    @PostMapping("/complete")
    public ActionResult<ReportStatisticsModel> getCompleteReport(
            @RequestBody @Valid ReportCompleteRequest request) {
        
        UserInfo userInfo = userProvider.get();
        Map<String, Object> conditions = request.toConditionMap();
        
        ReportStatisticsModel report = flowReportService.getCompleteReport(
                userInfo.getUserId(), request.getReportType(), conditions);
        
        return ActionResult.success(report);
    }

    /**
     * 获取快速统计数据
     */
    @Operation(summary = "获取快速统计数据")
    @GetMapping("/quick")
    public ActionResult<Map<String, Object>> getQuickStatistics() {
        UserInfo userInfo = userProvider.get();
        Map<String, Object> statistics = flowReportService.getQuickStatistics(userInfo.getUserId());
        return ActionResult.success(statistics);
    }

    /**
     * 获取热门分类排行
     */
    @Operation(summary = "获取热门分类排行")
    @PostMapping("/top-categories")
    public ActionResult<List<ReportStatisticsModel.CategoryStatItem>> getTopCategories(
            @RequestBody @Valid ReportQueryRequest request) {
        
        UserInfo userInfo = userProvider.get();
        int limit = request.getLimit() != null ? request.getLimit() : 10;
        
        List<ReportStatisticsModel.CategoryStatItem> list = flowReportService.getTopCategories(
                userInfo.getUserId(), request.getStartDate(), request.getEndDate(),
                request.getTransType(), limit);
        
        return ActionResult.success(list);
    }

    /**
     * 获取月度对比数据
     */
    @Operation(summary = "获取月度对比数据")
    @PostMapping("/monthly-comparison")
    public ActionResult<List<ReportStatisticsModel.IncomeExpenseItem>> getMonthlyComparison(
            @RequestParam("currentMonth") Date currentMonth,
            @RequestParam(value = "compareMonths", defaultValue = "6") int compareMonths) {
        
        UserInfo userInfo = userProvider.get();
        List<ReportStatisticsModel.IncomeExpenseItem> list = flowReportService.getMonthlyComparison(
                userInfo.getUserId(), currentMonth, compareMonths);
        
        return ActionResult.success(list);
    }
}
