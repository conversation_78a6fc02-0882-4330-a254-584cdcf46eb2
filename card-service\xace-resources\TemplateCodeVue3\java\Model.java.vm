#parse("PublicMacro/ConstantMarco.vm")
#ConstantParams()
#set($moduleName = "${context.genInfo.className.toLowerCase()}")
#if($context.isForm)
    #set($package = "package ${context.package}.${context.isForm}.model.${moduleName};")
#else
    #set($package = "package ${context.package}.model.${moduleName};")
#end
${package}

import lombok.Data;
import java.util.List;
import java.util.Date;
import java.math.BigDecimal;
import com.alibaba.fastjson.annotation.JSONField;
import cn.afterturn.easypoi.excel.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 *
 * ${context.genInfo.description}
 * 版本： ${context.genInfo.version}
 * 版权: ${context.genInfo.copyright}
 * 作者： ${context.genInfo.createUser}
 * 日期： ${context.genInfo.createDate}
 */
@Data
public class ${context.className}Model  {
#foreach($html in ${context.children.childList})
    #set($fieLdsModel = ${html.fieLdsModel})
    #set($config = ${fieLdsModel.config})
    #set($xhkey = ${config.xhKey})
    #set($vModel = "${fieLdsModel.vModel}")
    #set($fieldName=${config.label})
    /** ${fieldName} **/
    #if($fieLdsModel.needImport)
    @Excel(name = "${fieldName}",orderNum = "1",isImportField = "true")
    #end
    #if(${xhkey}=='datePicker')
    @JSONField(name = "${vModel}")
    private Long ${vModel};

    #elseif(${xhkey}=='inputNumber' || ${xhkey}=='calculate')
        #if(${fieLdsModel.precision}==0)
    @JSONField(name = "${vModel}")
    private Integer ${vModel};

        #else
    @JSONField(name = "${vModel}")
    private BigDecimal ${vModel};

        #end
    #elseif(${xhkey}=='slider'|| ${xhkey}=='rate')
    @JSONField(name = "${vModel}")
    private Integer ${vModel};

    #elseif(${xhkey}=='relationFormAttr'|| ${xhkey}=='popupAttr')
        #if(${fieLdsModel.isStorage} == 0)
            #set($vModel ="${fieLdsModel.relationField}_${fieLdsModel.showField}")
        #end
    @JSONField(name = "${vModel}")
    private String ${vModel};

    #elseif(${xhkey}=='relationForm')
    @JSONField(name = "${vModel}")
    private String ${vModel};
    private String ${vModel}_id;

    #elseif(${multipleUnit.contains($xhkey)} || ${UploadFileUnit.contains(${xhkey})})
    @JSONField(name = "${vModel}")
    private Object ${vModel};

    #else
    @JSONField(name = "${vModel}")
    private String ${vModel};
    #end
#end
}
