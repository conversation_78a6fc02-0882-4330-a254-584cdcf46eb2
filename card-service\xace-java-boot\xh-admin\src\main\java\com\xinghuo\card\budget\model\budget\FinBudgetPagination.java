package com.xinghuo.card.budget.model.budget;

import com.xinghuo.common.base.PaginationForm;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 预算分页查询参数
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2024-06-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FinBudgetPagination extends PaginationForm {

    /**
     * 预算名称（模糊查询）
     */
    private String name;

    /**
     * 周期类型：MONTHLY-月度, YEARLY-年度
     */
    private String periodType;

    /**
     * 状态：ACTIVE-激活, ARCHIVED-归档
     */
    private String status;

    /**
     * 开始日期范围-开始
     */
    private Date startDateBegin;

    /**
     * 开始日期范围-结束
     */
    private Date startDateEnd;

    /**
     * 用户ID
     */
    private String userId;
}