import { defHttp } from '/@/utils/http/axios';

enum Api {
  Prefix = '/api/card/flow/flowAcc',
}

// 获取羊毛数据列表（分页）
export function getFlowAccList(data) {
  return defHttp.post({ url: Api.Prefix + '/getList', data });
}
// 创建羊毛
export function createFlowAcc() {
  return defHttp.post({ url: Api.Prefix });
}

export function qFlowAcc(id) {
  return defHttp.get({ url: Api.Prefix + '/Q/' + id });
}
// 修改羊毛
export function updateFlowAcc(data) {
  return defHttp.put({ url: Api.Prefix + '/' + data.id, data });
}
// 获取羊毛
export function getFlowAccInfo(id) {
  return defHttp.get({ url: Api.Prefix + '/' + id });
}
// 删除羊毛
export function delFlowAcc(id) {
  return defHttp.delete({ url: Api.Prefix + '/' + id });
}

// 获取指定账户的最近记录（用于快速复制）
export function getRecentFlowAccList(accId: string, limit: number = 10) {
  return defHttp.post({
    url: Api.Prefix + '/getRecentList',
    data: { accId, limit }
  });
}
