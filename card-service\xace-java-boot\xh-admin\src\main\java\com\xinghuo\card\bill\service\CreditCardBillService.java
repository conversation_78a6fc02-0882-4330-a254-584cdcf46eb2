package com.xinghuo.card.bill.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xinghuo.card.bill.entity.CreditCardBillEntity;
import com.xinghuo.card.bill.entity.CreditCardConfigEntity;

import java.util.Date;
import java.util.List;

/**
 * 信用卡账单服务接口
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2024-01-01
 */
public interface CreditCardBillService extends IService<CreditCardBillEntity> {

    /**
     * 根据信用卡配置自动生成账单
     *
     * @param cardConfig 信用卡配置
     * @param billDate 账单日期
     * @return 生成的账单
     */
    CreditCardBillEntity generateBill(CreditCardConfigEntity cardConfig, Date billDate);

    /**
     * 定时任务：检查并生成到期的账单
     */
    void checkAndGenerateBills();

    /**
     * 计算账单周期内的消费金额
     *
     * @param accId 账户ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 消费金额
     */
    java.math.BigDecimal calculateConsumptionAmount(String accId, Date startDate, Date endDate);

    /**
     * 计算账单周期内的还款金额
     *
     * @param accId 账户ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 还款金额
     */
    java.math.BigDecimal calculatePaymentAmount(String accId, Date startDate, Date endDate);

    /**
     * 获取指定信用卡的最新账单
     *
     * @param cardAccId 信用卡账户ID
     * @return 最新账单
     */
    CreditCardBillEntity getLatestBill(String cardAccId);

    /**
     * 获取指定信用卡的历史账单列表
     *
     * @param cardAccId 信用卡账户ID
     * @param limit 限制数量
     * @return 历史账单列表
     */
    List<CreditCardBillEntity> getHistoryBills(String cardAccId, int limit);

    /**
     * 更新账单还款状态
     *
     * @param billId 账单ID
     * @param paymentAmount 还款金额
     * @return 更新结果
     */
    boolean updatePaymentStatus(String billId, java.math.BigDecimal paymentAmount);

    /**
     * 检查账单是否逾期
     *
     * @param bill 账单信息
     * @return 是否逾期
     */
    boolean isOverdue(CreditCardBillEntity bill);

    /**
     * 获取需要还款提醒的账单列表
     *
     * @return 需要提醒的账单列表
     */
    List<CreditCardBillEntity> getBillsNeedReminder();

    /**
     * 计算账单周期的开始和结束日期
     *
     * @param billDay 账单日
     * @param currentDate 当前日期
     * @return 数组[开始日期, 结束日期]
     */
    Date[] calculateBillPeriod(int billDay, Date currentDate);

    /**
     * 根据账单日计算还款到期日
     *
     * @param billDate 账单日期
     * @param repaymentDay 还款日
     * @return 还款到期日
     */
    Date calculateDueDate(Date billDate, int repaymentDay);
}
