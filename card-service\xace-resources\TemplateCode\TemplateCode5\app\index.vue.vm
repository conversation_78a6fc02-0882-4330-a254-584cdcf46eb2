<template>
    <view class="xh-wrap xh-wrap-form">
        <FlowBox v-if="flowVisible" ref="FlowBox" @close="closeFlow"></FlowBox>
        <u-picker mode="selector" v-model="show" :default-selector="[0]" title="请选择流程" :range="templateList" range-key="fullName" @confirm="confirm"></u-picker>
    </view>
</template>

<script>
    import {FlowJsonList} from '@/api/workFlow/flowEngine'
    import FlowBox from '@/pages/workFlow/flowBefore/index.vue'
    import {getFormById} from '@/api/workFlow/workFlowForm'

    export default {
        components: {
            FlowBox
        },
        data() {
            return {
                menuId: '',
                enCode: '${context.flowEnCode}',
                formId: '${context.flowId}',
                flowId: '',
                flowVisible:false,
                show: false,
                templateList: [],
            }
        },
        onLoad(e) {
            this.menuId = e.menuId
            this.getFormById()
        },
        methods: {
            getFormById() {
                getFormById(this.formId).then(res => {
                    this.flowId = res.data&&res.data.id
                    this.enCode = res.data&&res.data.enCode
                    this.getJsonList()
                })
            },
            getJsonList() {
                FlowJsonList(this.flowId,'1').then(res => {
                    this.templateList = res.data;
                    if (!this.templateList.length) return this.$u.toast('流程不存在')
                    if (this.templateList.length > 1) {
                        this.show = true
                    } else {
                        this.flowId = this.templateList[0].id
                        this.flow()
                    }
                })
            },
            confirm(e) {
                this.flowId = this.templateList[e[0]].id
                this.flow()
            },
            flow() {
                const config = {
                    enCode: this.enCode,
                    flowId: this.flowId,
                    menuId: this.menuId,
                    formType: 1,
                    opType: '-1',
                }
                if (!this.flowId) return this.$message.error("该功能未配置流程不可用!")
                this.flowVisible = true
                this.$nextTick(()=>{
                    this.$refs.FlowBox.handleCodeGeneration(config)
                })
            },
        },
    }
</script>
