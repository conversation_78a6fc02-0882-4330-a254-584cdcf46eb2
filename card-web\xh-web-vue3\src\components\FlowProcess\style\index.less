@prefix-cls: ~'@{namespace}-basic-process';
@line-color: #a9b4cd;

// Mixin flex 垂直居中布局
.flex-center() {
  display: flex;
  flex-wrap: nowrap;
  justify-content: center;
  align-items: center;
}

.@{prefix-cls} {
  display: flex;
  height: 100%;
  .left-container {
    flex-shrink: 0;
    width: 220px;
    margin-right: 10px;
    background-color: @component-background;
    border-radius: 4px;
    height: 100%;
    .left-list {
      padding: 10px;
      height: calc(100% - 40px);
      border-bottom: 1px solid @border-color-base;
      overflow: auto;
      .left-item {
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        border: 1px solid @border-color-base;
        padding: 0 10px;
        border-radius: 4px;
        margin-bottom: 10px;
        .option-drag {
          margin-right: 6px;
          cursor: move;
        }
        .icon {
          cursor: pointer;
          font-size: 20px;
        }
        .name {
          width: 160px;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
          word-break: break-all;
        }
        &.active {
          border: 1px solid @primary-color;
          background: @primary-color;
          .name,
          .icon,
          .option-drag {
            color: #fff;
          }
        }
      }
    }
    .add-btn {
      height: 40px;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
    }
  }
  .center-container {
    flex: 1;
    height: 100%;
    overflow: hidden;
  }
}
.process-flow-container {
  display: inline-block;
  background: @app-main-background;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  text-align: center;
  overflow: auto;
  position: relative;
  padding-top: 1px;
  .scale-slider {
    position: fixed;
    right: 0;
    z-index: 199;
    .num {
      display: inline-block;
      width: 40px;
      text-align: center;
      font-size: 14px;
    }
    .btn {
      display: inline-block;
      padding: 4px;
      color: @text-color-secondary;
      border: 1px solid @border-color-base;
      border-radius: 3px;
      background: @component-background;
      margin-left: 10px;
      margin-right: 10px;
      cursor: pointer;
    }
  }
  .tips {
    position: absolute;
    left: 20px;
    top: 0px;
    z-index: 199;
    text-align: left;
    .tips-item {
      line-height: 20px;
      font-size: 16px;
      display: inline-block;
      margin-right: 15px;
      .icon {
        font-size: 20px;
        margin-right: 5px;
        color: #b6b6b6;
        &.success {
          color: @success-color;
        }
        &.current {
          // color: @primary-color;
          color: #1890ff;
        }
      }
    }
  }
  &.process-flow-container-preview {
    background: @component-background;
    .branch-wrap {
      .branch-box {
        background: @component-background;
        > .col-box {
          &:first-of-type {
            &::before,
            &::after {
              background: @component-background !important;
            }
          }
          &:last-of-type {
            &::before,
            &::after {
              background: @component-background;
            }
          }
        }
      }
    }
    .node-wrap-box.approver::before {
      background: @component-background;
    }
    .flow-path-card {
      .title-input {
        display: none !important;
      }
      &:hover {
        .title-text {
          border-bottom: none !important;
        }
      }
      &.condition:hover,
      &.timer:hover {
        box-shadow: 0 0 0 2px #b6b6b6, 0 0 5px 4px rgb(0 0 0 / 20%);
      }
      &.start-node,
      &.approver,
      &.subFlow {
        &:hover {
          box-shadow: 0 0 0 2px #b6b6b6, 0 0 5px 4px rgb(0 0 0 / 20%);
        }
        .header {
          background-color: #b6b6b6;
        }
      }
      &.state-past {
        .header {
          background-color: #67c23a;
        }
        &:hover {
          box-shadow: 0 0 0 2px #67c23a, 0 0 5px 4px rgb(103 194 58 / 20%);
        }
      }
      &.state-curr {
        .header {
          background-color: #1890ff;
        }
        &:hover {
          box-shadow: 0 0 0 2px #1890ff, 0 0 5px 4px rgb(24 144 255 / 20%);
        }
      }
    }
  }
  .node-wrap-box {
    position: relative;
    .flex-center();

    flex-direction: column;
    &.condition,
    &.branchFlow,
    &.interflow {
      padding: 30px 50px 0;
    }
    &.empty {
      overflow: hidden;
    }
    &.approver::before {
      content: '';
      position: absolute;
      top: -10px;
      left: 50%;
      transform: translateX(-50%);
      width: 0;
      height: 4px;
      border-style: solid;
      border-width: 8px 6px 4px;
      border-color: @line-color transparent transparent;
      background: @app-main-background;
    }
    &.approver.branchFlow::before,
    &.approver.interflow::before {
      top: 20px;
    }
    &.error {
      &.condition .error-tip,
      &.branchFlow .error-tip,
      &.interflow .error-tip {
        right: 0;
      }
      .error-tip {
        right: -40px;
      }
      .flow-path-card {
        border: 1px solid @error-color;
        &:hover {
          border-width: 0;
        }
      }
    }
    .error-tip {
      position: absolute;
      right: 1px;
      top: 15%;
      width: 30px;
      height: 30px;
      color: @error-color;
      cursor: pointer;
      border-radius: 50%;
      border: 1px solid;
      line-height: 30px;
      transition: right 0.5s;
    }
    &.condition .error-tip,
    &.branchFlow .error-tip,
    &.interflow .error-tip {
      right: 60px;
    }
  }
  .end-node {
    font-size: 12px;
    text-align: center;
    .flex-center();

    flex-direction: column;
    &::before {
      content: '';
      width: 10px;
      height: 10px;
      margin: auto;
      border: none;
      margin-bottom: 12px;
      border-radius: 50%;
      background: #a9b4cd;
    }
  }
  .flow-path-card {
    width: 220px;
    min-height: 82px;
    text-align: left;
    cursor: pointer;
    overflow: hidden;
    position: relative;
    box-sizing: border-box;
    box-shadow: 0 0 4px 0 rgba(0, 0, 0, 0.3);
    background: @component-background;
    border-radius: 2px;
    font-size: 12px;
    &:hover {
      box-shadow: 0 0 0 2px #1890ff, 0 0 5px 4px rgba(0, 0, 0, 0.2);
    }
    &.copy {
      .header {
        background-color: #1890ff;
      }
    }
    &.timer {
      .header {
        color: @warning-color;
        border-bottom: 1px solid @border-color-base1;
        .actions {
          color: #606266;
        }
      }
    }
    &.approver,
    &.subFlow {
      &:hover {
        box-shadow: 0 0 0 2px #1890ff, 0 0 5px 4px rgba(0, 0, 0, 0.2);
      }
      .header {
        background-color: #1890ff;
      }
    }
    &.start-node {
      &:hover {
        box-shadow: 0 0 0 2px #576a95, 0 0 5px 4px rgba(0, 0, 0, 0.2);
      }
      .header {
        background-color: #576a95;
      }
    }
    .header {
      padding-left: 10px;
      padding-right: 30px;
      width: 100%;
      height: 30px;
      line-height: 30px;
      color: #fff;
      position: relative;
      box-sizing: border-box;
      .title-box {
        position: relative;
        display: inline-block;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        height: 30px;
      }
      .title-input {
        position: absolute;
        left: 0;
        border: none;
        background: inherit;
        color: inherit;
        opacity: 0;
        margin-top: 7px;
        transition: none;
        &:focus {
          border-radius: 2px;
          font-size: 12px;
          padding-left: 4px;
          width: 97%;
          height: 18px;
          box-sizing: border-box;
          box-shadow: unset;
          background-color: @app-main-background;
          color: @text-color-base;
          opacity: 1;
        }
      }
      > .actions {
        position: absolute;
        right: 0;
        top: 0;
        visibility: hidden;
        height: 30px;
      }
      > .async-state {
        position: absolute;
        right: 20px;
        top: 4px;
      }
    }
    &.timer:hover {
      .actions {
        visibility: visible;
        margin-right: 4px;
      }
    }
    &.subFlow {
      .header {
        .title-box {
          width: 140px !important;
        }
      }
    }
    &:not(.start-node):not(.timer):hover {
      .actions {
        visibility: visible;
        margin-right: 4px;
      }
      .title-text {
        border-bottom: 1px dashed currentColor;
      }
    }
    &.start-node:hover {
      .title-text {
        border-bottom: 1px dashed currentColor;
      }
    }
    .body {
      position: relative;
      padding: 10px;
      box-sizing: border-box;
      .text {
        word-break: break-all;
        margin: 0;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 4;
        -webkit-box-orient: vertical;
      }
    }
    .icon-wrapper {
      position: absolute;
      top: 0;
      height: 100%;
      width: 14px;
      box-sizing: border-box;
      &.left {
        left: 0;
      }
      &.right {
        right: 0;
      }
      > .right-arrow,
      > .left-arrow {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
      }
    }
  }
  .flow-path-card.condition {
    .header {
      line-height: 30px;
      color: inherit;
      border-bottom: 1px solid @border-color-base1;
      .title-box {
        height: auto !important;
      }
      .title-text {
        color: #15bc83;
      }
    }
    .body {
      padding: 10px;
      color: #606266;
    }
    .icon-wrapper {
      &:hover {
        background-color: #f1f1f1;
      }
    }
    .right-arrow,
    .left-arrow {
      visibility: hidden;
    }
    &:hover {
      .right-arrow,
      .left-arrow {
        visibility: visible;
      }
      .priority {
        display: none;
      }
    }
  }
  .col-box:first-of-type > .node-wrap .left {
    display: none;
  }
  .col-box:last-of-type > .node-wrap .right {
    display: none;
  }
  .add-node-btn-box {
    width: 220px;
    height: 100px;
    position: relative;
    padding-top: 30px;
    margin: auto;
    &::before {
      content: '';
      position: absolute;
      inset: 0;
      z-index: -1;
      margin: auto;
      width: 1px;
      height: 100%;
      background-color: @line-color;
    }
    .add-node-btn {
      display: flex;
      justify-content: center;
      .btn {
        width: 32px;
        height: 32px;
        border-radius: 16px;
        cursor: pointer;
        outline: none;
        background-color: #1890ff;
        border-color: transparent;
        transition: transform 0.5s;
        &:hover {
          transform: scale(1.2);
          box-shadow: 0 8px 16px 0 rgba(0, 0, 0, 0.1);
        }
        .icon {
          color: #fff;
        }
      }
    }
  }
  .branch-wrap {
    .branch-box-wrap {
      display: inline-flex;
      flex-direction: column;
      align-items: center;
    }
    .branch-box {
      align-items: stretch;
      border-bottom: 1px solid @line-color;
      border-top: 1px solid @line-color;
      box-sizing: border-box;
      background: @app-main-background;
      > .col-box {
        display: flex;
        flex-direction: column;
        align-items: center;
        position: relative;
        &:first-of-type {
          &::before,
          &::after {
            content: '';
            position: absolute;
            left: 0;
            height: 3px;
            width: calc(50% - 1px);
            background: @app-main-background;
          }
          &::before {
            top: -2px;
          }
          &::after {
            bottom: -2px;
          }
        }
        &:last-of-type {
          &::before,
          &::after {
            content: '';
            position: absolute;
            right: 0;
            height: 3px;
            width: calc(50% - 1px);
            background: @app-main-background;
          }
          &::before {
            top: -2px;
          }
          &::after {
            bottom: -2px;
          }
        }
        .center-line {
          height: 100%;
          width: 1px;
          background: @line-color;
          position: absolute;
        }
      }
      > .btn {
        font-size: 14px;
        z-index: 99;
        cursor: pointer;
        position: absolute;
        top: 0;
        left: 50%;
        outline: none;
        transform: translate(-50%, -50%);
        padding: 9px 16px;
        border: none;
        border-radius: 20px;
        background: @component-background;
        box-shadow: 0 0 10px 0px rgba(0, 0, 0, 0.2);
        transition: transform 0.3s;
        color: #1890ff;
        &:hover {
          transform: scale(1.1) translate(-46%, -50%);
        }
      }
    }
  }
  .node-wrap {
    .relative {
      position: relative;
    }
    .flex {
      display: flex;
    }
    .justify-center {
      justify-content: center;
    }
    .icon {
      vertical-align: middle;
      width: 12px;
      height: 12px;
      font-size: 12px;
    }
    .priority {
      position: absolute;
      right: 0;
      font-size: 12px;
    }
    input::-ms-clear,
    input::-ms-reveal {
      display: none;
    }
  }
}
.add-popover-main {
  .condition-box {
    display: flex;
    justify-content: space-around;
    align-items: center;
    text-align: center;
    padding: 10px 0;
    width: 440px;
    > div:nth-child(1) .iconfont {
      color: @warning-color;
    }
    > div:nth-child(2) .iconfont {
      color: #1890ff;
    }
    .condition-disabled {
      color: #c0c4cc;
      .condition-icon {
        background: #e5e5e5;
        color: #999;
        cursor: default;
        &:hover {
          background: #e5e5e5;
          box-shadow: none;
          > .icon-ym,
          > .ym-custom {
            color: #999;
          }
        }
      }
    }
    .condition-icon {
      width: 60px;
      height: 60px;
      line-height: 60px;
      border: 1px solid #e5e5e5;
      border-radius: 30px;
      box-sizing: border-box;
      font-size: 12px;
      cursor: pointer;
      margin-bottom: 4px;
      .icon-ym,
      .ym-custom {
        font-size: 32px;
      }
      &:hover {
        color: #1890ff;
        background-color: #1890ff;
        box-shadow: 0 0 8px 4px rgba(0, 0, 0, 0.1);
        > .icon-ym,
        > .ym-custom {
          color: #fff;
        }
      }
    }
  }
}
html[data-theme='dark'] {
  .process-flow-container {
    .node-wrap-box.approver::before {
      border-color: @line-color transparent transparent;
    }
    .flow-path-card {
      box-shadow: 0 0 4px 0 rgb(255 255 255 / 30%);
      background: #333;
    }
  }
}
