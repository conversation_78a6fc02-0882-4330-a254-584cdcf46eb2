package com.xinghuo.card.fin.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xinghuo.card.fin.dao.FinDebtMapper;
import com.xinghuo.card.fin.entity.FinDebtEntity;
import com.xinghuo.card.fin.model.finDebt.FinDebtPagination;
import com.xinghuo.card.fin.model.finDebt.FinDebtVO;
import com.xinghuo.card.fin.service.FinDebtService;
import com.xinghuo.common.base.service.impl.ExtendedBaseServiceImpl;
import com.xinghuo.common.util.UserProvider;
import com.xinghuo.common.util.core.StrXhUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

/**
 * 债务管理服务实现类
 * 提供债务的增删改查、统计分析、状态管理等功能
 *
 * <AUTHOR> Assistant
 * @version V1.0.0
 * @date 2024-12-29
 */
@Slf4j
@Service
public class FinDebtServiceImpl extends ExtendedBaseServiceImpl<FinDebtMapper, FinDebtEntity> implements FinDebtService {

    @Resource
    private UserProvider userProvider;

    @Override
    public List<FinDebtEntity> getList(FinDebtPagination pagination) {
        QueryWrapper<FinDebtEntity> queryWrapper = new QueryWrapper<>();
        
        // 添加查询条件
        if (StrXhUtil.isNotEmpty(pagination.getManId())) {
            queryWrapper.lambda().eq(FinDebtEntity::getManId, pagination.getManId());
        }
        if (StrXhUtil.isNotEmpty(pagination.getDebtType())) {
            queryWrapper.lambda().eq(FinDebtEntity::getDebtType, pagination.getDebtType());
        }
        if (StrXhUtil.isNotEmpty(pagination.getStatus())) {
            queryWrapper.lambda().eq(FinDebtEntity::getStatus, pagination.getStatus());
        }
        if (StrXhUtil.isNotEmpty(pagination.getBankName())) {
            queryWrapper.lambda().like(FinDebtEntity::getBankName, pagination.getBankName());
        }
        
        // 排序
        queryWrapper.lambda().orderByDesc(FinDebtEntity::getCreateTime);
        
        return this.list(queryWrapper);
    }

    @Override
    public FinDebtEntity getInfo(String id) {
        Assert.notBlank(id, "债务ID不能为空");
        return this.getById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveInfo(FinDebtEntity entity) {
        Assert.notNull(entity, "债务信息不能为空");
        
        // 设置默认值
        if (StrXhUtil.isEmpty(entity.getStatus())) {
            entity.setStatus("ACTIVE"); // 默认激活状态
        }
        
        boolean result = this.save(entity);
        if (result) {
            log.info("创建债务成功，债务ID: {}, 债务名称: {}", entity.getId(), entity.getDebtName());
        }
        
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateInfo(FinDebtEntity entity) {
        Assert.notNull(entity, "债务信息不能为空");
        Assert.notBlank(entity.getId(), "债务ID不能为空");
        
        boolean result = this.updateById(entity);
        if (result) {
            log.info("更新债务成功，债务ID: {}", entity.getId());
        }
        
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteInfo(String id) {
        Assert.notBlank(id, "债务ID不能为空");
        
        FinDebtEntity entity = this.getById(id);
        if (entity == null) {
            log.warn("要删除的债务不存在，ID: {}", id);
            return false;
        }
        
        boolean result = this.removeById(id);
        if (result) {
            log.info("删除债务成功，债务ID: {}, 债务名称: {}", id, entity.getDebtName());
        }
        
        return result;
    }

    @Override
    public List<FinDebtEntity> getListByManId(String manId) {
        Assert.notBlank(manId, "持卡人ID不能为空");
        
        QueryWrapper<FinDebtEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(FinDebtEntity::getManId, manId)
                .orderByDesc(FinDebtEntity::getCreateTime);
        
        return this.list(queryWrapper);
    }

    @Override
    public Map<String, Object> getDebtStatistics() {
        Map<String, Object> statistics = new HashMap<>();
        
        // 总债务数量
        long totalCount = this.count();
        statistics.put("totalCount", totalCount);
        
        // 总债务金额
        QueryWrapper<FinDebtEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().select(FinDebtEntity::getCurrentBalance);
        List<FinDebtEntity> debts = this.list(queryWrapper);
        
        BigDecimal totalAmount = debts.stream()
                .map(debt -> debt.getCurrentBalance() != null ? debt.getCurrentBalance() : BigDecimal.ZERO)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        statistics.put("totalAmount", totalAmount);
        
        // 活跃债务数量
        QueryWrapper<FinDebtEntity> activeWrapper = new QueryWrapper<>();
        activeWrapper.lambda().eq(FinDebtEntity::getStatus, "ACTIVE");
        long activeCount = this.count(activeWrapper);
        statistics.put("activeCount", activeCount);
        
        log.info("债务统计信息：总数量={}, 总金额={}, 活跃数量={}", totalCount, totalAmount, activeCount);
        
        return statistics;
    }

    @Override
    public List<Map<String, Object>> getDebtTypeDistribution() {
        return baseMapper.selectDebtTypeStats(null);
    }

    @Override
    public List<Map<String, Object>> getBankDistribution() {
        QueryWrapper<FinDebtEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .select(FinDebtEntity::getBankName)
                .groupBy(FinDebtEntity::getBankName);
        
        List<FinDebtEntity> debts = this.list(queryWrapper);
        List<Map<String, Object>> distribution = new ArrayList<>();
        
        Map<String, Integer> bankCount = new HashMap<>();
        for (FinDebtEntity debt : debts) {
            String bankName = debt.getBankName();
            if (StrXhUtil.isNotEmpty(bankName)) {
                bankCount.put(bankName, bankCount.getOrDefault(bankName, 0) + 1);
            }
        }
        
        for (Map.Entry<String, Integer> entry : bankCount.entrySet()) {
            Map<String, Object> item = new HashMap<>();
            item.put("bankName", entry.getKey());
            item.put("count", entry.getValue());
            distribution.add(item);
        }
        
        return distribution;
    }

    @Override
    public BigDecimal calculateTotalDebt(String manId) {
        Assert.notBlank(manId, "持卡人ID不能为空");
        
        QueryWrapper<FinDebtEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(FinDebtEntity::getManId, manId)
                .eq(FinDebtEntity::getStatus, "ACTIVE");
        
        List<FinDebtEntity> debts = this.list(queryWrapper);
        
        return debts.stream()
                .map(debt -> debt.getCurrentBalance() != null ? debt.getCurrentBalance() : BigDecimal.ZERO)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    @Override
    public BigDecimal calculateMonthlyPayment(String manId) {
        Assert.notBlank(manId, "持卡人ID不能为空");
        
        QueryWrapper<FinDebtEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(FinDebtEntity::getManId, manId)
                .eq(FinDebtEntity::getStatus, "ACTIVE");
        
        List<FinDebtEntity> debts = this.list(queryWrapper);
        
        return debts.stream()
                .map(debt -> debt.getMonthlyPayment() != null ? debt.getMonthlyPayment() : BigDecimal.ZERO)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    @Override
    public List<Map<String, Object>> getDebtTrend(String manId, Integer months) {
        Assert.notBlank(manId, "持卡人ID不能为空");
        
        if (months == null || months <= 0) {
            months = 12; // 默认12个月
        }
        
        // TODO: 实现债务趋势分析逻辑
        // 这里需要根据历史数据计算债务变化趋势
        List<Map<String, Object>> trend = new ArrayList<>();
        
        log.info("获取债务趋势，持卡人ID: {}, 月份数: {}", manId, months);
        
        return trend;
    }

    @Override
    public List<FinDebtEntity> getNearExpiryDebts(String manId, Integer days) {
        Assert.notBlank(manId, "持卡人ID不能为空");
        
        if (days == null || days <= 0) {
            days = 30; // 默认30天
        }
        
        List<FinDebtVO> debtVOs = baseMapper.selectNearExpiryDebts(days);
        
        // 转换为Entity列表
        List<FinDebtEntity> result = new ArrayList<>();
        for (FinDebtVO vo : debtVOs) {
            if (manId.equals(vo.getManId())) {
                FinDebtEntity entity = this.getById(vo.getId());
                if (entity != null) {
                    result.add(entity);
                }
            }
        }
        
        return result;
    }

    @Override
    public List<FinDebtEntity> getHighRiskDebts(String manId) {
        Assert.notBlank(manId, "持卡人ID不能为空");
        
        QueryWrapper<FinDebtEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(FinDebtEntity::getManId, manId)
                .eq(FinDebtEntity::getStatus, "ACTIVE")
                .and(wrapper -> wrapper
                        .gt(FinDebtEntity::getInterestRate, new BigDecimal("0.15")) // 利率超过15%
                        .or()
                        .gt(FinDebtEntity::getCurrentBalance, new BigDecimal("100000")) // 余额超过10万
                );
        
        return this.list(queryWrapper);
    }

    @Override
    public Map<String, Object> getDebtHealthScore(String manId) {
        Assert.notBlank(manId, "持卡人ID不能为空");
        
        return baseMapper.selectDebtHealthIndicators(manId);
    }

    @Override
    public List<Map<String, Object>> getRepaymentSchedule(String manId, Integer months) {
        Assert.notBlank(manId, "持卡人ID不能为空");
        
        if (months == null || months <= 0) {
            months = 12; // 默认12个月
        }
        
        // TODO: 实现还款计划生成逻辑
        List<Map<String, Object>> schedule = new ArrayList<>();
        
        log.info("生成还款计划，持卡人ID: {}, 月份数: {}", manId, months);
        
        return schedule;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateDebtStatus(String id, String status) {
        Assert.notBlank(id, "债务ID不能为空");
        Assert.notBlank(status, "状态不能为空");

        int result = baseMapper.updateDebtStatus(id, status);
        if (result > 0) {
            log.info("更新债务状态成功，债务ID: {}, 新状态: {}", id, status);
            return true;
        }

        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchUpdateDebtStatus(List<String> ids, String status) {
        Assert.notEmpty(ids, "债务ID列表不能为空");
        Assert.notBlank(status, "状态不能为空");

        int result = baseMapper.batchUpdateDebtStatus(ids, status);
        log.info("批量更新债务状态完成，更新数量: {}, 新状态: {}", result, status);

        return result;
    }

    @Override
    public List<FinDebtEntity> getOverdueDebts(String manId) {
        Assert.notBlank(manId, "持卡人ID不能为空");

        List<FinDebtVO> overdueVOs = baseMapper.selectOverdueDebts();

        // 转换为Entity列表并过滤指定用户
        List<FinDebtEntity> result = new ArrayList<>();
        for (FinDebtVO vo : overdueVOs) {
            if (manId.equals(vo.getManId())) {
                FinDebtEntity entity = this.getById(vo.getId());
                if (entity != null) {
                    result.add(entity);
                }
            }
        }

        return result;
    }

    @Override
    public Map<String, Object> calculateInterest(String id) {
        Assert.notBlank(id, "债务ID不能为空");

        FinDebtEntity debt = this.getById(id);
        if (debt == null) {
            log.warn("债务不存在，ID: {}", id);
            return new HashMap<>();
        }

        Map<String, Object> interestInfo = new HashMap<>();

        // 计算月利息
        BigDecimal monthlyInterest = BigDecimal.ZERO;
        if (debt.getCurrentBalance() != null && debt.getInterestRate() != null) {
            monthlyInterest = debt.getCurrentBalance()
                    .multiply(debt.getInterestRate())
                    .divide(new BigDecimal("12"), 2, BigDecimal.ROUND_HALF_UP);
        }

        interestInfo.put("monthlyInterest", monthlyInterest);
        interestInfo.put("currentBalance", debt.getCurrentBalance());
        interestInfo.put("interestRate", debt.getInterestRate());

        return interestInfo;
    }

    @Override
    public List<Map<String, Object>> generateRepaymentSchedule(String id) {
        Assert.notBlank(id, "债务ID不能为空");

        FinDebtEntity debt = this.getById(id);
        if (debt == null) {
            log.warn("债务不存在，ID: {}", id);
            return new ArrayList<>();
        }

        List<Map<String, Object>> schedule = new ArrayList<>();

        // TODO: 实现还款计划生成算法
        // 根据债务金额、利率、期限生成详细的还款计划

        log.info("生成债务还款计划，债务ID: {}", id);

        return schedule;
    }

    @Override
    public Map<String, Object> calculatePrepayment(String id, BigDecimal prepaymentAmount) {
        Assert.notBlank(id, "债务ID不能为空");
        Assert.notNull(prepaymentAmount, "提前还款金额不能为空");

        FinDebtEntity debt = this.getById(id);
        if (debt == null) {
            log.warn("债务不存在，ID: {}", id);
            return new HashMap<>();
        }

        Map<String, Object> prepaymentInfo = new HashMap<>();

        // 计算提前还款后的余额
        BigDecimal newBalance = debt.getCurrentBalance().subtract(prepaymentAmount);
        if (newBalance.compareTo(BigDecimal.ZERO) < 0) {
            newBalance = BigDecimal.ZERO;
        }

        // 计算节省的利息
        BigDecimal savedInterest = BigDecimal.ZERO;
        if (debt.getInterestRate() != null && debt.getRemainingTerm() != null) {
            savedInterest = prepaymentAmount
                    .multiply(debt.getInterestRate())
                    .multiply(new BigDecimal(debt.getRemainingTerm()))
                    .divide(new BigDecimal("12"), 2, BigDecimal.ROUND_HALF_UP);
        }

        prepaymentInfo.put("newBalance", newBalance);
        prepaymentInfo.put("savedInterest", savedInterest);
        prepaymentInfo.put("prepaymentAmount", prepaymentAmount);

        return prepaymentInfo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean recordRepayment(String id, BigDecimal amount, String paymentMethod) {
        Assert.notBlank(id, "债务ID不能为空");
        Assert.notNull(amount, "还款金额不能为空");

        FinDebtEntity debt = this.getById(id);
        if (debt == null) {
            log.warn("债务不存在，ID: {}", id);
            return false;
        }

        // 更新债务余额
        BigDecimal newBalance = debt.getCurrentBalance().subtract(amount);
        if (newBalance.compareTo(BigDecimal.ZERO) < 0) {
            newBalance = BigDecimal.ZERO;
        }

        int result = baseMapper.updateDebtBalance(id, newBalance, debt.getRemainingTerm());

        if (result > 0) {
            log.info("记录还款成功，债务ID: {}, 还款金额: {}, 新余额: {}", id, amount, newBalance);

            // 如果余额为0，更新状态为已结清
            if (newBalance.compareTo(BigDecimal.ZERO) == 0) {
                baseMapper.updateDebtStatus(id, "SETTLED");
                log.info("债务已结清，债务ID: {}", id);
            }

            return true;
        }

        return false;
    }

    @Override
    public List<Map<String, Object>> getDebtAnalysisReport(String manId) {
        Assert.notBlank(manId, "持卡人ID不能为空");

        List<Map<String, Object>> report = new ArrayList<>();

        // 债务总览
        Map<String, Object> overview = new HashMap<>();
        overview.put("totalDebt", calculateTotalDebt(manId));
        overview.put("monthlyPayment", calculateMonthlyPayment(manId));
        overview.put("debtCount", getListByManId(manId).size());
        report.add(overview);

        // 债务类型分布
        List<Map<String, Object>> typeDistribution = baseMapper.selectDebtTypeStats(manId);
        report.addAll(typeDistribution);

        log.info("生成债务分析报告，持卡人ID: {}", manId);

        return report;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean optimizeDebtStructure(String manId) {
        Assert.notBlank(manId, "持卡人ID不能为空");

        // TODO: 实现债务结构优化算法
        // 分析用户的债务情况，提供优化建议

        log.info("执行债务结构优化，持卡人ID: {}", manId);

        return true;
    }

    @Override
    public Map<String, Object> getDebtRiskAssessment(String manId) {
        Assert.notBlank(manId, "持卡人ID不能为空");

        Map<String, Object> assessment = new HashMap<>();

        // 计算债务收入比
        BigDecimal totalDebt = calculateTotalDebt(manId);
        BigDecimal monthlyPayment = calculateMonthlyPayment(manId);

        assessment.put("totalDebt", totalDebt);
        assessment.put("monthlyPayment", monthlyPayment);

        // 风险等级评估
        String riskLevel = "LOW";
        if (monthlyPayment.compareTo(new BigDecimal("5000")) > 0) {
            riskLevel = "MEDIUM";
        }
        if (monthlyPayment.compareTo(new BigDecimal("10000")) > 0) {
            riskLevel = "HIGH";
        }

        assessment.put("riskLevel", riskLevel);

        return assessment;
    }
}
