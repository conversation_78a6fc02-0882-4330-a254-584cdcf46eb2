package com.xinghuo.card.flow.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xinghuo.card.flow.dao.DataFlowAccMapper;
import com.xinghuo.card.flow.entity.DataFlowAccEntity;
import com.xinghuo.card.flow.model.dataflowacc.DataFlowAccPagination;
import com.xinghuo.card.flow.service.DataFlowAccService;
import com.xinghuo.common.util.UserProvider;
import com.xinghuo.common.util.core.DateXhUtil;
import com.xinghuo.common.util.core.StrXhUtil;
import com.xinghuo.common.util.extra.ServletUtil;
import com.xinghuo.permission.model.authorize.AuthorizeConditionModel;
import com.xinghuo.permission.service.AuthorizeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static java.math.BigDecimal.ZERO;
import lombok.extern.slf4j.Slf4j;

/**
 * data_flow_acc
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2022-11-26
 */
@Slf4j
@Service
public class DataFlowAccServiceImpl extends ServiceImpl<DataFlowAccMapper, DataFlowAccEntity> implements DataFlowAccService {


    @Autowired
    private UserProvider userProvider;


    @Override
    public List<DataFlowAccEntity> getList(DataFlowAccPagination pagination) {
        try {
            QueryWrapper<DataFlowAccEntity> queryWrapper = new QueryWrapper<>();


            // 收支类型过滤
            if (StrXhUtil.isNotEmpty(pagination.getType())) {
                queryWrapper.lambda().eq(DataFlowAccEntity::getType, pagination.getType());
            }

            // 账户过滤
            if (StrXhUtil.isNotEmpty(pagination.getAccId())) {
                queryWrapper.lambda().eq(DataFlowAccEntity::getAccId, pagination.getAccId());
            }

            // 日期范围过滤
            if (CollUtil.isNotEmpty(pagination.getFlowDate()) && pagination.getFlowDate().size() >= 2) {
                try {
                    List<String> flowDateList = pagination.getFlowDate();
                    Long startTime = Long.valueOf(flowDateList.get(0));
                    Long endTime = Long.valueOf(flowDateList.get(1));
                    queryWrapper.lambda().ge(DataFlowAccEntity::getFlowDate, new Date(startTime))
                            .le(DataFlowAccEntity::getFlowDate, DateXhUtil.endOfDay(endTime));
                } catch (NumberFormatException e) {
                    log.warn("日期格式转换失败，忽略日期过滤条件", e);
                }
            }

            // 关键词搜索
            if (StrXhUtil.isNotEmpty(pagination.getKeyword())) {
                queryWrapper.lambda().like(DataFlowAccEntity::getNote, pagination.getKeyword());
            }

            // 排序处理
            if (StrXhUtil.isBlank(pagination.getSidx())) {
                queryWrapper.lambda().orderByDesc(DataFlowAccEntity::getFlowDate)
                        .orderByDesc(DataFlowAccEntity::getListOrder);
            } else {
                // 自定义排序逻辑
                handleCustomSort(queryWrapper, pagination);
            }

            Page<DataFlowAccEntity> page = new Page<>(pagination.getCurrentPage(), pagination.getPageSize());
            IPage<DataFlowAccEntity> userIpage = this.page(page, queryWrapper);
            return pagination.setDataList(userIpage.getRecords(), userIpage.getTotal());

        } catch (Exception e) {
            log.error("查询流水账户列表失败", e);
            throw new RuntimeException("查询失败：" + e.getMessage());
        }
    }

    @Override
    public List<DataFlowAccEntity> getTypeList(DataFlowAccPagination dataFlowAccPagination, int dataType) {
        return getListByType(dataFlowAccPagination, dataType);
    }

    private List<DataFlowAccEntity> getListByType(DataFlowAccPagination dataFlowAccPagination, int dataType) {
        List<String> allIdList = new ArrayList();
        int total = 0;
        int dataFlowAccNum = 0;
        QueryWrapper<DataFlowAccEntity> dataFlowAccQueryWrapper = new QueryWrapper<>();
        boolean pcPermission = false;
        boolean appPermission = false;
        boolean isPc = "pc".equals(ServletUtil.getHeader("xh-origin"));

        if (StrXhUtil.isNotEmpty(dataFlowAccPagination.getType())) {
            dataFlowAccNum++;
            dataFlowAccQueryWrapper.lambda().eq(DataFlowAccEntity::getType, dataFlowAccPagination.getType());
        }
        if (StrXhUtil.isNotEmpty(dataFlowAccPagination.getAccId())) {
            dataFlowAccNum++;
            dataFlowAccQueryWrapper.lambda().eq(DataFlowAccEntity::getAccId, dataFlowAccPagination.getAccId());
        }
        if (CollUtil.isNotEmpty(dataFlowAccPagination.getFlowDate())) {
            dataFlowAccNum++;
            List<String> FlowDateList = dataFlowAccPagination.getFlowDate();
            Long fir = Long.valueOf(FlowDateList.get(0));
            Long sec = Long.valueOf(FlowDateList.get(1));
            dataFlowAccQueryWrapper.lambda().ge(DataFlowAccEntity::getFlowDate, new Date(fir))
                    .le(DataFlowAccEntity::getFlowDate, DateXhUtil.endOfDay(sec));
        }
        if (allIdList.size() > 0) {
            dataFlowAccQueryWrapper.lambda().in(DataFlowAccEntity::getId, allIdList);
        }
        //排序
        if (StrXhUtil.isEmpty(dataFlowAccPagination.getSidx())) {
            dataFlowAccQueryWrapper.lambda().orderByDesc(DataFlowAccEntity::getFlowDate).orderByDesc(DataFlowAccEntity::getListOrder);
        } else {
            try {
                DataFlowAccEntity dataFlowAccEntity = new DataFlowAccEntity();
                Field declaredField = dataFlowAccEntity.getClass().getDeclaredField(dataFlowAccPagination.getSidx());
                declaredField.setAccessible(true);
                String value = declaredField.getAnnotation(TableField.class).value();
                dataFlowAccQueryWrapper = "asc".equals(dataFlowAccPagination.getSort().toLowerCase()) ? dataFlowAccQueryWrapper.orderByAsc(value) : dataFlowAccQueryWrapper.orderByDesc(value);
            } catch (NoSuchFieldException e) {
                e.printStackTrace();
            }
        }
        //
        dataFlowAccQueryWrapper.lambda().orderByDesc(DataFlowAccEntity::getListOrder);


            return this.list(dataFlowAccQueryWrapper);

    }

    @Override
    public DataFlowAccEntity getInfo(String id) {
        QueryWrapper<DataFlowAccEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(DataFlowAccEntity::getId, id);
        return this.getOne(queryWrapper);
    }

    @Override
    public void create(DataFlowAccEntity entity) {
        try {
            // 参数验证
            if (entity == null) {
                throw new IllegalArgumentException("实体对象不能为空");
            }

            if (entity.getFlowDate() == null) {
                throw new IllegalArgumentException("流水日期不能为空");
            }

            // 设置默认值
            if(entity.getIncome() == null) {
                entity.setIncome(ZERO);
            }
            if(entity.getPay() == null) {
                entity.setPay(ZERO);
            }


            // 计算排序字段
            entity.setListOrder2(Integer.valueOf(DateXhUtil.format(entity.getFlowDate(), "yyyyMMdd")) * 100
                    + NumberUtil.nullToZero(entity.getListOrder()));

            // 设置创建时间
            if (entity.getFlowDate() == null) {
                entity.setFlowDate(new Date());
            }

            this.save(entity);
            log.info("创建流水记录成功，ID：{}", entity.getId());

        } catch (Exception e) {
            log.error("创建流水记录失败", e);
            throw new RuntimeException("创建失败：" + e.getMessage());
        }
    }

    @Override
    public boolean update(String id, DataFlowAccEntity entity) {
        try {
            // 参数验证
            if (StrXhUtil.isBlank(id)) {
                throw new IllegalArgumentException("记录ID不能为空");
            }

            if (entity == null) {
                throw new IllegalArgumentException("实体对象不能为空");
            }

            // 检查记录是否存在
            DataFlowAccEntity existingEntity = this.getById(id);
            if (existingEntity == null) {
                throw new IllegalArgumentException("记录不存在，ID：" + id);
            }

            entity.setId(id);

            // 设置默认值
            if(entity.getIncome() == null) {
                entity.setIncome(ZERO);
            }
            if(entity.getPay() == null) {
                entity.setPay(ZERO);
            }

            // 验证流水日期
            if (entity.getFlowDate() == null) {
                throw new IllegalArgumentException("流水日期不能为空");
            }

            // 计算排序字段
            entity.setListOrder2(Integer.valueOf(DateXhUtil.format(entity.getFlowDate(), "yyyyMMdd")) * 100
                    + NumberUtil.nullToZero(entity.getListOrder()));

            boolean success = this.updateById(entity);
            if (success) {
                log.info("更新流水记录成功，ID：{}", id);
            } else {
                log.warn("更新流水记录失败，ID：{}", id);
            }

            return success;

        } catch (Exception e) {
            log.error("更新流水记录失败，ID：{}", id, e);
            throw new RuntimeException("更新失败：" + e.getMessage());
        }
    }

    @Override
    public void delete(DataFlowAccEntity entity) {
        if (entity != null) {
            this.removeById(entity.getId());
        }
    }

    @Override
    public int selectMaxListOrder(DataFlowAccEntity dataFlowAcc) {

        return this.getBaseMapper().selectMaxListOrder(dataFlowAcc);
    }

    @Override
    public int updateDataFlowAccBalance(DataFlowAccEntity dataFlowAcc) {
        return this.getBaseMapper().updateDataFlowAccBalance(dataFlowAcc);
    }

    @Override
    public DataFlowAccEntity getDataFlowAccByFlowId(String accId, String flowId) {
        QueryWrapper<DataFlowAccEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(DataFlowAccEntity::getAccId, accId);
        queryWrapper.lambda().eq(DataFlowAccEntity::getFlowId, flowId);
        return this.getOne(queryWrapper);
    }

    @Override
    public int deleteDataFlowAccByFlowId(String flowId) {
        QueryWrapper<DataFlowAccEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(DataFlowAccEntity::getFlowId, flowId);
        return this.getBaseMapper().delete(queryWrapper);
    }

    /**
     * 处理自定义排序
     */
    private void handleCustomSort(QueryWrapper<DataFlowAccEntity> queryWrapper, DataFlowAccPagination pagination) {
        try {
            DataFlowAccEntity dataFlowAccEntity = new DataFlowAccEntity();
            Field declaredField = dataFlowAccEntity.getClass().getDeclaredField(pagination.getSidx());
            declaredField.setAccessible(true);
            String value = declaredField.getAnnotation(TableField.class).value();

            if ("asc".equals(pagination.getSort().toLowerCase())) {
                queryWrapper.orderByAsc(value);
            } else {
                queryWrapper.orderByDesc(value);
            }
        } catch (NoSuchFieldException e) {
            log.warn("排序字段不存在：{}，使用默认排序", pagination.getSidx());
            queryWrapper.lambda().orderByDesc(DataFlowAccEntity::getFlowDate)
                    .orderByDesc(DataFlowAccEntity::getListOrder);
        } catch (Exception e) {
            log.error("处理自定义排序失败", e);
            queryWrapper.lambda().orderByDesc(DataFlowAccEntity::getFlowDate)
                    .orderByDesc(DataFlowAccEntity::getListOrder);
        }
    }
}
