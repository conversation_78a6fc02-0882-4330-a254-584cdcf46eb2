package com.xinghuo.card.sys.model.datasyspaytype;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.List;


/**
 * 收支类型管理
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2022-11-14
 */
@Data
public class DataSysPaytypeVO {

    @Schema(description = "主键")
    private String id;

    @Schema(description = "上级")
    private String parentId;

    @Schema(description = "类型")
    private Integer type;

    @Schema(description = "名称")
    @JsonProperty("fullName")
    private String name;

    @Schema(description = "排序")
    private Integer listOrder;

    @Schema(description = "系统分类")
    private Integer systemFlag;

    @Schema(description = "拼音")
    private String py;

    private List<DataSysPaytypeVO> children;

    @Schema(description = "创建人")
    private String createBy;

    @Schema(description = "创建时间")
    private Date createTime;

}
