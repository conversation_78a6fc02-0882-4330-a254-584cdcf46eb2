package com.xinghuo.card.report.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xinghuo.card.report.entity.ReportQueryConditionEntity;

import java.util.List;
import java.util.Map;

/**
 * 报表查询条件服务接口
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2024-01-01
 */
public interface ReportConditionService extends IService<ReportQueryConditionEntity> {

    /**
     * 根据用户ID获取查询条件列表
     *
     * @param userId 用户ID
     * @param reportType 报表类型
     * @return 查询条件列表
     */
    List<ReportQueryConditionEntity> getByUserId(String userId, Integer reportType);

    /**
     * 获取公共查询条件
     *
     * @param reportType 报表类型
     * @return 公共查询条件列表
     */
    List<ReportQueryConditionEntity> getPublicConditions(Integer reportType);

    /**
     * 获取收藏的查询条件
     *
     * @param userId 用户ID
     * @return 收藏的查询条件列表
     */
    List<ReportQueryConditionEntity> getFavoriteConditions(String userId);

    /**
     * 保存查询条件
     *
     * @param condition 查询条件
     * @return 保存结果
     */
    boolean saveCondition(ReportQueryConditionEntity condition);

    /**
     * 增加使用次数
     *
     * @param conditionId 条件ID
     * @return 操作结果
     */
    boolean incrementUseCount(String conditionId);

    /**
     * 收藏/取消收藏查询条件
     *
     * @param conditionId 条件ID
     * @param favorite 是否收藏
     * @return 操作结果
     */
    boolean toggleFavorite(String conditionId, boolean favorite);

    /**
     * 将查询条件转换为查询参数
     *
     * @param condition 查询条件
     * @return 查询参数Map
     */
    Map<String, Object> convertToQueryParams(ReportQueryConditionEntity condition);

    /**
     * 从查询参数创建查询条件
     *
     * @param params 查询参数
     * @param userId 用户ID
     * @param conditionName 条件名称
     * @return 查询条件
     */
    ReportQueryConditionEntity createFromParams(Map<String, Object> params, String userId, String conditionName);

    /**
     * 管理端：分页获取查询条件列表
     *
     * @param page 分页参数
     * @param keyword 关键词
     * @param reportType 报表类型
     * @param isPublic 是否公共
     * @return 分页结果
     */
    IPage<ReportQueryConditionEntity> getConditionListForAdmin(Page<ReportQueryConditionEntity> page,
                                                               String keyword,
                                                               Integer reportType,
                                                               Boolean isPublic);

    /**
     * 批量设置公共状态
     *
     * @param conditionIds 条件ID列表
     * @param isPublic 是否公共
     * @return 操作结果
     */
    boolean batchSetPublic(List<String> conditionIds, Boolean isPublic);
}
