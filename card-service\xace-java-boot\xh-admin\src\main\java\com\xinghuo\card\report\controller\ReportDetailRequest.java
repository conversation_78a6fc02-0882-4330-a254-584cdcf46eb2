package com.xinghuo.card.report.controller;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 报表明细查询请求参数
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "报表明细查询请求参数")
public class ReportDetailRequest extends ReportQueryRequest {

    /**
     * 页码
     */
    @Schema(description = "页码")
    private Integer pageNum;

    /**
     * 页大小
     */
    @Schema(description = "页大小")
    private Integer pageSize;
}
