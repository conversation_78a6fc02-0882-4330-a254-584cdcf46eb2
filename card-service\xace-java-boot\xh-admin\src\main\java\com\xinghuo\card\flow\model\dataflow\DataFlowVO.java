package com.xinghuo.card.flow.model.dataflow;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;

/**
 * 流水表
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2022-11-26
 */
@Data
public class DataFlowVO {

    @Schema(description = "主键")
    private String id;

    @Schema(description = "类型")
    private String transType;

    @Schema(description = "收支类型")
    private String type;

    @Schema(description = "金额")
    private BigDecimal amout;

    @Schema(description = "日期")
    private Date flowDate;

    @Schema(description = "收支账户")
    private String accId;

    @Schema(description = "标签")
    private String manId;

    @Schema(description = "转入账户")
    private String inAccId;

    @Schema(description = "转出账户")
    private String outAccId;

    @Schema(description = "备注")
    private String note;

    @Schema(description = "创建人员")
    private String createBy;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "修改人员")
    private String updateBy;

    @Schema(description = "最后修改时间")
    private Date updateTime;

}
