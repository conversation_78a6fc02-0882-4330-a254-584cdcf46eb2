package com.xinghuo.card.fin.model.finAssetProperty;

import com.xinghuo.common.base.model.Pagination;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 不动产资产分页查询参数
 *
 * <AUTHOR> Assistant
 * @date 2024-12-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "不动产资产分页查询参数")
public class FinAssetPropertyPagination extends Pagination {

    /**
     * 资产名称
     * 支持模糊查询
     */
    @Schema(description = "资产名称", example = "北京朝阳区公寓")
    private String name;

    /**
     * 不动产类型
     * HOUSE-住宅 COMMERCIAL-商业地产 LAND-土地 PARKING-车位 OTHER-其他
     */
    @Schema(description = "不动产类型", example = "HOUSE", 
            allowableValues = {"HOUSE", "COMMERCIAL", "LAND", "PARKING", "OTHER"})
    private String propertyType;

    /**
     * 持卡人ID
     * 精确匹配查询
     */
    @Schema(description = "持卡人ID", example = "man_001")
    private String manId;

    /**
     * 持卡人代称
     * 支持模糊查询
     */
    @Schema(description = "持卡人代称", example = "张三")
    private String manName;

    /**
     * 地区
     * 支持模糊查询
     */
    @Schema(description = "地区", example = "北京市朝阳区")
    private String location;

    /**
     * 最小购买价格
     * 用于价格范围查询
     */
    @Schema(description = "最小购买价格", example = "1000000.00")
    private BigDecimal minPurchasePrice;

    /**
     * 最大购买价格
     * 用于价格范围查询
     */
    @Schema(description = "最大购买价格", example = "5000000.00")
    private BigDecimal maxPurchasePrice;

    /**
     * 最小当前估值
     * 用于估值范围查询
     */
    @Schema(description = "最小当前估值", example = "1200000.00")
    private BigDecimal minCurrentValue;

    /**
     * 最大当前估值
     * 用于估值范围查询
     */
    @Schema(description = "最大当前估值", example = "6000000.00")
    private BigDecimal maxCurrentValue;

    /**
     * 购买日期开始
     * 用于购买日期范围查询
     */
    @Schema(description = "购买日期开始", example = "2020-01-01")
    private Date purchaseDateStart;

    /**
     * 购买日期结束
     * 用于购买日期范围查询
     */
    @Schema(description = "购买日期结束", example = "2024-12-31")
    private Date purchaseDateEnd;

    /**
     * 搜索关键词
     * 支持资产名称、地区的模糊搜索
     */
    @Schema(description = "搜索关键词", example = "朝阳")
    private String keyword;

    /**
     * 创建时间开始
     * 用于创建时间范围查询
     */
    @Schema(description = "创建时间开始", example = "2024-01-01")
    private Date createTimeStart;

    /**
     * 创建时间结束
     * 用于创建时间范围查询
     */
    @Schema(description = "创建时间结束", example = "2024-12-31")
    private Date createTimeEnd;

    /**
     * 创建人
     * 用于按创建人查询
     */
    @Schema(description = "创建人", example = "admin")
    private String createBy;

    /**
     * 是否包含统计信息
     * 是否在查询结果中包含相关统计信息
     */
    @Schema(description = "是否包含统计信息", example = "true")
    private Boolean includeStatistics;

    /**
     * 是否只查询有估值的资产
     */
    @Schema(description = "是否只查询有估值的资产", example = "false")
    private Boolean onlyWithValuation;

    /**
     * 是否只查询高价值资产
     * 价值超过指定阈值的资产
     */
    @Schema(description = "是否只查询高价值资产", example = "false")
    private Boolean onlyHighValue;

    /**
     * 高价值阈值
     * 用于筛选高价值资产的阈值
     */
    @Schema(description = "高价值阈值", example = "5000000.00")
    private BigDecimal highValueThreshold;

    /**
     * 是否只查询增值资产
     * 当前估值高于购买价格的资产
     */
    @Schema(description = "是否只查询增值资产", example = "false")
    private Boolean onlyAppreciatedAssets;

    /**
     * 估值方法
     * MANUAL-手动估值 AUTO-自动估值 MARKET-市场估值
     */
    @Schema(description = "估值方法", example = "MARKET", 
            allowableValues = {"MANUAL", "AUTO", "MARKET"})
    private String valuationMethod;

    /**
     * 排序字段
     * 支持的排序字段：name, propertyType, purchasePrice, currentValue, purchaseDate, createTime
     */
    @Schema(description = "排序字段", example = "currentValue", 
            allowableValues = {"name", "propertyType", "purchasePrice", "currentValue", "purchaseDate", "createTime"})
    private String sortField;

    /**
     * 排序方向
     * ASC-升序 DESC-降序
     */
    @Schema(description = "排序方向", example = "DESC", allowableValues = {"ASC", "DESC"})
    private String sortOrder;

    private Integer status;
}
