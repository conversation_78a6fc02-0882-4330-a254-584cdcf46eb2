package com.xinghuo.card.sys.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xinghuo.card.sys.entity.SysManEntity;
import com.xinghuo.card.sys.model.sysman.SysManPagination;
import com.alicp.jetcache.anno.Cached;
import com.alicp.jetcache.anno.CacheInvalidate;
import com.alicp.jetcache.anno.CacheUpdate;
import com.alicp.jetcache.anno.CacheType;
import com.xinghuo.common.base.service.BaseService;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 持卡账户管理服务接口
 * 用于管理持卡人的基本信息和代称
 * 支持多持卡人管理，每个持卡人有唯一的代称标识
 *
 * <AUTHOR> Assistant
 * @version V1.0.0
 * @date 2024-12-29
 */
public interface SysManService extends BaseService<SysManEntity> {

    String CACHE_NAME = "dataSysMan";

    /**
     * 查询分页数据
     *
     * @param sysManPagination 查询对象
     * @return 查询结果
     */
    List<SysManEntity> getList(SysManPagination sysManPagination);

    /**
     * 查询分页或者不分页列表
     *
     * @param sysManPagination 查询对象
     * @param dataType         0:分页 1:不分页
     * @return 查询结果
     */
    List<SysManEntity> getTypeList(SysManPagination sysManPagination, String dataType);

    /**
     * 获取持卡人选择器列表
     * 用于下拉框等选择组件
     *
     * @return 持卡人列表
     */
    @Cached(name = CACHE_NAME, key = "selectList", cacheType = CacheType.LOCAL, expire = 120)
    List<SysManEntity> getSelectList();

    /**
     * 获取DataSysManEntity详细信息
     *
     * @param id 主键
     * @return 持卡人详细信息
     */
    @Cached(name = CACHE_NAME, key = "#id", cacheType = CacheType.LOCAL, expire = 360)
    SysManEntity getInfo(String id);

    /**
     * 根据持卡人代称查询
     *
     * @param name 持卡人代称
     * @return 持卡人信息
     */
    @Cached(name = CACHE_NAME, key = "name:#name", cacheType = CacheType.LOCAL, expire = 360)
    SysManEntity getInfoByName(String name);

    /**
     * 删除持卡人
     *
     * @param entity 删除的对象
     */
    @CacheInvalidate(name = CACHE_NAME, key = "#entity.id")
    void delete(SysManEntity entity);

    /**
     * 新增保存持卡人
     *
     * @param entity 新增的对象
     */
    void create(SysManEntity entity);

    /**
     * 修改保存持卡人
     *
     * @param id     主键
     * @param entity 修改的对象
     * @return 是否成功
     */
    @CacheUpdate(name = CACHE_NAME, key = "#id", value = "#entity")
    boolean update(String id, SysManEntity entity);

    /**
     * 检查持卡人代称是否唯一
     *
     * @param name 持卡人代称
     * @param id   排除的ID（用于更新时检查）
     * @return 是否唯一
     */
    boolean checkNameUnique(String name, String id);

    /**
     * 获取下一个排序序号
     *
     * @return 下一个排序序号
     */
    Integer getNextListOrder();

    /**
     * 批量删除持卡人
     *
     * @param ids 持卡人ID列表
     * @return 删除成功的数量
     */
    int batchDelete(List<String> ids);

    /**
     * 更新排序序号
     *
     * @param id        持卡人ID
     * @param listOrder 新的排序序号
     * @return 是否成功
     */
    boolean updateListOrder(String id, Integer listOrder);

    /**
     * 获取持卡人统计信息
     *
     * @return 统计信息
     */
    @Cached(name = CACHE_NAME, key = "statistics", cacheType = CacheType.LOCAL, expire = 300)
    Map<String, Object> getManStatistics();

    /**
     * 获取性别分布统计
     *
     * @return 性别分布
     */
    @Cached(name = CACHE_NAME, key = "sexDistribution", cacheType = CacheType.LOCAL, expire = 300)
    List<Map<String, Object>> getSexDistribution();

    /**
     * 获取年龄分布统计
     *
     * @return 年龄分布
     */
    @Cached(name = CACHE_NAME, key = "ageDistribution", cacheType = CacheType.LOCAL, expire = 300)
    List<Map<String, Object>> getAgeDistribution();

    /**
     * 验证持卡人是否可以删除
     * 检查是否有关联的用户银行信息或信用卡账户
     *
     * @param id 持卡人ID
     * @return 验证结果
     */
    Map<String, Object> validateCanDelete(String id);

    /**
     * 批量验证持卡人是否可以删除
     *
     * @param ids 持卡人ID列表
     * @return 验证结果
     */
    Map<String, Object> batchValidateCanDelete(List<String> ids);

    /**
     * 导入持卡人数据
     *
     * @param manList 持卡人数据列表
     * @return 导入结果
     */
    Map<String, Object> importManData(List<SysManEntity> manList);

    /**
     * 导出持卡人数据
     *
     * @param sysManPagination 查询条件
     * @return 导出数据
     */
    List<Map<String, Object>> exportManData(SysManPagination sysManPagination);

    /**
     * 复制持卡人信息
     *
     * @param id      源持卡人ID
     * @param newName 新持卡人代称
     * @return 复制的持卡人信息
     */
    SysManEntity copyMan(String id, String newName);

    /**
     * 获取持卡人详细信息（包含关联信息）
     *
     * @param id 持卡人ID
     * @return 详细信息
     */
    Map<String, Object> getManDetailWithRelated(String id);

    /**
     * 获取生日提醒列表
     *
     * @param days 提前天数
     * @return 生日提醒列表
     */
    List<SysManEntity> getBirthdayReminders(Integer days);

    /**
     * 获取本月生日的持卡人
     *
     * @return 本月生日列表
     */
    List<SysManEntity> getThisMonthBirthdays();

    /**
     * 获取今日生日的持卡人
     *
     * @return 今日生日列表
     */
    List<SysManEntity> getTodayBirthdays();

    /**
     * 根据性别获取持卡人列表
     *
     * @param sex 性别
     * @return 持卡人列表
     */
    List<SysManEntity> getListBySex(String sex);

    /**
     * 根据年龄范围获取持卡人列表
     *
     * @param minAge 最小年龄
     * @param maxAge 最大年龄
     * @return 持卡人列表
     */
    List<SysManEntity> getListByAgeRange(Integer minAge, Integer maxAge);

    /**
     * 计算持卡人年龄
     *
     * @param birthday 生日
     * @return 年龄
     */
    Integer calculateAge(Date birthday);

    /**
     * 批量更新排序序号
     *
     * @param orderList 排序列表
     * @return 更新成功的数量
     */
    int batchUpdateListOrder(List<Map<String, Object>> orderList);

    /**
     * 获取持卡人关联统计
     *
     * @param id 持卡人ID
     * @return 关联统计信息
     */
    Map<String, Object> getManRelatedStats(String id);
}
