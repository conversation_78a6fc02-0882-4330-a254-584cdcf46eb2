package com.xinghuo.card.report.controller;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.validation.constraints.NotNull;
import java.util.HashMap;
import java.util.Map;

/**
 * 完整报表查询请求参数
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "完整报表查询请求参数")
public class ReportCompleteRequest extends ReportQueryRequest {

    /**
     * 报表类型：1-收支统计，2-分类统计，3-趋势分析，4-卡片统计
     */
    @Schema(description = "报表类型：1-收支统计，2-分类统计，3-趋势分析，4-卡片统计")
    @NotNull(message = "报表类型不能为空")
    private Integer reportType;

    /**
     * 转换为查询条件Map
     */
    public Map<String, Object> toConditionMap() {
        Map<String, Object> conditions = new HashMap<>();
        conditions.put("startDate", this.getStartDate());
        conditions.put("endDate", this.getEndDate());
        conditions.put("accIds", this.getAccIds());
        conditions.put("flowTypeIds", this.getFlowTypeIds());
        conditions.put("transType", this.getTransType());
        conditions.put("keywords", this.getKeywords());
        conditions.put("minAmount", this.getMinAmount());
        conditions.put("maxAmount", this.getMaxAmount());
        conditions.put("groupBy", this.getGroupBy());
        return conditions;
    }
}
