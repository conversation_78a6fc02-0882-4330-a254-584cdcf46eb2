package com.xinghuo.card.report.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.xinghuo.card.flow.entity.DataFlowAccEntity;
import com.xinghuo.card.flow.service.DataFlowAccService;
import com.xinghuo.card.report.model.ReportStatisticsModel;
import com.xinghuo.card.report.service.FlowReportService;
import com.xinghuo.card.sys.entity.DataAccEntity;
import com.xinghuo.card.sys.entity.DataSysPaytypeEntity;
import com.xinghuo.card.sys.service.DataAccService;
import com.xinghuo.card.sys.service.DataSysPaytypeService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 流水报表统计服务实现类
 *
 * <AUTHOR>
 * @version V1.0.0
 * date 2024-01-01
 */
@Slf4j
@Service
public class FlowReportServiceImpl        implements FlowReportService {

    @Autowired
    private DataFlowAccService dataFlowAccService;

    @Autowired
    private DataAccService dataAccService;

    @Autowired
    private DataSysPaytypeService dataSysPaytypeService;

    /**
     * 获取所有分类信息（带缓存）
     */
    private Map<String, DataSysPaytypeEntity> getCategoryMap() {
        return dataSysPaytypeService.list().stream()
                .collect(Collectors.toMap(DataSysPaytypeEntity::getId, item -> item));
    }

    /**
     * 获取所有账户信息（带缓存）
     */
    private Map<String, DataAccEntity> getAccountMap() {
        return dataAccService.list().stream()
                .collect(Collectors.toMap(DataAccEntity::getId, item -> item));
    }

    @Override
    public ReportStatisticsModel.StatisticsSummary getStatisticsSummary(
            String userId, Date startDate, Date endDate, List<String> accIds,
            List<String> flowTypeIds, List<Integer> transType, String keywords,
            BigDecimal minAmount, BigDecimal maxAmount) {

        // 参数验证
        if (StringUtils.isBlank(userId)) {
            throw new IllegalArgumentException("用户ID不能为空");
        }

        log.debug("获取统计概要，用户ID：{}，开始日期：{}，结束日期：{}", userId, startDate, endDate);

        QueryWrapper<DataFlowAccEntity> queryWrapper = buildQueryWrapper(
                userId, startDate, endDate, accIds, flowTypeIds, transType, keywords, minAmount, maxAmount);

        List<DataFlowAccEntity> flowList = dataFlowAccService.list(queryWrapper);

        ReportStatisticsModel.StatisticsSummary summary = new ReportStatisticsModel.StatisticsSummary();
        
        BigDecimal totalIncome = BigDecimal.ZERO;
        BigDecimal totalExpense = BigDecimal.ZERO;
        BigDecimal maxAmountValue = BigDecimal.ZERO;
        BigDecimal minAmountValue = null;
        Set<String> accountSet = new HashSet<>();
        Set<String> categorySet = new HashSet<>();

        for (DataFlowAccEntity flow : flowList) {
            if (flow.getIncome() != null && flow.getIncome().compareTo(BigDecimal.ZERO) > 0) {
                totalIncome = totalIncome.add(flow.getIncome());
                if (flow.getIncome().compareTo(maxAmountValue) > 0) {
                    maxAmountValue = flow.getIncome();
                }
                if (minAmountValue == null || flow.getIncome().compareTo(minAmountValue) < 0) {
                    minAmountValue = flow.getIncome();
                }
            }
            if (flow.getPay() != null && flow.getPay().compareTo(BigDecimal.ZERO) > 0) {
                totalExpense = totalExpense.add(flow.getPay());
                if (flow.getPay().compareTo(maxAmountValue) > 0) {
                    maxAmountValue = flow.getPay();
                }
                if (minAmountValue == null || flow.getPay().compareTo(minAmountValue) < 0) {
                    minAmountValue = flow.getPay();
                }
            }
            
            accountSet.add(flow.getAccId());
            if (StringUtils.isNotBlank(flow.getType())) {
                categorySet.add(flow.getType());
            }
        }

        summary.setTotalIncome(totalIncome);
        summary.setTotalExpense(totalExpense);
        summary.setNetIncome(totalIncome.subtract(totalExpense));
        summary.setTransactionCount(flowList.size());
        summary.setMaxAmount(maxAmountValue);
        summary.setMinAmount(minAmountValue != null ? minAmountValue : BigDecimal.ZERO);
        summary.setAccountCount(accountSet.size());
        summary.setCategoryCount(categorySet.size());

        if (flowList.size() > 0) {
            BigDecimal totalAmount = totalIncome.add(totalExpense);
            summary.setAverageAmount(totalAmount.divide(new BigDecimal(flowList.size()), 2, RoundingMode.HALF_UP));
        } else {
            summary.setAverageAmount(BigDecimal.ZERO);
        }

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String dateRange = "";
        if (startDate != null && endDate != null) {
            dateRange = sdf.format(startDate) + " ~ " + sdf.format(endDate);
        } else if (startDate != null) {
            dateRange = "从 " + sdf.format(startDate) + " 开始";
        } else if (endDate != null) {
            dateRange = "到 " + sdf.format(endDate) + " 结束";
        } else {
            dateRange = "全部时间";
        }
        summary.setDateRange(dateRange);

        return summary;
    }

    @Override
    public List<ReportStatisticsModel.IncomeExpenseItem> getIncomeExpenseStatistics(
            String userId, Date startDate, Date endDate, String groupBy,
            List<String> accIds, List<String> flowTypeIds) {

        // 参数验证
        if (StringUtils.isBlank(userId)) {
            throw new IllegalArgumentException("用户ID不能为空");
        }
        if (StringUtils.isBlank(groupBy)) {
            groupBy = "month"; // 默认按月分组
        }

        log.debug("获取收支统计，用户ID：{}，分组方式：{}", userId, groupBy);

        QueryWrapper<DataFlowAccEntity> queryWrapper = buildQueryWrapper(
                userId, startDate, endDate, accIds, flowTypeIds, null, null, null, null);

        List<DataFlowAccEntity> flowList = dataFlowAccService.list(queryWrapper);

        Map<String, ReportStatisticsModel.IncomeExpenseItem> groupMap = new LinkedHashMap<>();
        SimpleDateFormat sdf;
        
        switch (groupBy) {
            case "day":
                sdf = new SimpleDateFormat("yyyy-MM-dd");
                break;
            case "month":
                sdf = new SimpleDateFormat("yyyy-MM");
                break;
            case "year":
                sdf = new SimpleDateFormat("yyyy");
                break;
            default:
                sdf = new SimpleDateFormat("yyyy-MM-dd");
        }

        for (DataFlowAccEntity flow : flowList) {
            if (flow.getFlowDate() == null) {
                log.warn("流水记录日期为空，跳过处理，流水ID：{}", flow.getId());
                continue;
            }
            String dateKey = sdf.format(flow.getFlowDate());
            
            ReportStatisticsModel.IncomeExpenseItem item = groupMap.computeIfAbsent(dateKey, k -> {
                ReportStatisticsModel.IncomeExpenseItem newItem = new ReportStatisticsModel.IncomeExpenseItem();
                newItem.setDate(k);
                newItem.setIncomeAmount(BigDecimal.ZERO);
                newItem.setExpenseAmount(BigDecimal.ZERO);
                newItem.setIncomeCount(0);
                newItem.setExpenseCount(0);
                return newItem;
            });

            if (flow.getIncome() != null && flow.getIncome().compareTo(BigDecimal.ZERO) > 0) {
                item.setIncomeAmount(item.getIncomeAmount().add(flow.getIncome()));
                item.setIncomeCount(item.getIncomeCount() + 1);
            }
            if (flow.getPay() != null && flow.getPay().compareTo(BigDecimal.ZERO) > 0) {
                item.setExpenseAmount(item.getExpenseAmount().add(flow.getPay()));
                item.setExpenseCount(item.getExpenseCount() + 1);
            }
        }

        // 计算净收入
        groupMap.values().forEach(item -> 
            item.setNetAmount(item.getIncomeAmount().subtract(item.getExpenseAmount())));

        return new ArrayList<>(groupMap.values());
    }

    @Override
    public List<ReportStatisticsModel.CategoryStatItem> getCategoryStatistics(
            String userId, Date startDate, Date endDate, List<Integer> transType,
            List<String> accIds, String keywords) {

        // 参数验证
        if (StringUtils.isBlank(userId)) {
            throw new IllegalArgumentException("用户ID不能为空");
        }

        log.debug("获取分类统计，用户ID：{}，交易类型：{}", userId, transType);

        QueryWrapper<DataFlowAccEntity> queryWrapper = buildQueryWrapper(
                userId, startDate, endDate, accIds, null, transType, keywords, null, null);

        List<DataFlowAccEntity> flowList = dataFlowAccService.list(queryWrapper);

        // 获取所有分类信息
        Map<String, DataSysPaytypeEntity> categoryMap = getCategoryMap();

        Map<String, ReportStatisticsModel.CategoryStatItem> categoryStatMap = new HashMap<>();
        BigDecimal totalAmount = BigDecimal.ZERO;

        for (DataFlowAccEntity flow : flowList) {
            if (StringUtils.isBlank(flow.getType())) continue;

            DataSysPaytypeEntity category = categoryMap.get(flow.getType());
            if (category == null) continue;

            String categoryId = category.getId();
            ReportStatisticsModel.CategoryStatItem item = categoryStatMap.computeIfAbsent(categoryId, k -> {
                ReportStatisticsModel.CategoryStatItem newItem = new ReportStatisticsModel.CategoryStatItem();
                newItem.setCategoryId(categoryId);
                newItem.setCategoryName(category.getName());
                newItem.setTotalAmount(BigDecimal.ZERO);
                newItem.setTransactionCount(0);
                newItem.setMaxAmount(BigDecimal.ZERO);
                newItem.setMinAmount(null);
                
                // 设置父分类
                if (StringUtils.isNotBlank(category.getParentId())) {
                    DataSysPaytypeEntity parentCategory = categoryMap.get(category.getParentId());
                    if (parentCategory != null) {
                        newItem.setParentCategoryName(parentCategory.getName());
                    }
                }
                
                return newItem;
            });

            BigDecimal amount = BigDecimal.ZERO;
            if (flow.getIncome() != null && flow.getIncome().compareTo(BigDecimal.ZERO) > 0) {
                amount = flow.getIncome();
                item.setTransType(1);
            } else if (flow.getPay() != null && flow.getPay().compareTo(BigDecimal.ZERO) > 0) {
                amount = flow.getPay();
                item.setTransType(2);
            }

            if (amount.compareTo(BigDecimal.ZERO) > 0) {
                item.setTotalAmount(item.getTotalAmount().add(amount));
                item.setTransactionCount(item.getTransactionCount() + 1);
                totalAmount = totalAmount.add(amount);

                if (amount.compareTo(item.getMaxAmount()) > 0) {
                    item.setMaxAmount(amount);
                }
                if (item.getMinAmount() == null || amount.compareTo(item.getMinAmount()) < 0) {
                    item.setMinAmount(amount);
                }
            }
        }

        // 计算平均金额和占比
        List<ReportStatisticsModel.CategoryStatItem> result = new ArrayList<>(categoryStatMap.values());
        for (ReportStatisticsModel.CategoryStatItem item : result) {
            if (item.getTransactionCount() > 0) {
                item.setAverageAmount(item.getTotalAmount().divide(
                        new BigDecimal(item.getTransactionCount()), 2, RoundingMode.HALF_UP));
            }
            if (totalAmount.compareTo(BigDecimal.ZERO) > 0) {
                item.setPercentage(item.getTotalAmount().divide(totalAmount, 4, RoundingMode.HALF_UP)
                        .multiply(new BigDecimal(100)));
            }
        }

        // 按金额降序排序
        result.sort((a, b) -> b.getTotalAmount().compareTo(a.getTotalAmount()));

        return result;
    }

    @Override
    public List<ReportStatisticsModel.TrendAnalysisItem> getTrendAnalysis(
            String userId, Date startDate, Date endDate, String groupBy,
            List<String> accIds, List<Integer> transType) {

        List<ReportStatisticsModel.IncomeExpenseItem> incomeExpenseList = 
                getIncomeExpenseStatistics(userId, startDate, endDate, groupBy, accIds, null);

        List<ReportStatisticsModel.TrendAnalysisItem> result = new ArrayList<>();
        BigDecimal cumulativeIncome = BigDecimal.ZERO;
        BigDecimal cumulativeExpense = BigDecimal.ZERO;
        ReportStatisticsModel.IncomeExpenseItem previousItem = null;

        for (ReportStatisticsModel.IncomeExpenseItem item : incomeExpenseList) {
            cumulativeIncome = cumulativeIncome.add(item.getIncomeAmount());
            cumulativeExpense = cumulativeExpense.add(item.getExpenseAmount());

            ReportStatisticsModel.TrendAnalysisItem trendItem = new ReportStatisticsModel.TrendAnalysisItem();
            trendItem.setTimePoint(item.getDate());
            trendItem.setCumulativeIncome(cumulativeIncome);
            trendItem.setCumulativeExpense(cumulativeExpense);
            trendItem.setCumulativeNet(cumulativeIncome.subtract(cumulativeExpense));
            trendItem.setCurrentIncome(item.getIncomeAmount());
            trendItem.setCurrentExpense(item.getExpenseAmount());
            trendItem.setCurrentNet(item.getNetAmount());

            // 计算环比增长率
            if (previousItem != null) {
                if (previousItem.getIncomeAmount().compareTo(BigDecimal.ZERO) > 0) {
                    BigDecimal incomeGrowth = item.getIncomeAmount().subtract(previousItem.getIncomeAmount())
                            .divide(previousItem.getIncomeAmount(), 4, RoundingMode.HALF_UP)
                            .multiply(new BigDecimal(100));
                    trendItem.setIncomeGrowthRate(incomeGrowth);
                }
                if (previousItem.getExpenseAmount().compareTo(BigDecimal.ZERO) > 0) {
                    BigDecimal expenseGrowth = item.getExpenseAmount().subtract(previousItem.getExpenseAmount())
                            .divide(previousItem.getExpenseAmount(), 4, RoundingMode.HALF_UP)
                            .multiply(new BigDecimal(100));
                    trendItem.setExpenseGrowthRate(expenseGrowth);
                }
            }

            result.add(trendItem);
            previousItem = item;
        }

        return result;
    }

    @Override
    public List<ReportStatisticsModel.CardStatItem> getCardStatistics(
            String userId, Date startDate, Date endDate, List<String> accIds) {

        QueryWrapper<DataFlowAccEntity> queryWrapper = buildQueryWrapper(
                userId, startDate, endDate, accIds, null, null, null, null, null);

        List<DataFlowAccEntity> flowList = dataFlowAccService.list(queryWrapper);

        // 获取账户信息
        Map<String, DataAccEntity> accMap = getAccountMap();

        Map<String, ReportStatisticsModel.CardStatItem> cardStatMap = new HashMap<>();

        for (DataFlowAccEntity flow : flowList) {
            String accId = flow.getAccId();
            DataAccEntity acc = accMap.get(accId);
            if (acc == null) continue;

            ReportStatisticsModel.CardStatItem item = cardStatMap.computeIfAbsent(accId, k -> {
                ReportStatisticsModel.CardStatItem newItem = new ReportStatisticsModel.CardStatItem();
                newItem.setAccId(accId);
                newItem.setAccName(acc.getName());
                newItem.setAccType(acc.getType());
                newItem.setIncomeAmount(BigDecimal.ZERO);
                newItem.setExpenseAmount(BigDecimal.ZERO);
                newItem.setTransactionCount(0);
                newItem.setCurrentBalance(acc.getBalance());
                return newItem;
            });

            if (flow.getIncome() != null && flow.getIncome().compareTo(BigDecimal.ZERO) > 0) {
                item.setIncomeAmount(item.getIncomeAmount().add(flow.getIncome()));
            }
            if (flow.getPay() != null && flow.getPay().compareTo(BigDecimal.ZERO) > 0) {
                item.setExpenseAmount(item.getExpenseAmount().add(flow.getPay()));
            }
            
            item.setTransactionCount(item.getTransactionCount() + 1);
            
            if (item.getLastTransactionTime() == null || 
                flow.getFlowDate().after(item.getLastTransactionTime())) {
                item.setLastTransactionTime(flow.getFlowDate());
            }
        }

        // 计算净收入
        List<ReportStatisticsModel.CardStatItem> result = new ArrayList<>(cardStatMap.values());
        result.forEach(item -> 
            item.setNetAmount(item.getIncomeAmount().subtract(item.getExpenseAmount())));

        // 按交易笔数降序排序
        result.sort((a, b) -> b.getTransactionCount().compareTo(a.getTransactionCount()));

        return result;
    }

    @Override
    public List<ReportStatisticsModel.FlowDetailItem> getFlowDetails(
            String userId, Date startDate, Date endDate, List<String> accIds,
            List<String> flowTypeIds, List<Integer> transType, String keywords,
            BigDecimal minAmount, BigDecimal maxAmount, int pageNum, int pageSize) {

        // 参数验证
        if (StringUtils.isBlank(userId)) {
            throw new IllegalArgumentException("用户ID不能为空");
        }
        if (pageNum <= 0) {
            pageNum = 1;
        }
        if (pageSize <= 0 || pageSize > 1000) {
            pageSize = 20; // 默认每页20条，最大1000条
        }

        log.debug("获取流水明细，用户ID：{}，页码：{}，页大小：{}", userId, pageNum, pageSize);

        QueryWrapper<DataFlowAccEntity> queryWrapper = buildQueryWrapper(
                userId, startDate, endDate, accIds, flowTypeIds, transType, keywords, minAmount, maxAmount);

        queryWrapper.orderByDesc("FLOW_DATE");

        // 使用安全的分页方式
        int offset = (pageNum - 1) * pageSize;
        queryWrapper.last("LIMIT " + offset + ", " + pageSize);

        List<DataFlowAccEntity> flowList = dataFlowAccService.list(queryWrapper);

        // 获取账户和分类信息
        Map<String, DataAccEntity> accMap = getAccountMap();
        Map<String, DataSysPaytypeEntity> categoryMap = getCategoryMap();

        List<ReportStatisticsModel.FlowDetailItem> result = new ArrayList<>();

        for (DataFlowAccEntity flow : flowList) {
            ReportStatisticsModel.FlowDetailItem item = new ReportStatisticsModel.FlowDetailItem();
            item.setFlowId(flow.getId());
            item.setFlowDate(flow.getFlowDate());
            item.setBalance(flow.getBalance());
            item.setNote(flow.getNote());

            // 设置账户信息
            DataAccEntity acc = accMap.get(flow.getAccId());
            if (acc != null) {
                item.setAccName(acc.getName());
            }

            // 设置分类信息
            if (StringUtils.isNotBlank(flow.getType())) {
                DataSysPaytypeEntity category = categoryMap.get(flow.getType());
                if (category != null) {
                    item.setCategoryName(category.getName());
                }
            }

            // 设置交易类型和金额
            if (flow.getIncome() != null && flow.getIncome().compareTo(BigDecimal.ZERO) > 0) {
                item.setTransType(1);
                item.setAmount(flow.getIncome());
            } else if (flow.getPay() != null && flow.getPay().compareTo(BigDecimal.ZERO) > 0) {
                item.setTransType(2);
                item.setAmount(flow.getPay());
            } else {
                item.setTransType(3);
                // 转账时设置转入转出账户
                if (StringUtils.isNotBlank(flow.getAccId())) {
                    DataAccEntity inAcc = accMap.get(flow.getAccId());
                    if (inAcc != null) {
                        item.setInAccName(inAcc.getName());
                    }
                }
                if (StringUtils.isNotBlank(flow.getOutAccId())) {
                    DataAccEntity outAcc = accMap.get(flow.getOutAccId());
                    if (outAcc != null) {
                        item.setOutAccName(outAcc.getName());
                    }
                }
            }

            result.add(item);
        }

        return result;
    }

    @Override
    public ReportStatisticsModel getCompleteReport(String userId, Integer reportType, Map<String, Object> conditions) {
        // 解析查询条件
        Date startDate = (Date) conditions.get("startDate");
        Date endDate = (Date) conditions.get("endDate");
        List<String> accIds = (List<String>) conditions.get("accIds");
        List<String> flowTypeIds = (List<String>) conditions.get("flowTypeIds");
        List<Integer> transType = (List<Integer>) conditions.get("transType");
        String keywords = (String) conditions.get("keywords");
        BigDecimal minAmount = (BigDecimal) conditions.get("minAmount");
        BigDecimal maxAmount = (BigDecimal) conditions.get("maxAmount");
        String groupBy = (String) conditions.getOrDefault("groupBy", "month");

        ReportStatisticsModel report = new ReportStatisticsModel();

        // 获取统计概要
        report.setSummary(getStatisticsSummary(userId, startDate, endDate, accIds, 
                flowTypeIds, transType, keywords, minAmount, maxAmount));

        // 根据报表类型获取相应数据
        switch (reportType) {
            case 1: // 收支统计
                report.setIncomeExpenseList(getIncomeExpenseStatistics(userId, startDate, endDate, groupBy, accIds, flowTypeIds));
                break;
            case 2: // 分类统计
                report.setCategoryStatList(getCategoryStatistics(userId, startDate, endDate, transType, accIds, keywords));
                break;
            case 3: // 趋势分析
                report.setTrendAnalysisList(getTrendAnalysis(userId, startDate, endDate, groupBy, accIds, transType));
                break;
            case 4: // 卡片统计
                report.setCardStatList(getCardStatistics(userId, startDate, endDate, accIds));
                break;
            default:
                // 获取所有类型数据
                report.setIncomeExpenseList(getIncomeExpenseStatistics(userId, startDate, endDate, groupBy, accIds, flowTypeIds));
                report.setCategoryStatList(getCategoryStatistics(userId, startDate, endDate, transType, accIds, keywords));
                report.setTrendAnalysisList(getTrendAnalysis(userId, startDate, endDate, groupBy, accIds, transType));
                report.setCardStatList(getCardStatistics(userId, startDate, endDate, accIds));
        }

        return report;
    }

    @Override
    public String exportReport(String userId, Integer reportType, Map<String, Object> conditions, String exportFormat) {
        try {
            log.info("开始导出报表，用户ID：{}，报表类型：{}，导出格式：{}", userId, reportType, exportFormat);

            // 获取完整报表数据
            ReportStatisticsModel report = getCompleteReport(userId, reportType, conditions);

            // 根据导出格式处理
            switch (exportFormat.toLowerCase()) {
                case "excel":
                case "xlsx":
                    return exportToExcel(report, reportType, conditions);
                case "csv":
                    return exportToCsv(report, reportType, conditions);
                case "pdf":
                    return exportToPdf(report, reportType, conditions);
                default:
                    log.warn("不支持的导出格式：{}", exportFormat);
                    throw new IllegalArgumentException("不支持的导出格式：" + exportFormat);
            }
        } catch (Exception e) {
            log.error("导出报表失败，用户ID：{}，报表类型：{}", userId, reportType, e);
            throw new RuntimeException("导出报表失败：" + e.getMessage(), e);
        }
    }

    /**
     * 导出为Excel格式
     */
    private String exportToExcel(ReportStatisticsModel report, Integer reportType, Map<String, Object> conditions) {
        // TODO: 实现Excel导出逻辑
        // 可以使用EasyExcel或Apache POI
        log.info("Excel导出功能待实现");
        throw new UnsupportedOperationException("Excel导出功能正在开发中");
    }

    /**
     * 导出为CSV格式
     */
    private String exportToCsv(ReportStatisticsModel report, Integer reportType, Map<String, Object> conditions) {
        // TODO: 实现CSV导出逻辑
        log.info("CSV导出功能待实现");
        throw new UnsupportedOperationException("CSV导出功能正在开发中");
    }

    /**
     * 导出为PDF格式
     */
    private String exportToPdf(ReportStatisticsModel report, Integer reportType, Map<String, Object> conditions) {
        // TODO: 实现PDF导出逻辑
        log.info("PDF导出功能待实现");
        throw new UnsupportedOperationException("PDF导出功能正在开发中");
    }

    @Override
    public Map<String, Object> getQuickStatistics(String userId) {
        Map<String, Object> result = new HashMap<>();
        
        // 获取本月统计
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.DAY_OF_MONTH, 1);
        Date monthStart = cal.getTime();
        cal.add(Calendar.MONTH, 1);
        cal.add(Calendar.DAY_OF_MONTH, -1);
        Date monthEnd = cal.getTime();

        ReportStatisticsModel.StatisticsSummary monthSummary = getStatisticsSummary(
                userId, monthStart, monthEnd, null, null, null, null, null, null);
        result.put("monthSummary", monthSummary);

        // 获取本年统计
        cal = Calendar.getInstance();
        cal.set(Calendar.MONTH, 0);
        cal.set(Calendar.DAY_OF_MONTH, 1);
        Date yearStart = cal.getTime();
        cal.set(Calendar.MONTH, 11);
        cal.set(Calendar.DAY_OF_MONTH, 31);
        Date yearEnd = cal.getTime();

        ReportStatisticsModel.StatisticsSummary yearSummary = getStatisticsSummary(
                userId, yearStart, yearEnd, null, null, null, null, null, null);
        result.put("yearSummary", yearSummary);

        return result;
    }

    @Override
    public List<ReportStatisticsModel.CategoryStatItem> getTopCategories(
            String userId, Date startDate, Date endDate, List<Integer> transType, int limit) {
        
        List<ReportStatisticsModel.CategoryStatItem> categoryStats = 
                getCategoryStatistics(userId, startDate, endDate, transType, null, null);
        
        return categoryStats.stream().limit(limit).collect(Collectors.toList());
    }

    @Override
    public List<ReportStatisticsModel.IncomeExpenseItem> getMonthlyComparison(
            String userId, Date currentMonth, int compareMonths) {
        
        List<ReportStatisticsModel.IncomeExpenseItem> result = new ArrayList<>();
        Calendar cal = Calendar.getInstance();
        cal.setTime(currentMonth);

        for (int i = 0; i < compareMonths; i++) {
            cal.set(Calendar.DAY_OF_MONTH, 1);
            Date monthStart = cal.getTime();
            cal.set(Calendar.DAY_OF_MONTH, cal.getActualMaximum(Calendar.DAY_OF_MONTH));
            Date monthEnd = cal.getTime();

            List<ReportStatisticsModel.IncomeExpenseItem> monthData = 
                    getIncomeExpenseStatistics(userId, monthStart, monthEnd, "month", null, null);
            
            result.addAll(monthData);
            cal.add(Calendar.MONTH, -1);
        }

        return result;
    }

    /**
     * 构建查询条件
     */
    private QueryWrapper<DataFlowAccEntity> buildQueryWrapper(
            String userId, Date startDate, Date endDate, List<String> accIds,
            List<String> flowTypeIds, List<Integer> transType, String keywords,
            BigDecimal minAmount, BigDecimal maxAmount) {

        QueryWrapper<DataFlowAccEntity> queryWrapper = new QueryWrapper<>();

        // 用户ID过滤 - 通过账户表关联过滤
//        if (StringUtils.isNotBlank(userId)) {
//            queryWrapper.inSql("ACC_ID", "SELECT ID FROM data_acc WHERE USER_ID = '" + userId + "'");
//        }

        if (startDate != null) {
            queryWrapper.lambda().ge(DataFlowAccEntity::getFlowDate, startDate);
        }
        if (endDate != null) {
            queryWrapper.lambda().le(DataFlowAccEntity::getFlowDate, endDate);
        }
        if (accIds != null && !accIds.isEmpty()) {
            queryWrapper.lambda().in(DataFlowAccEntity::getAccId, accIds);
        }
        if (flowTypeIds != null && !flowTypeIds.isEmpty()) {
            queryWrapper.lambda().in(DataFlowAccEntity::getType, flowTypeIds);
        }
        if (StringUtils.isNotBlank(keywords)) {
            queryWrapper.lambda().like(DataFlowAccEntity::getNote, keywords);
        }

        // 交易类型过滤 - 支持多选
        if (transType != null && !transType.isEmpty()) {
            queryWrapper.and(wrapper -> {
                for (int i = 0; i < transType.size(); i++) {
                    Integer type = transType.get(i);
                    if (i > 0) {
                        wrapper.or();
                    }

                    switch (type) {
                        case 1: // 收入
                            wrapper.isNotNull("INCOME").gt("INCOME", BigDecimal.ZERO);
                            break;
                        case 2: // 支出
                            wrapper.isNotNull("PAY").gt("PAY", BigDecimal.ZERO);
                            break;
                        case 3: // 转账 - 根据实际业务逻辑，转账可能体现为收入或支出
                            wrapper.and(w -> w.isNotNull("OUT_ACC_ID").or().isNotNull("CC_ID"));
                            break;
                    }
                }
            });
        }

        // 金额范围过滤
        if (minAmount != null || maxAmount != null) {
            queryWrapper.and(wrapper -> {
                if (minAmount != null && maxAmount != null) {
                    wrapper.or(w -> w.between("INCOME", minAmount, maxAmount))
                           .or(w -> w.between("PAY", minAmount, maxAmount));
                } else if (minAmount != null) {
                    wrapper.or(w -> w.ge("INCOME", minAmount))
                           .or(w -> w.ge("PAY", minAmount));
                } else {
                    wrapper.or(w -> w.le("INCOME", maxAmount))
                           .or(w -> w.le("PAY", maxAmount));
                }
            });
        }

        return queryWrapper;
    }
}
