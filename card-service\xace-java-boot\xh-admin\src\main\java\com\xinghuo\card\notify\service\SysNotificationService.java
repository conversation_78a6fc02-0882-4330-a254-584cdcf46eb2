package com.xinghuo.card.notify.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xinghuo.card.notify.entity.SysNotificationEntity;
import com.xinghuo.admin.model.dto.NotificationCreateDTO;
import com.xinghuo.admin.model.dto.NotificationQueryDTO;
import com.xinghuo.common.base.service.BaseService;

import java.util.List;
import java.util.Map;

/**
 * 系统通知服务接口
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 * @date 2024-06-28
 */
public interface SysNotificationService extends BaseService<SysNotificationEntity> {

    /**
     * 创建通知
     * 
     * @param createDTO 创建通知DTO
     * @return 通知ID
     */
    String createNotification(NotificationCreateDTO createDTO);

    /**
     * 分页查询通知列表
     * 
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    IPage<SysNotificationEntity> getNotificationPage(NotificationQueryDTO queryDTO);

    /**
     * 获取用户未读通知数量
     * 
     * @param userId 用户ID
     * @return 未读通知数量
     */
    Long getUnreadCount(String userId);

    /**
     * 获取用户各类型未读通知数量
     * 
     * @param userId 用户ID
     * @return 各类型未读通知数量统计
     */
    Map<String, Long> getUnreadCountByType(String userId);

    /**
     * 标记通知为已读
     * 
     * @param userId 用户ID
     * @param notificationId 通知ID
     * @return 是否成功
     */
    boolean markAsRead(String userId, String notificationId);

    /**
     * 批量标记通知为已读
     * 
     * @param userId 用户ID
     * @param notificationIds 通知ID列表
     * @return 成功标记的数量
     */
    int batchMarkAsRead(String userId, List<String> notificationIds);

    /**
     * 标记所有通知为已读
     * 
     * @param userId 用户ID
     * @return 成功标记的数量
     */
    int markAllAsRead(String userId);

    /**
     * 删除通知
     * 
     * @param userId 用户ID
     * @param notificationId 通知ID
     * @return 是否成功
     */
    boolean deleteNotification(String userId, String notificationId);

    /**
     * 批量删除通知
     * 
     * @param userId 用户ID
     * @param notificationIds 通知ID列表
     * @return 成功删除的数量
     */
    int batchDeleteNotifications(String userId, List<String> notificationIds);

    /**
     * 获取用户最新通知
     * 
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 最新通知列表
     */
    List<SysNotificationEntity> getLatestNotifications(String userId, Integer limit);

    /**
     * 根据关联业务查询通知
     * 
     * @param relatedId 关联业务ID
     * @param relatedType 关联业务类型
     * @return 通知列表
     */
    List<SysNotificationEntity> getNotificationsByRelated(String relatedId, String relatedType);

    /**
     * 获取用户通知统计概况
     * 
     * @param userId 用户ID
     * @return 通知统计信息
     */
    Map<String, Object> getNotificationSummary(String userId);

    /**
     * 清理过期通知
     * 
     * @return 清理的数量
     */
    int cleanExpiredNotifications();

    /**
     * 推送通知到移动设备
     * 
     * @param notificationId 通知ID
     * @return 是否成功
     */
    boolean pushNotification(String notificationId);

    /**
     * 批量推送通知
     * 
     * @param notificationIds 通知ID列表
     * @return 成功推送的数量
     */
    int batchPushNotifications(List<String> notificationIds);

    /**
     * 创建账单还款提醒
     * 
     * @param userId 用户ID
     * @param billId 账单ID
     * @param cardName 卡片名称
     * @param amount 还款金额
     * @param repayDate 还款日期
     * @return 通知ID
     */
    String createBillRepayReminder(String userId, String billId, String cardName, Double amount, String repayDate);

    /**
     * 创建预算提醒
     * 
     * @param userId 用户ID
     * @param budgetId 预算ID
     * @param budgetName 预算名称
     * @param percentage 使用百分比
     * @param spentAmount 已支出金额
     * @param remainingAmount 剩余金额
     * @return 通知ID
     */
    String createBudgetAlert(String userId, String budgetId, String budgetName, Double percentage, Double spentAmount, Double remainingAmount);

    /**
     * 创建卡片过期提醒
     * 
     * @param userId 用户ID
     * @param cardId 卡片ID
     * @param cardName 卡片名称
     * @param expireDate 过期日期
     * @return 通知ID
     */
    String createCardExpireReminder(String userId, String cardId, String cardName, String expireDate);

    /**
     * 创建系统通知
     * 
     * @param userId 用户ID
     * @param title 标题
     * @param content 内容
     * @param priority 优先级
     * @return 通知ID
     */
    String createSystemNotification(String userId, String title, String content, String priority);
}
