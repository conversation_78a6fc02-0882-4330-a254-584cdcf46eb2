package com.xinghuo.card.budget.model.budget;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 预算视图对象
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2024-06-28
 */
@Data
public class FinBudgetVO {

    /**
     * 主键ID
     */
    private String id;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 预算名称
     */
    private String name;

    /**
     * 周期类型：MONTHLY-月度, YEARLY-年度
     */
    private String periodType;

    /**
     * 周期类型名称
     */
    private String periodTypeName;

    /**
     * 开始日期
     */
    private Date startDate;

    /**
     * 结束日期
     */
    private Date endDate;

    /**
     * 总预算金额
     */
    private BigDecimal totalBudgetAmount;

    /**
     * 总已支出金额
     */
    private BigDecimal totalSpentAmount;

    /**
     * 剩余金额
     */
    private BigDecimal remainingAmount;

    /**
     * 支出百分比
     */
    private BigDecimal spentPercentage;

    /**
     * 状态：ACTIVE-激活, ARCHIVED-归档
     */
    private String status;

    /**
     * 状态名称
     */
    private String statusName;

    /**
     * 预算子项数量
     */
    private Integer itemCount;

    /**
     * 超支项目数量
     */
    private Integer overBudgetCount;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}