package com.xinghuo.card.flow.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xinghuo.card.flow.entity.DataFlowOrderEntity;
import com.xinghuo.card.flow.model.datafloworder.DataFlowOrderPagination;

import java.util.List;

/**
 * 订单管理服务接口
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2022-11-27
 */
public interface DataFlowOrderService extends IService<DataFlowOrderEntity> {

    /**
     * 查询订单分页列表
     *
     * @param dataFlowOrderPagination 分页查询参数
     * @return 订单列表
     */
    List<DataFlowOrderEntity> getList(DataFlowOrderPagination dataFlowOrderPagination);

    /**
     * 查询订单列表（支持分页和不分页）
     *
     * @param dataFlowOrderPagination 查询参数
     * @param dataType                数据类型：0-分页，1-不分页
     * @return 订单列表
     */
    List<DataFlowOrderEntity> getTypeList(DataFlowOrderPagination dataFlowOrderPagination, int dataType);

    /**
     * 根据ID获取订单详细信息
     *
     * @param orderid 订单ID
     * @return 订单实体
     */
    DataFlowOrderEntity getInfo(String orderid);

    /**
     * 删除订单
     *
     * @param entity 要删除的订单实体
     */
    void delete(DataFlowOrderEntity entity);

    /**
     * 创建订单
     *
     * @param entity 订单实体
     */
    void create(DataFlowOrderEntity entity);

    /**
     * 更新订单
     *
     * @param orderid 订单ID
     * @param entity  更新的订单实体
     * @return 更新是否成功
     */
    boolean update(String orderid, DataFlowOrderEntity entity);


}
