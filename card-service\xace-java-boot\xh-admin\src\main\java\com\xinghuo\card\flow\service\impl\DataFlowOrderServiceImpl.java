package com.xinghuo.card.flow.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xinghuo.card.flow.dao.DataFlowOrderMapper;
import com.xinghuo.card.flow.entity.DataFlowOrderEntity;
import com.xinghuo.card.flow.model.datafloworder.DataFlowOrderPagination;
import com.xinghuo.card.flow.service.DataFlowOrderService;
import com.xinghuo.common.util.UserProvider;
import com.xinghuo.common.util.core.DateXhUtil;
import com.xinghuo.common.util.core.StrXhUtil;
import com.xinghuo.common.util.extra.ServletUtil;
import com.xinghuo.permission.model.authorize.AuthorizeConditionModel;
import com.xinghuo.permission.service.AuthorizeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 订单管理服务实现类
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2022-11-27
 */
@Service
public class DataFlowOrderServiceImpl extends ServiceImpl<DataFlowOrderMapper, DataFlowOrderEntity> implements DataFlowOrderService {

    @Autowired
    private UserProvider userProvider;

    @Autowired
    private AuthorizeService authorizeService;

    @Override
    public List<DataFlowOrderEntity> getList(DataFlowOrderPagination dataFlowOrderPagination) {
        return getListByType(dataFlowOrderPagination, 0);
    }

    @Override
    public List<DataFlowOrderEntity> getTypeList(DataFlowOrderPagination dataFlowOrderPagination, int dataType) {
        return getListByType(dataFlowOrderPagination, dataType);
    }

    private List<DataFlowOrderEntity> getListByType(DataFlowOrderPagination dataFlowOrderPagination, int dataType) {
        List<String> allIdList = new ArrayList();
        int total = 0;
        int dataFlowOrderNum = 0;
        QueryWrapper<DataFlowOrderEntity> dataFlowOrderQueryWrapper = new QueryWrapper<>();
        boolean pcPermission = false;
        boolean appPermission = false;
        boolean isPc = "pc".equals(ServletUtil.getHeader("xh-origin"));
        if (isPc && pcPermission) {
            if (!userProvider.get().getIsAdministrator()) {
                Object dataFlowOrderObj = authorizeService.getCondition(new AuthorizeConditionModel(dataFlowOrderQueryWrapper, dataFlowOrderPagination.getMenuId(), "data_flow_order"));
                if (ObjectUtil.isEmpty(dataFlowOrderObj)) {
                    return new ArrayList<>();
                } else {
                    dataFlowOrderQueryWrapper = (QueryWrapper<DataFlowOrderEntity>) dataFlowOrderObj;
                    dataFlowOrderNum++;
                }
            }
        }
        if (!isPc && appPermission) {
            if (!userProvider.get().getIsAdministrator()) {
                Object dataFlowOrderObj = authorizeService.getCondition(new AuthorizeConditionModel(dataFlowOrderQueryWrapper, dataFlowOrderPagination.getMenuId(), "data_flow_order"));
                if (ObjectUtil.isEmpty(dataFlowOrderObj)) {
                    return new ArrayList<>();
                } else {
                    dataFlowOrderQueryWrapper = (QueryWrapper<DataFlowOrderEntity>) dataFlowOrderObj;
                    dataFlowOrderNum++;
                }
            }
        }
        if (StrXhUtil.isNotEmpty(dataFlowOrderPagination.getAccId())) {
            dataFlowOrderNum++;
            dataFlowOrderQueryWrapper.lambda().eq(DataFlowOrderEntity::getAccId, dataFlowOrderPagination.getAccId());
        }
        if (CollUtil.isNotEmpty(dataFlowOrderPagination.getOrderDate())) {
            dataFlowOrderNum++;
            List<String> OrderDateList = dataFlowOrderPagination.getOrderDate();
            Long fir = Long.valueOf(OrderDateList.get(0));
            Long sec = Long.valueOf(OrderDateList.get(1));
            dataFlowOrderQueryWrapper.lambda().ge(DataFlowOrderEntity::getOrderDate, new Date(fir))
                    .le(DataFlowOrderEntity::getOrderDate, DateXhUtil.endOfDay(sec) );
        }
        if (StrXhUtil.isNotEmpty(dataFlowOrderPagination.getFormStatus())) {
            dataFlowOrderNum++;
            dataFlowOrderQueryWrapper.lambda().eq(DataFlowOrderEntity::getFormStatus, dataFlowOrderPagination.getFormStatus());
        }
        if (StrXhUtil.isNotEmpty(dataFlowOrderPagination.getOrderNo())) {
            dataFlowOrderNum++;
            dataFlowOrderQueryWrapper.lambda().like(DataFlowOrderEntity::getOrderNo, dataFlowOrderPagination.getOrderNo());
        }
        if (allIdList.size() > 0) {
            dataFlowOrderQueryWrapper.lambda().in(DataFlowOrderEntity::getOrderId, allIdList);
        }
        //排序
        if (StrXhUtil.isEmpty(dataFlowOrderPagination.getSidx())) {
            dataFlowOrderQueryWrapper.lambda().orderByDesc(DataFlowOrderEntity::getOrderId);
        } else {
            try {
                DataFlowOrderEntity dataFlowOrderEntity = new DataFlowOrderEntity();
                Field declaredField = dataFlowOrderEntity.getClass().getDeclaredField(dataFlowOrderPagination.getSidx());
                declaredField.setAccessible(true);
                String value = declaredField.getAnnotation(TableField.class).value();
                dataFlowOrderQueryWrapper = "asc".equals(dataFlowOrderPagination.getSort().toLowerCase()) ? dataFlowOrderQueryWrapper.orderByAsc(value) : dataFlowOrderQueryWrapper.orderByDesc(value);
            } catch (NoSuchFieldException e) {
                e.printStackTrace();
            }
        }

            return this.list(dataFlowOrderQueryWrapper);
    }

    @Override
    public DataFlowOrderEntity getInfo(String orderId) {
        QueryWrapper<DataFlowOrderEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(DataFlowOrderEntity::getOrderId, orderId);
        return this.getOne(queryWrapper);
    }

    @Override
    public void create(DataFlowOrderEntity entity) {
        this.save(entity);
    }

    @Override
    public boolean update(String orderId, DataFlowOrderEntity entity) {
        entity.setOrderId(orderId);
        return this.updateById(entity);
    }

    @Override
    public void delete(DataFlowOrderEntity entity) {
        if (entity != null) {
            this.removeById(entity.getOrderId());
        }
    }


}
