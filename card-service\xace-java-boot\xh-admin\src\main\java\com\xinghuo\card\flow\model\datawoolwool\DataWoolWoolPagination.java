package com.xinghuo.card.flow.model.datawoolwool;

import com.xinghuo.common.base.model.Pagination;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 羊毛记录表
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2022-12-12
 */
@Data
public class DataWoolWoolPagination extends Pagination {

    @Schema(description = "活动账户 ")
    private String srcAccId;

    @Schema(description = "活动名称 ")
    private String acitivity;

    @Schema(description = "物品名称 ")
    private String goods;

    @Schema(description = "菜单id")
    private String menuId;
}
