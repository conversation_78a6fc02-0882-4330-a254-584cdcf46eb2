#set($name = "${context.className.substring(0,1).toLowerCase()}${context.className.substring(1)}")
#set($pKeyName =${context.pKeyName})

#set($peimaryKeyName = "${pKeyName.substring(0,1).toUpperCase()}${pKeyName.substring(1)}")
#set($peimaryKeyname = "${pKeyName.substring(0,1).toLowerCase()}${pKeyName.substring(1)}")

package ${context.package}.model.$!{name.toLowerCase()};

import lombok.Data;
import com.alibaba.fastjson.annotation.JSONField;
import cn.afterturn.easypoi.excel.annotation.Excel;
/**
 *
 * ${context.genInfo.description}
 * @版本： ${context.genInfo.version}
 * @版权： ${context.genInfo.copyright}
 * @作者： ${context.genInfo.createUser}
 * @日期： ${context.genInfo.createDate}
 */
@Data
public class $!{context.className}ExcelErrorVO extends $!{context.className}ExcelVO{

	@Excel(name = "异常原因",orderNum = "1")
    @JSONField(name = "errorsInfo")
	private String errorsInfo;
}