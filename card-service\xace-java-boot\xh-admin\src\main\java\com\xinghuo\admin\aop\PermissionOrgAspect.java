package com.xinghuo.admin.aop;

import com.xinghuo.admin.constant.PermissionConstant;
import com.xinghuo.admin.util.PermissionAspectUtil;
import com.xinghuo.common.util.UserProvider;
import com.xinghuo.permission.entity.OrganizeEntity;
import com.xinghuo.permission.model.organize.OrganizeCrForm;
import com.xinghuo.permission.model.organize.OrganizeDepartCrForm;
import com.xinghuo.permission.model.organize.OrganizeDepartUpForm;
import com.xinghuo.permission.model.organize.OrganizeUpForm;
import com.xinghuo.permission.service.OrganizeService;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.StringJoiner;

/**
 * 切面类：组织权限管理切面
 *
 * <AUTHOR>
 * @date 2023-10-05
 */
@Slf4j
@Aspect
@Component
public class PermissionOrgAspect implements PermissionAdminBase {

    @Autowired
    private UserProvider userProvider;
    @Autowired
    private OrganizeService organizeService;

    /**
     * 定义切点：标注有 @OrganizePermission 注解的方法
     */
    @Pointcut("@annotation(com.xinghuo.common.annotation.OrganizePermission)")
    public void pointcut() {
    }

    /**
     * 环绕通知：处理标注了 @OrganizePermission 注解的方法
     *
     * @param pjp 切点参数
     * @return 切点执行结果
     * @throws Throwable 异常
     */
    @Around("pointcut()")
    public Object around(ProceedingJoinPoint pjp) throws Throwable {
        return PermissionAdminBase.permissionCommon(pjp, userProvider, this);
    }

    /**
     * 实现 PermissionAdminBase 接口中的 detailPermission 方法
     * 该方法根据方法名判断是否有权限执行操作
     *
     * @param pjp            切点参数
     * @param operatorUserId 操作者用户ID
     * @param methodName     方法名
     * @return 是否有权限
     */
    @Override
    public Boolean detailPermission(ProceedingJoinPoint pjp, String operatorUserId, String methodName) {
        switch (methodName) {
            case PermissionConstant.METHOD_CREATE:
                return PermissionAspectUtil.getPermitByOrgIds(
                        // 操作目标对象表单对象
                        ((OrganizeCrForm) pjp.getArgs()[0]).getParentId(),
                        operatorUserId,
                        PermissionConstant.METHOD_CREATE);
            case PermissionConstant.METHOD_CREATE_DEPARTMENT:
                return PermissionAspectUtil.getPermitByOrgIds(
                        // 操作目标对象表单对象
                        ((OrganizeDepartCrForm) pjp.getArgs()[0]).getParentId(),
                        operatorUserId,
                        PermissionConstant.METHOD_CREATE);
            case PermissionConstant.METHOD_UPDATE:
                // 当前组织id
                String orgId = (String) pjp.getArgs()[0];
                // 当前组织父级id
                OrganizeEntity info = organizeService.getInfo(orgId);
                // 修改后的id
                OrganizeUpForm organizeUpForm = (OrganizeUpForm) pjp.getArgs()[1];
                StringJoiner stringJoiner = new StringJoiner(",");
                stringJoiner.add(orgId);
                if (!organizeUpForm.getParentId().equals(info.getParentId()) && !"-1".equals(info.getParentId())) {
                    stringJoiner.add(info.getParentId());
                }
                if (!organizeUpForm.getParentId().equals(info.getParentId()) && !"-1".equals(organizeUpForm.getParentId())) {
                    stringJoiner.add(organizeUpForm.getParentId());
                }
                return PermissionAspectUtil.getPermitByOrgIds(
                        // 操作目标对象ID
                        stringJoiner.toString(),
                        operatorUserId,
                        PermissionConstant.METHOD_UPDATE);
            case PermissionConstant.METHOD_UPDATE_DEPARTMENT:
                // 当前组织id
                String orgIds = (String) pjp.getArgs()[0];
                // 当前组织父级id
                OrganizeEntity infos = organizeService.getInfo(orgIds);
                // 修改后的id
                OrganizeDepartUpForm organizeDepartUpForm = (OrganizeDepartUpForm) pjp.getArgs()[1];
                StringJoiner stringJoiners = new StringJoiner(",");
                stringJoiners.add(orgIds);
                if (!organizeDepartUpForm.getParentId().equals(infos.getParentId())) {
                    stringJoiners.add(infos.getParentId());
                    stringJoiners.add(organizeDepartUpForm.getParentId());
                }
                return PermissionAspectUtil.getPermitByOrgIds(
                        // 操作目标对象ID
                        stringJoiners.toString(),
                        operatorUserId,
                        PermissionConstant.METHOD_UPDATE);
            case PermissionConstant.METHOD_DELETE:
            case PermissionConstant.METHOD_DELETE_DEPARTMENT:
                return PermissionAspectUtil.getPermitByOrgIds(
                        // 操作目标对象ID
                        pjp.getArgs()[0].toString(),
                        operatorUserId,
                        PermissionConstant.METHOD_DELETE);
            default:
                return false;
        }
    }
}
