package com.xinghuo.card.flow.model.dataflow;

import com.xinghuo.common.base.model.Pagination;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 流水表
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2022-11-26
 */
@Data
public class DataFlowPagination extends Pagination {

    @Schema(description = "类型 ")
    private String transType;

    @Schema(description = "收支类型 ")
    private String type;

    @Schema(description = "日期 ")
    private List<String> flowDate;

    @Schema(description = "菜单id")
    private String menuId;
}
