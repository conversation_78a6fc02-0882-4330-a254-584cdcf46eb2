#set($name = "${context.className.substring(0,1).toLowerCase()}${context.className.substring(1)}")
#set($pKeyName =${context.pKeyName})

    #set($peimaryKeyName = "${pKeyName.substring(0,1).toUpperCase()}${pKeyName.substring(1)}")
    #set($peimaryKeyname = "${pKeyName.substring(0,1).toLowerCase()}${pKeyName.substring(1)}")


#set($moduleName = "${context.genInfo.className.toLowerCase()}")
#if($context.isForm)
    #set($package = "package ${context.package}.${context.isForm}.model.${moduleName};")
#else
    #set($package = "package ${context.package}.model.${moduleName};")
#end
${package}

#macro(CreateField $List,$isMast,$Name)
    #foreach($fieLdsModel in $List)
        #set($html = $fieLdsModel.fieLdsModel)
        #if($isMast)
            #set($html = $fieLdsModel.formColumnModel.fieLdsModel)
        #end
        #set($vModel = "${html.vModel.toLowerCase()}")
        #set($VModel = "${html.vModel}")
        #if($vModel != '')
            #set($config = $html.config)
            #set($xhkey = "${config.xhKey}")
            #set($fieldName=${config.label})
            #if(${xhkey}!='XHText' && ${xhkey}!='divider')
                #if(${xhkey}=='numInput' || ${xhkey}=='calculate' )
        /** ${fieldName} **/
        @JSONField(name = "${VModel}$Name")
                    #if($Name=="_name")
        private String ${VModel}$Name;
                    #else
                        #if($!{html.precision})
        private BigDecimal ${VModel}$Name;
                        #else
        private Integer ${VModel}$Name;
                        #end
                    #end
                #elseif(${xhkey}=='slider'|| ${xhkey}=='rate')
            /** ${fieldName} **/
        @JSONField(name = "${VModel}$Name")
        private Integer ${VModel}$Name;

                #elseif(${xhkey}=='modifyTime' || ${xhkey}=='createTime')
            /** ${fieldName} **/
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm",timezone = "GMT+8")
        @JSONField(name = "${VModel}$Name")
        private Date ${VModel}$Name;

                #elseif(${xhkey}=='date')
        /** ${fieldName} **/
                    #if($Name=="_name")
        @JsonFormat(pattern = "${html.format}",timezone = "GMT+8")
        @JSONField(name = "${VModel}$Name")
        private Date ${VModel}$Name;
                    #else
        @JSONField(name = "${VModel}$Name")
        private Long ${VModel}$Name;
                    #end
                #elseif(${xhkey}=='relationForm')
        /** ${fieldName} **/
        @JSONField(name = "${VModel}$Name")
        private String ${VModel}$Name;

        /** ${fieldName} **/
        @JSONField(name = "${VModel}${Name}_id")
        private String ${VModel}${Name}_id;


                #else
        /** ${fieldName} **/
        @JSONField(name = "${VModel}$Name")
        private String ${VModel}$Name;

                    #if($Name==''&& (
                        ${xhkey}=='select' || ${xhkey}=='depSelect' || ${xhkey} =='roleSelect' || ${xhkey} =='userSelect'
                    || ${xhkey}=='usersSelect' || ${xhkey} =='comSelect' || ${xhkey} =='posSelect' || ${xhkey} =='groupSelect'
                    || ${xhkey}=='address' || ${xhkey} =='cascader' || ${xhkey} =='currOrganize'
                    ))
                        #set($upModel = "${VModel.substring(0,1).toUpperCase()}${VModel.substring(1)}")
                        #set($isOrg = ${xhkey}=='comSelect')
                    /** 多选组件重写get **/
                    public void get${upModel}(String ${VModel}) {
                            this.${VModel} = DataSwapUtil.convertValueToString(${VModel},${html.multiple},${isOrg});
                            }
                    #end

                #end
            #end
        #end
    #end
#end

#macro(JSONfield $Name)
#if($context.isMain)

    #CreateField(${context.fields},"isMast",$Name)
#else
    #CreateField(${context.children.childList},"",$Name)
    #end
#end

import lombok.Data;
import java.sql.Time;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.alibaba.fastjson.annotation.JSONField;
import java.math.BigDecimal;
import java.util.List;
import com.xinghuo.obsolete.util.generater.DataSwapUtil;
/**
 *
 * ${context.genInfo.description}
 * @版本： ${context.genInfo.version}
 * @版权： ${context.genInfo.copyright}
 * @作者： ${context.genInfo.createUser}
 * @日期： ${context.genInfo.createDate}
 */
@Data
public class $!{context.className}ListVO{
#if($context.isMain)
	private String $!{peimaryKeyname};

	@JSONField(name = "flowtaskid")
	private String flowtaskid;

	#if($context.version)
    @JSONField(name = "version")
	private String version;
    #end

    #if($context.treeTable)
    @JSONField(name = "children")
    private List<$!{context.className}ListVO> children;
    #end

#foreach(${cc} in ${context.columnChildren})
    /**列表子表数据 */
#set($jsonName = "${cc.tableName.toLowerCase()}")
    @JSONField(name = "${jsonName}")
    private ${cc.modelUpName}ListVO ${jsonName};
#end
#foreach($html in ${context.children})
    #set($className = "${html.className.substring(0,1).toLowerCase()}${html.className.substring(1)}")
    #set($jsonClassName = "${html.className.toLowerCase()}")
    /** 子表数据 **/
    @JsonProperty("${html.tableModel}")
	@JSONField(name = "${html.tableModel}")
    private List<${html.className}Model> ${className}List;
#end
#end
    #JSONfield("")
    #JSONfield("_name")
    #if(${context.groupTable} == true)
        private List<$!{context.className}ListVO> children;
    #end
}
