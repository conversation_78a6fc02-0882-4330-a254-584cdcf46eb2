package com.xinghuo.admin.aop;

import com.xinghuo.common.base.ActionResult;
import com.xinghuo.common.base.UserInfo;
import com.xinghuo.common.constant.MsgCode;
import com.xinghuo.common.util.UserProvider;
import org.aspectj.lang.ProceedingJoinPoint;

/**
 * 权限管理基础接口
 *
 * <AUTHOR>
 * @date 2023-10-05
 */
public interface PermissionAdminBase {

    /**
     * 管理者权限判断
     *
     * @param pjp                 切点参数
     * @param userProvider        用户提供者
     * @param permissionAdminBase 权限管理基础接口实例
     * @return 切点执行结果
     * @throws Throwable 异常
     */
    static Object permissionCommon(ProceedingJoinPoint pjp, UserProvider userProvider, PermissionAdminBase permissionAdminBase) throws Throwable {
        // 获取用户信息
        UserInfo operatorUser = userProvider.get();
        // 是否是管理员
        if (operatorUser.getIsAdministrator()) {
            return pjp.proceed();
        } else {
            // 获取方法名
            String methodName = pjp.getSignature().getName();
            // 具体方法权限
            if (permissionAdminBase.detailPermission(pjp, operatorUser.getUserId(), methodName)) {
                return pjp.proceed();
            }
        }
        return ActionResult.fail(MsgCode.FA021.get());
    }

    /**
     * 详细的权限判断
     *
     * @param pjp            切点参数
     * @param operatorUserId 操作者用户ID
     * @param methodName     方法名
     * @return 是否有权限
     */
    Boolean detailPermission(ProceedingJoinPoint pjp, String operatorUserId, String methodName);
}
