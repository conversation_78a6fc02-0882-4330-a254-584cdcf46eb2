package com.xinghuo.card.flow.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;


/**
 * 撸货表
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2022-11-27
 */
@Data
@TableName("data_flow_order")
public class DataFlowOrderEntity {

    /**
     * 订单编号
     */
    @TableId("ORDER_ID")
    private String orderId;


    /**
     * 用户ID
     */
    @TableField("USER_ID")
    private String userId;


    /**
     * 电商类型
     */
    @TableField("TYPE")
    private Integer type;


    /**
     * 店铺
     */
    @TableField("ACC_ID")
    private String accId;


    /**
     * 采购细目
     */
    @TableField("ORDER_ITEM")
    private String orderItem;


    /**
     * 订单金额
     */
    @TableField("AMOUNT")
    private BigDecimal amount;

    /**
     * 订单编号
     */
    @TableField("ORDER_NO")
    private String orderNo;

    /**
     * 快递编号
     */
    @TableField("EXPRESS_NO")
    private String expressNo;

    /**
     * 订单日期
     */
    @TableField("ORDER_DATE")
    private Date orderDate;

    /**
     * 目的地
     */
    @TableField("DEST")
    private String dest;


    /**
     * 状态 0-未报单，1-已报单
     */
    @TableField("FORM_STATUS")
    private String formStatus;


    /**
     * 回款日期
     */
    @TableField("BACK_DATE")
    private Date backDate;


    /**
     * 支付方式
     */
    @TableField("PAY_TYPE")
    private String payType;


    /**
     * 预定支付账户
     */
    @TableField("PRE_PAY_ACC_ID")
    private String prePayAccId;


    /**
     * 预定金额
     */
    @TableField("PRE_PAY_AMOUNT")
    private BigDecimal prePayAmount;


    /**
     * 预定日期
     */
    @TableField("PRE_PAY_DATE")
    private Date prePayDate;


    /**
     * 支付卡片
     */
    @TableField("PAY_ACC_ID")
    private String payAccId;


    /**
     * 支付费用
     */
    @TableField("PAY_AMOUNT")
    private BigDecimal payAmount;


    /**
     * 苏宁卡/京东礼品卡
     */
    @TableField("PAY_LP1")
    private BigDecimal payLp1;


    /**
     * 京东钢镚
     */
    @TableField("PAY_LP2")
    private BigDecimal payLp2;


    /**
     * 苏宁云钻/京豆
     */
    @TableField("PAY_LP3")
    private BigDecimal payLp3;


    /**
     * 银行支付立减
     */
    @TableField("PAY_YH")
    private BigDecimal payYh;


    /**
     * 支付备注
     */
    @TableField("PAY_NOTE")
    private String payNote;


    /**
     * 实付金额
     */
    @TableField("PAY_SK")
    private BigDecimal paySk;

    /**
     * 回款金额
     */
    @TableField("BACK_AMOUNT")
    private BigDecimal backAmount;

    /**
     * 返钻金额
     */
    @TableField("BACK_LP2")
    private BigDecimal backLp2;

    /**
     * 结算账户ID
     */
    @TableField("BACK_ACC_ID")
    private String backAccId;


    /**
     * 签收日期
     */
    @TableField("REC_DATE")
    private Date recDate;


    /**
     * 退款日期
     */
    @TableField("REFUND_DATE")
    private Date refundDate;


    /**
     * 成本
     */
    @TableField("COST")
    private BigDecimal cost;


    /**
     * 利润
     */
    @TableField("BENEFIT")
    private BigDecimal benefit;


    /**
     * 总收益
     */
    @TableField("ALL_BENEFIT")
    private String allBenefit;

    /**
     * 备注信息
     */
    @TableField("NOTE")
    private String note;

    /**
     * 创建人ID
     */
    @TableField("CREATE_BY")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField("CREATE_TIME")
    private Date createTime;


    /**
     * 最后创建人
     */
    @TableField("UPDATE_BY")
    private String updateBy;


    /**
     * 最后修改时间
     */
    @TableField("UPDATE_TIME")
    private Date updateTime;

}
