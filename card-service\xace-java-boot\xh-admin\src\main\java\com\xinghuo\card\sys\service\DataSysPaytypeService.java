package com.xinghuo.card.sys.service;

import com.alicp.jetcache.anno.CacheInvalidate;
import com.alicp.jetcache.anno.CacheUpdate;
import com.alicp.jetcache.anno.Cached;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xinghuo.card.sys.entity.DataSysPaytypeEntity;
import com.xinghuo.card.sys.model.datasyspaytype.DataSysPaytypePagination;

import java.util.List;

/**
 * 收支类型管理
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2022-11-14
 */
public interface DataSysPaytypeService extends IService<DataSysPaytypeEntity> {

    String CACHE_NAME = "paytype";
    /**
     * 查询分页数据
     *
     * @param dataSysPaytypePagination 查询对象
     * @return 查询结果
     */
    List<DataSysPaytypeEntity> getList(DataSysPaytypePagination dataSysPaytypePagination);

    /**
     * 查询分页或者不分页列表
     *
     * @param dataSysPaytypePagination 查询对象
     * @param dataType                 0:分页 1:不分页
     * @return 查询结果
     */
    List<DataSysPaytypeEntity> getTypeList(DataSysPaytypePagination dataSysPaytypePagination, String dataType);

    /**
     * 获取DataSysPaytypeEntity详细信息
     *
     * @param id   主键
     */
    @Cached(name = CACHE_NAME, key = "#id")
    DataSysPaytypeEntity getInfo(String id);

    /**
     * 删除
     *
     * @param entity 删除的对象
     */
    @CacheInvalidate(name = CACHE_NAME, key = "#id")
    void delete(DataSysPaytypeEntity entity);

    /**
     * 新增保存
     *
     * @param entity 新增的对象
     */
    void create(DataSysPaytypeEntity entity);

    /**
     * 修改保存
     *
     * @param id     主键
     * @param entity 修改的对象
     */
    @CacheUpdate(name = CACHE_NAME, key = "#id", value = "#entity")
    boolean update(String id, DataSysPaytypeEntity entity);


}
