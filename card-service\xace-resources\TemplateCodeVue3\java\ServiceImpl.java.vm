#parse("PublicMacro/ServiceImpMarco.vm")
package ${package.ServiceImpl};

import ${package.Entity}.*;
import ${package.Mapper}.${table.mapperName};
import ${package.Service}.*;
import ${superServiceImplClassPackage};
#set($moduleName = "${mainModelName.substring(0,1).toLowerCase()}${mainModelName.substring(1).toLowerCase()}")
import ${modulePackageName}.model.${moduleName}.*;
import java.math.BigDecimal;
import cn.hutool.core.util.ObjectUtil;
import com.xinghuo.permission.model.authorize.AuthorizeConditionModel;
import com.xinghuo.example.util.GeneraterSwapUtil;
#set($peimaryKeyName="${pKeyName.substring(0,1).toUpperCase()}${pKeyName.substring(1)}")
#if($superQuery)
import com.xinghuo.common.database.model.superQuery.SuperQueryJsonModel;
import com.xinghuo.common.database.model.superQuery.ConditionJsonModel;
import com.xinghuo.common.database.model.superQuery.SuperQueryConditionModel;
#end
#if(${DS})
import com.baomidou.dynamic.datasource.annotation.DS;
import com.xinghuo.common.database.util.DataSourceUtil;
import com.xinghuo.common.database.model.entity.DbLinkEntity;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.xinghuo.common.database.plugins.DynamicSourceGeneratorInterface;
#end
#if(${main})
import java.lang.reflect.Field;
import com.baomidou.mybatisplus.annotation.TableField;
import java.util.regex.Pattern;
#end
import java.util.stream.Collectors;
import com.xinghuo.permission.util.AuthorizeUtil;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import java.text.SimpleDateFormat;
import com.xinghuo.common.util.*;
import com.xinghuo.common.hutool.*;
import java.util.*;
/**
 *
 * ${genInfo.description}
 * 版本： ${genInfo.version}
 * 版权： ${genInfo.copyright}
 * 作者： ${genInfo.createUser}
 * 日期： ${genInfo.createDate}
 */
@Service
#if(${DS})
@DS("${DS}")
#end
public class ${table.serviceImplName} extends ${superServiceImplClass}<${table.mapperName}, ${table.entityName}> implements ${table.serviceName}#if(${DS}),DynamicSourceGeneratorInterface #end{
    @Autowired
    private GeneraterSwapUtil generaterSwapUtil;

    @Autowired
    private UserProvider userProvider;

    @Autowired
    private AuthorizeUtil authorizeUtil;

#if(${main})
    #foreach($child in ${columnTableHandle})
    @Autowired
	private  ${child.modelUpName}Service ${child.modelLowName}Service;
    #end
    #foreach($subfield in ${childTableHandle})
    @Autowired
    private ${subfield.aliasUpName}Service ${subfield.aliasLowName}Service;
    #end
##    通用变量
    #set($Name = "${genInfo.className.substring(0,1).toUpperCase()}${genInfo.className.substring(1)}")
    #set($name = "${genInfo.className.substring(0,1).toLowerCase()}${genInfo.className.substring(1)}")
    #set($QueryWrapper = "${name}QueryWrapper")
    #set($serviceName = "${table.serviceName.substring(0,1).toLowerCase()}${table.serviceName.substring(1)}")
    #set($Entity = "${table.entityName}")
    #set($searchListSize =$!{searchList})
    #if($isList)
##  列表接口
    #GetTypeList()
    #end
##  增删改查接口
    #CrudMethod()
##  子表方法
    #ChildMethod()
##  副表方法
    #MastTableMethod()
##  表单验证
    #CheckForm()
#else
#if($isList)
##    子表service方法
    #set($childWrapperName = "${modelName.substring(0,1).toLowerCase()}${modelName.substring(1)}")
    //子表过滤方法
    @Override
    public QueryWrapper<${table.entityName}> getChild(${mainModelName}Pagination pagination, QueryWrapper<${table.entityName}> ${childWrapperName}QueryWrapper){
        #childPaginationSerach(${table.name})
        return ${childWrapperName}QueryWrapper;
    }
#end
#end
## 数据源切换
#if(${DS})
    @Override
    public DataSourceUtil getDataSource() {
        return generaterSwapUtil.getDataSource(this.getClass().getAnnotation(DS.class).value());
    }
#end
}
