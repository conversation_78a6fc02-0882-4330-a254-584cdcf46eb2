server {
  listen       80;
  server_name  localhost;

  # XH-START
  #设置上传文件的大小
  client_max_body_size 100m;

  # #添加头部信息
  proxy_set_header Cookie $http_cookie;
  proxy_set_header X-Forwarded-Host $host;
  proxy_set_header X-Real-IP $remote_addr;
  proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;

  # #请求头总长度大于128k时使用large_client_header_buffers设置的缓存区
  client_header_buffer_size 128k;

  # #指令参数4为个数，128k为大小，默认是8k。申请4个128k。
  large_client_header_buffers 4 128k;

  # 前端主项目(xh-web)伪静态
  location / {
    root /home/<USER>/xh-web;
    index index.html;
    try_files $uri $uri/ /index.html;
  }

  location /api/ {
    proxy_pass http://************:7005;
  }

}
