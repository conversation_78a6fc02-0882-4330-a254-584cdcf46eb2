package com.xinghuo.card.bill.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 信用卡账单实体类
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2024-01-01
 */
@Data
@TableName("credit_card_bill")
public class CreditCardBillEntity implements Serializable {

    /**
     * 主键ID
     */
    @TableId("ID")
    private String id;

    /**
     * 用户ID
     */
    @TableField("USER_ID")
    private String userId;

    /**
     * 信用卡账户ID
     */
    @TableField("CARD_ACC_ID")
    private String cardAccId;

    /**
     * 银行代码
     */
    @TableField("BANK_CODE")
    private String bankCode;

    /**
     * 银行名称
     */
    @TableField("BANK_NAME")
    private String bankName;

    /**
     * 信用卡号（脱敏）
     */
    @TableField("CARD_NUMBER")
    private String cardNumber;

    /**
     * 账单周期开始日期
     */
    @TableField("BILL_START_DATE")
    private Date billStartDate;

    /**
     * 账单周期结束日期
     */
    @TableField("BILL_END_DATE")
    private Date billEndDate;

    /**
     * 还款到期日
     */
    @TableField("DUE_DATE")
    private Date dueDate;

    /**
     * 本期应还金额
     */
    @TableField(value = "CURRENT_AMOUNT", updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal currentAmount;

    /**
     * 最低还款金额
     */
    @TableField(value = "MINIMUM_AMOUNT", updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal minimumAmount;

    /**
     * 本期消费金额
     */
    @TableField(value = "CONSUMPTION_AMOUNT", updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal consumptionAmount;

    /**
     * 本期还款金额
     */
    @TableField(value = "PAYMENT_AMOUNT", updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal paymentAmount;

    /**
     * 可用额度
     */
    @TableField(value = "AVAILABLE_CREDIT", updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal availableCredit;

    /**
     * 信用额度
     */
    @TableField(value = "CREDIT_LIMIT", updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal creditLimit;

    /**
     * 账单状态：0-未出账单，1-已出账单，2-已还款，3-逾期
     */
    @TableField("BILL_STATUS")
    private Integer billStatus;

    /**
     * 还款状态：0-未还款，1-部分还款，2-已还清
     */
    @TableField("PAYMENT_STATUS")
    private Integer paymentStatus;

    /**
     * 账单日（每月几号出账单）
     */
    @TableField("BILL_DAY")
    private Integer billDay;

    /**
     * 还款日（每月几号还款）
     */
    @TableField("REPAYMENT_DAY")
    private Integer repaymentDay;

    /**
     * 免息期天数
     */
    @TableField("INTEREST_FREE_DAYS")
    private Integer interestFreeDays;

    /**
     * 是否自动生成账单：0-否，1-是
     */
    @TableField("AUTO_GENERATE")
    private Boolean autoGenerate;

    /**
     * 上期账单余额
     */
    @TableField(value = "PREVIOUS_BALANCE", updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal previousBalance;

    /**
     * 备注
     */
    @TableField("NOTE")
    private String note;

    /**
     * 创建人
     */
    @TableField("CREATE_BY")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField("CREATE_TIME")
    private Date createTime;

    /**
     * 最后修改人
     */
    @TableField("UPDATE_BY")
    private String updateBy;

    /**
     * 最后修改时间
     */
    @TableField("UPDATE_TIME")
    private Date updateTime;

    /**
     * 删除标记：0-正常，1-已删除
     */
    @TableField("DELETE_FLAG")
    private Integer deleteFlag;

    /**
     * 排序字段
     */
    @TableField("LIST_ORDER")
    private Integer listOrder;
}
