package com.xinghuo.card.budget.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xinghuo.card.budget.dao.FinBudgetItemMapper;
import com.xinghuo.card.budget.entity.FinBudgetItemEntity;
import com.xinghuo.card.budget.model.budgetitem.FinBudgetItemForm;
import com.xinghuo.card.budget.model.budgetitem.FinBudgetItemPagination;
import com.xinghuo.card.budget.service.FinBudgetItemService;
import com.xinghuo.card.flow.dao.DataFlowMapper;
import com.xinghuo.card.sys.dao.DataAccMapper;
import com.xinghuo.card.sys.dao.DataSysPaytypeMapper;
import com.xinghuo.common.base.vo.PageListVO;
import com.xinghuo.common.base.vo.PaginationVO;
import com.xinghuo.common.util.UserProvider;
import com.xinghuo.common.util.core.BeanCopierUtils;
import com.xinghuo.common.util.core.RandomUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 预算子项管理Service实现
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2024-06-28
 */
@Service
@Slf4j
public class FinBudgetItemServiceImpl extends ServiceImpl<FinBudgetItemMapper, FinBudgetItemEntity> implements FinBudgetItemService {

    @Autowired
    private UserProvider userProvider;

    @Autowired
    private DataFlowMapper dataFlowMapper;

    @Autowired
    private DataAccMapper dataAccMapper;

    @Autowired
    private DataSysPaytypeMapper dataSysPaytypeMapper;

    @Override
    public PageListVO<FinBudgetItemEntity> getList(FinBudgetItemPagination finBudgetItemPagination) {
        Page<FinBudgetItemEntity> page = new Page<>(finBudgetItemPagination.getCurrentPage(), finBudgetItemPagination.getPageSize());
        List<FinBudgetItemEntity> list = this.baseMapper.getList(page, finBudgetItemPagination);
        
        PaginationVO paginationVO = BeanCopierUtils.copy(finBudgetItemPagination, PaginationVO.class);
        return new PageListVO<>(list, paginationVO);
    }

    @Override
    public FinBudgetItemEntity getInfo(String id) {
        return this.getById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(FinBudgetItemForm finBudgetItemForm) {
        FinBudgetItemEntity entity = BeanCopierUtils.copy(finBudgetItemForm, FinBudgetItemEntity.class);
        entity.setId(RandomUtil.uuId());
        
        // 设置默认值
        if (ObjectUtil.isNull(entity.getCurrentSpent())) {
            entity.setCurrentSpent(BigDecimal.ZERO);
        }
        if (ObjectUtil.isNull(entity.getAlertThreshold())) {
            entity.setAlertThreshold(new BigDecimal("80.00"));
        }
        if (ObjectUtil.isNull(entity.getIsAlerted())) {
            entity.setIsAlerted(false);
        }
        
        // 根据目标ID获取目标名称
        if (StrUtil.isNotBlank(entity.getTargetId())) {
            String targetName = getTargetName(entity.getItemType(), entity.getTargetId());
            entity.setTargetName(targetName);
        }
        
        entity.setCreateBy(userProvider.get().getUserId());
        entity.setCreateTime(new Date());
        entity.setUpdateBy(userProvider.get().getUserId());
        entity.setUpdateTime(new Date());
        
        this.save(entity);
        
        // 立即计算当前支出
        this.recalculateCurrentSpent(entity.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(String id, FinBudgetItemForm finBudgetItemForm) {
        FinBudgetItemEntity entity = this.getById(id);
        if (ObjectUtil.isNull(entity)) {
            throw new RuntimeException("预算子项不存在");
        }
        
        BeanCopierUtils.copy(finBudgetItemForm, entity);
        
        // 根据目标ID获取目标名称
        if (StrUtil.isNotBlank(entity.getTargetId())) {
            String targetName = getTargetName(entity.getItemType(), entity.getTargetId());
            entity.setTargetName(targetName);
        }
        
        entity.setUpdateBy(userProvider.get().getUserId());
        entity.setUpdateTime(new Date());
        
        this.updateById(entity);
        
        // 重新计算当前支出
        this.recalculateCurrentSpent(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(String id) {
        this.removeById(id);
    }

    @Override
    public List<FinBudgetItemEntity> getItemsByBudgetId(String budgetId) {
        return this.baseMapper.getItemsByBudgetId(budgetId);
    }

    @Override
    public List<FinBudgetItemEntity> getItemsNeedAlert(String userId) {
        return this.baseMapper.getItemsNeedAlert(userId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void recalculateCurrentSpent(String id) {
        FinBudgetItemEntity item = this.getById(id);
        if (ObjectUtil.isNull(item)) {
            return;
        }
        
        // 根据预算项类型计算当前支出
        BigDecimal currentSpent = calculateCurrentSpent(item);
        
        item.setCurrentSpent(currentSpent);
        item.setUpdateBy(userProvider.get().getUserId());
        item.setUpdateTime(new Date());
        
        this.updateById(item);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchUpdateCurrentSpent(String budgetId) {
        List<FinBudgetItemEntity> items = this.getItemsByBudgetId(budgetId);
        for (FinBudgetItemEntity item : items) {
            this.recalculateCurrentSpent(item.getId());
        }
    }

    @Override
    public boolean checkNeedAlert(String id) {
        FinBudgetItemEntity item = this.getById(id);
        if (ObjectUtil.isNull(item) || item.getIsAlerted()) {
            return false;
        }
        
        BigDecimal spentPercentage = BigDecimal.ZERO;
        if (item.getBudgetedAmount().compareTo(BigDecimal.ZERO) > 0) {
            spentPercentage = item.getCurrentSpent()
                    .divide(item.getBudgetedAmount(), 4, BigDecimal.ROUND_HALF_UP)
                    .multiply(new BigDecimal("100"));
        }
        
        return spentPercentage.compareTo(item.getAlertThreshold()) >= 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchCreate(List<FinBudgetItemForm> budgetItemForms) {
        for (FinBudgetItemForm form : budgetItemForms) {
            this.create(form);
        }
    }

    /**
     * 根据目标类型和ID获取目标名称
     *
     * @param itemType 预算项类型
     * @param targetId 目标ID
     * @return 目标名称
     */
    private String getTargetName(String itemType, String targetId) {
        try {
            if ("CATEGORY".equals(itemType)) {
                // 查询收支类型名称
                return dataSysPaytypeMapper.selectById(targetId).getName();
            } else if ("ACCOUNT".equals(itemType)) {
                // 查询账户名称
                return dataAccMapper.selectById(targetId).getName();
            }
        } catch (Exception e) {
            log.warn("获取目标名称失败: itemType={}, targetId={}", itemType, targetId, e);
        }
        return "";
    }

    /**
     * 计算当前支出金额
     *
     * @param item 预算项
     * @return 当前支出金额
     */
    private BigDecimal calculateCurrentSpent(FinBudgetItemEntity item) {
        // TODO: 根据预算项类型和目标ID，从data_flow表中统计支出金额
        // 这里需要根据预算的时间范围来计算
        // 示例逻辑，实际需要根据业务需求实现
        
        if ("CATEGORY".equals(item.getItemType())) {
            // 按分类统计支出
            // 需要关联预算表获取时间范围，然后统计该分类在时间范围内的支出
        } else if ("ACCOUNT".equals(item.getItemType())) {
            // 按账户统计支出
            // 需要关联预算表获取时间范围，然后统计该账户在时间范围内的支出
        }
        
        // 暂时返回0，具体实现需要根据业务逻辑完善
        return BigDecimal.ZERO;
    }
}