package com.xinghuo.card.flow.model.dataflowrecharge;

import com.xinghuo.common.base.model.Pagination;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 充值记录
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2022-11-27
 */
@Data
public class DataFlowRechargePagination extends Pagination {

    @Schema(description = "充值类型 ")
    private String chargeType;

    @Schema(description = "充值日期 ")
    private List<String> chargeDate;

    @Schema(description = "备注 ")
    private String note;

    @Schema(description = "菜单id")
    private String menuId;
}
