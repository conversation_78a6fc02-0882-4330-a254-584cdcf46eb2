import type { Component } from 'vue';
import type { ComponentType } from './types/index';

/**
 * Component list, register here to setting it in the form
 */
import { StrengthMeter } from '/@/components/StrengthMeter';
import { CountdownInput } from '/@/components/CountDown';
// xh 组件
import {
  XhAlert,
  XhAreaSelect,
  XhAutoComplete,
  XhButton,
  XhCron,
  XhCascader,
  XhColorPicker,
  XhCheckbox,
  XhCheckboxSingle,
  XhDatePicker,
  XhDateRange,
  XhTimePicker,
  XhTimeRange,
  XhMonthPicker,
  XhWeekPicker,
  XhDivider,
  XhEditor,
  XhGroupTitle,
  XhIconPicker,
  XhInput,
  XhInputPassword,
  XhInputGroup,
  XhInputSearch,
  XhTextarea,
  XhInputNumber,
  XhLink,
  XhOpenData,
  XhOrganizeSelect,
  XhDepSelect,
  XhPosSelect,
  XhGroupSelect,
  XhRoleSelect,
  XhUserSelect,
  XhUsersSelect,
  XhUserSelectDropdown,
  XhQrcode,
  XhBarcode,
  XhRadio,
  XhRate,
  XhSelect,
  XhSlider,
  XhSign,
  XhSwitch,
  XhText,
  XhTreeSelect,
  XhUploadFile,
  XhUploadImg,
  XhUploadImgSingle,
  XhRelationForm,
  XhRelationFormAttr,
  XhPopupSelect,
  XhPopupAttr,
  XhNumberRange,
  XhCalculate,
  XhInputTable,
  XhSysVars,
} from '/@/components/Xh/index';

const componentMap = new Map<ComponentType, Component>();

componentMap.set('StrengthMeter', StrengthMeter);
componentMap.set('InputCountDown', CountdownInput);

componentMap.set('InputGroup', XhInputGroup);
componentMap.set('InputSearch', XhInputSearch);
componentMap.set('MonthPicker', XhMonthPicker);
componentMap.set('WeekPicker', XhWeekPicker);

componentMap.set('Alert', XhAlert);
componentMap.set('AreaSelect', XhAreaSelect);
componentMap.set('AutoComplete', XhAutoComplete);
componentMap.set('Button', XhButton);
componentMap.set('Cron', XhCron);
componentMap.set('Cascader', XhCascader);
componentMap.set('ColorPicker', XhColorPicker);
componentMap.set('Checkbox', XhCheckbox);
componentMap.set('XhCheckboxSingle', XhCheckboxSingle);
componentMap.set('DatePicker', XhDatePicker);
componentMap.set('DateRange', XhDateRange);
componentMap.set('TimePicker', XhTimePicker);
componentMap.set('TimeRange', XhTimeRange);
componentMap.set('Divider', XhDivider);
componentMap.set('Editor', XhEditor);
componentMap.set('GroupTitle', XhGroupTitle);
componentMap.set('Input', XhInput);
componentMap.set('InputPassword', XhInputPassword);
componentMap.set('Textarea', XhTextarea);
componentMap.set('InputNumber', XhInputNumber);
componentMap.set('IconPicker', XhIconPicker);
componentMap.set('Link', XhLink);
componentMap.set('OrganizeSelect', XhOrganizeSelect);
componentMap.set('DepSelect', XhDepSelect);
componentMap.set('PosSelect', XhPosSelect);
componentMap.set('GroupSelect', XhGroupSelect);
componentMap.set('RoleSelect', XhRoleSelect);
componentMap.set('UserSelect', XhUserSelect);
componentMap.set('UsersSelect', XhUsersSelect);
componentMap.set('UserSelectDropdown', XhUserSelectDropdown);
componentMap.set('Qrcode', XhQrcode);
componentMap.set('Barcode', XhBarcode);
componentMap.set('Radio', XhRadio);
componentMap.set('Rate', XhRate);
componentMap.set('Select', XhSelect);
componentMap.set('Slider', XhSlider);
componentMap.set('Sign', XhSign);
componentMap.set('Switch', XhSwitch);
componentMap.set('Text', XhText);
componentMap.set('TreeSelect', XhTreeSelect);
componentMap.set('UploadFile', XhUploadFile);
componentMap.set('UploadImg', XhUploadImg);
componentMap.set('UploadImgSingle', XhUploadImgSingle);
componentMap.set('BillRule', XhInput);
componentMap.set('ModifyUser', XhInput);
componentMap.set('ModifyTime', XhInput);
componentMap.set('CreateUser', XhOpenData);
componentMap.set('CreateTime', XhOpenData);
componentMap.set('CurrOrganize', XhOpenData);
componentMap.set('CurrPosition', XhOpenData);
componentMap.set('RelationForm', XhRelationForm);
componentMap.set('RelationFormAttr', XhRelationFormAttr);
componentMap.set('PopupSelect', XhPopupSelect);
componentMap.set('PopupTableSelect', XhPopupSelect);
componentMap.set('PopupAttr', XhPopupAttr);
componentMap.set('NumberRange', XhNumberRange);
componentMap.set('Calculate', XhCalculate);
componentMap.set('InputTable', XhInputTable);
componentMap.set('SysVars', XhSysVars);

export function add(compName: ComponentType, component: Component) {
  componentMap.set(compName, component);
}

export function del(compName: ComponentType) {
  componentMap.delete(compName);
}

export { componentMap };
