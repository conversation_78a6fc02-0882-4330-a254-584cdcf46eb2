<template>
  <div class="input-number-range">
    <a-input-number
      v-model:value="minValue"
      :placeholder="placeholder[0]"
      :precision="precision"
      :min="min"
      :max="maxValue || max"
      style="width: calc(50% - 12px)"
      @change="handleMinChange"
    />
    <span class="range-separator">~</span>
    <a-input-number
      v-model:value="maxValue"
      :placeholder="placeholder[1]"
      :precision="precision"
      :min="minValue || min"
      :max="max"
      style="width: calc(50% - 12px)"
      @change="handleMaxChange"
    />
  </div>
</template>

<script setup lang="ts">
  import { ref, watch } from 'vue';

  interface Props {
    value?: [number?, number?];
    placeholder?: [string, string];
    precision?: number;
    min?: number;
    max?: number;
  }

  const props = withDefaults(defineProps<Props>(), {
    placeholder: () => ['最小值', '最大值'],
    precision: 2,
    min: 0
  });

  const emit = defineEmits(['update:value', 'change']);

  const minValue = ref<number>();
  const maxValue = ref<number>();

  // 监听外部值变化
  watch(() => props.value, (newValue) => {
    if (newValue) {
      minValue.value = newValue[0];
      maxValue.value = newValue[1];
    } else {
      minValue.value = undefined;
      maxValue.value = undefined;
    }
  }, { immediate: true });

  function handleMinChange() {
    emitChange();
  }

  function handleMaxChange() {
    emitChange();
  }

  function emitChange() {
    const value: [number?, number?] = [minValue.value, maxValue.value];
    emit('update:value', value);
    emit('change', value);
  }
</script>

<style scoped>
.input-number-range {
  display: flex;
  align-items: center;
  width: 100%;
}

.range-separator {
  margin: 0 12px;
  color: #bfbfbf;
}
</style>
