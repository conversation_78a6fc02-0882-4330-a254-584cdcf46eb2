import { defHttp } from '/@/utils/http/axios';

/**
 * 订单管理相关API接口
 */

enum Api {
  Prefix = '/api/card/flow/order',
}

/**
 * 订单分页查询参数
 */
export interface OrderListParams {
  accId?: string;
  orderDate?: string[];
  formStatus?: string;
  orderNo?: string;
  keyword?: string;
  current?: number;
  size?: number;
}

/**
 * 订单表单数据
 */
export interface OrderFormData {
  orderId?: string;
  accId: string;
  orderItem: string;
  amount: number;
  orderNo?: string;
  orderDate: string;
  expressNo?: string;
  dest?: string;
  formStatus: string;
  recDate?: string;
  note?: string;
}

/**
 * 获取订单列表（分页）
 */
export function getOrderList(data: OrderListParams) {
  return defHttp.post({ url: Api.Prefix + '/getList', data });
}

/**
 * 创建订单
 */
export function createOrder(data: OrderFormData) {
  return defHttp.post({ url: Api.Prefix, data });
}

/**
 * 修改订单
 */
export function updateOrder(data: OrderFormData) {
  return defHttp.put({ url: Api.Prefix + '/' + data.orderId, data });
}

/**
 * 获取订单详情
 */
export function getOrderInfo(id: string) {
  return defHttp.get({ url: Api.Prefix + '/' + id });
}

/**
 * 删除订单
 */
export function delOrder(id: string) {
  return defHttp.delete({ url: Api.Prefix + '/' + id });
}
