package com.xinghuo.card.sys.service.impl;

import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xinghuo.card.sys.entity.SysManEntity;
import com.xinghuo.card.sys.dao.SysManMapper;
import com.xinghuo.card.sys.model.sysman.SysManPagination;
import com.xinghuo.card.sys.service.SysManService;
import com.xinghuo.common.base.service.impl.ExtendedBaseServiceImpl;
import com.xinghuo.common.util.UserProvider;
import com.xinghuo.common.util.core.StrXhUtil;
import com.xinghuo.common.util.core.RandomUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.Period;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 持卡账户管理服务实现类
 * 用于管理持卡人的基本信息和代称
 * 支持多持卡人管理，每个持卡人有唯一的代称标识
 *
 * <AUTHOR> Assistant
 * @version V1.0.0
 * @date 2024-12-29
 */
@Slf4j
@Service
public class SysManServiceImpl extends ExtendedBaseServiceImpl<SysManMapper, SysManEntity> implements SysManService {

    @Autowired
    private UserProvider userProvider;

    @Override
    public List<SysManEntity> getList(SysManPagination sysManPagination) {
        return getListByType(sysManPagination, "0");
    }

    @Override
    public List<SysManEntity> getTypeList(SysManPagination sysManPagination, String dataType) {
        return getListByType(sysManPagination, dataType);
    }

    @Override
    public List<SysManEntity> getSelectList() {
        QueryWrapper<SysManEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .orderByAsc(SysManEntity::getListOrder)
                .orderByAsc(SysManEntity::getName);
        return this.list(queryWrapper);
    }

    /**
     * 根据类型获取列表数据
     *
     * @param sysManPagination 查询参数
     * @param dataType         数据类型 0:分页 1:不分页
     * @return 查询结果
     */
    private List<SysManEntity> getListByType(SysManPagination sysManPagination, String dataType) {
        QueryWrapper<SysManEntity> queryWrapper = new QueryWrapper<>();

        // 构建查询条件
        if (StrXhUtil.isNotEmpty(sysManPagination.getName())) {
            queryWrapper.lambda().like(SysManEntity::getName, sysManPagination.getName());
        }
        if (StrXhUtil.isNotEmpty(sysManPagination.getSex())) {
            queryWrapper.lambda().eq(SysManEntity::getSex, sysManPagination.getSex());
        }
        if (sysManPagination.getMinAge() != null || sysManPagination.getMaxAge() != null) {
            // 根据年龄范围计算生日范围
            Date birthdayStart = null;
            Date birthdayEnd = null;

            if (sysManPagination.getMaxAge() != null) {
                LocalDate startDate = LocalDate.now().minusYears(sysManPagination.getMaxAge() + 1);
                birthdayStart = Date.from(startDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
            }
            if (sysManPagination.getMinAge() != null) {
                LocalDate endDate = LocalDate.now().minusYears(sysManPagination.getMinAge());
                birthdayEnd = Date.from(endDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
            }

            if (birthdayStart != null) {
                queryWrapper.lambda().ge(SysManEntity::getBirthday, birthdayStart);
            }
            if (birthdayEnd != null) {
                queryWrapper.lambda().le(SysManEntity::getBirthday, birthdayEnd);
            }
        }
        if (sysManPagination.getBirthdayStart() != null) {
            queryWrapper.lambda().ge(SysManEntity::getBirthday, sysManPagination.getBirthdayStart());
        }
        if (sysManPagination.getBirthdayEnd() != null) {
            queryWrapper.lambda().le(SysManEntity::getBirthday, sysManPagination.getBirthdayEnd());
        }
        if (StrXhUtil.isNotEmpty(sysManPagination.getKeyword())) {
            queryWrapper.lambda().like(SysManEntity::getName, sysManPagination.getKeyword());
        }
        if (Boolean.TRUE.equals(sysManPagination.getOnlyWithBirthday())) {
            queryWrapper.lambda().isNotNull(SysManEntity::getBirthday);
        }
        if (Boolean.TRUE.equals(sysManPagination.getOnlyThisMonthBirthday())) {
            // 本月生日查询逻辑
            int currentMonth = LocalDate.now().getMonthValue();
            queryWrapper.lambda().apply("MONTH(birthday) = {0}", currentMonth);
        }
        if (Boolean.TRUE.equals(sysManPagination.getOnlyTodayBirthday())) {
            // 今日生日查询逻辑
            LocalDate today = LocalDate.now();
            queryWrapper.lambda().apply("MONTH(birthday) = {0} AND DAY(birthday) = {1}",
                    today.getMonthValue(), today.getDayOfMonth());
        }
        if (sysManPagination.getBirthdayReminderDays() != null) {
            // 生日提醒查询逻辑
            LocalDate today = LocalDate.now();
            LocalDate endDate = today.plusDays(sysManPagination.getBirthdayReminderDays());

            queryWrapper.lambda().apply(
                "((MONTH(birthday) = {0} AND DAY(birthday) >= {1}) OR " +
                "(MONTH(birthday) = {2} AND DAY(birthday) <= {3}))",
                today.getMonthValue(), today.getDayOfMonth(),
                endDate.getMonthValue(), endDate.getDayOfMonth()
            );
        }

        // 排序
        if (StrXhUtil.isNotEmpty(sysManPagination.getSortField()) && StrXhUtil.isNotEmpty(sysManPagination.getSortOrder())) {
            boolean isAsc = "ASC".equalsIgnoreCase(sysManPagination.getSortOrder());
            switch (sysManPagination.getSortField()) {
                case "name":
                    queryWrapper.lambda().orderBy(true, isAsc, SysManEntity::getName);
                    break;
                case "sex":
                    queryWrapper.lambda().orderBy(true, isAsc, SysManEntity::getSex);
                    break;
                case "birthday":
                    queryWrapper.lambda().orderBy(true, isAsc, SysManEntity::getBirthday);
                    break;
                case "listOrder":
                    queryWrapper.lambda().orderBy(true, isAsc, SysManEntity::getListOrder);
                    break;
                case "createTime":
                    queryWrapper.lambda().orderBy(true, isAsc, SysManEntity::getCreatedAt);
                    break;
                default:
                    queryWrapper.lambda()
                            .orderByAsc(SysManEntity::getListOrder)
                            .orderByAsc(SysManEntity::getName);
                    break;
            }
        } else {
            queryWrapper.lambda()
                    .orderByAsc(SysManEntity::getListOrder)
                    .orderByAsc(SysManEntity::getName);
        }

        return this.list(queryWrapper);
    }

    @Override
    public SysManEntity getInfo(String id) {
        Assert.notEmpty(id, "id不能为空");
        QueryWrapper<SysManEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(SysManEntity::getId, id);
        return this.getOne(queryWrapper);
    }

    @Override
    public SysManEntity getInfoByName(String name) {
        Assert.notEmpty(name, "持卡人代称不能为空");
        QueryWrapper<SysManEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(SysManEntity::getName, name);
        return this.getOne(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(SysManEntity entity) {
        // 检查代称唯一性
        if (!checkNameUnique(entity.getName(), null)) {
            throw new RuntimeException("持卡人代称已存在");
        }
        
        // 设置排序序号
        if (entity.getListOrder() == null) {
            entity.setListOrder(getNextListOrder());
        }
        
        this.save(entity);
        log.info("创建持卡人成功，ID: {}, 代称: {}", entity.getId(), entity.getName());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean update(String id, SysManEntity entity) {
        Assert.notEmpty(id, "id不能为空");
        
        // 检查代称唯一性
        if (!checkNameUnique(entity.getName(), id)) {
            throw new RuntimeException("持卡人代称已存在");
        }
        
        entity.setId(id);
        boolean result = this.updateById(entity);
        
        if (result) {
            log.info("更新持卡人成功，ID: {}, 代称: {}", id, entity.getName());
        }
        
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(SysManEntity entity) {
        if (entity != null) {
            this.removeById(entity.getId());
            log.info("删除持卡人成功，ID: {}, 代称: {}", entity.getId(), entity.getName());
        }
    }

    @Override
    public boolean checkNameUnique(String name, String id) {
        Assert.notEmpty(name, "持卡人代称不能为空");
        
        QueryWrapper<SysManEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(SysManEntity::getName, name);
        
        if (StrXhUtil.isNotEmpty(id)) {
            queryWrapper.lambda().ne(SysManEntity::getId, id);
        }
        
        return this.count(queryWrapper) == 0;
    }

    @Override
    public Integer getNextListOrder() {
        QueryWrapper<SysManEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .orderByDesc(SysManEntity::getListOrder)
                .last("LIMIT 1");
        
        SysManEntity lastEntity = this.getOne(queryWrapper);
        return lastEntity != null && lastEntity.getListOrder() != null 
                ? lastEntity.getListOrder() + 1 
                : 1;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchDelete(List<String> ids) {
        Assert.notEmpty(ids, "删除ID列表不能为空");
        
        int count = this.removeBatchByIds(ids) ? ids.size() : 0;
        log.info("批量删除持卡人完成，删除数量: {}", count);
        
        return count;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateListOrder(String id, Integer listOrder) {
        Assert.notEmpty(id, "id不能为空");
        Assert.notNull(listOrder, "排序序号不能为空");

        SysManEntity entity = new SysManEntity();
        entity.setId(id);
        entity.setListOrder(listOrder);

        boolean result = this.updateById(entity);

        if (result) {
            log.info("更新持卡人排序成功，ID: {}, 排序序号: {}", id, listOrder);
        }

        return result;
    }

    @Override
    public Map<String, Object> getManStatistics() {
        Map<String, Object> statistics = new HashMap<>();

        // 总持卡人数量
        long totalCount = this.count();
        statistics.put("totalCount", totalCount);

        // 按性别统计
        QueryWrapper<SysManEntity> sexQueryWrapper = new QueryWrapper<>();
        sexQueryWrapper.select("SEX, COUNT(*) as count")
                .groupBy("SEX");

        List<Map<String, Object>> sexStatistics = this.listMaps(sexQueryWrapper);
        statistics.put("sexStatistics", sexStatistics);

        // 有生日的持卡人数量
        QueryWrapper<SysManEntity> birthdayQueryWrapper = new QueryWrapper<>();
        birthdayQueryWrapper.lambda().isNotNull(SysManEntity::getBirthday);
        long birthdayCount = this.count(birthdayQueryWrapper);
        statistics.put("birthdayCount", birthdayCount);

        // 本月生日数量
        int currentMonth = LocalDate.now().getMonthValue();
        QueryWrapper<SysManEntity> thisMonthQueryWrapper = new QueryWrapper<>();
        thisMonthQueryWrapper.lambda()
                .isNotNull(SysManEntity::getBirthday)
                .apply("MONTH(birthday) = {0}", currentMonth);
        long thisMonthBirthdayCount = this.count(thisMonthQueryWrapper);
        statistics.put("thisMonthBirthdayCount", thisMonthBirthdayCount);

        return statistics;
    }

    @Override
    public List<Map<String, Object>> getSexDistribution() {
        QueryWrapper<SysManEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("SEX, COUNT(*) as count")
                .groupBy("SEX")
                .orderBy(true, false, "count");

        List<Map<String, Object>> result = this.listMaps(queryWrapper);

        // 添加性别名称
        for (Map<String, Object> item : result) {
            String sex = (String) item.get("SEX");
            String sexName = getSexName(sex);
            item.put("sexName", sexName);
        }

        return result;
    }

    @Override
    public List<Map<String, Object>> getAgeDistribution() {
        List<SysManEntity> allMen = this.list();
        Map<String, Integer> ageGroups = new HashMap<>();

        for (SysManEntity man : allMen) {
            if (man.getBirthday() != null) {
                Integer age = calculateAge(man.getBirthday());
                String ageGroup = getAgeGroup(age);
                ageGroups.put(ageGroup, ageGroups.getOrDefault(ageGroup, 0) + 1);
            }
        }

        return ageGroups.entrySet().stream()
                .map(entry -> {
                    Map<String, Object> item = new HashMap<>();
                    item.put("ageGroup", entry.getKey());
                    item.put("count", entry.getValue());
                    return item;
                })
                .sorted((a, b) -> ((String) a.get("ageGroup")).compareTo((String) b.get("ageGroup")))
                .collect(Collectors.toList());
    }

    @Override
    public Map<String, Object> validateCanDelete(String id) {
        Assert.notEmpty(id, "持卡人ID不能为空");

        Map<String, Object> result = new HashMap<>();
        result.put("canDelete", true);
        result.put("reason", "");

        // TODO: 检查是否有关联的用户银行信息
        // TODO: 检查是否有关联的信用卡账户

        return result;
    }

    @Override
    public Map<String, Object> batchValidateCanDelete(List<String> ids) {
        Assert.notEmpty(ids, "持卡人ID列表不能为空");

        Map<String, Object> result = new HashMap<>();
        List<String> canDeleteIds = new ArrayList<>();
        List<Map<String, Object>> cannotDeleteList = new ArrayList<>();

        for (String id : ids) {
            Map<String, Object> validateResult = validateCanDelete(id);
            if ((Boolean) validateResult.get("canDelete")) {
                canDeleteIds.add(id);
            } else {
                Map<String, Object> item = new HashMap<>();
                item.put("id", id);
                item.put("reason", validateResult.get("reason"));
                cannotDeleteList.add(item);
            }
        }

        result.put("canDeleteIds", canDeleteIds);
        result.put("cannotDeleteList", cannotDeleteList);
        result.put("canDeleteCount", canDeleteIds.size());
        result.put("cannotDeleteCount", cannotDeleteList.size());

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> importManData(List<SysManEntity> manList) {
        Assert.notEmpty(manList, "持卡人数据列表不能为空");

        Map<String, Object> result = new HashMap<>();
        int successCount = 0;
        int failCount = 0;
        List<String> errorMessages = new ArrayList<>();

        for (SysManEntity man : manList) {
            try {
                // 设置ID
                man.setId(RandomUtil.snowId());


                // 验证数据
                if (StrXhUtil.isEmpty(man.getName())) {
                    errorMessages.add("持卡人代称不能为空");
                    failCount++;
                    continue;
                }

                if (!checkNameUnique(man.getName(), null)) {
                    errorMessages.add("持卡人代称已存在: " + man.getName());
                    failCount++;
                    continue;
                }

                // 设置排序序号
                if (man.getListOrder() == null) {
                    man.setListOrder(getNextListOrder());
                }

                this.save(man);
                successCount++;

            } catch (Exception e) {
                errorMessages.add("导入失败: " + e.getMessage());
                failCount++;
                log.error("导入持卡人数据失败", e);
            }
        }

        result.put("successCount", successCount);
        result.put("failCount", failCount);
        result.put("errorMessages", errorMessages);

        log.info("导入持卡人数据完成，成功: {}, 失败: {}", successCount, failCount);

        return result;
    }

    @Override
    public List<Map<String, Object>> exportManData(SysManPagination sysManPagination) {
        List<SysManEntity> manList = getList(sysManPagination);

        return manList.stream().map(man -> {
            Map<String, Object> item = new HashMap<>();
            item.put("id", man.getId());
            item.put("name", man.getName());
            item.put("sex", man.getSex());
            item.put("sexName", getSexName(man.getSex()));
            item.put("birthday", man.getBirthday());
            item.put("age", man.getBirthday() != null ? calculateAge(man.getBirthday()) : null);
            item.put("listOrder", man.getListOrder());
            return item;
        }).collect(Collectors.toList());
    }

    /**
     * 获取性别名称
     */
    private String getSexName(String sex) {
        if (StrXhUtil.isEmpty(sex)) {
            return "未设置";
        }

        switch (sex) {
            case SysManEntity.Sex.MALE:
                return "男";
            case SysManEntity.Sex.FEMALE:
                return "女";
            default:
                return "未知";
        }
    }

    /**
     * 获取年龄组
     */
    private String getAgeGroup(Integer age) {
        if (age == null) {
            return "未知";
        }

        if (age < 18) {
            return "18岁以下";
        } else if (age < 30) {
            return "18-29岁";
        } else if (age < 40) {
            return "30-39岁";
        } else if (age < 50) {
            return "40-49岁";
        } else if (age < 60) {
            return "50-59岁";
        } else {
            return "60岁以上";
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SysManEntity copyMan(String id, String newName) {
        Assert.notEmpty(id, "源持卡人ID不能为空");
        Assert.notEmpty(newName, "新持卡人代称不能为空");

        SysManEntity sourceMan = getInfo(id);
        if (sourceMan == null) {
            throw new RuntimeException("源持卡人不存在");
        }

        if (!checkNameUnique(newName, null)) {
            throw new RuntimeException("持卡人代称已存在");
        }

        SysManEntity newMan = new SysManEntity();
        newMan.setId(RandomUtil.snowId());
        newMan.setName(newName);
        newMan.setSex(sourceMan.getSex());
        newMan.setBirthday(sourceMan.getBirthday());
        newMan.setListOrder(getNextListOrder());

        this.save(newMan);

        log.info("复制持卡人成功，源持卡人: {}, 新持卡人: {}", sourceMan.getName(), newName);

        return newMan;
    }

    @Override
    public Map<String, Object> getManDetailWithRelated(String id) {
        Assert.notEmpty(id, "持卡人ID不能为空");

        SysManEntity man = getInfo(id);
        if (man == null) {
            return null;
        }

        Map<String, Object> result = new HashMap<>();
        result.put("man", man);

        // TODO: 添加关联信息
        // result.put("userBankCount", getUserBankCount(id));
        // result.put("creditCardCount", getCreditCardCount(id));

        return result;
    }

    @Override
    public List<SysManEntity> getBirthdayReminders(Integer days) {
        if (days == null || days <= 0) {
            days = 7; // 默认7天
        }

        LocalDate today = LocalDate.now();
        LocalDate endDate = today.plusDays(days);

        QueryWrapper<SysManEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .isNotNull(SysManEntity::getBirthday)
                .apply("((MONTH(birthday) = {0} AND DAY(birthday) >= {1}) OR " +
                       "(MONTH(birthday) = {2} AND DAY(birthday) <= {3}))",
                       today.getMonthValue(), today.getDayOfMonth(),
                       endDate.getMonthValue(), endDate.getDayOfMonth())
                .orderByAsc(SysManEntity::getBirthday);

        return this.list(queryWrapper);
    }

    @Override
    public List<SysManEntity> getThisMonthBirthdays() {
        int currentMonth = LocalDate.now().getMonthValue();

        QueryWrapper<SysManEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .isNotNull(SysManEntity::getBirthday)
                .apply("MONTH(birthday) = {0}", currentMonth)
                .orderByAsc(SysManEntity::getBirthday);

        return this.list(queryWrapper);
    }

    @Override
    public List<SysManEntity> getTodayBirthdays() {
        LocalDate today = LocalDate.now();

        QueryWrapper<SysManEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .isNotNull(SysManEntity::getBirthday)
                .apply("MONTH(birthday) = {0} AND DAY(birthday) = {1}",
                       today.getMonthValue(), today.getDayOfMonth())
                .orderByAsc(SysManEntity::getName);

        return this.list(queryWrapper);
    }

    @Override
    public List<SysManEntity> getListBySex(String sex) {
        Assert.notEmpty(sex, "性别不能为空");

        QueryWrapper<SysManEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(SysManEntity::getSex, sex)
                .orderByAsc(SysManEntity::getListOrder)
                .orderByAsc(SysManEntity::getName);

        return this.list(queryWrapper);
    }

    @Override
    public List<SysManEntity> getListByAgeRange(Integer minAge, Integer maxAge) {
        Date birthdayStart = null;
        Date birthdayEnd = null;

        if (maxAge != null) {
            LocalDate startDate = LocalDate.now().minusYears(maxAge + 1);
            birthdayStart = Date.from(startDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
        }
        if (minAge != null) {
            LocalDate endDate = LocalDate.now().minusYears(minAge);
            birthdayEnd = Date.from(endDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
        }

        QueryWrapper<SysManEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().isNotNull(SysManEntity::getBirthday);

        if (birthdayStart != null) {
            queryWrapper.lambda().ge(SysManEntity::getBirthday, birthdayStart);
        }
        if (birthdayEnd != null) {
            queryWrapper.lambda().le(SysManEntity::getBirthday, birthdayEnd);
        }

        queryWrapper.lambda()
                .orderByAsc(SysManEntity::getListOrder)
                .orderByAsc(SysManEntity::getName);

        return this.list(queryWrapper);
    }

    @Override
    public Integer calculateAge(Date birthday) {
        if (birthday == null) {
            return null;
        }

        LocalDate birthDate = birthday.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate currentDate = LocalDate.now();

        return Period.between(birthDate, currentDate).getYears();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchUpdateListOrder(List<Map<String, Object>> orderList) {
        Assert.notEmpty(orderList, "排序列表不能为空");

        int count = 0;
        for (Map<String, Object> item : orderList) {
            String id = (String) item.get("id");
            Integer listOrder = (Integer) item.get("listOrder");

            if (StrXhUtil.isNotEmpty(id) && listOrder != null) {
                if (updateListOrder(id, listOrder)) {
                    count++;
                }
            }
        }

        log.info("批量更新排序完成，更新数量: {}", count);

        return count;
    }

    @Override
    public Map<String, Object> getManRelatedStats(String id) {
        Assert.notEmpty(id, "持卡人ID不能为空");

        Map<String, Object> stats = new HashMap<>();

        // TODO: 获取关联统计信息
        // stats.put("userBankCount", getUserBankCount(id));
        // stats.put("creditCardCount", getCreditCardCount(id));

        stats.put("userBankCount", 0);
        stats.put("creditCardCount", 0);

        return stats;
    }
}
