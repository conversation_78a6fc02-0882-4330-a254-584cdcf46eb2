-- xace200_card.data_acc definition

CREATE TABLE data_acc (
ID varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'ID',
USER_ID varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '管理用户ID',
TYPE int DEFAULT NULL,
PARENT_ID varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
NAME varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
PY varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '拼音',
HIDE_FLAG int DEFAULT '0' COMMENT '1-表示隐藏  0-表示正常',
BUILD_DATE date DEFAULT NULL COMMENT '建立时间',
MAN_ID varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
BALANCE decimal(10,2) DEFAULT NULL COMMENT '当前余额',
QDATE date DEFAULT NULL,
FAV_MARK int DEFAULT '0' COMMENT '1-常用，0-非常用',
QBALANCE decimal(10,2) DEFAULT NULL,
LIST_ORDER int DEFAULT NULL COMMENT '排序',
NOTE varchar(500) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '备注',
CREATE_BY varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '创建人',
CREATE_TIME datetime DEFAULT NULL COMMENT '创建时间',
UPDATE_BY varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '最后修改人',
UPDATE_TIME datetime DEFAULT NULL COMMENT '最后修改时间',
f_tenantid varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '租户id',
f_flowid varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '流程id',
PRIMARY KEY (ID) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='个人账号\r\n苏宁，京东 存在子账号表。在电商账号中处理';

-- xace200_card.data_acc_credit_card definition

CREATE TABLE data_acc_credit_card (
ID varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL,
UBANK_ID varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
BANK_ID varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '银行ID',
BILL_TYPE varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT 'FIX -固定账单日  LAST - 每月最后一天账单日',
BILL_DAY smallint DEFAULT NULL COMMENT '账单日',
REPAY_TYPE varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT '1' COMMENT 'FIX-固定还款日，LATER-账单日后多少天',
REPAY_DAY smallint DEFAULT NULL,
LIMIT_MONEY int DEFAULT NULL COMMENT '卡片额度',
BILL_DAY_TYPE varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT '' COMMENT '账单日刷卡类型 THIS 计入本期，NEXT 计入下期',
DEFAULT_CREDIT_ID varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '默认卡片ID',
PRIMARY KEY (ID) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='个人卡片管理';

-- xace200_card.data_acc_debit_card definition

CREATE TABLE data_acc_debit_card (
ID varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL,
BANK_ID varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
CARD_NO varchar(8) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '卡片尾号后4位',
PRIMARY KEY (ID) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='个人借记卡';

-- xace200_card.data_acc_pos definition

CREATE TABLE data_acc_pos (
ID varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT 'POS id',
ACTIVE_DATE date DEFAULT NULL COMMENT '启用日期',
DEBIT_CARD_ID varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '结算卡',
FEE_NORMAL varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '正常刷卡费率,结算时间，提现T+1  D+0',
FEE_CLOUD_FLASH_PAY varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '云闪付',
FEE_FLASH_PAY varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '闪付费率',
DEBIT_FEE double DEFAULT NULL COMMENT '结算额外费率',
DEBIT_CASH_FEE double DEFAULT NULL COMMENT '结算提现费用',
PRIMARY KEY (ID) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='个人持有POS管理';

-- xace200_card.data_acc_shop definition

CREATE TABLE data_acc_shop (
ID varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL,
MOBILE varchar(11) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '绑定手机号',
END_DATE date DEFAULT NULL COMMENT '会员过期时间',
AUTO_AMOUNT decimal(10,2) DEFAULT NULL,
LP1_ACC_ID varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
LP1_AUTO_AMOUNT decimal(10,2) DEFAULT NULL COMMENT '苏宁卡/礼品卡余额',
LP2_ACC_ID varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
LP2_AUTO_AMOUNT decimal(10,2) DEFAULT NULL COMMENT '云钻/京豆余额',
LP3_ACC_ID varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
LP3_AUTO_AMOUNT decimal(10,2) DEFAULT NULL COMMENT '钢镚/铜板余额',
PARENT_ID varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
SHOP_TYPE int DEFAULT NULL COMMENT '子账户类型，0-主账户，1-苏宁/京东卡 ，2-京豆、云钻，3-钢镚',
PRIMARY KEY (ID) USING BTREE,
UNIQUE KEY PID_TYPE (PARENT_ID,SHOP_TYPE) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='电商账户';

-- xace200_card.data_credit_account_day definition

CREATE TABLE data_credit_account_day (
ID varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '账单日ID',
CARD_ID varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '卡片ID',
ACTIVE_DATE date DEFAULT NULL COMMENT '生效时间',
BILL_TYPE varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '账单类型',
BILL_DAY mediumint NOT NULL COMMENT '账单日',
IS_DEFAULT tinyint(1) DEFAULT '1' COMMENT '1-当前，0-之前',
NOTE varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
CREATE_BY varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
CREATE_TIME datetime DEFAULT NULL,
UPDATE_BY varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '最后创建人',
UPDATE_TIME datetime DEFAULT NULL COMMENT '最后修改时间',
PRIMARY KEY (ID) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='卡片账单日管理';

-- xace200_card.data_credit_bill definition

CREATE TABLE data_credit_bill (
ID varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL,
ACC_ID varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL,
UBANK_ID varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '用户银行ID',
TYPE tinyint(1) DEFAULT '0' COMMENT '合并类型， 1-合并，0 不合并',
BILL_NAME varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '账单名称',
START_DATE date DEFAULT NULL COMMENT '开始日期',
END_DATE date DEFAULT NULL COMMENT '结束日期',
BILL_DATE date DEFAULT NULL COMMENT '账单日期',
REPAY_DATE date DEFAULT NULL COMMENT '还款日',
AUTO_BILL_MONEY decimal(10,2) DEFAULT NULL COMMENT '流出金额',
AUTO_BACK_MONEY decimal(10,2) DEFAULT NULL COMMENT '流入金额',
AUTO_OUT_MONEY decimal(10,2) DEFAULT NULL,
BILL_MONEY decimal(7,2) DEFAULT NULL COMMENT '账单金额',
REPAY_STATUS tinyint(1) DEFAULT '2' COMMENT '1-无需还款，2-未还款  3-已还款',
ACT_REPAY_DATE date DEFAULT NULL COMMENT '实际还款日期',
NOTE varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '备注',
IS_ARCHIVED tinyint(1) DEFAULT '0' COMMENT '归档  1-归档，历史账单  0-未归档',
CREATE_BY varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
CREATE_TIME datetime DEFAULT NULL,
UPDATE_BY varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '最后创建人',
UPDATE_TIME datetime DEFAULT NULL COMMENT '最后修改时间',
PRIMARY KEY (ID) USING BTREE,
KEY DCB_ACCID (ACC_ID) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='银行账单';

-- xace200_card.data_credit_card definition

CREATE TABLE data_credit_card (
ID varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '卡片ID',
NAME varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '名称',
ACC_ID varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
USER_ID varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '管理用户ID 冗余',
MAN_ID varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
CARD_BH varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '卡片编号',
CARD_NO varchar(8) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '卡号后4位',
CARD_NAME varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '卡片名称',
CARD_ZONE varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '卡组织',
CARD_LEVEL varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '卡片等级',
ACTIVE_DATE date DEFAULT NULL COMMENT '启用日期',
BEGIN_MONTH varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '开始月份',
END_MONTH varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '结束月份',
STATUS varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '状态',
UBANK_ID varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
BANK_ID varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '银行ID',
BILL_TYPE varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT 'FIX -固定账单日  LAST - 每月最后一天账单日',
BILL_DAY smallint DEFAULT NULL COMMENT '账单日',
REPAY_TYPE varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT '1' COMMENT 'FIX-固定还款日，LATER-账单日后多少天',
REPAY_DAY smallint DEFAULT NULL,
LIMIT_MONEY int DEFAULT NULL COMMENT '卡片额度',
FEE int DEFAULT NULL COMMENT '年费',
FEE_NUM int DEFAULT NULL COMMENT '刷卡免年费次数',
FEE_SK_MONEY int DEFAULT NULL COMMENT '刷卡免年费金额',
BILL_DAY_TYPE varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT '' COMMENT '账单日刷卡类型 THIS 计入本期，NEXT 计入下期',
BALANCE decimal(10,2) DEFAULT NULL COMMENT '当前余额',
LIST_ORDER decimal(5,0) DEFAULT NULL COMMENT '排序',
NOTE varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
CREATE_BY varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '创建人',
CREATE_TIME datetime DEFAULT NULL COMMENT '创建时间',
UPDATE_BY varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '最后修改人',
UPDATE_TIME datetime DEFAULT NULL COMMENT '最后修改时间',
PRIMARY KEY (ID) USING BTREE,
UNIQUE KEY UNI_UB (CARD_BH) USING BTREE COMMENT '约束每个人的卡片编号唯一'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='信用卡片管理';

-- xace200_card.data_credit_event definition

CREATE TABLE data_credit_event (
ID varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL,
ACC_ID varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '所属账号',
CARD_ID varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
HAPPENED_DATE date DEFAULT NULL COMMENT '发生日期',
EVENT_TYPE varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '事件类型',
STATUS tinyint(1) DEFAULT '1' COMMENT '1-成功 0-失败',
EVENT varchar(1000) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '描述',
OLD_ED decimal(10,0) DEFAULT '0' COMMENT '原额度',
NEW_ED decimal(10,0) DEFAULT NULL COMMENT '现额度',
CREATE_BY varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
CREATE_TIME datetime DEFAULT NULL,
UPDATE_BY varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '最后创建人',
UPDATE_TIME datetime DEFAULT NULL COMMENT '最后修改时间',
PRIMARY KEY (ID) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='卡片事件管理';

-- xace200_card.data_credit_money definition

CREATE TABLE data_credit_money (
ID varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL,
CARD_ID varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
HAPPEN_DATE date DEFAULT NULL COMMENT '发生日期',
OLD_SUM int DEFAULT NULL COMMENT '原始额度',
NEW_SUM int DEFAULT NULL COMMENT '新额度',
NOTE varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '备注',
CREATE_BY varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
CREATE_TIME datetime DEFAULT NULL,
UPDATE_BY varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '最后创建人',
UPDATE_TIME datetime DEFAULT NULL COMMENT '最后修改时间',
PRIMARY KEY (ID) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='卡片额度管理';

-- xace200_card.data_flow definition

CREATE TABLE data_flow (
ID varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT 'ID',
TRANS_TYPE varchar(3) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '类型',
TYPE varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '收支类型',
FLOW_DATE date NOT NULL,
ACC_ID varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
IN_ACC_ID varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '转入账号ID',
OUT_ACC_ID varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '转出账号ID',
AMOUT decimal(10,2) DEFAULT NULL COMMENT '金额',
CC_ID varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '信用卡卡片ID',
RELATED_ID varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '关联ID',
MAN_ID varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '标签',
NOTE varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
CREATE_BY varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
CREATE_TIME datetime DEFAULT NULL,
UPDATE_BY varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '最后创建人',
UPDATE_TIME datetime DEFAULT NULL COMMENT '最后修改时间',
PRIMARY KEY (ID) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='流水表';

-- xace200_card.data_flow_acc definition

CREATE TABLE data_flow_acc (
ID varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '流水ID',
FLOW_ID varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL,
ACC_ID varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL,
TYPE varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '收支类型',
INCOME decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '收支，1-收入  -1 支出',
PAY decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '金额',
CC_ID varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
OUT_ACC_ID varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '关联账号ID(转账时用)',
BALANCE decimal(10,2) DEFAULT NULL,
ISBACK tinyint(1) DEFAULT '0' COMMENT '1-表示退款',
MAN_ID varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '标签',
FLOW_DATE date NOT NULL COMMENT '日期',
LIST_ORDER int NOT NULL DEFAULT '99' COMMENT '排序',
BILL_ID varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
NOTE varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '备注',
LIST_ORDER2 int NOT NULL,
PRIMARY KEY (ID) USING BTREE,
KEY DFA_FLOWDATE (FLOW_DATE) USING BTREE,
KEY DFA_ACCID (ACC_ID) USING BTREE,
KEY DFA_BILLID (BILL_ID) USING BTREE,
KEY ACCID_FLOW_ID (FLOW_ID) USING BTREE,
KEY DFA_L2 (LIST_ORDER2) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='账号明细表';

-- xace200_card.data_flow_order definition

CREATE TABLE data_flow_order (
ORDER_ID varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '订单编号',
USER_ID varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '用户ID',
TYPE int DEFAULT '31' COMMENT '电商类型',
ACC_ID varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '店铺',
ORDER_ITEM varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '采购细目',
AMOUNT decimal(10,2) DEFAULT NULL,
ORDER_NO varchar(30) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '订单编号',
EXPRESS_NO varchar(30) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '快递编号',
ORDER_DATE date DEFAULT NULL COMMENT '订单日期',
DEST varchar(30) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
FORM_STATUS varchar(30) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT '0' COMMENT '状态 0-未报单，1-已报单',
BACK_DATE date DEFAULT NULL COMMENT '回款日期',
PAY_TYPE varchar(30) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '支付方式',
PRE_PAY_ACC_ID varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '预定支付账户',
PRE_PAY_AMOUNT decimal(10,2) DEFAULT NULL COMMENT '预定金额',
PRE_PAY_DATE date DEFAULT NULL COMMENT '预定日期',
PAY_ACC_ID varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '支付卡片',
PAY_AMOUNT decimal(10,2) DEFAULT NULL COMMENT '支付费用',
PAY_LP1 decimal(10,2) DEFAULT NULL COMMENT '苏宁卡/京东礼品卡',
PAY_LP2 decimal(10,2) DEFAULT NULL COMMENT '京东钢镚',
PAY_LP3 decimal(10,2) DEFAULT NULL COMMENT '苏宁云钻/京豆',
PAY_YH decimal(10,2) DEFAULT NULL COMMENT '银行支付立减',
PAY_NOTE varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '支付备注',
PAY_SK decimal(10,2) DEFAULT NULL,
BACK_AMOUNT decimal(10,2) DEFAULT NULL,
BACK_LP2 decimal(10,2) DEFAULT NULL COMMENT '返钻',
BACK_ACC_ID varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
REC_DATE date DEFAULT NULL COMMENT '签收日期',
REFUND_DATE date DEFAULT NULL COMMENT '退款日期',
COST decimal(10,2) DEFAULT NULL COMMENT '成本',
BENEFIT decimal(10,2) DEFAULT NULL COMMENT '利润',
ALL_BENEFIT varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
NOTE varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
CREATE_BY varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
CREATE_TIME datetime DEFAULT NULL,
UPDATE_BY varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '最后创建人',
UPDATE_TIME datetime DEFAULT NULL COMMENT '最后修改时间',
PRIMARY KEY (ORDER_ID) USING BTREE,
UNIQUE KEY ORDER_NO_INDEX (ORDER_NO) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='撸货表';

-- xace200_card.data_flow_recharge definition

CREATE TABLE data_flow_recharge (
ID varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL,
IN_ACC_ID varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '充值账户',
CHARGE_TYPE varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL DEFAULT '',
OUT_ACC_ID varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL,
CHARGE_DATE date NOT NULL COMMENT '充值日期',
AMOUNT decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '充值金额',
ADD_AMOUNT decimal(10,2) DEFAULT '0.00' COMMENT '赠送金额',
NOTE varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '备注',
CREATE_BY varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '创建人',
CREATE_TIME datetime DEFAULT NULL COMMENT '创建时间',
UPDATE_BY varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '最后修改人',
UPDATE_TIME datetime DEFAULT NULL COMMENT '最后修改时间',
PRIMARY KEY (ID) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='充值记录表';

-- xace200_card.data_report definition

CREATE TABLE data_report (
F_Id varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '	主键ID',
F_CategoryId varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '分组ID',
F_FullName varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '报表名称',
F_Content longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '报表内容',
F_EnCode varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '编码',
F_SortCode varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '排序码(默认0)',
F_EnabledMark int DEFAULT NULL COMMENT '状态(0-默认，禁用，1-启用)',
F_Description varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '描述或说明',
F_CreatorTime datetime DEFAULT NULL COMMENT '创建时间',
F_CreatorUserId varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建用户',
F_LastModifyTime datetime DEFAULT NULL COMMENT '修改时间',
F_LastModifyUserId varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改用户',
F_DeleteMark int DEFAULT NULL COMMENT '删除标志(默认0)',
F_DeleteTime datetime DEFAULT NULL COMMENT '删除时间',
F_DeleteUserId varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '删除用户id',
PRIMARY KEY (F_Id) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='数据报表';

-- xace200_card.data_report_auto_credit definition

CREATE TABLE data_report_auto_credit (
ACC_ID varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL,
NAME varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
LIST_ORDER int DEFAULT NULL,
BALANCE1 decimal(10,2) DEFAULT NULL,
BALANCE2 decimal(10,2) DEFAULT NULL,
COMPARE decimal(10,2) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC;

-- xace200_card.data_report_auto_data definition

CREATE TABLE data_report_auto_data (
ID varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT 'ID',
TYPE varchar(60) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '类型',
NAME varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '名称',
BALANCE decimal(10,2) DEFAULT NULL COMMENT '余额',
NOTE varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '备注',
CREATE_DATE date DEFAULT NULL,
f_tenantid varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '租户id',
f_flowid varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '流程id',
PRIMARY KEY (ID) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='自动统计表';

-- xace200_card.data_sys_bank definition

CREATE TABLE data_sys_bank (
ID varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL DEFAULT '' COMMENT 'UUID',
BANK_TYPE varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '银行类型',
BANK_ICON varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '银行logo地址',
BANK_NAME varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '银行名称',
BANK_URL varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '银行网站地址',
BANK_KEY varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '银行简称',
CREDIT_TYPE varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT '1' COMMENT '信用卡账号属性',
NOTE varchar(500) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
CREATE_BY varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
CREATE_TIME datetime DEFAULT NULL,
UPDATE_BY varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '最后创建人',
UPDATE_TIME datetime DEFAULT NULL COMMENT '最后修改时间',
PRIMARY KEY (ID) USING BTREE,
UNIQUE KEY UNM_NAME (BANK_NAME) USING BTREE,
UNIQUE KEY UNM_JS (BANK_KEY) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='银行信息管理';

-- xace200_card.data_sys_man definition

CREATE TABLE data_sys_man (
ID varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL,
NAME varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '持卡人代称',
SEX varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT '1' COMMENT '1-男 2-女',
BIRTHDAY date DEFAULT NULL COMMENT '出生日期，确保月和日是正确的。',
LIST_ORDER smallint DEFAULT '0' COMMENT '排序',
CREATE_BY varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
CREATE_TIME datetime DEFAULT NULL,
UPDATE_BY varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '最后创建人',
UPDATE_TIME datetime DEFAULT NULL COMMENT '最后修改时间',
PRIMARY KEY (ID) USING BTREE,
UNIQUE KEY UNI_DM_NAME (NAME) USING BTREE COMMENT '确保同一个账号的管理人代号唯一'
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='持卡账户管理';

-- xace200_card.data_sys_paytype definition

CREATE TABLE data_sys_paytype (
ID varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL,
NAME varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '名称',
PARENT_ID varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '父ID',
TYPE varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '类型 1-收入，2-支出 3-转账',
IS_SYSTEM tinyint(1) DEFAULT '0' COMMENT '系统分类',
LIST_ORDER int DEFAULT NULL,
PY varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '拼音',
CREATE_BY varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '创建人',
CREATE_TIME datetime DEFAULT NULL COMMENT '创建时间',
UPDATE_BY varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '最后修改人',
UPDATE_TIME datetime DEFAULT NULL COMMENT '最后修改时间',
PRIMARY KEY (ID) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='收支类型表';

-- xace200_card.data_sys_pos definition

CREATE TABLE data_sys_pos (
ID varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL DEFAULT '' COMMENT 'UUID',
NAME varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '支付机构全称',
SHORT_NAME varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
WEBSITE varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '网站',
PAYLICENSE varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '支付牌照图片地址',
CONTENT varchar(2000) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '收单范围',
EXPIRE_DATE date DEFAULT NULL COMMENT '牌照过期时间',
CREATE_BY varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
CREATE_TIME datetime DEFAULT NULL,
UPDATE_BY varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '最后创建人',
UPDATE_TIME datetime DEFAULT NULL COMMENT '最后修改时间',
PRIMARY KEY (ID) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='支付机构管理';

-- xace200_card.data_sys_user_bank definition

CREATE TABLE data_sys_user_bank (
ID varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL,
MAN_ID varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '持卡人',
BANK_ID varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL,
DEPT_ADDRESS varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
HOME_ADDRESS varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
ADDR_TYPE varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '账单地址  1-单位地址 2-家庭地址',
LIMIT_MONEY int DEFAULT '0' COMMENT '额度,来源于具体的卡片，自动汇聚',
POINT double DEFAULT NULL COMMENT '当前积分数',
NOTE varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
CARD_TYPE tinyint(1) DEFAULT NULL COMMENT '信用卡类型，合并账户合并还款，分别还款',
CREATE_BY varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
CREATE_TIME datetime DEFAULT NULL,
UPDATE_BY varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '最后创建人',
UPDATE_TIME datetime DEFAULT NULL COMMENT '最后修改时间',
BANKNAME varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '疑问',
PRIMARY KEY (ID) USING BTREE,
UNIQUE KEY UNI_BANK_MAN (MAN_ID,BANK_ID) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='用户银行信息表';

-- xace200_card.data_user_pos_bill definition

CREATE TABLE data_user_pos_bill (
ID varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL,
INIT_MONEY double DEFAULT NULL COMMENT '结算金额',
FEE_MONEY double DEFAULT NULL COMMENT '额外结算手续费，比如立即体现',
BALANCE_MONEY double DEFAULT NULL COMMENT '结算入库金额',
UD_ID varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '结算卡ID',
BALANCE_DATE datetime DEFAULT NULL COMMENT '结算时间',
USER_ID varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
NOTE varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '备注',
CREATE_BY varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
CREATE_DATE datetime DEFAULT NULL,
UPDATE_BY varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '最后创建人',
UPDATE_DATE datetime DEFAULT NULL COMMENT '最后修改时间',
PRIMARY KEY (ID) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='POS结算管理，存在多笔结算和第二天结算';

-- xace200_card.data_wool_mile definition

CREATE TABLE data_wool_mile (
ID varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL,
TYPE tinyint(1) DEFAULT NULL COMMENT '类型，1-增加，2-小号',
MAN_ID varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
AIRLINE varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '航空公司',
ACC_ID varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '入账账户',
MILES int DEFAULT NULL COMMENT '里程',
DEAL_DATE date DEFAULT NULL,
AMOUNT decimal(7,2) DEFAULT NULL COMMENT '折现金额',
IN_ACC_ID varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '入账金额',
NOTE varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '备注',
CREATE_BY varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '创建人',
CREATE_TIME datetime DEFAULT NULL COMMENT '创建时间',
UPDATE_BY varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '最后修改人',
UPDATE_TIME datetime DEFAULT NULL COMMENT '最后修改时间',
PRIMARY KEY (ID) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='里程管理';

-- xace200_card.data_wool_wool definition

CREATE TABLE data_wool_wool (
ID varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL,
TYPE int DEFAULT NULL COMMENT '类型',
SRC_ACC_ID varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '来源ID',
SRC_POINT decimal(10,2) DEFAULT NULL,
AMOUNT decimal(10,2) DEFAULT NULL COMMENT '折算金额',
ACITIVITY varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '活动名称',
GOODS varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '物品名称',
IN_DATE date DEFAULT NULL COMMENT '入账日期',
IN_ACC_ID varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '入账账户',
FLOW_AMOUNT decimal(5,2) DEFAULT NULL COMMENT '入账金额',
NOTE varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '备注',
CREATE_BY varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '创建人',
CREATE_TIME datetime DEFAULT NULL COMMENT '创建时间',
UPDATE_BY varchar(36) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '最后修改人',
UPDATE_TIME datetime DEFAULT NULL COMMENT '最后修改时间',
PRIMARY KEY (ID) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='羊毛记录表';

-- xace200_card.data_worklog definition

CREATE TABLE data_worklog (
Id varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '主键',
work_date datetime DEFAULT NULL COMMENT '工作日期',
timeunits decimal(10,0) DEFAULT NULL COMMENT '工作时长',
content varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '工作内容',
problem varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '遗留问题',
F_CreatorUserId varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '创建用户',
F_CreatorTime datetime DEFAULT NULL COMMENT '创建时间',
f_flowid varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '流程id',
f_tenantid varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '租户id',
PRIMARY KEY (Id) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC COMMENT='工作日志';  当前系统中的项目表如上。请结合这个已有表单，重新规划 个人财务收支管理系统功能完善规划




好的，收到。这是一个非常重要的步骤——**基于现有数据模型进行功能规划**。这能确保新功能的实现能够最大化地复用现有数据结构，减少数据迁移和冗余，保证系统的一致性。

在详细分析了您提供的26个表结构后，我对您现有系统的理解是：

*   **核心非常扎实**：以 `data_acc` (账户)、`data_flow` (流水)、`data_credit_card` (信用卡) 和 `data_credit_bill` (账单) 为核心，已经构建了强大的收支记录和信用卡管理能力。
*   **账户体系完善**：`data_acc` 通过 `PARENT_ID` 实现了账户的树形结构，并且通过 `data_acc_credit_card`, `data_acc_debit_card`, `data_acc_shop` 等表进行了专业化扩展，这是一个非常好的基础。
*   **业务覆盖广泛**：已经涉及到了“羊毛管理” (`data_wool_wool`, `data_wool_mile`) 和“订单管理” (`data_flow_order`)，显示出系统不止于简单的记账。
*   **数据模型偏向于关联**：大量使用 `ID` 和 `_ID` 后缀的字段进行表间关联，逻辑清晰。

现在，让我们结合这个已有的、坚实的数据基础，重新审视和调整之前的“功能完善规划”，使其更具落地性。

---

## 个人财务收支管理系统功能完善规划 (基于现有数据模型)

### 系统概述

基于现有强大的账户、流水和信用卡管理系统，本规划旨在利用和扩展现有数据模型，引入预算、债务、资产、目标等高级财务管理功能，将系统从一个**精密的记账和卡片管理工具**，升级为一个**全面的个人财务规划与决策支持平台**。

### 现有功能分析 (基于表结构反推)

*   **账户管理 (`data_acc` & 子表)**: ✅ 支持多类型账户（借记卡、信用卡、电商账户、POS机），支持账户层级，余额管理。
*   **信用卡管理 (`data_credit_card` & 相关表)**: ✅ 极为详细，包含卡片信息、额度、账单日、还款日、年费策略、事件、额度变更等。
*   **账单管理 (`data_credit_bill`)**: ✅ 完善的信用卡账单生成、还款状态跟踪。
*   **流水管理 (`data_flow`, `data_flow_acc`)**: ✅ 核心流水记录，支持多种交易类型（收/支/转），关联账户和信用卡。
*   **收支分类 (`data_sys_paytype`)**: ✅ 支持树形结构的收支分类。
*   **订单/羊毛管理 (`data_flow_order`, `data_wool_*`)**: ✅ 已具备特定场景下的高级功能。
*   **报表 (`data_report_*`)**: ✅ 具备基础的报表和数据统计能力。

### 需要完善的功能模块 (重新规划)

#### **第一阶段 (高优先级) - 核心体验与控制力强化**

##### **1. 预算管理模块 🆕**

这个模块是第一优先级，因为它能直接利用 `data_flow` 和 `data_sys_paytype`，为用户提供急需的财务控制能力。

*   **1.1 预算设置**
    *   **月度/年度预算**: 利用 `data_sys_paytype` (收支分类) 设置预算限额。
        *   **实现建议**: 新建 `fin_budget` (预算主表) 和 `fin_budget_item` (预算子项表)。`fin_budget_item` 关联 `data_sys_paytype.ID`。
    *   **账户预算**: 按 `data_acc` (账户) 设置预算限额。
        *   **实现建议**: 在 `fin_budget_item` 中增加一个 `acc_id` 字段，允许预算项关联到账户或账户分类。

*   **1.2 预算监控与分析**
    *   **预算执行进度**: 实时聚合 `data_flow` 中符合周期、分类、账户的支出数据，与 `fin_budget_item` 中的预算额进行对比。
    *   **预算预警**: 达到阈值时触发提醒。
    *   **预算报表**: 生成月度/年度执行报告，对 `fin_budget` 和聚合后的 `data_flow` 数据进行对比分析。

##### **2. 移动端适配与优化 🆕**

*   **2.1 响应式设计**: 确保所有现有和新增页面在移动端可用。
*   **2.2 快速记账**: 优化 `data_flow` 的录入流程。
    *   **实现建议**: 创建一个简化的前端组件，后端接口不变。
*   **2.3 语音/拍照记账 (OCR/NLP)**: 智能解析数据并填充到 `data_flow` 的对应字段。

##### **3. 智能提醒功能 🆕**

*   **3.1 提醒中心**: 新建 `sys_notification` 表来管理所有提醒。
*   **3.2 提醒类型**:
    *   **账单提醒**: 基于 `data_credit_bill` 的 `REPAY_DATE`。
    *   **预算提醒**: 基于预算监控逻辑。
    *   **卡片事件提醒**: 基于 `data_credit_card` 的 `END_MONTH` (卡片到期) 或 `data_credit_event` (自定义事件)。

---

#### **第二阶段 (中优先级) - 财务全景构建**

##### **5. 债务管理模块 (对现有功能的整合与增强)**

您的系统已有强大的信用卡债务管理。此模块旨在将其扩展并统一管理所有负债。

*   **5.1 负债记录**
    *   **信贷产品管理**:
        *   **实现建议**: 扩展 `data_acc` 的账户类型，或新建一个 `fin_debt` 表来专门记录非信用卡的债务，如房贷、车贷、消费贷。`fin_debt` 表需包含本金、利率、期限、还款方式等字段。
    *   **还款计划**: 基于 `fin_debt` 的信息自动生成还款计划表 (`fin_debt_repayment_schedule`)。
    *   **提前还款**: 在 `fin_debt` 模块中加入计算器和记录功能。

*   **5.2 债务分析**
    *   **负债率分析**:
        *   **实现建议**: `总负债` = `SUM(data_credit_bill.BILL_MONEY)` (未还账单) + `SUM(fin_debt.remaining_principal)`。 `总资产` 来自下文的资产模块。
    *   **利息成本统计**: 聚合 `data_flow` 中所有利息支出和 `fin_debt_repayment_schedule` 中的利息部分。
    *   **债务优化建议**: 分析 `data_credit_card` 和 `fin_debt` 中的利率，提供优化建议。

##### **6. 资产管理模块 (对现有功能的整合与增强)**

此模块旨在将所有账户和新增资产类型统一管理，计算净资产。

*   **6.1 资产登记**
    *   **金融资产**:
        *   **实现建议**: `data_acc` 已能很好地代表银行存款等。对于股票、基金等投资，初期可在 `data_acc` 中创建手动更新市值的“投资账户”。
    *   **不动产/动产**:
        *   **实现建议**: 新建 `fin_asset` 表，用于记录房产、汽车等非金融资产。包含字段：`asset_type`, `name`, `estimated_value`, `valuation_date`。

*   **6.2 资产分析**
    *   **资产结构**: 聚合 `data_acc` 的 `BALANCE` 和 `fin_asset` 的 `estimated_value`，按类型进行分析。
    *   **净资产计算**: **`净资产 = SUM(data_acc.BALANCE where type is positive) + SUM(fin_asset.estimated_value) - (SUM(data_credit_bill.BILL_MONEY) + SUM(fin_debt.remaining_principal))`**。
    *   **净资产趋势**:
        *   **实现建议**: 新建 `fin_net_worth_snapshot` 表，通过定时任务每日或每月记录计算出的总资产、总负债、净资产值，用于生成趋势图。

##### **7. 财务目标管理模块 🆕**

*   **7.1 目标设定与追踪**
    *   **实现建议**: 新建 `fin_goal` 表，包含目标名称、金额、期望日期等。
    *   **资金来源**: 目标的资金可以虚拟地从 `data_acc` 账户“划转”。每次划转，本质是创建一个特殊的 `data_flow` 记录（从储蓄账户到目标虚拟账户），并更新 `fin_goal` 的已存金额。

---

#### **第三阶段 (低优先级) - 智能化与生态扩展**

##### **8. 投资理财管理模块 (高级功能)**

*   **8.1 投资账户与交易记录**
    *   **实现建议**: 新建 `inv_account` (投资账户，可关联 `data_acc`), `inv_holding` (持仓), `inv_transaction` (交易记录：买入/卖出/分红等)。这是对资产管理中“投资账户”的深化。
*   **8.2 收益与报表分析**: 基于以上投资相关表进行复杂的数据聚合与计算。

##### **9. 智能分析与建议模块 (高级功能)**

*   **9.1 消费行为分析**: 基于 `data_flow` 和 `data_sys_paytype` 进行机器学习建模。
*   **9.2 财务健康评估**:
    *   **实现建议**: 基于计算出的各项财务比率（储蓄率、负债率等），结合规则引擎或模型，生成综合评分和建议。

##### **10. 家庭财务管理模块 🆕**

*   **10.1 家庭成员管理**:
    *   **实现建议**: 您现有的 `data_sys_man` (持卡人) 表是实现此功能的绝佳基础！可以将其扩展为“家庭成员”，并在所有核心表（`data_acc`, `data_flow` 等）中利用 `MAN_ID` 来进行数据隔离和授权。
    *   **共同账户**: 创建一个 `data_acc` 账户，并允许多个 `MAN_ID` 对其有操作权限。



### 实施优先级与技术实现总结

**实施优先级（已根据数据模型调整）：**

*   **第一阶段**：预算管理、移动端优化、智能提醒。**（低成本、高回报，能最大化利用现有数据）**
*   **第二阶段**：债务管理（整合）、资产管理（整合）、财务目标。**（构建产品核心价值，需要新建少量核心表，但逻辑复用性高）**
*   **第三阶段**：投资理财（深度）、智能分析、家庭财务（扩展）、高级同步。**（高投入、高壁垒，需要大量新建表和复杂算法/集成）**

**技术实现建议（已更新）：**

1.  **数据库设计**:
    *   **核心思路**：**优先扩展和利用现有表**，如 `data_acc`, `data_sys_man`。
    *   **新建表建议 (带前缀以区分)**:
        *   `fin_budget`, `fin_budget_item` (预算)
        *   `fin_debt`, `fin_debt_repayment_schedule` (非信用卡债务)
        *   `fin_asset` (非金融资产)
        *   `fin_net_worth_snapshot` (净资产快照)
        *   `fin_goal` (财务目标)
        *   `inv_account`, `inv_holding`, `inv_transaction` (投资，第三阶段)
        *   `sys_notification` (消息中心)
2.  **API与后端逻辑**:
    *   保持现有风格。
    *   大量查询将围绕 `data_flow` 进行，务必保证其索引优化和查询性能。
    *   开发定时任务（如：生成账单、记录净资产快照、发送提醒）。
3.  **前端**:
    *   专注于组件化开发，特别是预算、资产、债务等模块的可视化组件。

这份基于您现有数据模型调整后的规划，更加贴近实际，实施路径也更为清晰。它充分肯定并利用了您已有的扎实工作，在此基础上进行有序、高效的扩展。



好的，遵照您的要求，我们来对**第一阶段（高优先级）**的所有功能点进行一次极其详尽的、可作为开发基础的拆解。

这份文档将包含每个功能点的**用户故事、功能描述、前后端实现要点、关联数据表以及UI/UX建议**，力求覆盖开发前需要明确的所有细节。

---

## 第一阶段（高优先级）功能模块详细设计方案 (PRD)

### 模块一：预算管理模块

**核心目标**: 帮助用户从被动记账转向主动规划，提升财务掌控力，增强用户粘性。

#### **1.1 预算设置**

##### **1.1.1 功能点：周期预算创建与管理**

*   **用户故事**: 作为一个用户，我希望能按月或按年创建一个预算方案，并能自定义它的起止日期，方便我对齐我的工资周期。
*   **功能描述**:
    1.  用户可以创建一个新的预算方案。
    2.  创建时需选择预算周期类型：**月度 (Monthly)** 或 **年度 (Yearly)**。
    3.  用户可以自定义预算的**起始日期**。选择月度预算后，结束日期自动计算（如：1月15日开始，则2月14日结束）。
    4.  系统自动生成预算名称（如“2023年10月预算”），用户可修改。
    5.  支持查看、编辑、删除历史预算方案。
*   **后端实现**:
    *   **新建表 `fin_budget`**:
        ```sql
        CREATE TABLE `fin_budget` (
          `ID` varchar(36) NOT NULL COMMENT '主键ID',
          `USER_ID` varchar(36) NOT NULL COMMENT '用户ID, 关联至现有用户体系',
          `NAME` varchar(100) NOT NULL COMMENT '预算名称',
          `PERIOD_TYPE` enum('MONTHLY', 'YEARLY') NOT NULL COMMENT '周期类型',
          `START_DATE` date NOT NULL COMMENT '开始日期',
          `END_DATE` date NOT NULL COMMENT '结束日期',
          `TOTAL_BUDGET_AMOUNT` decimal(12,2) DEFAULT NULL COMMENT '总预算金额 (由子项汇总)',
          `STATUS` enum('ACTIVE', 'ARCHIVED') DEFAULT 'ACTIVE' COMMENT '状态：激活/归档',
          `CREATE_TIME` datetime,
          `UPDATE_TIME` datetime,
          PRIMARY KEY (`ID`),
          KEY `idx_user_date` (`USER_ID`, `START_DATE`)
        ) COMMENT='预算主表';
        ```
    *   提供对 `fin_budget` 表的 CRUD API。
*   **前端实现**:
    *   提供一个预算列表页，展示所有预算方案。
    *   创建一个预算设置向导或表单，包含周期类型选择器、日期选择器等组件。

##### **1.1.2 功能点：分类预算与账户预算**

*   **用户故事**:
    *   (分类预算) 我想为我的“餐饮”和“购物”类别设置每月花费上限。
    *   (账户预算) 我想限制我这个月用“招行信用卡”消费不能超过2000元。
*   **功能描述**:
    1.  在一个预算方案内，用户可以添加多个预算子项。
    2.  每个预算子项可以基于**“收支分类”**或**“账户”**来设定。
    3.  **类型为“分类”**: 用户从 `data_sys_paytype` 树形结构中选择一个或多个支出分类，并为其设置预算金额。
    4.  **类型为“账户”**: 用户从 `data_acc` 树形结构中选择一个或多个账户，并为其设置预算金额。
    5.  用户可以同时设置分类预算和账户预算（例如，为“餐饮”设置1500元预算，同时为“招行信用卡”设置2000元预算）。
*   **后端实现**:
    *   **新建表 `fin_budget_item`**:
        ```sql
        CREATE TABLE `fin_budget_item` (
          `ID` varchar(36) NOT NULL,
          `BUDGET_ID` varchar(36) NOT NULL COMMENT '关联 fin_budget.ID',
          `ITEM_TYPE` enum('CATEGORY', 'ACCOUNT') NOT NULL COMMENT '预算项类型: 分类/账户',
          `TARGET_ID` varchar(36) NOT NULL COMMENT '目标ID (关联 data_sys_paytype.ID 或 data_acc.ID)',
          `BUDGETED_AMOUNT` decimal(12,2) NOT NULL COMMENT '预算金额',
          `CREATE_TIME` datetime,
          `UPDATE_TIME` datetime,
          PRIMARY KEY (`ID`),
          KEY `idx_budget_id` (`BUDGET_ID`)
        ) COMMENT='预算子项表';
        ```
    *   API 需要能根据 `ITEM_TYPE` 关联到不同的表 (`data_sys_paytype` 或 `data_acc`) 来获取名称等信息。
*   **前端实现**:
    *   在预算设置页面，提供“添加预算项”功能。
    *   弹窗中让用户先选择“按分类”或“按账户”。
    *   根据选择，分别显示 `data_sys_paytype` 或 `data_acc` 的树形选择器。

#### **1.2 预算监控与分析**

##### **1.2.1 功能点：预算执行实时监控**

*   **用户故事**: 我想随时查看我各项预算的开销进度，知道还剩多少钱可以花。
*   **功能描述**:
    1.  提供一个预算监控仪表盘页面，展示当前激活的预算方案 (`fin_budget` where `STATUS`='ACTIVE')。
    2.  对于每一个预算子项 (`fin_budget_item`)，系统需要实时计算其**“实际支出”**。
    3.  **计算逻辑**:
        *   `IF item.ITEM_TYPE == 'CATEGORY'`: `SUM(data_flow.AMOUT)` where `data_flow.TYPE` (收支分类ID) in `(item.TARGET_ID 及其所有子分类ID)` and `data_flow.TRANS_TYPE`='支出' and `data_flow.FLOW_DATE` between `budget.START_DATE` and `budget.END_DATE`.
        *   `IF item.ITEM_TYPE == 'ACCOUNT'`: `SUM(data_flow.AMOUT)` where `data_flow.ACC_ID` in `(item.TARGET_ID 及其所有子账户ID)` and `data_flow.TRANS_TYPE`='支出' and `data_flow.FLOW_DATE` between `budget.START_DATE` and `budget.END_DATE`.
    4.  前端以进度条、百分比、具体数字（已用/预算）的形式可视化展示每个子项的执行情况。
*   **后端实现**:
    *   提供一个高效的API (`GET /api/budgets/current/dashboard`)，该API会执行上述聚合查询逻辑。
    *   **性能考量**: `data_flow` 表会非常大，必须确保 `TYPE`, `ACC_ID`, `FLOW_DATE` 字段上有合适的索引。对于非常大的数据集，可以考虑使用物化视图或缓存来优化查询性能。
*   **前端实现**:
    *   设计仪表盘UI，使用卡片和列表展示总览和各子项进度。
    *   进度条根据百分比变换颜色（绿 -> 黄 -> 红）。

##### **1.2.2 功能点：预算报表与分析**

*   **用户故事**: 月底了，我想看看这个月预算的最终执行结果，分析哪些地方超支了，以便下个月改进。
*   **功能描述**:
    1.  当一个预算周期结束，用户可以查看其最终的执行报告。
    2.  报告内容包括：总预算、总支出、总结余/超支。
    3.  以表格形式详细对比每个预算子项的“预算金额”与“实际支出”，并计算“差额”。
    4.  用户可以点击任何一个超支项，下钻查看构成该项支出的所有 `data_flow` 流水记录。
*   **后端实现**:
    *   提供一个API (`GET /api/budgets/{id}/report`)，逻辑与实时监控类似，但查询的是一个已归档的预算周期。
    *   下钻查询API (`GET /api/budgets/report/details?itemId=...`)，根据预算子项ID返回其关联的所有流水。
*   **前端实现**:
    *   设计专门的报告页面，UI/UX要清晰易读，像一份财务体检报告。
    *   使用图表（如柱状图）进行“预算 vs 实际”的可视化对比。

---

### 模块二：移动端适配与优化

#### **2.1 功能点：全站响应式设计**

*   **用户故事**: 我希望在手机上使用这个系统时，不需要左右拖动，所有内容都能清晰地展示，按钮也方便我点击。
*   **功能描述**: 系统的所有页面，包括登录、仪表盘、列表、表单、报表等，都必须能在移动端屏幕上正确、美观地显示。
*   **实现要点**:
    *   **CSS框架**: 使用支持响应式的CSS框架，如 Bootstrap, Tailwind CSS，或使用CSS媒体查询 (Media Queries)。
    *   **布局**: 采用流式布局 (Fluid Grids) 和弹性布局 (Flexbox/Grid)。在小屏幕上，多列表格应变为单列卡片式布局；侧边栏导航应收起到汉堡菜单中。
    *   **元素尺寸**: 使用相对单位（如 `rem`, `%`, `vw`）而非固定像素 `px`。确保按钮和链接有足够大的点击区域。
    *   **测试**: 必须在多种尺寸的真实设备或模拟器上进行充分测试。

#### **2.2 功能点：快速记账组件**

*   **用户故事**: 我消费完后，希望能用最快的速度记下一笔账，不要让我填太多东西。
*   **功能描述**:
    1.  在移动端界面提供一个全局、悬浮的“+”按钮。
    2.  点击后，弹出一个模态框或底部滑出面板，作为快速记账界面。
    3.  界面仅包含：**金额输入框（唤起数字键盘）、分类选择、账户选择、日期（默认为今天）、备注（可选）**。
    4.  分类和账户选择器应优先展示用户最近使用或标记为“常用”(`FAV_MARK=1`)的选项。
*   **后端实现**:
    *   **复用现有接口**: 此功能主要在前端，后端复用现有的创建 `data_flow` 的API即可。无需改动。
*   **前端实现**:
    *   开发一个高度优化的Vue/React组件。
    *   组件内部状态管理要高效，确保弹出迅速。
    *   与后端API交互，保存成功后给出“记账成功”的轻提示 (Toast)。

#### **2.3 功能点：语音/拍照记账 (OCR/NLP)**

*   **用户故事**: 我懒得打字，希望能直接说“昨天打车30块”或者拍张小票就自动记账。
*   **功能描述**:
    1.  在快速记账组件中，增加“麦克风”和“相机”图标。
    2.  **语音记账**:
        *   点击麦克风，调用设备麦克风录音。
        *   将音频文件发送到后端或调用第三方语音识别（ASR）服务，转换为文本（如“昨天打车30块”）。
        *   后端对文本进行自然语言处理（NLP），提取关键实体：**时间（昨天）、金额（30）、事件/分类关键词（打车）**。
        *   将解析出的实体返回给前端，自动填充到记账表单中，由用户最终确认。
    3.  **拍照记账**:
        *   点击相机，调用设备相机或相册。
        *   将图片文件发送到后端或调用第三方光学字符识别（OCR）服务。
        *   后端对OCR返回的文本块进行分析，尝试识别**商户名称、总金额、交易日期**等。
        *   将识别结果返回前端，填充表单，待用户确认。
*   **后端实现**:
    *   **新建API**: `POST /api/flows/parse-voice`, `POST /api/flows/parse-image`。
    *   **集成第三方服务**: 调研并集成成熟的ASR和OCR云服务（如阿里云、腾讯云、百度云等）。这些服务通常比自研成本低、效果好。
    *   **NLP规则引擎**: 开发一套规则或简单的模型来从文本中提取实体。例如，包含“元/块”的数字是金额，包含“昨天/今天”的是时间，包含“打车/吃饭/购物”的是分类关键词。
*   **前端实现**:
    *   集成设备API来调用麦克风和相机。
    *   处理文件上传。
    *   接收后端解析的结果并填充表单。

---

### 模块三：智能提醒功能

#### **3.1 功能点：统一提醒中心**

*   **用户故事**: 我想在一个地方看到我所有的重要提醒，比如还款、预算超支等，并且能管理这些消息。
*   **功能描述**:
    1.  在系统导航栏增加一个“铃铛”图标，显示未读消息数量。
    2.  点击后进入消息中心页面，以列表形式展示所有历史通知。
    3.  每条通知包含：**类型图标、标题、摘要、时间**。
    4.  支持将消息标记为已读/未读，或删除。
*   **后端实现**:
    *   **新建表 `sys_notification`**:
        ```sql
        CREATE TABLE `sys_notification` (
          `ID` varchar(36) NOT NULL,
          `USER_ID` varchar(36) NOT NULL,
          `TITLE` varchar(255) NOT NULL,
          `CONTENT` text,
          `NOTIF_TYPE` varchar(50) COMMENT '提醒类型: BILL_REPAY, BUDGET_ALERT, CARD_EVENT等',
          `RELATED_ID` varchar(36) COMMENT '关联对象ID (如账单ID, 预算项ID)',
          `IS_READ` tinyint(1) DEFAULT '0' COMMENT '0-未读, 1-已读',
          `CREATE_TIME` datetime,
          PRIMARY KEY (`ID`),
          KEY `idx_user_read` (`USER_ID`, `IS_READ`)
        ) COMMENT='系统通知表';
        ```
    *   提供获取通知列表、标记已读、获取未读数量的API。
*   **前端实现**:
    *   开发通知中心页面组件。
    *   开发轮询或WebSocket机制来实时更新未读消息数。

#### **3.2 功能点：多类型提醒服务**

*   **用户故事**: 我希望系统能在信用卡该还款时、预算快用完时、卡片快过期时主动告诉我。
*   **功能描述**: 后端通过定时任务（如Cron Job）定期扫描，满足条件时在 `sys_notification` 表中创建记录，并触发推送。
*   **后端实现 (定时任务逻辑)**:
    1.  **账单还款提醒**:
        *   **触发**: 每天凌晨执行。
        *   **逻辑**: 扫描 `data_credit_bill` 表，找出所有 `REPAY_STATUS=2` (未还款) 且 `REPAY_DATE` 在未来N天内（如3天或1天）的记录。
        *   **动作**: 为每条记录的 `USER_ID` 生成一条还款提醒。
    2.  **预算提醒**:
        *   **触发**: 每小时或每几小时执行。
        *   **逻辑**: 对所有激活的预算，重新计算其实际支出（同1.2.1的逻辑）。检查是否有任何子项的 `(实际支出 / 预算金额)` 首次超过了用户设定的阈值（如80%, 90%）。
        *   **动作**: 为达到阈值的预算项生成提醒。需要记录已提醒过的阈值，避免重复提醒。
    3.  **卡片事件提醒**:
        *   **触发**: 每天凌晨执行。
        *   **逻辑**:
            *   扫描 `data_credit_card`，找出 `END_MONTH` (卡片有效期) 在未来M个月内（如2个月）的卡片。
            *   扫描 `data_credit_event`，找出用户自定义的、即将发生的事件。
        *   **动作**: 生成对应的卡片到期或事件提醒。
    *   **推送服务**: 在生成通知记录后，调用推送服务（如集成Firebase Cloud Messaging, APNs, 或第三方推送SDK）将消息推送到用户的移动设备。

---

这份详细的设计方案应该能为您的开发团队提供一个清晰、可执行的蓝图。每个功能点都明确了其价值、实现方式和数据依赖，有助于顺利推进第一阶段的开发工作。