<template>
  <div class="column-design-container">
    <div class="main-board">
      <xh-group-title content="查询字段" :bordered="false" />
      <a-table :data-source="columnData.searchList" :columns="searchColumns" size="small" :pagination="false" rowKey="id" class="search-table">
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'drag'">
            <i class="drag-handler icon-ym icon-ym-darg" title="点击拖动"></i>
          </template>
          <template v-if="column.key === 'label' && webType == WebType.DATA_VIEW">
            <a-input v-model:value="record.label" placeholder="列名" allowClear />
          </template>
          <template v-if="column.key === 'searchType'">
            <xh-select v-model:value="record.searchType" :options="searchTypeOptions" :disabled="!['input', 'textarea'].includes(record.xhKey)" />
          </template>
          <template v-if="column.key === 'searchMultiple'">
            <a-checkbox v-model:checked="record.searchMultiple" :disabled="!multipleList.includes(record.xhKey)" />
          </template>
        </template>
      </a-table>
      <xh-group-title content="列表字段" :bordered="false" class="mt-20px" help-message="开启列拖拽后需设置列宽度，且列宽度只能为数字" />
      <a-table :data-source="columnData.columnList" :columns="columnColumns" size="small" :pagination="false" rowKey="id" class="column-table">
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'drag'">
            <i class="drag-handler icon-ym icon-ym-darg" title="点击拖动"></i>
          </template>
          <template v-if="column.key === 'label' && webType == WebType.DATA_VIEW">
            <a-input v-model:value="record.label" placeholder="列名" allowClear />
          </template>
          <template v-if="column.key === 'sortable'">
            <a-checkbox v-model:checked="record.sortable" :disabled="record.__config__ && record.__config__.isSubTable" />
          </template>
          <template v-if="column.key === 'ellipsis'">
            <a-checkbox v-model:checked="record.ellipsis" />
          </template>
          <template v-if="column.key === 'resizable'">
            <a-checkbox v-model:checked="record.resizable" />
          </template>
          <template v-if="column.key === 'fixed'">
            <xh-select v-model:value="record.fixed" :options="fixedOptions" :disabled="record.__config__ && record.__config__.isSubTable" />
          </template>
          <template v-if="column.key === 'align'">
            <xh-select v-model:value="record.align" :options="alignOptions" />
          </template>
          <template v-if="column.key === 'width'">
            <a-input-number v-model:value="record.width" placeholder="宽度" :min="0" :precision="0" />
          </template>
          <template v-if="column.key === 'format'">
            <a-button :class="setBtnWarnClass(defaultCellFormat, record.format || '')" block @click="editCellFormat(record)">格式化</a-button>
          </template>
        </template>
      </a-table>
    </div>
    <div class="right-board">
      <a-tabs v-model:activeKey="activeKey" :tabBarGutter="10" class="average-tabs">
        <a-tab-pane key="search" tab="查询字段" />
        <a-tab-pane key="field" tab="列表字段" />
        <a-tab-pane key="column">
          <template #tab>列表属性<i v-show="hasScript" style="color: rgb(235, 181, 99)" class="ym-custom ym-custom-language-javascript"></i></template>
        </a-tab-pane>
      </a-tabs>
      <div class="right-main">
        <div class="h-full" v-show="activeKey === 'search'">
          <a-table
            :data-source="state.searchOptions"
            :columns="rightColumns"
            size="small"
            :pagination="false"
            :scroll="{ y: 'calc(100vh - 161px)' }"
            rowKey="id"
            :row-selection="{ columnWidth: 50, selectedRowKeys: searchSelectedRowKeys, onChange: onSearchSelectChange }">
            <template #headerCell>查询字段</template>
          </a-table>
        </div>
        <div class="h-full" v-show="activeKey === 'field'">
          <a-table
            :data-source="state.columnOptions"
            :columns="rightColumns"
            size="small"
            :pagination="false"
            :scroll="{ y: 'calc(100vh - 161px)' }"
            rowKey="id"
            :row-selection="{ columnWidth: 50, selectedRowKeys: columnSelectedRowKeys, onChange: onColumnSelectChange }">
            <template #headerCell>列表字段</template>
          </a-table>
        </div>
        <ScrollContainer v-show="activeKey === 'column'">
          <a-form :colon="false" labelAlign="left" :labelCol="{ style: { width: '90px' } }" class="right-board-form">
            <div class="typeList">
              <div
                class="item"
                :class="{ 'view-item': webType == WebType.DATA_VIEW }"
                v-for="(item, index) in getTypeList"
                :key="index"
                @click="toggleType(item.value)">
                <div class="item-img" :class="{ checked: columnData.type == item.value }">
                  <img :src="item.url" />
                  <div class="icon-checked" v-if="columnData.type == item.value">
                    <check-outlined />
                  </div>
                </div>
                <p class="item-name">{{ item.name }}</p>
              </div>
            </div>
            <div v-if="columnData.type == TableType.LEFTTREE_TABLE">
              <a-divider>左侧配置</a-divider>
              <a-form-item>
                <template #label>左侧查询<BasicHelp text="暂不支持异步的左侧查询" /></template>
                <a-switch v-model:checked="columnData.hasTreeQuery" />
              </a-form-item>
              <a-form-item label="左侧标题">
                <a-input v-model:value="columnData.treeTitle" placeholder="树形标题" />
              </a-form-item>
              <a-form-item label="数据来源">
                <xh-select
                  v-model:value="columnData.treeDataSource"
                  :options="treeDataSourceOptions"
                  showSearch
                  placeholder="请选择数据来源"
                  @change="dataTypeChange" />
              </a-form-item>
              <div v-if="columnData.treeDataSource === 'dictionary'">
                <a-form-item label="数据字典">
                  <xh-tree-select v-model:value="columnData.treeDictionary" :options="dicOptions" lastLevel allowClear placeholder="请选择数据字典" />
                </a-form-item>
                <a-form-item label="主键字段">
                  <a-select v-model:value="columnData.treePropsValue" placeholder="请选择主键字段">
                    <a-select-option value="id">id</a-select-option>
                    <a-select-option value="enCode">enCode</a-select-option>
                  </a-select>
                </a-form-item>
              </div>
              <div v-if="columnData.treeDataSource === 'api'">
                <a-form-item label="远端数据">
                  <interface-modal :value="columnData.treePropsUrl" :title="columnData.treePropsName" popupTitle="数据接口" @change="onTreePropsUrlChange" />
                </a-form-item>
                <a-form-item label="主键字段">
                  <a-input v-model:value="columnData.treePropsValue" placeholder="主键字段" />
                </a-form-item>
                <a-form-item label="显示字段">
                  <a-input v-model:value="columnData.treePropsLabel" placeholder="显示字段" />
                </a-form-item>
                <a-form-item label="子级字段">
                  <a-input v-model:value="columnData.treePropsChildren" placeholder="子级字段" />
                </a-form-item>
              </div>
              <a-form-item label="关联字段">
                <xh-select
                  v-model:value="columnData.treeRelation"
                  :options="selectOptions"
                  placeholder="请选择关联字段"
                  :fieldNames="{ options: 'options1' }"
                  showSearch />
              </a-form-item>
              <div v-if="columnData.treeDataSource === 'api'">
                <a-form-item label="数据加载">
                  <xh-radio v-model:value="columnData.treeSynType" :options="treeSynTypeOptions" />
                </a-form-item>
                <a-form-item v-if="columnData.treeSynType == 1">
                  <template #label>数据接口<BasicHelp text="提供异步调用的数据接口" /></template>
                  <interface-modal
                    :value="columnData.treeInterfaceId"
                    :title="columnData.treeInterfaceName"
                    popupTitle="数据接口"
                    @change="onTreeInterfaceChange" />
                </a-form-item>
                <a-table
                  :data-source="columnData.treeTemplateJson"
                  :columns="treeTemplateJsonColumns"
                  size="small"
                  :pagination="false"
                  v-if="columnData.treeTemplateJson.length">
                  <template #bodyCell="{ column, record }">
                    <template v-if="column.key === 'field'">
                      <span class="required-sign">{{ record.required ? '*' : '' }}</span>
                      {{ record.field }}{{ record.fieldName ? '(' + record.fieldName + ')' : '' }}
                    </template>
                    <template v-if="column.key === 'relationField'">
                      <a-input v-model:value="record.relationField" placeholder="请输入" />
                    </template>
                  </template>
                </a-table>
              </div>
            </div>
            <a-divider>表格配置</a-divider>
            <a-form-item label="数据过滤" v-if="webType != WebType.DATA_VIEW">
              <a-button block @click="editRuleList">{{ getRuleBtnText }}</a-button>
            </a-form-item>
            <a-form-item label="分组字段" v-if="columnData.type == TableType.GROUP_TABLE">
              <xh-select
                v-model:value="columnData.groupField"
                :options="state.groupFieldOptions"
                placeholder="请选择分组字段"
                :fieldNames="{ options: 'options1' }"
                showSearch />
            </a-form-item>
            <a-form-item label="父级字段" v-if="columnData.type == TableType.TREE_TABLE">
              <xh-select
                v-model:value="columnData.parentField"
                :options="state.treeFieldOptions"
                placeholder="请选择父级字段"
                :fieldNames="{ options: 'options1' }"
                showSearch />
            </a-form-item>
            <a-form-item label="排序类型">
              <xh-select v-model:value="columnData.sort" :options="sortTypeOptions" placeholder="请选择排序类型" />
            </a-form-item>
            <a-form-item label="排序字段">
              <xh-select
                v-model:value="columnData.defaultSidx"
                :options="state.groupFieldOptions"
                placeholder="请选择排序字段"
                :fieldNames="{ options: 'options1' }"
                showSearch
                allowClear />
            </a-form-item>
            <a-form-item label="高级查询" v-if="webType != WebType.DATA_VIEW">
              <a-switch v-model:checked="columnData.hasSuperQuery" />
            </a-form-item>
            <div v-if="columnData.type !== TableType.GROUP_TABLE && columnData.type !== TableType.TREE_TABLE">
              <a-form-item label="分页设置">
                <a-switch v-model:checked="columnData.hasPage" />
              </a-form-item>
              <a-form-item label="分页条数" v-if="columnData.hasPage">
                <xh-radio v-model:value="columnData.pageSize" :options="pageSizeOptions" optionType="button" button-style="solid" class="right-radio" />
              </a-form-item>
              <a-form-item label="合计配置">
                <a-switch v-model:checked="columnData.showSummary" />
              </a-form-item>
              <a-form-item label="合计字段" v-if="columnData.showSummary">
                <xh-select
                  v-model:value="columnData.summaryField"
                  :options="summaryFieldOptions"
                  placeholder="请选择合计字段"
                  :fieldNames="{ options: 'options1' }"
                  showSearch
                  allowClear
                  multiple />
              </a-form-item>
            </div>
            <a-form-item
              label="子表样式"
              v-if="(columnData.type == TableType.NORMAL_TABLE || columnData.type == TableType.LEFTTREE_TABLE) && webType != WebType.DATA_VIEW">
              <xh-select v-model:value="columnData.childTableStyle" :options="childTableStyleOptions" placeholder="请选择子表样式" />
            </a-form-item>
            <a-divider>字典配置</a-divider>
            <a-form-item label="字典主题">
              <a-switch v-model:checked="columnData.useDicTheme" />
            </a-form-item>
            <template v-if="modelType == AppType.APP_DEV">
              <a-divider>查询配置</a-divider>
              <a-form-item>
                <template #label>接口来源<BasicHelp text="配置列表查询接口来源" /></template>
                <a-radio-group v-model:value="columnData.listType" button-style="solid">
                  <a-radio-button value="0">内置接口</a-radio-button>
                  <a-radio-button value="1">数据接口</a-radio-button>
                </a-radio-group>
              </a-form-item>
              <a-form-item v-if="columnData.listType == 1">
                <template #label>选择接口<BasicHelp text="可在【数据接口】模块配置" /></template>
                <interface-modal
                  v-model:value="columnData.listInterface.interfaceId"
                  v-model:title="columnData.listInterface.interfaceName"
                  popupTitle="数据接口" />
              </a-form-item>
            </template>
            <a-divider>按钮配置</a-divider>
            <a-checkbox-group v-model:value="state.btnsList" class="btnsList">
              <div v-for="item in btnsOption" :key="item.value">
                <a-checkbox :value="item.value">
                  <span class="btn-label">{{ getBtnText(item.value) }}</span>
                  <a-input v-model:value="item.label" placeholder="按钮名称" />
                </a-checkbox>
                <a-button class="btn-upload" v-if="item.value === 'upload' && state.btnsList.includes('upload')" @click="editUpLoadTpl">
                  请设置导入模板
                </a-button>
                <div class="btn-upload" v-if="item.value === 'batchPrint' && state.btnsList.includes('batchPrint')">
                  <xh-tree-select
                    v-model:value="columnData.printIds"
                    placeholder="请选择打印模板"
                    multiple
                    :options="printTplOptions"
                    lastLevel
                    :showCheckedStrategy="TreeSelect.SHOW_CHILD"
                    class="w-full" />
                </div>
              </div>
            </a-checkbox-group>
            <a-checkbox-group v-model:value="state.columnBtnsList" class="btnsList">
              <div v-for="item in columnBtnsOption" :key="item.value">
                <a-checkbox :value="item.value">
                  <span class="btn-label">{{ getBtnText(item.value) }}</span>
                  <a-input v-model:value="item.label" placeholder="按钮名称" />
                </a-checkbox>
              </div>
            </a-checkbox-group>
            <div v-if="modelType == AppType.APP_DEV">
              <p class="btn-cap">自定义按钮区</p>
              <div class="custom-btns-list">
                <a-form-item v-show="columnData.customBtnsList.length > 0">
                  <template #label>按钮合并<BasicHelp text="合并后自定义按钮会收藏到更多下拉框中" /></template>
                  <a-switch v-model:checked="columnData.mergeCustomBtnsList" />
                </a-form-item>
                <draggable v-model="columnData.customBtnsList" :animation="300" group="selectItem" handle=".option-drag" itemKey="value">
                  <template #item="{ element, index }">
                    <div class="custom-item">
                      <div class="custom-line-icon option-drag">
                        <i class="icon-ym icon-ym-darg"></i>
                      </div>
                      <p class="custom-line-value">{{ element.value }}</p>
                      <a-input v-model:value="element.label" placeholder="按钮名称">
                        <template #addonAfter>
                          <span class="cursor-pointer" @click="editBtnEvent(element, index)">事件</span>
                        </template>
                      </a-input>
                      <div class="close-btn custom-line-icon" @click="columnData.customBtnsList.splice(index, 1)">
                        <i class="icon-ym icon-ym-btn-clearn"></i>
                      </div>
                    </div>
                  </template>
                </draggable>
                <div class="add-btn">
                  <a-button type="link" preIcon="icon-ym icon-ym-btn-add" @click="addCustomBtn">添加选项</a-button>
                </div>
              </div>
            </div>
            <div v-if="webType != WebType.DATA_VIEW">
              <a-divider>权限设置</a-divider>
              <a-form-item label="按钮权限">
                <a-switch v-model:checked="columnData.useBtnPermission" />
              </a-form-item>
              <a-form-item label="列表权限">
                <a-switch v-model:checked="columnData.useColumnPermission" />
              </a-form-item>
              <a-form-item label="表单权限">
                <a-switch v-model:checked="columnData.useFormPermission" />
              </a-form-item>
              <a-form-item label="数据权限">
                <a-switch v-model:checked="columnData.useDataPermission" />
              </a-form-item>
            </div>
            <div v-if="modelType == AppType.APP_DEV">
              <a-divider>脚本事件</a-divider>
              <a-form-item :label="getFuncText(key)" v-for="(_value, key) in columnData.funcs" :key="key">
                <a-button :class="setBtnWarnClass(defaultFuncsData[key], columnData.funcs[key])" block @click="editFunc(key)">脚本编写 </a-button>
              </a-form-item>
            </div>
          </a-form>
        </ScrollContainer>
      </div>
    </div>
    <FormScript @register="registerScriptModal" @confirm="updateScript" />
    <FormateCellScript @register="registerFormatCellModal" @confirm="updateFormatCellScript" />
    <UpLoadTpl @register="registerUpLoadTplModal" @confirm="updateUpLoadTpl" />
    <BtnEvent @register="registerBtnEventModal" @confirm="updateBtnEvent" />
    <ConditionModal @register="registerConditionModal" @confirm="updateRuleList" />
  </div>
</template>
<script lang="ts" setup>
  import { ref, reactive, onMounted, computed, toRefs, unref, nextTick, watch, watchEffect } from 'vue';
  import { TreeSelect } from 'ant-design-vue';
  import { ScrollContainer } from '/@/components/Container';
  import { InterfaceModal } from '/@/components/CommonModal';
  import { CheckOutlined } from '@ant-design/icons-vue';
  import { getDrawingList } from '/@/components/FormGenerator/src/helper/db';
  import { setBtnWarnClass, compareFuncStr } from '/@/components/FormGenerator/src/helper/funcsStr';
  import {
    noColumnShowList,
    noSearchList,
    systemComponentsList,
    noUploadList,
    getSearchType,
    getSearchMultiple,
    defaultFuncsData,
    defaultColumnData,
  } from '../helper/config';
  import { cloneDeep } from 'lodash-es';
  import { getDictionaryTypeSelector } from '/@/api/systemData/dictionary';
  import { getPrintDevSelector } from '/@/api/system/printDev';
  import draggable from 'vuedraggable';
  import { buildBitUUID } from '/@/utils/uuid';
  import columnType1 from '/@/assets/images/generator/columnType1.png';
  import columnType2 from '/@/assets/images/generator/columnType2.png';
  import columnType3 from '/@/assets/images/generator/columnType3.png';
  import columnType4 from '/@/assets/images/generator/columnType4.png';
  import columnType5 from '/@/assets/images/generator/columnType5.png';
  import columnTypeDark1 from '/@/assets/images/generator/columnType1-dark.png';
  import columnTypeDark2 from '/@/assets/images/generator/columnType2-dark.png';
  import columnTypeDark3 from '/@/assets/images/generator/columnType3-dark.png';
  import columnTypeDark4 from '/@/assets/images/generator/columnType4-dark.png';
  import columnTypeDark5 from '/@/assets/images/generator/columnType5-dark.png';
  import { useModal } from '/@/components/Modal';
  import FormScript from './FormScript.vue';
  import FormateCellScript from './FormatCellScript.vue';
  import UpLoadTpl from './UpLoadTpl.vue';
  import BtnEvent from './BtnEvent.vue';
  import ConditionModal from './ConditionModal.vue';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useAppStore } from '/@/store/modules/app';
  import Sortablejs from 'sortablejs';
  import { WebType, TableType, AppType } from '/@/enums/onlineEnum';
  import { defaultCellFormat } from '../helper/config';

  interface State {
    columnData: any;
    groupFieldOptions: any[];
    treeFieldOptions: any[];
    columnOptions: any[];
    searchOptions: any[];
    btnsList: any[];
    columnBtnsList: any[];
    activeFunc: string;
    activeBtn: string;
    searchSelectedRowKeys: string[];
    columnSelectedRowKeys: string[];
    printTplOptions: any[];
  }

  defineExpose({ getData });
  const props = defineProps(['conf', 'formInfo', 'viewFields']);
  const appStore = useAppStore();
  const { createMessage } = useMessage();
  const treeDataSourceOptions = [
    { id: 'dictionary', fullName: '数据字典' },
    { id: 'api', fullName: '远端数据' },
    { id: 'organize', fullName: '组织数据' },
    { id: 'department', fullName: '部门数据' },
  ];
  const treeSynTypeOptions = [
    { id: 0, fullName: '同步' },
    { id: 1, fullName: '异步' },
  ];
  const sortTypeOptions = [
    { id: 'asc', fullName: '升序' },
    { id: 'desc', fullName: '降序' },
  ];
  const childTableStyleOptions = [
    { id: 1, fullName: '分组展示' },
    { id: 2, fullName: '折叠展示' },
  ];
  const pageSizeOptions = [
    { id: 20, fullName: '20' },
    { id: 50, fullName: '50' },
    { id: 80, fullName: '80' },
    { id: 100, fullName: '100' },
    { id: 500, fullName: '500' },
  ];
  const treeTemplateJsonColumns = [
    { width: 50, title: '序号', align: 'center', customRender: ({ index }) => index + 1 },
    { title: '参数名称', dataIndex: 'field', key: 'field', width: 135 },
    { title: '表单字段', dataIndex: 'relationField', key: 'relationField', width: 135 },
  ];
  const defaultBtnsOption = [
    { value: 'add', icon: 'icon-ym icon-ym-btn-add', label: '新增' },
    { value: 'download', icon: 'icon-ym icon-ym-btn-download', label: '导出' },
    { value: 'upload', icon: 'icon-ym icon-ym-btn-upload', label: '导入' },
    { value: 'batchRemove', icon: 'icon-ym icon-ym-btn-clearn', label: '批量删除' },
    { value: 'batchPrint', icon: 'icon-ym icon-ym-report-icon-preview-printPreview', label: '批量打印' },
  ];
  const btnsOption = ref(defaultBtnsOption);
  const columnBtnsOption = ref([
    { value: 'edit', icon: 'icon-ym icon-ym-btn-edit', label: '编辑' },
    { value: 'remove', icon: 'icon-ym icon-ym-btn-clearn', label: '删除' },
    { value: 'detail', icon: 'icon-ym icon-ym-generator-menu', label: '详情' },
  ]);
  const rightColumns = [{ title: '字段', dataIndex: 'fullName', key: 'fullName' }];
  const searchColumns = [
    { title: '拖动', dataIndex: 'drag', key: 'drag', align: 'center', width: 50 },
    { title: '列名', dataIndex: 'label', key: 'label', width: 200 },
    { title: '字段', dataIndex: 'prop', key: 'prop' },
    { title: '类型', dataIndex: 'searchType', key: 'searchType', width: 200 },
    { title: '是否多选', dataIndex: 'searchMultiple', key: 'searchMultiple', width: 100, align: 'center' },
  ];
  const getTypeList = computed(() => {
    const isLight = appStore.getDarkMode === 'light';
    const type1 = isLight ? columnType1 : columnTypeDark1;
    const type2 = isLight ? columnType2 : columnTypeDark2;
    const type3 = isLight ? columnType3 : columnTypeDark3;
    const type4 = isLight ? columnType4 : columnTypeDark4;
    const type5 = isLight ? columnType5 : columnTypeDark5;
    let list = [
      { url: type1, value: TableType.NORMAL_TABLE, name: '普通表格' },
      { url: type2, value: TableType.LEFTTREE_TABLE, name: '左侧树+普通表格' },
      { url: type4, value: TableType.EDIT_TABLE, name: '编辑表格' },
      { url: type3, value: TableType.GROUP_TABLE, name: '分组表格' },
      { url: type5, value: TableType.TREE_TABLE, name: '树形表格' },
    ];
    if (unref(webType) == WebType.DATA_VIEW) list = list.filter(o => o.value == TableType.NORMAL_TABLE || o.value == TableType.GROUP_TABLE);
    return list;
  });
  const columnColumns = computed(() => {
    let list = [
      { title: '拖动', dataIndex: 'drag', key: 'drag', align: 'center', width: 50 },
      { title: '列名', dataIndex: 'label', key: 'label', width: 200 },
      { title: '字段', dataIndex: 'prop', key: 'prop' },
      { title: '超出隐藏', dataIndex: 'ellipsis', key: 'ellipsis', width: 80, align: 'center' },
      { title: '列拖拽', dataIndex: 'resizable', key: 'resizable', width: 80, align: 'center' },
      { title: '排序', dataIndex: 'sortable', key: 'sortable', width: 80, align: 'center' },
      { title: '冻结', dataIndex: 'fixed', key: 'fixed', width: 150 },
      { title: '对齐', dataIndex: 'align', key: 'align', width: 150 },
      { title: '宽度', dataIndex: 'width', key: 'width', width: 150 },
      { title: '格式化', dataIndex: 'format', key: 'format', width: 150, align: 'center' },
    ];
    if (state.columnData.childTableStyle == 2) {
      list = list.filter(o => o.dataIndex != 'fixed');
    }
    return list;
  });
  const multipleList = ['select', 'depSelect', 'roleSelect', 'userSelect', 'usersSelect', 'organizeSelect', 'posSelect', 'groupSelect'];
  const searchTypeOptions = [
    { id: 1, fullName: '等于查询' },
    { id: 2, fullName: '模糊查询' },
    { id: 3, fullName: '范围查询' },
  ];
  const alignOptions = [
    { id: 'left', fullName: 'left' },
    { id: 'center', fullName: 'center' },
    { id: 'right', fullName: 'right' },
  ];
  const fixedOptions = [
    { id: 'none', fullName: 'none' },
    { id: 'left', fullName: 'left' },
    { id: 'right', fullName: 'right' },
  ];

  const activeKey = ref('column');
  const dicOptions = ref<any[]>([]);
  const state = reactive<State>({
    columnData: cloneDeep(defaultColumnData),
    groupFieldOptions: [],
    treeFieldOptions: [],
    columnOptions: [],
    searchOptions: [],
    btnsList: [],
    columnBtnsList: [],
    activeFunc: '',
    activeBtn: '',
    searchSelectedRowKeys: [],
    columnSelectedRowKeys: [],
    printTplOptions: [],
  });
  const { columnData, searchSelectedRowKeys, columnSelectedRowKeys, printTplOptions } = toRefs(state);
  const [registerScriptModal, { openModal: openScriptModal }] = useModal();
  const [registerFormatCellModal, { openModal: openFormatCellScript }] = useModal();
  const [registerUpLoadTplModal, { openModal: openUpLoadTplModal }] = useModal();
  const [registerBtnEventModal, { openModal: openBtnEventModal }] = useModal();
  const [registerConditionModal, { openModal: openConditionModal }] = useModal();
  const hasScript = ref<boolean>(false);
  watchEffect(() => {
    for (let key in state.columnData.funcs) {
      let func = state.columnData.funcs[key];
      if (!compareFuncStr(defaultFuncsData[key], func)) {
        hasScript.value = true;
        return;
      }
    }
    hasScript.value = false;
  });
  const webType = computed(() => props.formInfo?.webType);
  const modelType = computed(() => props.formInfo?.type);
  const getRuleBtnText = computed(() => (state.columnData?.ruleList.length ? '编辑过滤条件' : '添加过滤条件'));
  const formFieldsOptions = computed(() => {
    let list: any[] = [];
    const loop = (data, parent?) => {
      if (!data) return;
      if (data.__config__ && data.__config__.children && Array.isArray(data.__config__.children)) {
        loop(data.__config__.children, data);
      }
      if (Array.isArray(data)) data.forEach(d => loop(d, parent));
      if (data.__config__ && data.__config__.xhKey) {
        const visibility = !data.__config__.visibility || (Array.isArray(data.__config__.visibility) && data.__config__.visibility.includes('pc'));
        if (data.__config__.layout === 'colFormItem' && data.__vModel__ && visibility) {
          const isTableChild = parent && parent.__config__ && parent.__config__.xhKey === 'table';
          list.push({
            id: isTableChild ? parent.__vModel__ + '-' + data.__vModel__ : data.__vModel__,
            fullName: isTableChild ? parent.__config__.label + '-' + data.__config__.label : data.__config__.label,
            ...data,
          });
        }
      }
    };
    loop(getDrawingList());
    return list;
  });
  const viewFieldOptions = computed(() => props.viewFields.map(o => ({ id: o, fullName: o, __vModel__: o, __config__: { xhKey: 'input' } })));
  const selectOptions = computed(() => unref(formFieldsOptions).map(o => ({ ...o, disabled: false })));
  const summaryFieldOptions = computed(() =>
    state.groupFieldOptions.filter(o => ['input', 'inputNumber', 'calculate'].includes(o.__config__.xhKey) && o.__vModel__),
  );

  watch(
    () => unref(viewFieldOptions),
    () => {
      if (unref(webType) == WebType.DATA_VIEW) init(unref(viewFieldOptions));
    },
  );

  // 供父组件使用 获取表单JSON
  function getData() {
    updateBtnList();
    if (!state.columnData.columnList.length) {
      createMessage.warning('列表字段不允许为空');
      return;
    }
    if (state.columnData.listType == 1 && !state.columnData?.listInterface?.interfaceId) {
      createMessage.warning('请选择查询配置接口');
      return;
    }
    if (state.columnData.type == TableType.LEFTTREE_TABLE) {
      if (state.columnData.treeDataSource === 'dictionary' && !state.columnData.treeDictionary) {
        createMessage.warning('请选择数据字典');
        return;
      }
      if (state.columnData.treeDataSource === 'api') {
        if (!state.columnData.treePropsValue) {
          createMessage.warning('请输入主键字段');
          return;
        }
        if (!state.columnData.treePropsLabel) {
          createMessage.warning('请输入显示字段');
          return;
        }
        if (!state.columnData.treePropsChildren) {
          createMessage.warning('请输入子级字段');
          return;
        }
      }
      if (!state.columnData.treeRelation) {
        createMessage.warning('请选择关联字段');
        return;
      }
      if (!state.columnData.treeInterfaceId && state.columnData.treeSynType == 1) {
        createMessage.warning('请选择异步数据接口');
        return;
      }
    }
    if (state.columnData.type == TableType.GROUP_TABLE && !state.columnData.groupField) {
      createMessage.warning('请选择分组字段');
      return;
    }
    if (state.columnData.type == TableType.TREE_TABLE && !state.columnData.parentField) {
      createMessage.warning('请选择父级字段');
      return;
    }
    if (state.btnsList.includes('upload') && !state.columnData.uploaderTemplateJson?.selectKey) {
      createMessage.warning('请设置导入模板');
      return;
    }
    if (state.btnsList.includes('batchPrint') && !state.columnData.printIds?.length) {
      createMessage.warning('请选择打印模板');
      return;
    }
    state.columnData.defaultColumnList = state.columnOptions.map(o => ({
      ...o,
      checked: state.columnData.columnList.some(i => i.prop === o.prop),
    }));
    return state.columnData;
  }
  function updateBtnList() {
    const list: any[] = [];
    for (let i = 0; i < unref(btnsOption).length; i++) {
      if (state.btnsList.includes(unref(btnsOption)[i].value)) {
        list.push(unref(btnsOption)[i]);
      }
    }
    state.columnData.btnsList = list;
    const columnBtns: any[] = [];
    for (let i = 0; i < unref(columnBtnsOption).length; i++) {
      if (state.columnBtnsList.includes(unref(columnBtnsOption)[i].value)) {
        columnBtns.push(unref(columnBtnsOption)[i]);
      }
    }
    state.columnData.columnBtnsList = columnBtns;
  }
  function getDictionaryType() {
    getDictionaryTypeSelector().then(res => {
      dicOptions.value = res.data.list;
    });
  }
  function getPrintTplList() {
    getPrintDevSelector('2').then(res => {
      state.printTplOptions = res.data.list.filter(o => o.children && o.children.length).map(o => ({ ...o, hasChildren: true }));
    });
  }
  function getBtnText(key) {
    let text = '';
    switch (key) {
      case 'download':
        text = '导出';
        break;
      case 'batchRemove':
        text = '批量删除';
        break;
      case 'batchPrint':
        text = '批量打印';
        break;
      case 'edit':
        text = '编辑';
        break;
      case 'remove':
        text = '删除';
        break;
      case 'detail':
        text = '详情';
        break;
      case 'upload':
        text = '导入';
        break;
      default:
        text = '新增';
        break;
    }
    return text;
  }
  function getFuncText(key) {
    let text = '';
    switch (key) {
      case 'afterOnload':
        text = '表格事件';
        break;
      case 'rowStyle':
        text = '表格行设置';
        break;
      case 'cellStyle':
        text = '单元格设置';
        break;
      default:
        text = '';
        break;
    }
    return text;
  }
  function toggleType(val) {
    if (state.columnData.type == val) return;
    state.columnData.type = val;
    if (val == TableType.GROUP_TABLE || val == TableType.TREE_TABLE) state.columnData.childTableStyle = 1;
    if (unref(webType) == WebType.DATA_VIEW) return;
    if (val == TableType.TREE_TABLE) {
      btnsOption.value = defaultBtnsOption.filter(o => o.value === 'add');
      state.btnsList = state.btnsList.filter(o => o === 'add');
    } else {
      btnsOption.value = defaultBtnsOption;
    }
  }
  function dataTypeChange() {
    state.columnData.treePropsValue = 'id';
    state.columnData.treePropsLabel = 'fullName';
    state.columnData.treePropsChildren = 'children';
  }
  function onTreePropsUrlChange(val, row) {
    if (!val) {
      state.columnData.treePropsUrl = '';
      state.columnData.treePropsName = '';
      return;
    }
    if (state.columnData.treePropsUrl === val) return;
    state.columnData.treePropsUrl = val;
    state.columnData.treePropsName = row.fullName;
  }
  function onTreeInterfaceChange(val, row) {
    if (!val) {
      state.columnData.treeInterfaceId = '';
      state.columnData.treeInterfaceName = '';
      state.columnData.treeTemplateJson = [];
      return;
    }
    if (state.columnData.treeInterfaceId === val) return;
    state.columnData.treeInterfaceId = val;
    state.columnData.treeInterfaceName = row.fullName;
    state.columnData.treeTemplateJson = row.templateJson ? row.templateJson.map(o => ({ ...o, relationField: '' })) : [];
  }
  function addCustomBtn() {
    const id = buildBitUUID();
    state.columnData.customBtnsList.push({
      value: 'btn_' + id,
      label: '按钮' + id,
      event: {},
    });
  }
  function editBtnEvent(item, index) {
    state.activeBtn = index;
    openBtnEventModal(true, {
      showType: 'pc',
      formFieldsOptions: unref(webType) == WebType.DATA_VIEW ? state.columnOptions : unref(formFieldsOptions),
      dataForm: item.event,
    });
  }
  function updateBtnEvent(data) {
    state.columnData.customBtnsList[state.activeBtn].event = data;
  }
  function editRuleList() {
    openConditionModal(true, {
      ruleList: state.columnData.ruleList,
      formFieldsOptions: unref(webType) == WebType.DATA_VIEW ? state.columnOptions : unref(formFieldsOptions),
    });
  }
  function updateRuleList(data) {
    state.columnData.ruleList = data;
  }
  function editFunc(funcName) {
    state.activeFunc = funcName;
    if (!state.columnData.funcs[state.activeFunc]) state.columnData.funcs[state.activeFunc] = defaultFuncsData[state.activeFunc];
    openScriptModal(true, { text: state.columnData.funcs[state.activeFunc], funcName });
  }
  function editCellFormat(record) {
    openFormatCellScript(true, { text: record.format, key: record.__vModel__ });
  }
  function updateScript(data) {
    state.columnData.funcs[state.activeFunc] = data;
  }
  function updateFormatCellScript(data) {
    const item = state.columnData.columnList.find(item => item.__vModel__ === data.key);
    if (item) {
      item.format = data.text;
    }
  }
  function editUpLoadTpl() {
    const data = { selectKey: [], dataType: '1', ...state.columnData.uploaderTemplateJson, fieldsOptions: unref(formFieldsOptions) };
    openUpLoadTplModal(true, data);
  }
  function updateUpLoadTpl(data) {
    state.columnData.uploaderTemplateJson = data;
  }
  function setBtnValue(replacedData, data, key?) {
    key = key ? key : 'value';
    outer: for (let i = 0; i < replacedData.length; i++) {
      inter: for (let ii = 0; ii < data.length; ii++) {
        if (replacedData[i][key] === data[ii][key]) {
          data[ii] = replacedData[i];
          break inter;
        }
      }
    }
  }
  function setListValue(data, defaultData, type) {
    data = data.filter(o => defaultData.some(e => o.prop == e.prop));
    outer: for (let i = 0; i < data.length; i++) {
      inter: for (let ii = 0; ii < defaultData.length; ii++) {
        if (data[i].prop === defaultData[ii].prop) {
          if (type === 'column') {
            defaultData[ii].fixed = data[i].fixed;
            defaultData[ii].align = data[i].align;
            defaultData[ii].width = data[i].width;
            defaultData[ii].sortable = data[i].sortable;
            defaultData[ii].ellipsis = data[i].ellipsis;
            defaultData[ii].format = data[i].format;
            defaultData[ii].resizable = data[i].resizable;
            defaultData[ii].xhKey = data[i].xhKey;
            if (unref(webType) == WebType.DATA_VIEW) defaultData[ii].label = data[i].label;
            data[i] = defaultData[ii];
          }
          if (type === 'search') {
            defaultData[ii].searchType = data[i].searchType;
            defaultData[ii].searchMultiple = data[i].searchMultiple;
            defaultData[ii].xhKey = data[i].xhKey;
            if (unref(webType) == WebType.DATA_VIEW) defaultData[ii].label = data[i].label;
            data[i] = defaultData[ii];
          }
          break inter;
        }
      }
    }
    state[type + 'SelectedRowKeys'] = data.map(o => o.prop);
    return data;
  }
  function updateListValue(selectedRowKeys, selectedRows, type) {
    state[type + 'SelectedRowKeys'] = selectedRowKeys;
    if (!selectedRowKeys.length) return (state.columnData[type + 'List'] = []);
    state.columnData[type + 'List'] = state.columnData[type + 'List'].filter(o => selectedRowKeys.some(e => o.prop == e));
    for (let i = 0; i < selectedRows.length; i++) {
      if (!state.columnData[type + 'List'].some(o => o.prop === selectedRows[i].prop)) {
        state.columnData[type + 'List'].push(selectedRows[i]);
      }
    }
  }
  function onSearchSelectChange(selectedRowKeys, selectedRows) {
    updateListValue(selectedRowKeys, selectedRows, 'search');
  }
  function onColumnSelectChange(selectedRowKeys, selectedRows) {
    updateListValue(selectedRowKeys, selectedRows, 'column');
  }
  function setDefaultUpLoadData() {
    const selectKey = state.columnData.uploaderTemplateJson?.selectKey || [];
    const newList: any[] = [];
    for (let i = 0; i < selectKey.length; i++) {
      if (unref(formFieldsOptions).some(item => item.id == selectKey[i])) newList.push(selectKey[i]);
    }
    for (let i = 0; i < unref(formFieldsOptions).length; i++) {
      const e = unref(formFieldsOptions)[i];
      const required = e.__config__.required;
      const xhKey = e.__config__.xhKey;
      if (!noUploadList.includes(xhKey) && (required || systemComponentsList.includes(xhKey)) && !newList.includes(e.id)) {
        newList.push(e.id);
      }
    }
    state.columnData.uploaderTemplateJson.selectKey = newList;
  }
  function initSort() {
    const searchTable: any = document.querySelector(`.search-table .ant-table-tbody`);
    Sortablejs.create(searchTable, {
      handle: '.drag-handler',
      animation: 150,
      easing: 'cubic-bezier(1, 0, 0, 1)',
      onStart: () => {},
      onEnd: ({ newIndex, oldIndex }: any) => {
        const currRow = state.columnData.searchList.splice(oldIndex, 1)[0];
        state.columnData.searchList.splice(newIndex, 0, currRow);
      },
    });
    const columnTable: any = document.querySelector(`.column-table .ant-table-tbody`);
    Sortablejs.create(columnTable, {
      handle: '.drag-handler',
      animation: 150,
      easing: 'cubic-bezier(1, 0, 0, 1)',
      onStart: () => {},
      onEnd: ({ newIndex, oldIndex }: any) => {
        const currRow = state.columnData.columnList.splice(oldIndex, 1)[0];
        state.columnData.columnList.splice(newIndex, 0, currRow);
      },
    });
  }
  function init(list) {
    const columnOptions = list.filter(o => !noColumnShowList.includes(o.__config__.xhKey) || o.isStorage);
    const searchOptions = list.filter(o => !noSearchList.includes(o.__config__.xhKey));
    state.groupFieldOptions = list.filter(o => o.id.indexOf('-') < 0).map(o => ({ ...o, disabled: false }));
    state.treeFieldOptions = list.filter(o => o.id.indexOf('-') < 0 && o.__config__.xhKey == 'treeSelect');
    state.columnOptions = columnOptions.map(o => ({
      label: o.fullName,
      prop: o.id,
      fixed: 'none',
      align: 'left',
      xhKey: o.__config__.xhKey,
      sortable: false,
      ellipsis: false,
      resizable: false,
      width: null,
      ...o,
    }));
    state.searchOptions = searchOptions.map(o => ({
      label: o.fullName,
      prop: o.id,
      xhKey: o.__config__.xhKey,
      value: '',
      searchType: getSearchType(o),
      searchMultiple: getSearchMultiple(o.__config__.xhKey),
      ...o,
    }));
    state.columnData.columnOptions = columnOptions;
    if (!state.columnOptions.length) state.columnData.columnList = [];
    if (!state.searchOptions.length) state.columnData.searchList = [];
    setBtnValue(state.columnData.btnsList, btnsOption.value);
    setBtnValue(state.columnData.columnBtnsList, columnBtnsOption.value);
    state.btnsList = state.columnData.btnsList.map(o => o.value);
    state.columnBtnsList = state.columnData.columnBtnsList.map(o => o.value);
    nextTick(() => {
      if (state.btnsList.includes('upload')) setDefaultUpLoadData();
      state.columnData.searchList = setListValue(state.columnData.searchList, state.searchOptions, 'search');
      state.columnData.columnList = setListValue(state.columnData.columnList, state.columnOptions, 'column');
      initSort();
    });
  }

  onMounted(() => {
    getDictionaryType();
    getPrintTplList();
    if (typeof props.conf === 'object' && props.conf !== null) {
      state.columnData = Object.assign({}, defaultColumnData, props.conf);
    }
    if (unref(webType) != WebType.DATA_VIEW) {
      // 树形表格过滤按钮
      if (state.columnData.type == TableType.TREE_TABLE) btnsOption.value = defaultBtnsOption.filter(o => o.value === 'add');
      init(unref(formFieldsOptions));
    } else {
      btnsOption.value = btnsOption.value.filter(o => o.value === 'download');
      columnBtnsOption.value = [];
    }
  });
</script>
