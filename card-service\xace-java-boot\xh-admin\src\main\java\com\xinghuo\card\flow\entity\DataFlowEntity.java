package com.xinghuo.card.flow.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;


/**
 * 流水表
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2022-11-26
 */
@Data
@TableName("data_flow")
public class DataFlowEntity {

    /**
     * ID
     */
    @TableId("ID")
    private String id;


    /**
     * 类型
     */
    @TableField("TRANS_TYPE")
    private String transType;


    /**
     * 收支类型
     */
    @TableField("TYPE")
    private String type;


    /**
     * 流水日期
     */
    @TableField("FLOW_DATE")
    private Date flowDate;

    /**
     * 账户ID
     */
    @TableField("ACC_ID")
    private String accId;


    /**
     * 转入账号ID
     */
    @TableField("IN_ACC_ID")
    private String inAccId;


    /**
     * 转出账号ID
     */
    @TableField("OUT_ACC_ID")
    private String outAccId;

    /**
     * 标签ID
     */
    @TableField("MAN_ID")
    private String manId;

    /**
     * 金额
     */
    @TableField(value = "AMOUT", updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal amout;

    /**
     * 信用卡卡片ID
     */
    @TableField("CC_ID")
    private String ccId;

    /**
     * 关联ID
     */
    @TableField("RELATED_ID")
    private String relatedId;

    /**
     * 备注信息
     */
    @TableField("NOTE")
    private String note;

    /**
     * 创建人ID
     */
    @TableField("CREATE_BY")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField("CREATE_TIME")
    private Date createTime;


    /**
     * 最后创建人
     */
    @TableField("UPDATE_BY")
    private String updateBy;


    /**
     * 最后修改时间
     */
    @TableField("UPDATE_TIME")
    private Date updateTime;

}
