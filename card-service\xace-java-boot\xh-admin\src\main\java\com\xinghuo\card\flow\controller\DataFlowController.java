package com.xinghuo.card.flow.controller;


import com.xinghuo.admin.util.GeneraterSwapUtil;

import com.xinghuo.card.flow.entity.DataFlowEntity;
import com.xinghuo.card.flow.model.dataflow.DataFlowForm;
import com.xinghuo.card.flow.model.dataflow.DataFlowPagination;
import com.xinghuo.card.flow.model.dataflow.DataFlowVO;
import com.xinghuo.card.flow.service.DataFlowService;
import com.xinghuo.card.sys.entity.DataAccEntity;
import com.xinghuo.card.sys.service.DataAccService;
import com.xinghuo.card.sys.entity.DataSysPaytypeEntity;
import com.xinghuo.card.sys.service.DataSysPaytypeService;
import com.xinghuo.common.base.ActionResult;
import com.xinghuo.common.base.UserInfo;
import com.xinghuo.common.base.vo.PageListVO;
import com.xinghuo.common.base.vo.PaginationVO;
import com.xinghuo.common.exception.DataException;
import com.xinghuo.common.util.UserProvider;
import com.xinghuo.common.util.core.BeanCopierUtils;
import com.xinghuo.common.util.core.DateXhUtil;
import com.xinghuo.common.util.core.RandomUtil;
import com.xinghuo.common.util.core.StrXhUtil;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import org.apache.commons.lang3.StringUtils;


/**
 * 流水表
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2022-11-26
 */
@Slf4j
@RestController
@RequestMapping("/api/card/flow/flow")
public class DataFlowController {

    @Autowired
    private GeneraterSwapUtil generaterSwapUtil;

    @Autowired
    private UserProvider userProvider;

    @Autowired
    private DataFlowService dataFlowService;

    @Autowired
    private DataAccService dataAccService;

    @Autowired
    private DataSysPaytypeService dataSysPaytypeService;


    /**
     * 获取流水记录列表
     *
     * @param dataFlowPagination 分页查询参数
     * @return 流水记录列表
     */
    @Operation(summary = "获取流水记录列表")
    @PostMapping("/getList")
    public ActionResult list(@RequestBody DataFlowPagination dataFlowPagination) throws Exception {
        List<DataFlowEntity> list = dataFlowService.getList(dataFlowPagination);
        List<DataFlowVO> listVO = BeanCopierUtils.copyList(list, DataFlowVO.class);

        // 批量获取账户信息，避免重复查询
        Set<String> accIds = new HashSet<>();
        for (DataFlowVO vo : listVO) {
            if (StringUtils.isNotEmpty(vo.getAccId())) {
                accIds.add(vo.getAccId());
            }
            if (StringUtils.isNotEmpty(vo.getOutAccId())) {
                accIds.add(vo.getOutAccId());
            }
            if (StringUtils.isNotEmpty(vo.getInAccId())) {
                accIds.add(vo.getInAccId());
            }
        }

        // 批量查询账户信息（使用缓存）
        Map<String, String> accNameMap = new HashMap<>();
        for (String accId : accIds) {
            DataAccEntity acc = dataAccService.getInfo(accId);
            if (acc != null) {
                accNameMap.put(accId, acc.getName());
            }
        }

        // 处理数据转换和补充
        for (DataFlowVO vo : listVO) {
            // 账户名称转换
            if (StringUtils.isNotEmpty(vo.getAccId())) {
                vo.setAccId(accNameMap.getOrDefault(vo.getAccId(), vo.getAccId()));
            }
            if (StringUtils.isNotEmpty(vo.getOutAccId())) {
                vo.setOutAccId(accNameMap.getOrDefault(vo.getOutAccId(), vo.getOutAccId()));
            }
            if (StringUtils.isNotEmpty(vo.getInAccId())) {
                vo.setInAccId(accNameMap.getOrDefault(vo.getInAccId(), vo.getInAccId()));
            }

            // 交易类型转换
            vo.setTransType(generaterSwapUtil.getDicName(vo.getTransType(), "1009903952115712"));

            // 收支类型转换
            if (StringUtils.isNotEmpty(vo.getType())) {
                DataSysPaytypeEntity paytype = dataSysPaytypeService.getInfo(vo.getType());
                if (paytype != null && StringUtils.isNotEmpty(paytype.getName())) {
                    vo.setType(paytype.getName());
                }
            }

            // 用户名称转换
            vo.setCreateBy(generaterSwapUtil.userSelectValue(vo.getCreateBy()));
            vo.setUpdateBy(generaterSwapUtil.userSelectValue(vo.getUpdateBy()));
        }

        PaginationVO page = BeanCopierUtils.copy(dataFlowPagination, PaginationVO.class);
        return ActionResult.page(listVO, page);
    }

    /**
     * 创建流水记录
     *
     * @param dataFlowForm 流水记录表单数据
     * @return 操作结果
     */
    @Operation(summary = "创建流水记录")
    @PostMapping
    @Transactional
    public ActionResult create(@RequestBody @Valid DataFlowForm dataFlowForm) throws DataException {
        String mainId = RandomUtil.snowId();
        UserInfo userInfo = userProvider.get();

        // 转换Form为Entity
        DataFlowEntity entity = BeanCopierUtils.copy(dataFlowForm, DataFlowEntity.class);
        entity.setId(mainId);
        entity.setCreateBy(userInfo.getUserId());
        entity.setCreateTime(new Date());

        dataFlowService.create(entity);
        return ActionResult.success("创建成功");
    }

    /**
     * 信息
     *
     * @param id
     * @return
     */
    @Operation(summary = "查询单条")
    @GetMapping("/{id}")
    public ActionResult<DataFlowVO> info(@PathVariable("id") String id) {
        DataFlowEntity entity = dataFlowService.getInfo(id);
        DataFlowVO vo = BeanCopierUtils.copy(entity, DataFlowVO.class);
        vo.setCreateBy(generaterSwapUtil.userSelectValue(vo.getCreateBy()));
        vo.setUpdateBy(generaterSwapUtil.userSelectValue(vo.getUpdateBy()));
        return ActionResult.success(vo);
    }

    /**
     * 表单信息(详情页)
     *
     * @param id
     * @return
     */
    @Operation(summary = "表单信息(详情页)")
    @GetMapping("/detail/{id}")
    public ActionResult<DataFlowVO> detailInfo(@PathVariable("id") String id) {
        DataFlowEntity entity = dataFlowService.getInfo(id);
        DataFlowVO vo = BeanCopierUtils.copy(entity, DataFlowVO.class);

        // 添加到详情表单对象中
        vo.setTransType(generaterSwapUtil.getDicName(vo.getTransType(), "1009903952115712"));
        vo.setCreateBy(generaterSwapUtil.userSelectValue(vo.getCreateBy()));
        vo.setUpdateBy(generaterSwapUtil.userSelectValue(vo.getUpdateBy()));
        return ActionResult.success(vo);
    }

    /**
     * 更新流水记录
     *
     * @param id 流水记录ID
     * @param dataFlowForm 流水记录表单数据
     * @return 操作结果
     */
    @Operation(summary = "更新流水记录")
    @PutMapping("/{id}")
    @Transactional
    public ActionResult update(@PathVariable("id") String id, @RequestBody @Valid DataFlowForm dataFlowForm) throws DataException {
        UserInfo userInfo = userProvider.get();
        DataFlowEntity entity = dataFlowService.getInfo(id);
        if (entity == null) {
            return ActionResult.fail("更新失败，数据不存在");
        }

        // 转换Form为Entity
        DataFlowEntity updateEntity = BeanCopierUtils.copy(dataFlowForm, DataFlowEntity.class);
        updateEntity.setId(id);
        updateEntity.setUpdateBy(userInfo.getUserId());
        updateEntity.setUpdateTime(new Date());

        // 保留原有的创建信息
        updateEntity.setCreateBy(entity.getCreateBy());
        updateEntity.setCreateTime(entity.getCreateTime());

        dataFlowService.update(id, updateEntity);
        return ActionResult.success("更新成功");
    }

    /**
     * 删除流水记录
     *
     * @param id 流水记录ID
     * @return 操作结果
     */
    @Operation(summary = "删除流水记录")
    @DeleteMapping("/{id}")
    @Transactional
    public ActionResult delete(@PathVariable("id") String id) {
        DataFlowEntity entity = dataFlowService.getInfo(id);
        if (entity != null) {
            dataFlowService.delete(entity);
            return ActionResult.success("删除成功");
        } else {
            return ActionResult.fail("删除失败，数据不存在");
        }
    }
}
