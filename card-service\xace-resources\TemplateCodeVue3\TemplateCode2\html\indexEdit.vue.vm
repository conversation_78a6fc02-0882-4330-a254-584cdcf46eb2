##通用参数
#parse("PublicMacro/ConstantMarco.vm")
#ConstantParams()
#parse("PublicMacro/FormMarco.vm")
##  dataform属性生成
#macro(EditDataform)
    #foreach($fieLdsModel in ${context.fields})
        #set($html = $fieLdsModel.formColumnModel.fieLdsModel)
        #set($vModel = "${html.vModel}")
        #if($vModel !='')
            #set($config = $html.config)
            #set($jk = $html.config.xhKey)
            #set($dataType = "$!{config.dataType}")
            #set($defaultCurrent = $config.defaultCurrent)
            #set($defult = $config.defaultValue)
            #set($format = $html.format)
            #if($jk!='text' && $jk!='divider')
                #GetFeildDefaultValue("")
            #end
        #end
    #end
    #foreach($mast in ${context.mastTable})
        #set($mastField = $mast.formMastTableModel.mastTable.fieLdsModel)
        #set($config =$mastField.config)
        #set($jk = ${config.xhKey})
        #set($vModel = ${mast.formMastTableModel.vModel})
        #set($dataType = "$!{config.dataType}")
        #set($defaultCurrent = $config.defaultCurrent)
        #set($defult = $config.defaultValue)
        #set($format = $mastField.format)
        #if($jk!='text' && $jk!='divider')
            #GetFeildDefaultValue("")
        #end
    #end
    #if($context.version)
    version: 0,
    #end
#end
<template>
    <div class="xh-content-wrapper">
        <div class="xh-content-wrapper-center">
##            <!-- 有搜索 -->
#if(${context.columnData.searchList.size()}>0)
            <div class="xh-content-wrapper-search-box">
                <BasicForm @register="registerSearchForm" :schemas="searchSchemas"
                           @advanced-change="redoHeight" @submit="handleSearchSubmit" @reset="handleSearchReset"
                           class="search-form">
                </BasicForm>
            </div>
#end
            <div class="xh-content-wrapper-content">
                <BasicTable @register="registerTable" v-bind="getTableBindValue" ref="tableRef"
                            @columns-change="handleColumnChange">
                    #if(${context.btnPcList.size()}>0)
                        <template #tableTitle>
                            #foreach($btn in  ${context.btnPcList})
                                #if(${btn.value}=='add')
                                    <a-button type="primary" preIcon="${btn.icon}" #if(${context.columnData.useBtnPermission}) v-auth="'btn_${btn.value}'" #end
                                              @click="addHandle()">${btn.label}</a-button>
                                #end
                                #if(${btn.value}=='download')
                                    <a-button type="link" preIcon="${btn.icon}" #if(${context.columnData.useBtnPermission}) v-auth="'btn_${btn.value}'" #end
                                              @click="openExportModal(true, { columnList: state.exportList })">${btn.label}</a-button>
                                #end
                                #if(${btn.value}=='upload')
                                    <a-button type="link" preIcon="${btn.icon}" #if(${context.columnData.useBtnPermission}) v-auth="'btn_${btn.value}'" #end
                                              @click="openImportModal(true, { url: '${context.module}/${context.className}' })">${btn.label}</a-button>
                                #end
                                #if(${btn.value}=='batchRemove')
                                    <a-button type="link" preIcon="${btn.icon}" #if(${context.columnData.useBtnPermission}) v-auth="'btn_${btn.value}'" #end
                                              @click="handelBatchRemove()">${btn.label}</a-button>
                                #end
                                #if(${btn.value}=='batchPrint')
                                    <a-button type="link" preIcon="${btn.icon}" #if(${context.columnData.useBtnPermission}) v-auth="'btn_${btn.value}'" #end
                                              @click="handelBatchPrint()">${btn.label}</a-button>
                                #end
                            #end
                        </template>
                    #end
##                    <!-- 有高级查询：开始 -->
#if(${context.superQuery})
                    <template #toolbar>
                        <a-tooltip placement="top">
                            <template #title>
                                <span>{{ t('common.superQuery') }}</span>
                            </template>
                            <filter-outlined @click="openSuperQuery(true, { columnOptions: superQueryJson })" />
                        </a-tooltip>
                    </template>
#end
##                    <!-- 有高级查询：结束 -->
                    <template #bodyCell="{ column, record, index }">
                        <template v-if="column.flag === 'INDEX'">
                            <div class="edit-row-action">
                            #if(!$context.isFlow)
                                <span class="edit-row-index">{{ index + 1 }}</span>
                                <i class="ym-custom ym-custom-arrow-expand" @click="handleRowForm(record)"></i>
                            #else
                                <span>{{ index + 1 }}</span>
                            #end
                            </div>
                        </template>
                        <template v-if="record.rowEdit">
                            <template v-if="column.xhKey === 'inputNumber'">
                                <xh-input-number
                                        v-model:value="record[column.prop]"
                                        :placeholder="column.placeholder"
                                        :min="column.min"
                                        :max="column.max"
                                        :step="column.step"
                                        :controls="column.controls"
                                        :addonBefore="column.addonBefore"
                                        :addonAfter="column.addonAfter"
                                        :precision="column.precision"
                                        :thousands="column.thousands"
                                        :disabled="column.disabled" />
                            </template>
                            <template v-else-if="column.xhKey === 'calculate'">
                                <xh-calculate
                                        v-model:value="record[column.prop]"
                                        :isStorage="column.isStorage"
                                        :precision="column.precision"
                                        :thousands="column.thousands"
                                        detailed />
                            </template>
                            <template v-else-if="['rate', 'slider'].includes(column.xhKey)">
                                <xh-input-number v-model:value="record[column.prop]" placeholder="请输入"
                                                :disabled="column.disabled" />
                            </template>
                            <template v-else-if="column.xhKey === 'switch'">
                                <xh-switch v-model:value="record[column.prop]" :disabled="column.disabled" />
                            </template>
                            <template v-else-if="column.xhKey === 'timePicker'">
                                <xh-time-picker v-model:value="record[column.prop]" :format="column.format"
                                                  :placeholder="column.placeholder" :allowClear="column.clearable"
                                                  :disabled="column.disabled" />
                            </template>
                            <template v-else-if="column.xhKey === 'datePicker'">
                                <xh-date-picker v-model:value="record[column.prop]" :type="column.type"
                                                  :allowClear="column.clearable" :placeholder="column.placeholder"
                                                  :format="column.format" :disabled="column.disabled" />
                            </template>
                            <template v-else-if="column.xhKey === 'organizeSelect'">
                                <xh-organize-select v-model:value="record[column.prop]"
                                                      :placeholder="column.placeholder" :multiple="column.multiple"
                                                      :allowClear="column.clearable" :disabled="column.disabled" />
                            </template>
                            <template v-else-if="column.xhKey === 'depSelect'">
                                <xh-dep-select v-model:value="record[column.prop]"
                                                 :placeholder="column.placeholder" :multiple="column.multiple"
                                                 :allowClear="column.clearable" :disabled="column.disabled"
                                                 :selectType="column.selectType" :ableDepIds="column.ableDepIds" />
                            </template>
                            <template v-else-if="column.xhKey === 'roleSelect'">
                                <xh-role-select v-model:value="record[column.prop]"
                                                  :placeholder="column.placeholder" :multiple="column.multiple"
                                                  :allowClear="column.clearable" :disabled="column.disabled" />
                            </template>
                            <template v-else-if="column.xhKey === 'groupSelect'">
                                <xh-group-select v-model:value="record[column.prop]"
                                                   :placeholder="column.placeholder" :multiple="column.multiple"
                                                   :allowClear="column.clearable" :disabled="column.disabled" />
                            </template>
                            <template v-else-if="column.xhKey === 'posSelect'">
                                <xh-pos-select v-model:value="record[column.prop]"
                                                 :placeholder="column.placeholder" :multiple="column.multiple"
                                                 :allowClear="column.clearable" :disabled="column.disabled"
                                                 :selectType="column.selectType" :ableDepIds="column.ableDepIds"
                                                 :ablePosIds="column.ablePosIds" />
                            </template>
                            <template v-else-if="column.xhKey === 'userSelect'">
                                <xh-user-select v-model:value="record[column.prop]"
                                                  :placeholder="column.placeholder" :multiple="column.multiple"
                                                  :allowClear="column.clearable" :disabled="column.disabled"
                                                  :selectType="['all', 'custom'].includes(column.selectType) ? column.selectType : 'all'"
                                                  :ableDepIds="column.ableDepIds"
                                                  :ablePosIds="column.ablePosIds" :ableUserIds="column.ableUserIds"
                                                  :ableRoleIds="column.ableRoleIds" :ableGroupIds="column.ableGroupIds" />
                            </template>
                            <template v-else-if="column.xhKey === 'usersSelect'">
                                <xh-users-select v-model:value="record[column.prop]"
                                                   :placeholder="column.placeholder" :multiple="column.multiple"
                                                   :allowClear="column.clearable" :disabled="column.disabled"
                                                   :selectType="column.selectType" :ableIds="column.ableIds" />
                            </template>
                            <template v-else-if="column.xhKey === 'areaSelect'">
                                <xh-area-select v-model:value="record[column.prop]" :level="column.level"
                                                  :placeholder="column.placeholder" :multiple="column.multiple"
                                                  :allowClear="column.clearable" :disabled="column.disabled" />
                            </template>
                            <template v-else-if="['select', 'radio', 'checkbox'].includes(column.xhKey)">
                                <xh-select v-model:value="record[column.prop]" :placeholder="column.placeholder"
                                             :multiple="column.multiple || column.xhKey === 'checkbox'"
                                             :allowClear="column.clearable || ['radio', 'checkbox'].includes(column.xhKey)" :showSearch="column.filterable"
                                             :disabled="column.disabled" :options="column.options"
                                             :fieldNames="column.props" />
                            </template>
                            <template v-else-if="column.xhKey === 'cascader'">
                                <xh-cascader v-model:value="record[column.prop]" :placeholder="column.placeholder"
                                               :multiple="column.multiple" :allowClear="column.clearable"
                                               :showSearch="column.filterable" :disabled="column.disabled"
                                               :options="column.options" :fieldNames="column.props"
                                               :showAllLevels="column.showAllLevels" />
                            </template>
                            <template v-else-if="column.xhKey === 'treeSelect'">
                                <xh-tree-select v-model:value="record[column.prop]"
                                                  :placeholder="column.placeholder" :multiple="column.multiple"
                                                  :allowClear="column.clearable" :showSearch="column.filterable"
                                                  :disabled="column.disabled" :options="column.options"
                                                  :fieldNames="column.props" />
                            </template>
                            <template v-else-if="column.xhKey === 'relationForm'">
                                <xh-relation-form v-model:value="record[column.prop]"
                                                    :placeholder="column.placeholder" :allowClear="column.clearable"
                                                    :disabled="column.disabled" :modelId="column.modelId"
                                                    :columnOptions="column.columnOptions" :relationField="column.relationField"
                                                    :hasPage="column.hasPage" :pageSize="column.pageSize"
                                                    :popupType="column.popupType" :popupTitle="column.popupTitle"
                                                    :popupWidth="column.popupWidth" />
                            </template>
                            <template
                                    v-else-if="column.xhKey === 'popupSelect' || column.xhKey === 'popupTableSelect'">
                                <xh-popup-select v-model:value="record[column.prop]"
                                                   :placeholder="column.placeholder" :multiple="column.multiple"
                                                   :allowClear="column.clearable" :disabled="column.disabled"
                                                   :interfaceId="column.interfaceId" :columnOptions="column.columnOptions"
                                                   :propsValue="column.propsValue" :relationField="column.relationField"
                                                   :hasPage="column.hasPage" :pageSize="column.pageSize"
                                                   :popupType="column.popupType" :popupTitle="column.popupTitle"
                                                   :popupWidth="column.popupWidth" />
                            </template>
                            <template v-else-if="column.xhKey === 'autoComplete'">
                                <xh-auto-complete
                                        v-model:value="record[column.prop]"
                                        :placeholder="column.placeholder"
                                        :allowClear="column.clearable"
                                        :disabled="column.disabled"
                                        :interfaceId="column.interfaceId"
                                        :relationField="column.relationField"
                                        :templateJson="column.templateJson"
                                        :total="column.total" />
                            </template>
                            <template v-else-if="['input', 'textarea'].includes(column.xhKey)">
                                <xh-input v-model:value="record[column.prop]" :placeholder="column.placeholder"
                                            :allowClear="column.clearable" :disabled="column.disabled"
                                            :readonly="column.readonly" :prefixIcon="column.prefixIcon"
                                            :suffixIcon="column.suffixIcon" :addonBefore="column.addonBefore"
                                            :addonAfter="column.addonAfter" :maxlength="column.maxlength"
                                            :showPassword="column.showPassword">
                                </xh-input>
                            </template>
                            <template v-else-if="systemComponentsList.includes(column.xhKey)">
                                {{ record[column.prop + '_name'] || record[column.prop] }}
                            </template>
                            <template v-else>
                                {{ record[column.prop] }}
                            </template>
                        </template>
                        <template v-else>
                            <template v-if="column.xhKey === 'inputNumber'">
                                <xh-input-number v-model:value="record[column.prop]" :precision="column.precision" :thousands="column.thousands" disabled detailed />
                            </template>
                            <template v-else-if="column.xhKey === 'calculate'">
                                <xh-calculate
                                        v-model:value="record[column.prop]"
                                        :isStorage="column.isStorage"
                                        :precision="column.precision"
                                        :thousands="column.thousands"
                                        detailed />
                            </template>
                            <template v-else-if="column.xhKey === 'relationForm'">
                                <p class="link-text" @click="toDetail(column.modelId, record[`${column.prop}_id`])">
                                    {{ record[column.prop + '_name'] || record[column.prop] }}
                                </p>
                            </template>
                            <template v-else>
                                {{ record[column.prop + '_name'] || record[column.prop] }}
                            </template>
                        </template>
##                        <!-- 有工作流：开始 -->
#if($!{context.isFlow})
                        <template v-if="column.key === 'flowState' && !record.top">
                            <a-tag color="processing" v-if="record.flowState == 1">等待审核</a-tag>
                            <a-tag color="success" v-else-if="record.flowState == 2">审核通过</a-tag>
                            <a-tag color="error" v-else-if="record.flowState == 3">审核退回</a-tag>
                            <a-tag v-else-if="record.flowState == 4">流程撤回</a-tag>
                            <a-tag v-else-if="record.flowState == 5">审核终止</a-tag>
                            <a-tag color="error" v-else-if="record.flowState == 6">已被挂起</a-tag>
                            <a-tag color="warning" v-else>等待提交</a-tag>
                        </template>
#end
##                        <!-- 有工作流：结束 -->
                        <template v-if="column.key === 'action' && !record.top">
                            <TableAction :actions="getTableActions(record,index)" />
                        </template>
                    </template>
##                    <!-- 有合计：开始 -->
#if(${context.configurationTotal} == true)
                    <template #summary v-if="state.cacheList.length">
                        <a-table-summary fixed>
                            <a-table-summary-row>
                                <a-table-summary-cell :index="0">合计</a-table-summary-cell>
                                <a-table-summary-cell v-for="(item, index) in getColumnSum" :key="index"
                                                      :index="index + 1">{{ item }}</a-table-summary-cell>
                                <a-table-summary-cell :index="getColumnSum.length + 1"></a-table-summary-cell>
                            </a-table-summary-row>
                        </a-table-summary>
                    </template>
#end
##                    <!-- 有合计：结束 -->
                </BasicTable>
            </div>
        </div>
#if(!$context.isFlow)
        <ExtraForm ref="extraFormRef" @reload="reload" />
#end
#foreach($itemBtn in ${context.columnBtnPcList})
    #if(!${context.isFlow} && ${itemBtn.value}=="detail")
##                <!-- 有详情：开始 -->
        <Detail ref="detailRef" />
    #end
    #if(${itemBtn.value}=="edit")
    #end
    #if(${itemBtn.value}=="remove")
    #end
#end
        <!-- 有关联表单详情：开始 -->
        <RelationDetail ref="relationDetailRef" />
        <!-- 有关联表单详情：结束 -->
#foreach($itemBtn in ${context.btnPcList})
    #if(${itemBtn.value}=="add")
    #end
    #if(${itemBtn.value}=="upload")
        <ImportModal @register="registerImportModal" @reload="reload" />
    #end
    #if(${itemBtn.value}=="download")
        <ExportModal @register="registerExportModal" @download="handleDownload" />
    #end
    #if(${itemBtn.value}=="batchRemove")
    #end
    #if(${itemBtn.value}=="batchPrint")
        <PrintSelect @register="registerPrintSelect" @change="handleShowBrowse" />
        <PrintBrowse @register="registerPrintBrowse" />
    #end
#end
#if(${context.superQuery})
##        <!-- 有高级查询 -->
        <SuperQueryModal @register="registerSuperQueryModal" @superQuery="handleSuperQuery" />
#end
#if(${context.isFlow})
##        <!-- 带流程：开始 -->
        <CandidateModal @register="registerCandidate" @confirm="submitCandidate" />
        <FlowParser @register="registerFlowParser" @reload="reload" />
        <BasicModal v-bind="$attrs" @register="registerFlowListModal" title="请选择流程" :footer="null"
                    :width="400" destroyOnClose class="xh-flow-list-modal">
            <div class="template-list">
                <ScrollContainer>
                    <div class="template-item" v-for="item in flowList" :key="item.id"
                         @click="selectFlow(item)">
                        {{ item.fullName }}
                    </div>
                </ScrollContainer>
            </div>
        </BasicModal>
#end
    </div>
</template>

<script lang="ts" setup>

        #if($context.isFlow)
        import {batchDelete, create, del, exportData, getList, update} from './helper/api';
        import {getConfigData} from '/@/api/onlineDev/visualDev';
        import {getCandidates} from '/@/api/workFlow/flowBefore';
        import {getFlowByFormId} from '/@/api/workFlow/formDesign';
        import {getFlowList} from '/@/api/workFlow/flowEngine';
        // 工作流
        import {getDictionaryDataSelector} from '/@/api/systemData/dictionary';
        import {getDataInterfaceRes} from '/@/api/systemData/dataInterface';
        import {computed, nextTick, onMounted, reactive, ref, toRefs, unref} from 'vue';
        import {useMessage} from '/@/hooks/web/useMessage';
        import {useI18n} from '/@/hooks/web/useI18n';
        import {useOrganizeStore} from '/@/store/modules/organize';
        import {useUserStore} from '/@/store/modules/user';
        import {useModal} from '/@/components/Modal';
        import {usePopup} from '/@/components/Popup';
        import {useForm} from '/@/components/Form';
        import {ActionItem, TableActionType, useTable} from '/@/components/Table';
        import {downloadByUrl} from '/@/utils/file/download';
        // 打印模板多条生成PrintSelect
        // 有关联表单详情：开始
        import {useRoute} from 'vue-router';
        import {getSearchFormSchemas} from '/@/components/FormGenerator/src/helper/transform';
        import {cloneDeep} from 'lodash-es';
        import columnList from './helper/columnList';
        import searchList from './helper/searchList';
        import {dyOptionsList} from '/@/components/FormGenerator/src/helper/config';
        import dayjs from 'dayjs';
        import {getTimeUnit, thousandsFormat} from '/@/utils/xh';
        #else
    #end
        #if(${context.superQuery})
        #end
#foreach($itemBtn in ${context.columnBtnPcList})
    #if(!${context.isFlow} &&${itemBtn.value}=="detail")
    #end
    #if(${itemBtn.value}=="edit")
    #end
    #if(${itemBtn.value}=="remove")
    #end
#end
#foreach($itemBtn in ${context.btnPcList})
    #if(${itemBtn.value}=="add")
    #end
    #if(${itemBtn.value}=="upload")
    #end
    #if(${itemBtn.value}=="download")
    #end
    #if(${itemBtn.value}=="batchRemove")
    #end
    #if(${itemBtn.value}=="batchPrint")
    #end
#end
    // 有关联表单详情：结束
#if(!$context.isFlow)
#end

    interface State {
        formFlowId: string;
        flowList: any[];
        config: any;
        columnList: any[];
        printListOptions: any[];
        columnBtnsList: any[];
        customBtnsList: any[];
        treeFieldNames: any;
        leftTreeData: any[];
        leftTreeLoading: boolean;
        treeActiveId: string;
        treeActiveNodePath: any;
        columns: any[];
        complexColumns: any[];
        childColumnList: any[];
        exportList: any[];
        cacheList: any[];
        currFlow: any;
        isCustomCopy: boolean;
        candidateType: number;
        currRow: any;
        workFlowFormData: any;
        expandObj: any;
        columnSettingList: any[];
        searchSchemas: any[];
        treeRelationObj: any;
        resetFromTree: boolean;
    }

    const route = useRoute();
    const { createMessage, createConfirm } = useMessage();
    const { t } = useI18n();
    const organizeStore = useOrganizeStore();
    const userStore = useUserStore();
    const userInfo = userStore.getUserInfo;

    const [registerExportModal, { openModal: openExportModal, closeModal: closeExportModal, setModalProps: setExportModalProps }] = useModal();
    const [registerImportModal, { openModal: openImportModal }] = useModal();
    const [registerSuperQueryModal, { openModal: openSuperQuery }] = useModal();
    #if(${context.hasPrintBtn})
    const [registerPrintSelect, { openModal: openPrintSelect }] = useModal();
    const [registerPrintBrowse, { openModal: openPrintBrowse }] = useModal();
    #end
    // 工作流
    const [registerFlowParser, { openPopup: openFlowParser }] = usePopup();
    const [registerFlowListModal, { openModal: openFlowListModal, closeModal: closeFlowListModal }] = useModal();
    const [registerCandidate, { openModal: openCandidateModal, closeModal: closeCandidateModal }] = useModal();
    // 工作流
    const formRef = ref<any>(null);
    const tableRef = ref<Nullable<TableActionType>>(null);
    const detailRef = ref<any>(null);
    #if(!$context.isFlow)
    const extraFormRef = ref<any>(null);
    #end
    const relationDetailRef = ref<any>(null);
    const defaultSearchInfo = {
        menuId: route.meta.modelId as string,
        moduleId:'${context.moduleId}',
        superQueryJson: '',
    };
    const searchInfo = reactive({
        ...cloneDeep(defaultSearchInfo),
    });
    const state = reactive<State>({
        formFlowId: '',
        flowList: [],
        config: {},
        columnList: [],
        printListOptions: [],
        columnBtnsList: [],
        customBtnsList: [],
        treeFieldNames: {
        children: #if(${context.columnData.treePropsChildren}) '${context.columnData.treePropsChildren}' #else 'children' #end,
        title: #if(${context.columnData.treePropsLabel}) '${context.columnData.treePropsLabel}' #else 'fullName' #end,
        key: #if(${context.columnData.treePropsValue}) '${context.columnData.treePropsValue}' #else 'id' #end,
            isLeaf: 'isLeaf',
        },
        leftTreeData: [],
        leftTreeLoading: false,
        treeActiveId: '',
        treeActiveNodePath: [],
        columns: [],
        complexColumns: [], // 复杂表头
        childColumnList: [],
        exportList: [],
        cacheList: [],
        currFlow: {},
        isCustomCopy: false,
        candidateType: 1,
        currRow: {},
        workFlowFormData: {},
        expandObj: {},
        columnSettingList: [],
        searchSchemas: [],
        treeRelationObj: null,
        resetFromTree: false,
    });
    const { flowList, childColumnList, searchSchemas } = toRefs(state);
    const [registerSearchForm, { updateSchema, resetFields, setFieldsValue, submit: searchFormSubmit }] = useForm({
        baseColProps: { span: 6 },
        showActionButtonGroup: true,
        showAdvancedButton: true,
        compact: true,
    });
    const [registerTable, { reload, setLoading, getFetchParams, getSelectRowKeys, redoHeight, insertTableDataRecord, updateTableDataRecord, deleteTableDataRecord,clearSelectedRowKeys }] = useTable({
        api: getList,
        immediate: false,
        clickToRowSelect: false,
        afterFetch: (data) => {
            const list = data.map((o) => ({ ...o, rowEdit: false }));
            state.cacheList = cloneDeep(list);
            return list;
        },
    });

    const getTableBindValue = computed(() => {
        let columns = state.columns;
    #if(${context.isFlow})
        columns.push({ title: '状态', dataIndex: 'flowState', width: 100 });
    #end
        const data: any = {
        #if(!${context.hasPage} || ${context.groupTable} || ${context.treeTable})
            pagination: false, //没有分页，树形，分组
        #else
            pagination: { pageSize: ${context.columnData.pageSize} }, //有分页
        #end
            searchInfo: unref(searchInfo),
            defSort: {
                sort: "${context.columnData.sort}", //sort
                sidx:"${context.columnData.defaultSidx}", //取defaultSidx
            },
            columns,
            rowSelection: {
                type: 'checkbox',
                getCheckboxProps: (record) => ({ disabled: !!record.top }),
            },
            actionColumn: {
                width: 150,
                title: '操作',
                dataIndex: 'action',
            },
        };
        return data;
    });
##合计变量
#if(${context.configurationTotal})
    const getSummaryColumn = computed(() => {
        let defaultColumns = state.columns;
        // 处理列固定
        if (state.columnSettingList?.length) {
            for (let i = 0; i < defaultColumns.length; i++) {
                inner: for (let j = 0; j < state.columnSettingList.length; j++) {
                    if (defaultColumns[i].dataIndex === state.columnSettingList[j].dataIndex) {
                        defaultColumns[i].fixed = state.columnSettingList[j].fixed;
                        defaultColumns[i].visible = state.columnSettingList[j].visible;
                        break inner;
                    }
                }
            }
            defaultColumns = defaultColumns.filter((o) => o.visible);
        }
        let columns: any[] = [];
        for (let i = 0; i < defaultColumns.length; i++) {
            if (defaultColumns[i].xhKey === 'table') {
                columns.push(...defaultColumns[i].children);
            } else {
                columns.push(defaultColumns[i]);
            }
        }
        const leftFixedList = columns.filter((o) => o.fixed === 'left');
        const rightFixedList = columns.filter((o) => o.fixed === 'right');
        const noFixedList = columns.filter((o) => o.fixed !== 'left' && o.fixed !== 'right');
        return [...leftFixedList, ...noFixedList, ...rightFixedList];
    });
    const getColumnSum = computed(() => {
        const sums: any[] = [];
        const summaryField: any = #if(${context.fieldsTotal})${context.fieldsTotal}#else [] #end; //取summaryField
        const isSummary = (key) => summaryField.includes(key);
        const useThousands = key => unref(getSummaryColumn).some(o => o.__vModel__ === key && o.thousands);
        unref(getSummaryColumn).forEach((column, index) => {
            let sumVal = state.cacheList.reduce((sum, d) => sum + getCmpValOfRow(d, column.prop), 0);
            if (!isSummary(column.prop)) sumVal = '';
            sumVal = Number.isNaN(sumVal) ? '' : sumVal;
            const realVal = sumVal && !Number.isInteger(sumVal) ? Number(sumVal).toFixed(2) : sumVal;
            sums[index] = useThousands(column.prop) ? thousandsFormat(realVal) : realVal;
        });
        // 有多选
        sums.unshift('');
        // 有多选
        return sums;
    });
#end
##合计方法
#if(${context.configurationTotal})
    //合计方法
    function getCmpValOfRow(row, key) {
        const summaryField: any = #if(${context.fieldsTotal})${context.fieldsTotal}#else [] #end; //取summaryField
        const isSummary = (key) => summaryField.includes(key);
        if (!summaryField.length || !isSummary(key)) return 0;
        const target = row[key];
        if (!target) return 0;
        const data = isNaN(target) ? 0 : Number(target);
        return data;
    }
#end
##行内按键
    function getTableActions(record,index): ActionItem[] {
        const list: any[] = [
        #foreach($itemBtn in ${context.columnBtnPcList})
            #if(${itemBtn.value}=="edit")
                {
                    label: '${itemBtn.label}',
                    #if(${context.isFlow})
                        disabled: [1, 2, 4, 5].includes(record.flowState), //有流程加上
                    #end
                    onClick: updateHandle.bind(null, record),
                    #if(${context.columnData.useBtnPermission})
                        auth: 'btn_edit', //有按钮权限
                    #end
                },
            #end
            #if(${itemBtn.value}=="detail")
                {
                    label: '${itemBtn.label}',
                    #if(${context.isFlow})
                        disabled: !record.flowState, //有流程加上
                    #end
                    onClick: goDetail.bind(null, record),
                    #if(${context.columnData.useBtnPermission})
                        auth: 'btn_detail', //有按钮权限
                    #end
                },
            #end
            #if(${itemBtn.value}=="remove")
                {
                    label: '${itemBtn.label}',
                    color: 'error',
                    #if(${context.isFlow})
                        disabled: [1, 2, 3, 5].includes(record.flowState), //有流程加上
                    #end
                    modelConfirm: {
                        onOk: handleDelete.bind(null, record.id),
                    },
                    #if(${context.columnData.useBtnPermission})
                        auth: 'btn_remove', //有按钮权限
                    #end
                },
            #end
        #end
        ];
        if (record.rowEdit) {
            let editBtnList: ActionItem[] = [
                { label: '保存', onClick: saveForRowEdit.bind(null, record, 1) },
                { label: '取消', color: 'error', onClick: cancelRowEdit.bind(null, record, index) },
            ];
            #if($context.isFlow)
                // 有工作流
                editBtnList.push({ label: '提交', onClick: submitForRowEdit.bind(null, record) });
            #end
            return editBtnList;
        }
        return list;
    }
##开启流程--流程调用相关方法
#if(${context.isFlow})
    function getFlowId() {
        getFlowByFormId("${context.moduleId}").then((res) => {
            const flowId = res.data && res.data.id;
            state.formFlowId = flowId;
            getFlowOptions();
        });
    }
    // 获取子流程list
    function getFlowOptions() {
        getFlowList(state.formFlowId, '1').then((res) => {
            state.flowList = res.data;
        });
    }
    function selectFlow(item) {
        state.currFlow = item;
        closeFlowListModal();
        const flowTemplateJson = item.flowTemplateJson ? JSON.parse(item.flowTemplateJson) : {};
        state.isCustomCopy = (flowTemplateJson.properties && flowTemplateJson.properties.isCustomCopy) || false;
        let record = { rowEdit: true, id: 'xhAdd', #EditDataform() };
        insertTableDataRecord(record, 0);
    }
    // 选择候选人
    function submitCandidate(data) {
        saveForRowEdit(state.currRow, '0', data);
    }
#end
    function cancelRowEdit(record, index) {
        const id = !record.id || record.id === 'xhAdd' ? '' : record.id;
        if (!id) return deleteTableDataRecord('xhAdd');
        record.rowEdit = false;
        const item = cloneDeep(state.cacheList[index]);
        updateTableDataRecord(item.id, item);
    }
    // 行内编辑保存
    function saveForRowEdit(record, status = '1', candidateData: any = null) {
        const id = !record.id || record.id === 'xhAdd' ? '' : record.id;
#if(${context.isFlow})
        // 工作流
        let query = {
          id,
          status: status || '1',
          candidateType: state.candidateType,
          formData: record,
          flowId: state.currFlow.id || state.flowList[0].id,
          flowUrgent: 1,
        };
        if (candidateData) query = { ...query, ...candidateData };
        const formMethod = query.id ? update : create;
        formMethod(query).then(res => {
          createMessage.success(res.msg);
          closeCandidateModal();
          reload({ page: 1 });
        });
#else
        record.id = id;
        const query = { ...record };
        const formMethod = query.id ? update : create;
        formMethod(query).then((res) => {
            createMessage.success(res.msg);
            reload({ page: 1 });
        });
#end
    }
    // 行内编辑提交审核
    function submitForRowEdit(record) {
        record.id = !record.id || record.id === 'xhAdd' ? '' : record.id;
        state.currRow = record;
        state.workFlowFormData = {
            id: record.id,
            formData: record,
            flowId: state.currFlow.id,
        };
        getCandidates(0, state.workFlowFormData).then((res) => {
            const data = res.data;
            state.candidateType = data.type;
            if (data.type == 3 && !state.isCustomCopy) {
                createConfirm({
                    iconType: 'warning',
                    title: '提示',
                    content: '您确定要提交当前流程吗, 是否继续?',
                    onOk: () => {
                        saveForRowEdit(record, '0');
                    },
                });
                return;
            }
            let branchList = [];
            let candidateList = [];
            if (data.type == 1) {
                branchList = res.data.list.filter((o) => o.isBranchFlow);
                candidateList = res.data.list.filter((o) => !o.isBranchFlow && o.isCandidates);
            }
            if (data.type == 2) {
                candidateList = res.data.list.filter((o) => o.isCandidates);
            }
            openCandidateModal(true, {
                branchList,
                candidateList,
                isCustomCopy: state.isCustomCopy,
                taskId: state.config.taskId,
                formData: state.workFlowFormData,
            });
        });
    }

##行内按键方法
#foreach($itemBtn in ${context.columnBtnPcList})
    #if(${itemBtn.value}=="edit")
    // 编辑
    function updateHandle(record) {
        buildRowRelation();
        record.rowEdit = true;
        #if(${context.isFlow})
        // 带工作流
        const flowId = record.flowId || state.flowList[0];
        if (!flowId) return;
        const list = state.flowList.filter((o) => o.id === flowId);
        if (!list.length) return;
        state.currFlow = list[0];
        const flowTemplateJson = state.currFlow.flowTemplateJson ? JSON.parse(state.currFlow.flowTemplateJson) : {};
        state.isCustomCopy = (flowTemplateJson.properties && flowTemplateJson.properties.isCustomCopy) || false;
        #end
    }
    #end
    #if(${itemBtn.value}=="detail")
    // 查看详情
    function goDetail(record) {
        #if(${context.isFlow})
            // 带流程
            const data = {
                id: record.id,
                flowId: record.flowId || state.flowList[0].id,
                opType: 0,
                status: record.flowState,
            };
            openFlowParser(true, data);
        #else
            // 不带流程
            const data = {
                id: record.id,
            };
            detailRef.value?.init(data);
        #end
    }
    #end
    #if(${itemBtn.value}=="remove")
    // 删除
    function handleDelete(id) {
        del(id).then((res) => {
            createMessage.success(res.msg);
            clearSelectedRowKeys();
            reload();
        });
    }
    #end
#end
##表头按键方法
#foreach($itemBtn in ${context.btnPcList})
    #if(${itemBtn.value}=="add")
    // 新增
    function addHandle() {
        buildRowRelation();
        #if(${context.isFlow})
            // 带流程新增
            if (!state.flowList.length) return createMessage.error('流程不存在');
            if (state.flowList.length === 1) return selectFlow(state.flowList[0]);
            openFlowListModal(true);
        #else
            // 不带流程新增
            let record = { rowEdit: true, id: 'xhAdd', #EditDataform() };
            insertTableDataRecord(record, 0);
        #end
    }
    #end
    #if(${itemBtn.value}=="upload")
    #end
    #if(${itemBtn.value}=="download")
    // 导出
    function handleDownload(data) {
        let query = { ...getFetchParams(), ...data };
        exportData(query)
                .then((res) => {
                    setExportModalProps({ confirmLoading: false });
                    if (!res.data.url) return;
                    downloadByUrl({ url: res.data.url });
                    closeExportModal();
                })
                .catch(() => {
                    setExportModalProps({ confirmLoading: false });
                });
    }
    #end
    #if(${itemBtn.value}=="batchRemove")
    // 批量删除
    function handelBatchRemove() {
        const ids = getSelectRowKeys();
        if (!ids.length) return createMessage.error('请选择一条数据');
        createConfirm({
            iconType: 'warning',
            title: t('common.tipTitle'),
            content: '您确定要删除这些数据吗, 是否继续?',
            onOk: () => {
                batchDelete(ids).then((res) => {
                    createMessage.success(res.msg);
                    clearSelectedRowKeys();
                    reload();
                });
            },
        });
    }
    #end
    #if(${itemBtn.value}=="batchPrint")
    //打印方法
    function handelBatchPrint() {
    let printIds=#if(${context.columnData.printIds}) ${context.columnData.printIds} #else [] #end
        if (!printIds?.length) return createMessage.error('未配置打印模板');
        const ids = getSelectRowKeys();
        if (!ids.length) return createMessage.error('请选择一条数据');
        if (printIds?.length === 1) return handleShowBrowse(printIds[0]);
        openPrintSelect(true, printIds);
    }
    function handleShowBrowse(id) {
        openPrintBrowse(true, { id, batchIds: getSelectRowKeys().join() });
    }
    #end
#end

    function init() {
        state.config = {};
    #if(${context.isFlow})
        // 带流程
        getFlowId();
    #end
        searchInfo.menuId = route.meta.modelId as string;
        state.columnList = columnList;
    #if(${context.groupTable})
        // 分组
        state.columnList = state.columnList.filter((o) => o.prop != '${context.groupField}');
    #end
        setLoading(true);
        getSearchSchemas();
        getColumnList();
    #if(${context.leftTreeTable})
        // 有左侧树
        getTreeView(true);
    #else
        nextTick(() => {
            #if(${context.columnData.searchList.size()}>0)
                // 有搜索列表
                searchFormSubmit();
            #else
                //  无搜索列表
                reload({ page: 1 });
            #end
        });
    #end
    }

    function getSearchSchemas() {
        const schemas = getSearchFormSchemas(searchList);
        state.searchSchemas = schemas;
        schemas.forEach((cur) => {
            const config = cur.__config__;
            if (dyOptionsList.includes(config.xhKey)) {
                if (config.dataType === 'dictionary') {
                    if (!config.dictionaryType) return;
                    getDictionaryDataSelector(config.dictionaryType).then((res) => {
                        updateSchema([{ field: cur.field, componentProps: { options: res.data.list } }]);
                    });
                }
                if (config.dataType === 'dynamic') {
                    if (!config.propsUrl) return;
                    const query = { paramList: config.templateJson || [] };
                    getDataInterfaceRes(config.propsUrl, query).then((res) => {
                        const data = Array.isArray(res.data) ? res.data : [];
                        updateSchema([{ field: cur.field, componentProps: { options: data } }]);
                    });
                }
            }
            if (config.defaultCurrent) {
                if (config.xhKey === 'organizeSelect' && userInfo.organizeIdList?.length) {
                    cur.defaultValue = cur.componentProps.multiple ? [userInfo.organizeIdList] : userInfo.organizeIdList;
                }
                if (config.xhKey === 'depSelect' && config.defaultValue) cur.defaultValue = config.defaultValue;
                if (config.xhKey === 'userSelect' && config.defaultValue) cur.defaultValue = config.defaultValue;
            }
        });
    }
    function getColumnList() {
        #if(${context.columnData.useColumnPermission})
            // 开启列表过滤权限
            let columnList: any[] = [];
            const permissionList = userStore.getPermissionList;
            const list = permissionList.filter(o => o.modelId === searchInfo.menuId);
            const perColumnList = list[0] && list[0].column ? list[0].column : [];
            for (let i = 0; i < state.columnList.length; i++) {
                inner: for (let j = 0; j < perColumnList.length; j++) {
                    if (state.columnList[i].prop === perColumnList[j].enCode) {
                        columnList.push(state.columnList[i]);
                        break inner;
                    }
                }
            }
        #else
            // 没有开启列表权限
            let  columnList = state.columnList;
        #end
        state.exportList = columnList.filter(o => o.prop.indexOf('-') < 0);
        const columns = columnList.map((o) => ({
            ...o,
            title: o.label,
            dataIndex: o.prop,
            align: o.align,
            fixed: o.fixed == 'none' ? false : o.fixed,
            sorter: o.sortable,
            width: o.width || 100,
        }));
        state.columns = columns.filter((o) => o.prop.indexOf('-') < 0);
    }
    // 关联表单查看详情
    function toDetail(modelId, id) {
        if (!id) return;
        getConfigData(modelId).then((res) => {
            if (!res.data || !res.data.formData) return;
            const formConf = JSON.parse(res.data.formData);
            formConf.popupType = 'general';
            const data = { id, formConf, modelId };
            relationDetailRef.value?.init(data);
        });
    }
    function handleColumnChange(data) {
        state.columnSettingList = data;
    }
##高级查询
#if(${context.superQuery})
    // 高级查询
    function handleSuperQuery(superQueryJson) {
        searchInfo.superQueryJson = superQueryJson;
        reload({ page: 1 });
    }
#end
##有普通查询
#if(${context.columnData.searchList.size()}>0)
    function handleSearchReset() {
        clearSelectedRowKeys();
        if (!state.resetFromTree) updateSearchFormValue();
        if (state.resetFromTree) state.resetFromTree = false;
    }
    function handleSearchSubmit(data) {
        clearSelectedRowKeys();
        let obj = {
            ...defaultSearchInfo,
            superQueryJson: searchInfo.superQueryJson,
            ...data,
        };
        Object.keys(searchInfo).map(key => {
            delete searchInfo[key];
        });
        for (let [key, value] of Object.entries(obj)) {
            searchInfo[key.replaceAll('-', '_')] = value;
        }
        console.log(searchInfo);
        reload({ page: 1 });
    }
    function updateSearchFormValue() {
        if (!state.treeActiveId) return searchFormSubmit();
        let queryJson: any = {};
        const isMultiple = !state.treeRelationObj ? false : state.treeRelationObj.searchMultiple;
        //多级左侧树，需要拼父级->转为查询参数
        if (state.treeRelationObj && state.treeRelationObj.xhKey && ${multipleTwoUnitStr}.includes(state.treeRelationObj.xhKey)) {
            const currValue = state.treeActiveNodePath.map(o => o[state.treeFieldNames.key]);
            queryJson = { '${context.columnData.treeRelation}': isMultiple ? [currValue] : currValue };
        } else {
            queryJson = { '${context.columnData.treeRelation}': isMultiple ? [state.treeActiveId] : state.treeActiveId };
        }
        #if(${context.columnData.searchList.size()}>0)
            // 有搜索列表
            setFieldsValue(queryJson);
            searchFormSubmit();
        #else
            // 无搜索列表
            handleSearchSubmit(queryJson);
        #end
    }
#end
    // 行内编辑获取选项
    function buildOptions() {
        state.columns.forEach((cur) => {
            const config = cur.__config__;
            if (dyOptionsList.includes(config.xhKey)) {
                if (config.dataType === 'dictionary') {
                    if (!config.dictionaryType) return;
                    getDictionaryDataSelector(config.dictionaryType).then((res) => {
                        cur.options = res.data.list;
                    });
                }
                if (config.dataType === 'dynamic') {
                    if (!config.propsUrl) return;
                    const query = { paramList: config.templateJson || [] };
                    getDataInterfaceRes(config.propsUrl, query).then((res) => {
                        cur.options = Array.isArray(res.data) ? res.data : [];
                    });
                }
            }
        });
    }
    #if(!$context.isFlow)
    function handleRowForm(record) {
        const data = {
            id: record.id,
            menuId: searchInfo.menuId,
            formData: record,
        };
        extraFormRef.value?.init(data);
    }
    #end
##动态时间处理
    function buildRowRelation() {
        for (let i = 0; i < state.columns.length; i++) {
            let cur = state.columns[i];
            const config = cur.__config__;
            if (config.xhKey === 'datePicker') {
                if (config.startTimeRule) {
                    if (config.startTimeType == 1) cur.startTime = config.startTimeValue;
                    if (config.startTimeType == 3) cur.startTime = new Date().getTime();
                    if (config.startTimeType == 4 || config.startTimeType == 5) {
                        const type = getTimeUnit(config.startTimeTarget);
                        const method = config.startTimeType == 4 ? 'subtract' : 'add';
                        const startTime = dayjs()[method](config.startTimeValue, type);
                        let realStartTime = startTime.startOf('day').valueOf();
                        if (config.startTimeTarget == 4) realStartTime = startTime.startOf('minute').valueOf();
                        if (config.startTimeTarget == 5) realStartTime = startTime.startOf('second').valueOf();
                        if (config.startTimeTarget == 6) realStartTime = startTime.valueOf();
                        cur.startTime = realStartTime;
                    }
                }
                if (config.endTimeRule) {
                    if (config.endTimeType == 1) cur.endTime = config.endTimeValue;
                    if (config.endTimeType == 3) cur.endTime = new Date().getTime();
                    if (config.endTimeType == 4 || config.endTimeType == 5) {
                        const type = getTimeUnit(config.endTimeTarget);
                        const method = config.endTimeType == 4 ? 'subtract' : 'add';
                        const endTime = dayjs()[method](config.endTimeValue, type);
                        let realEndTime = endTime.endOf('day').valueOf();
                        if (config.endTimeTarget == 4) realEndTime = endTime.endOf('minute').valueOf();
                        if (config.endTimeTarget == 5) realEndTime = endTime.endOf('second').valueOf();
                        if (config.endTimeTarget == 6) realEndTime = endTime.valueOf();
                        cur.endTime = realEndTime;
                    }
                }
            }
            if (config.xhKey === 'timePicker') {
                if (config.startTimeRule) {
                    if (config.startTimeType == 1) cur.startTime = config.startTimeValue || null;
                    if (config.startTimeType == 3) cur.startTime = dayjs().format(cur.format);
                    if (config.startTimeType == 4 || config.startTimeType == 5) {
                        const type = getTimeUnit(config.startTimeTarget + 3);
                        const method = config.startTimeType == 4 ? 'subtract' : 'add';
                        const startTime = dayjs()[method](config.startTimeValue, type).format(cur.format);
                        cur.startTime = startTime;
                    }
                }
                if (config.endTimeRule) {
                    if (config.endTimeType == 1) cur.endTime = config.endTimeValue || null;
                    if (config.endTimeType == 3) cur.endTime = dayjs().format(cur.format);
                    if (config.endTimeType == 4 || config.endTimeType == 5) {
                        const type = getTimeUnit(config.endTimeTarget + 3);
                        const method = config.endTimeType == 4 ? 'subtract' : 'add';
                        const endTime = dayjs()[method](config.endTimeValue, type).format(cur.format);
                        cur.endTime = endTime;
                    }
                }
            }
        }
    }
    onMounted(() => {
        init();
    });
</script>
