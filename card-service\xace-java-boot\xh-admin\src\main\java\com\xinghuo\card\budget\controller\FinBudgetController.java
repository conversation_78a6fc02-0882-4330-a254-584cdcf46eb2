package com.xinghuo.card.budget.controller;

import com.xinghuo.card.budget.entity.FinBudgetEntity;
import com.xinghuo.card.budget.model.budget.FinBudgetForm;
import com.xinghuo.card.budget.model.budget.FinBudgetPagination;
import com.xinghuo.card.budget.model.budget.FinBudgetVO;
import com.xinghuo.card.budget.service.FinBudgetService;
import com.xinghuo.common.base.ActionResult;
import com.xinghuo.common.base.vo.PageListVO;
import com.xinghuo.common.base.vo.PaginationVO;
import com.xinghuo.common.util.core.BeanCopierUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 预算管理
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2024-06-28
 */
@Slf4j
@RestController
@Tag(name = "预算管理", description = "预算管理")
@RequestMapping("/api/card/budget")
public class FinBudgetController {

    @Autowired
    private FinBudgetService finBudgetService;

    /**
     * 获取预算列表
     *
     * @param finBudgetPagination 分页查询参数
     * @return 预算列表
     */
    @PostMapping("/getList")
    @Operation(summary = "获取预算列表")
    public ActionResult<PageListVO<FinBudgetVO>> list(@RequestBody FinBudgetPagination finBudgetPagination) {
        PageListVO<FinBudgetEntity> pageList = finBudgetService.getList(finBudgetPagination);
        List<FinBudgetVO> listVOs = BeanCopierUtils.copyList(pageList.getList(), FinBudgetVO.class);
        
        // 补充状态名称等显示信息
        for (FinBudgetVO vo : listVOs) {
            enrichBudgetVO(vo);
        }
        
        PaginationVO page = BeanCopierUtils.copy(finBudgetPagination, PaginationVO.class);
        return ActionResult.page(listVOs, page);
    }

    /**
     * 获取预算详情
     *
     * @param id 预算ID
     * @return 预算详情
     */
    @GetMapping("/{id}")
    @Operation(summary = "获取预算详情")
    public ActionResult<FinBudgetVO> info(@PathVariable("id") String id) {
        FinBudgetEntity entity = finBudgetService.getInfo(id);
        if (entity == null) {
            return ActionResult.fail("预算不存在");
        }
        
        FinBudgetVO vo = BeanCopierUtils.copy(entity, FinBudgetVO.class);
        enrichBudgetVO(vo);
        
        return ActionResult.success(vo);
    }

    /**
     * 创建预算
     *
     * @param finBudgetForm 预算表单
     * @return 操作结果
     */
    @PostMapping
    @Operation(summary = "创建预算")
    public ActionResult<String> create(@RequestBody @Valid FinBudgetForm finBudgetForm) {
        finBudgetService.create(finBudgetForm);
        return ActionResult.success("预算创建成功");
    }

    /**
     * 更新预算
     *
     * @param id 预算ID
     * @param finBudgetForm 预算表单
     * @return 操作结果
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新预算")
    public ActionResult<String> update(@PathVariable("id") String id, @RequestBody @Valid FinBudgetForm finBudgetForm) {
        finBudgetService.update(id, finBudgetForm);
        return ActionResult.success("预算更新成功");
    }

    /**
     * 删除预算
     *
     * @param id 预算ID
     * @return 操作结果
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除预算")
    public ActionResult<String> delete(@PathVariable("id") String id) {
        finBudgetService.delete(id);
        return ActionResult.success("预算删除成功");
    }

    /**
     * 归档预算
     *
     * @param id 预算ID
     * @return 操作结果
     */
    @PutMapping("/{id}/archive")
    @Operation(summary = "归档预算")
    public ActionResult<String> archive(@PathVariable("id") String id) {
        finBudgetService.archive(id);
        return ActionResult.success("预算归档成功");
    }

    /**
     * 激活预算
     *
     * @param id 预算ID
     * @return 操作结果
     */
    @PutMapping("/{id}/activate")
    @Operation(summary = "激活预算")
    public ActionResult<String> activate(@PathVariable("id") String id) {
        finBudgetService.activate(id);
        return ActionResult.success("预算激活成功");
    }

    /**
     * 重新计算预算支出
     *
     * @param id 预算ID
     * @return 操作结果
     */
    @PutMapping("/{id}/recalculate")
    @Operation(summary = "重新计算预算支出")
    public ActionResult<String> recalculateSpent(@PathVariable("id") String id) {
        finBudgetService.recalculateBudgetSpent(id);
        return ActionResult.success("预算支出重新计算完成");
    }

    /**
     * 复制预算
     *
     * @param id 源预算ID
     * @param newName 新预算名称
     * @return 操作结果
     */
    @PostMapping("/{id}/copy")
    @Operation(summary = "复制预算")
    public ActionResult<String> copyBudget(@PathVariable("id") String id, @RequestParam("newName") String newName) {
        String newBudgetId = finBudgetService.copyBudget(id, newName);
        return ActionResult.success("预算复制成功，新预算ID：" + newBudgetId);
    }

    /**
     * 获取当前用户的激活预算列表
     *
     * @return 激活预算列表
     */
    @GetMapping("/active")
    @Operation(summary = "获取激活预算列表")
    public ActionResult<List<FinBudgetVO>> getActiveBudgets() {
        // 这里需要获取当前用户ID，暂时留空
        String userId = ""; // TODO: 从UserProvider获取
        List<FinBudgetEntity> list = finBudgetService.getActiveBudgetsByUserId(userId);
        List<FinBudgetVO> listVOs = BeanCopierUtils.copyList(list, FinBudgetVO.class);
        
        for (FinBudgetVO vo : listVOs) {
            enrichBudgetVO(vo);
        }
        
        return ActionResult.success(listVOs);
    }

    /**
     * 补充预算VO的显示信息
     *
     * @param vo 预算VO
     */
    private void enrichBudgetVO(FinBudgetVO vo) {
        // 周期类型名称
        if ("MONTHLY".equals(vo.getPeriodType())) {
            vo.setPeriodTypeName("月度");
        } else if ("YEARLY".equals(vo.getPeriodType())) {
            vo.setPeriodTypeName("年度");
        }
        
        // 状态名称
        if ("ACTIVE".equals(vo.getStatus())) {
            vo.setStatusName("激活");
        } else if ("ARCHIVED".equals(vo.getStatus())) {
            vo.setStatusName("归档");
        }
        
        // TODO: 计算已支出金额、剩余金额、支出百分比等
        // 这些需要从预算子项中汇总计算
    }
}