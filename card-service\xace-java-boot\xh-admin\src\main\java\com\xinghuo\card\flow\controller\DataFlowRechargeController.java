package com.xinghuo.card.flow.controller;

import com.xinghuo.admin.util.GeneraterSwapUtil;
import com.xinghuo.card.flow.entity.DataFlowEntity;
import com.xinghuo.card.flow.entity.DataFlowRechargeEntity;
import com.xinghuo.card.flow.model.dataflowrecharge.DataFlowRechargeForm;
import com.xinghuo.card.flow.model.dataflowrecharge.DataFlowRechargePagination;
import com.xinghuo.card.flow.model.dataflowrecharge.DataFlowRechargeVO;
import com.xinghuo.card.flow.service.DataFlowRechargeService;
import com.xinghuo.card.flow.service.DataFlowService;
import com.xinghuo.card.sys.entity.DataAccEntity;
import com.xinghuo.card.sys.service.DataAccService;
import com.xinghuo.common.base.ActionResult;
import com.xinghuo.common.base.UserInfo;
import com.xinghuo.common.base.vo.PageListVO;
import com.xinghuo.common.base.vo.PaginationVO;
import com.xinghuo.common.exception.DataException;
import com.xinghuo.common.util.UserProvider;
import com.xinghuo.common.util.core.BeanCopierUtils;
import com.xinghuo.common.util.core.DateXhUtil;
import com.xinghuo.common.util.core.RandomUtil;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.*;
import java.util.Date;

/**
 * 充值记录
 *
 * <AUTHOR>
 * @version V1.0.0
 * date 2022-11-27
 */
@Slf4j
@RestController
@RequestMapping("/api/card/flow/recharge")
public class DataFlowRechargeController {

    @Autowired
    private GeneraterSwapUtil generaterSwapUtil;

    @Autowired
    private UserProvider userProvider;

    @Autowired
    private DataFlowRechargeService dataFlowRechargeService;

    @Autowired
    private DataFlowService dataFlowService;

    @Autowired
    private DataAccService dataAccService;
    /**
     * 获取充值记录列表
     *
     * @param dataFlowRechargePagination 分页查询参数
     * @return 充值记录列表
     */
    @Operation(summary = "获取充值记录列表")
    @PostMapping("/getList")
    public ActionResult list(@RequestBody DataFlowRechargePagination dataFlowRechargePagination) throws IOException {
        List<DataFlowRechargeEntity> list = dataFlowRechargeService.getList(dataFlowRechargePagination);
        List<DataFlowRechargeVO> listVO = BeanCopierUtils.copyList(list, DataFlowRechargeVO.class);

        // 批量获取账户信息，避免重复查询
        Set<String> accIds = new HashSet<>();
        for (DataFlowRechargeVO vo : listVO) {
            if (StringUtils.isNotEmpty(vo.getInAccId())) {
                accIds.add(vo.getInAccId());
            }
            if (StringUtils.isNotEmpty(vo.getOutAccId())) {
                accIds.add(vo.getOutAccId());
            }
        }

        // 批量查询账户信息
        Map<String, String> accNameMap = new HashMap<>();
        for (String accId : accIds) {
            DataAccEntity acc = dataAccService.getInfo(accId);
            if (acc != null) {
                accNameMap.put(accId, acc.getName());
            }
        }

        // 处理数据转换和补充
        for (DataFlowRechargeVO vo : listVO) {
            // 充值类型转换
            vo.setChargeType(generaterSwapUtil.getDicName(vo.getChargeType(), "1063010976836608"));

            // 账户名称转换
            if (StringUtils.isNotEmpty(vo.getInAccId())) {
                vo.setInAccId(accNameMap.getOrDefault(vo.getInAccId(), vo.getInAccId()));
            }
            if (StringUtils.isNotEmpty(vo.getOutAccId())) {
                vo.setOutAccId(accNameMap.getOrDefault(vo.getOutAccId(), vo.getOutAccId()));
            }

            // 用户名称转换
            vo.setCreateBy(generaterSwapUtil.userSelectValue(vo.getCreateBy()));
            vo.setUpdateBy(generaterSwapUtil.userSelectValue(vo.getUpdateBy()));
        }

        PaginationVO page = BeanCopierUtils.copy(dataFlowRechargePagination, PaginationVO.class);
        return ActionResult.page(listVO, page);
    }

    /**
     * 创建充值记录
     *
     * @param dataFlowRechargeForm 充值记录表单数据
     * @return 操作结果
     */
    @Operation(summary = "创建充值记录")
    @PostMapping
    @Transactional
    public ActionResult create(@RequestBody @Valid DataFlowRechargeForm dataFlowRechargeForm) throws DataException {
        String mainId = RandomUtil.snowId();
        UserInfo userInfo = userProvider.get();

        // 转换Form为Entity
        DataFlowRechargeEntity entity = BeanCopierUtils.copy(dataFlowRechargeForm, DataFlowRechargeEntity.class);
        entity.setId(mainId);
        entity.setCreateBy(userInfo.getUserId());
        entity.setCreateTime(DateXhUtil.date());

        // 处理日期转换
        if (dataFlowRechargeForm.getChargeDate() != null) {
            entity.setChargeDate(new Date(dataFlowRechargeForm.getChargeDate()));
        }

        dataFlowRechargeService.save(entity);
        syncDataFlow(entity);
        return ActionResult.success("创建成功");
    }

    /**
     * 信息
     *
     * @param id
     * @return
     */
    @Operation(summary = "查询单条")
    @GetMapping("/{id}")
    public ActionResult<DataFlowRechargeVO> info(@PathVariable("id") String id) {
        DataFlowRechargeEntity entity = dataFlowRechargeService.getInfo(id);
        DataFlowRechargeVO vo = BeanCopierUtils.copy(entity, DataFlowRechargeVO.class);
        vo.setCreateBy(generaterSwapUtil.userSelectValue(vo.getCreateBy()));
        vo.setUpdateBy(generaterSwapUtil.userSelectValue(vo.getUpdateBy()));
        return ActionResult.success(vo);
    }

    /**
     * 表单信息(详情页)
     *
     * @param id
     * @return
     */
    @Operation(summary = "表单信息(详情页)")
    @GetMapping("/detail/{id}")
    public ActionResult<DataFlowRechargeVO> detailInfo(@PathVariable("id") String id) {
        DataFlowRechargeEntity entity = dataFlowRechargeService.getInfo(id);
        DataFlowRechargeVO vo = BeanCopierUtils.copy(entity, DataFlowRechargeVO.class);
        //添加到详情表单对象中
        vo.setChargeType(generaterSwapUtil.getDicName(vo.getChargeType(), "1063010976836608"));
//        vo.setOutAccId(generaterSwapUtil.getDynName("1056694166801408" ,"fullName" ,"id","" ,vo.getOutAccId()));
//        vo.setInAccId(generaterSwapUtil.getDynName("1056694166801408" ,"fullName" ,"id","" ,vo.getInAccId()));
        vo.setCreateBy(generaterSwapUtil.userSelectValue(vo.getCreateBy()));
        vo.setUpdateBy(generaterSwapUtil.userSelectValue(vo.getUpdateBy()));
        return ActionResult.success(vo);
    }

    /**
     * 更新充值记录
     *
     * @param id 充值记录ID
     * @param dataFlowRechargeForm 充值记录表单数据
     * @return 操作结果
     */
    @Operation(summary = "更新充值记录")
    @PutMapping("/{id}")
    @Transactional
    public ActionResult update(@PathVariable("id") String id, @RequestBody @Valid DataFlowRechargeForm dataFlowRechargeForm) throws DataException {
        UserInfo userInfo = userProvider.get();
        DataFlowRechargeEntity entity = dataFlowRechargeService.getInfo(id);
        if (entity == null) {
            return ActionResult.fail("更新失败，数据不存在");
        }

        // 转换Form为Entity
        DataFlowRechargeEntity updateEntity = BeanCopierUtils.copy(dataFlowRechargeForm, DataFlowRechargeEntity.class);
        updateEntity.setId(id);
        updateEntity.setUpdateBy(userInfo.getUserId());
        updateEntity.setUpdateTime(DateXhUtil.date());

        // 保留原有的创建信息
        updateEntity.setCreateBy(entity.getCreateBy());
        updateEntity.setCreateTime(entity.getCreateTime());

        // 处理日期转换
        if (dataFlowRechargeForm.getChargeDate() != null) {
            updateEntity.setChargeDate(new Date(dataFlowRechargeForm.getChargeDate()));
        }

        dataFlowRechargeService.update(id, updateEntity);
        syncDataFlow(updateEntity);
        return ActionResult.success("更新成功");
    }

    /**
     * 删除充值记录
     *
     * @param id 充值记录ID
     * @return 操作结果
     */
    @Operation(summary = "删除充值记录")
    @DeleteMapping("/{id}")
    @Transactional
    public ActionResult delete(@PathVariable("id") String id) {
        DataFlowRechargeEntity entity = dataFlowRechargeService.getInfo(id);
        if (entity != null) {
            dataFlowRechargeService.delete(entity);
            dataFlowService.deleteDataFlowByRelatedIds(id);
        }
        return ActionResult.success("删除成功");
    }

    /**
     * 同步充值记录到流水表
     * 1、删除关联的历史流水数据
     * 2、根据充值金额创建转账流水记录
     * 3、根据赠送金额创建收入流水记录
     *
     * @param dataFlowRecharge 充值记录实体
     */
    private void syncDataFlow(DataFlowRechargeEntity dataFlowRecharge) {
        // 第一步：删除关联的历史流水数据
        String relatedId = dataFlowRecharge.getId();
        dataFlowService.deleteDataFlowByRelatedIds(relatedId);

        // 根据充值类型确定备注前缀
        String chargeType = dataFlowRecharge.getChargeType();
        String notePrefix = getChargeTypeNote(chargeType);

        // 第二步：创建充值转账流水记录
        createChargeFlow(dataFlowRecharge, notePrefix);

        // 第三步：创建赠送收入流水记录（如果有赠送金额）
        createGiftFlow(dataFlowRecharge, notePrefix);
    }

    /**
     * 根据充值类型获取备注前缀
     *
     * @param chargeType 充值类型
     * @return 备注前缀
     */
    private String getChargeTypeNote(String chargeType) {
        switch (chargeType) {
            case "JD":
                return "【京东卡充值】";
            case "GB":
                return "【钢镚充值】";
            case "TB":
                return "【铜板充值】";
            default:
                return "【苏宁卡充值】";
        }
    }


    /**
     * 创建充值转账流水记录
     *
     * @param dataFlowRecharge 充值记录
     * @param notePrefix 备注前缀
     */
    private void createChargeFlow(DataFlowRechargeEntity dataFlowRecharge, String notePrefix) {
        DataFlowEntity chargeFlow = new DataFlowEntity();
        chargeFlow.setRelatedId(dataFlowRecharge.getId());
        chargeFlow.setFlowDate(dataFlowRecharge.getChargeDate());
        chargeFlow.setInAccId(dataFlowRecharge.getInAccId());
        chargeFlow.setOutAccId(dataFlowRecharge.getOutAccId());
        chargeFlow.setAmout(dataFlowRecharge.getAmount());
        chargeFlow.setType("3"); // 转账类型
        chargeFlow.setNote(notePrefix + StringUtils.stripToEmpty(dataFlowRecharge.getNote()));
        dataFlowService.create(chargeFlow);
    }

    /**
     * 创建赠送收入流水记录
     *
     * @param dataFlowRecharge 充值记录
     * @param notePrefix 备注前缀
     */
    private void createGiftFlow(DataFlowRechargeEntity dataFlowRecharge, String notePrefix) {
        if (dataFlowRecharge.getAddAmount() != null && dataFlowRecharge.getAddAmount().doubleValue() > 0) {
            DataFlowEntity giftFlow = new DataFlowEntity();
            giftFlow.setRelatedId(dataFlowRecharge.getId());
            giftFlow.setFlowDate(dataFlowRecharge.getChargeDate());
            giftFlow.setAccId(dataFlowRecharge.getInAccId());
            giftFlow.setAmout(dataFlowRecharge.getAddAmount());
            giftFlow.setType("CZSR"); // 充值收入类型
            giftFlow.setNote(notePrefix + "[赠送]" + StringUtils.stripToEmpty(dataFlowRecharge.getNote()));
            dataFlowService.create(giftFlow);
        }
    }

}
