export enum DialogEnum {
  // 子页面全屏弹窗
  'FULLSCREEN' = 3,
  // 抽屉弹窗
  'DRAWER' = 2,
  // 居中弹窗
  'MODAL' = 1,
}

export interface CustomDialogParams {
  id: string;
  // 在线表单id
  modelId?: string;
  // 类型
  type?: DialogEnum | DialogEnum.DRAWER | DialogEnum.FULLSCREEN | DialogEnum.MODAL;
  // 弹窗标题
  title?: string;
  // 父页面给弹窗页面传递的参数
  params?: Recordable;
  // 弹窗宽度
  width?: string;
  // 弹窗样式
  style?: Recordable;
  // 打开系统表单文件路径（仅限views目录下文件）
  formUrl?: string;
}
