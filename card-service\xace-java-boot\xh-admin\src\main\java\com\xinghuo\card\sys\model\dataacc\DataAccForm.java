package com.xinghuo.card.sys.model.dataacc;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 个人账号
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2022-12-02
 */
@Data
public class DataAccForm {

    @Schema(description = "主键")
    private String id;


    @Schema(description = "名称")
    @JsonProperty("name")
    private String name;


    @Schema(description = "ID")
    @JsonProperty("manId")
    private String manId;


    @Schema(description = "父ID")
    @JsonProperty("parentId")
    private String parentId;


}
