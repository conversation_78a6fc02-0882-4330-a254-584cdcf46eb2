import { defHttp } from '/@/utils/http/axios';

enum Api {
  Prefix = '/api/card/config',
}

// 获取信用卡配置列表
export function getConfigList() {
  return defHttp.get({ url: Api.Prefix });
}

// 根据账户ID获取配置
export function getConfigByAccId(accId: string) {
  return defHttp.get({ url: Api.Prefix + `/acc/${accId}` });
}

// 获取配置详情
export function getConfigDetail(configId: string) {
  return defHttp.get({ url: Api.Prefix + `/${configId}` });
}

// 创建信用卡配置
export function createConfig(data: any) {
  return defHttp.post({ url: Api.Prefix, data });
}

// 更新信用卡配置
export function updateConfig(configId: string, data: any) {
  return defHttp.put({ url: Api.Prefix + `/${configId}`, data });
}

// 删除信用卡配置
export function deleteConfig(configId: string) {
  return defHttp.delete({ url: Api.Prefix + `/${configId}` });
}

// 切换自动生成账单
export function toggleAutoGenerate(configId: string, enable: boolean) {
  return defHttp.post({ url: Api.Prefix + `/${configId}/toggle-auto-generate`, params: { enable } });
}

// 验证配置信息
export function validateConfig(data: any) {
  return defHttp.post({ url: Api.Prefix + '/validate', data });
}
