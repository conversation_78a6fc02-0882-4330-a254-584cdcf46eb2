package com.xinghuo.card.sys.service.impl;

import com.alicp.jetcache.anno.CacheInvalidate;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.CacheUpdate;
import com.alicp.jetcache.anno.Cached;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xinghuo.card.sys.dao.SysUserBankMapper;
import com.xinghuo.card.sys.entity.SysUserBankEntity;
import com.xinghuo.card.sys.model.sysuserbank.SysUserBankPagination;
import com.xinghuo.card.sys.service.SysUserBankService;
import com.xinghuo.common.util.UserProvider;
import com.xinghuo.common.util.core.StrXhUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import jakarta.annotation.Resource;
import java.util.*;

/**
 * 用户银行信息服务实现类
 * 用于管理用户与银行的关联关系，包括地址信息、额度汇总、积分等
 * 支持合并账户管理和分别还款设置
 *
 * <AUTHOR> Assistant
 * @version V1.0.0
 * @date 2024-12-29
 */
@Slf4j
@Service
public class SysUserBankServiceImpl extends BaseServiceImpl<SysUserBankMapper, SysUserBankEntity> implements SysUserBankService {

    @Resource
    private UserProvider userProvider;

    @Override
    public List<SysUserBankEntity> getList(SysUserBankPagination sysUserBankPagination) {
        QueryWrapper<SysUserBankEntity> queryWrapper = new QueryWrapper<>();
        
        // 构建查询条件
        if (StrXhUtil.isNotEmpty(sysUserBankPagination.getManId())) {
            queryWrapper.lambda().eq(SysUserBankEntity::getManId, sysUserBankPagination.getManId());
        }
        
        if (StrXhUtil.isNotEmpty(sysUserBankPagination.getBankId())) {
            queryWrapper.lambda().eq(SysUserBankEntity::getBankId, sysUserBankPagination.getBankId());
        }
        
        if (StrXhUtil.isNotEmpty(sysUserBankPagination.getBankName())) {
            queryWrapper.lambda().like(SysUserBankEntity::getBankName, sysUserBankPagination.getBankName());
        }
        
        if (StrXhUtil.isNotEmpty(sysUserBankPagination.getKeyword())) {
            queryWrapper.lambda().and(wrapper -> 
                wrapper.like(SysUserBankEntity::getBankName, sysUserBankPagination.getKeyword())
            );
        }
        
        if (sysUserBankPagination.getMinLimitMoney() != null) {
            queryWrapper.lambda().ge(SysUserBankEntity::getLimitMoney, 
                sysUserBankPagination.getMinLimitMoney().multiply(new java.math.BigDecimal("100")).intValue());
        }
        
        if (sysUserBankPagination.getMaxLimitMoney() != null) {
            queryWrapper.lambda().le(SysUserBankEntity::getLimitMoney, 
                sysUserBankPagination.getMaxLimitMoney().multiply(new java.math.BigDecimal("100")).intValue());
        }
        
        if (sysUserBankPagination.getOnlyWithLimit() != null && sysUserBankPagination.getOnlyWithLimit()) {
            queryWrapper.lambda().gt(SysUserBankEntity::getLimitMoney, 0);
        }
        
        // 排序
        if (StrXhUtil.isNotEmpty(sysUserBankPagination.getSortField())) {
            String sortField = sysUserBankPagination.getSortField();
            boolean isAsc = "ASC".equalsIgnoreCase(sysUserBankPagination.getSortOrder());
            
            switch (sortField) {
                case "bankName":
                    if (isAsc) {
                        queryWrapper.lambda().orderByAsc(SysUserBankEntity::getBankName);
                    } else {
                        queryWrapper.lambda().orderByDesc(SysUserBankEntity::getBankName);
                    }
                    break;
                case "limitMoney":
                    if (isAsc) {
                        queryWrapper.lambda().orderByAsc(SysUserBankEntity::getLimitMoney);
                    } else {
                        queryWrapper.lambda().orderByDesc(SysUserBankEntity::getLimitMoney);
                    }
                    break;
                case "createTime":
                    if (isAsc) {
                        queryWrapper.lambda().orderByAsc(SysUserBankEntity::getCreatedAt);
                    } else {
                        queryWrapper.lambda().orderByDesc(SysUserBankEntity::getCreatedAt);
                    }
                    break;
                default:
                    queryWrapper.lambda().orderByDesc(SysUserBankEntity::getCreatedAt);
                    break;
            }
        } else {
            queryWrapper.lambda().orderByDesc(SysUserBankEntity::getCreatedAt);
        }
        
        return this.list(queryWrapper);
    }

    @Override
    public List<SysUserBankEntity> getTypeList(SysUserBankPagination sysUserBankPagination, String dataType) {
        // 对于分页和不分页，这里都返回相同的结果，具体的分页逻辑在Controller层处理
        return getList(sysUserBankPagination);
    }

    @Override
    @Cached(name = CACHE_NAME, key = "man:#manId", cacheType = CacheType.LOCAL, expire = 120)
    public List<SysUserBankEntity> getListByManId(String manId) {
        Assert.hasText(manId, "持卡人ID不能为空");
        
        QueryWrapper<SysUserBankEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(SysUserBankEntity::getManId, manId)
                .orderByDesc(SysUserBankEntity::getCreatedAt);
        
        return this.list(queryWrapper);
    }

    @Override
    @Cached(name = CACHE_NAME, key = "#id", cacheType = CacheType.LOCAL, expire = 360)
    public SysUserBankEntity getInfo(String id) {
        Assert.hasText(id, "用户银行关系ID不能为空");
        return this.getById(id);
    }

    @Override
    @Cached(name = CACHE_NAME, key = "relation:#manId:#bankId", cacheType = CacheType.LOCAL, expire = 360)
    public SysUserBankEntity getInfoByManAndBank(String manId, String bankId) {
        Assert.hasText(manId, "持卡人ID不能为空");
        Assert.hasText(bankId, "银行ID不能为空");
        
        QueryWrapper<SysUserBankEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(SysUserBankEntity::getManId, manId)
                .eq(SysUserBankEntity::getBankId, bankId);
        
        return this.getOne(queryWrapper);
    }

    @Override
    @CacheInvalidate(name = CACHE_NAME, key = "#entity.id")
    @Transactional(rollbackFor = Exception.class)
    public void delete(SysUserBankEntity entity) {
        Assert.notNull(entity, "删除对象不能为空");
        Assert.hasText(entity.getId(), "用户银行关系ID不能为空");
        
        boolean result = this.removeById(entity.getId());
        if (result) {
            log.info("删除用户银行关系成功，ID: {}", entity.getId());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(SysUserBankEntity entity) {
        Assert.notNull(entity, "创建对象不能为空");
        Assert.hasText(entity.getManId(), "持卡人ID不能为空");
        Assert.hasText(entity.getBankId(), "银行ID不能为空");
        
        // 检查是否已存在相同的关系
        if (checkRelationExists(entity.getManId(), entity.getBankId(), null)) {
            throw new RuntimeException("该持卡人与银行的关系已存在");
        }
        
        // 设置默认值
        if (entity.getLimitMoney() == null) {
            entity.setLimitMoney(0);
        }
        if (entity.getPoint() == null) {
            entity.setPoint(0.0);
        }
        if (entity.getCardType() == null) {
            entity.setCardType(SysUserBankEntity.CardType.SEPARATE_ACCOUNT_SEPARATE_REPAY);
        }
        
        boolean result = this.save(entity);
        if (result) {
            log.info("创建用户银行关系成功，持卡人ID: {}, 银行ID: {}", entity.getManId(), entity.getBankId());
        }
    }

    @Override
    @CacheUpdate(name = CACHE_NAME, key = "#id", value = "#entity")
    @Transactional(rollbackFor = Exception.class)
    public boolean update(String id, SysUserBankEntity entity) {
        Assert.hasText(id, "用户银行关系ID不能为空");
        Assert.notNull(entity, "更新对象不能为空");
        
        entity.setId(id);
        
        // 如果修改了持卡人或银行，检查是否已存在相同的关系
        if (StrXhUtil.isNotEmpty(entity.getManId()) && StrXhUtil.isNotEmpty(entity.getBankId())) {
            if (checkRelationExists(entity.getManId(), entity.getBankId(), id)) {
                throw new RuntimeException("该持卡人与银行的关系已存在");
            }
        }
        
        boolean result = this.updateById(entity);
        if (result) {
            log.info("更新用户银行关系成功，ID: {}", id);
        }
        
        return result;
    }

    @Override
    public boolean checkRelationExists(String manId, String bankId, String id) {
        Assert.hasText(manId, "持卡人ID不能为空");
        Assert.hasText(bankId, "银行ID不能为空");
        
        QueryWrapper<SysUserBankEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(SysUserBankEntity::getManId, manId)
                .eq(SysUserBankEntity::getBankId, bankId);
        
        if (StrXhUtil.isNotEmpty(id)) {
            queryWrapper.lambda().ne(SysUserBankEntity::getId, id);
        }
        
        return this.count(queryWrapper) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchDelete(List<String> ids) {
        Assert.notEmpty(ids, "删除ID列表不能为空");
        
        boolean result = this.removeByIds(ids);
        int count = result ? ids.size() : 0;
        
        if (result) {
            log.info("批量删除用户银行关系成功，删除数量: {}", count);
        }
        
        return count;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateLimitMoney(String id) {
        Assert.hasText(id, "用户银行关系ID不能为空");
        
        // TODO: 实现根据关联信用卡自动计算总额度的逻辑
        log.info("更新用户银行关系总额度，ID: {}", id);
        
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updatePoint(String id, Double point) {
        Assert.hasText(id, "用户银行关系ID不能为空");
        Assert.notNull(point, "积分数不能为空");
        
        UpdateWrapper<SysUserBankEntity> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda()
                .eq(SysUserBankEntity::getId, id)
                .set(SysUserBankEntity::getPoint, point);
        
        boolean result = this.update(updateWrapper);
        if (result) {
            log.info("更新用户银行关系积分成功，ID: {}, 积分: {}", id, point);
        }
        
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchUpdateLimitMoneyByManId(String manId) {
        Assert.hasText(manId, "持卡人ID不能为空");
        
        List<SysUserBankEntity> userBanks = getListByManId(manId);
        int count = 0;
        
        for (SysUserBankEntity userBank : userBanks) {
            if (updateLimitMoney(userBank.getId())) {
                count++;
            }
        }
        
        log.info("批量更新持卡人银行关系总额度，持卡人ID: {}, 更新数量: {}", manId, count);
        
        return count;
    }

    @Override
    @Cached(name = CACHE_NAME, key = "statistics:#manId", cacheType = CacheType.LOCAL, expire = 300)
    public Object getUserBankStatistics(String manId) {
        Assert.hasText(manId, "持卡人ID不能为空");
        
        List<SysUserBankEntity> userBanks = getListByManId(manId);
        
        Map<String, Object> statistics = new HashMap<>();
        statistics.put("totalBanks", userBanks.size());
        statistics.put("totalLimitMoney", userBanks.stream()
                .mapToInt(ub -> ub.getLimitMoney() != null ? ub.getLimitMoney() : 0)
                .sum());
        statistics.put("totalPoints", userBanks.stream()
                .mapToDouble(ub -> ub.getPoint() != null ? ub.getPoint() : 0.0)
                .sum());
        
        return statistics;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int syncBankName(String bankId, String bankName) {
        Assert.hasText(bankId, "银行ID不能为空");
        Assert.hasText(bankName, "银行名称不能为空");

        UpdateWrapper<SysUserBankEntity> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda()
                .eq(SysUserBankEntity::getBankId, bankId)
                .set(SysUserBankEntity::getBankName, bankName);

        boolean result = this.update(updateWrapper);

        // 获取更新的数量
        QueryWrapper<SysUserBankEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(SysUserBankEntity::getBankId, bankId);
        int count = (int) this.count(queryWrapper);

        if (result) {
            log.info("同步银行名称成功，银行ID: {}, 银行名称: {}, 更新数量: {}", bankId, bankName, count);
        }

        return count;
    }

    @Override
    @Cached(name = CACHE_NAME, key = "statistics", cacheType = CacheType.LOCAL, expire = 300)
    public Map<String, Object> getUserBankStatistics() {
        Map<String, Object> statistics = new HashMap<>();

        // 总数量
        long totalCount = this.count();
        statistics.put("totalCount", totalCount);

        // TODO: 实现更详细的统计逻辑
        statistics.put("totalLimitMoney", 0);
        statistics.put("totalPoints", 0.0);
        statistics.put("activeBanks", 0);

        return statistics;
    }

    @Override
    @Cached(name = CACHE_NAME, key = "bankDistribution", cacheType = CacheType.LOCAL, expire = 300)
    public List<Map<String, Object>> getBankDistribution() {
        // TODO: 实现银行分布统计
        List<Map<String, Object>> distribution = new ArrayList<>();

        return distribution;
    }

    @Override
    @Cached(name = CACHE_NAME, key = "manDistribution", cacheType = CacheType.LOCAL, expire = 300)
    public List<Map<String, Object>> getManDistribution() {
        // TODO: 实现持卡人分布统计
        List<Map<String, Object>> distribution = new ArrayList<>();

        return distribution;
    }

    @Override
    public Map<String, Object> validateCanDelete(String id) {
        Assert.hasText(id, "用户银行关系ID不能为空");

        Map<String, Object> result = new HashMap<>();
        result.put("canDelete", true);
        result.put("message", "可以删除");

        // TODO: 实现检查是否有关联的信用卡账户的逻辑

        return result;
    }

    @Override
    public Map<String, Object> batchValidateCanDelete(List<String> ids) {
        Assert.notEmpty(ids, "用户银行关系ID列表不能为空");

        Map<String, Object> result = new HashMap<>();
        List<String> canDeleteIds = new ArrayList<>();
        List<String> cannotDeleteIds = new ArrayList<>();

        for (String id : ids) {
            Map<String, Object> validateResult = validateCanDelete(id);
            if ((Boolean) validateResult.get("canDelete")) {
                canDeleteIds.add(id);
            } else {
                cannotDeleteIds.add(id);
            }
        }

        result.put("canDeleteIds", canDeleteIds);
        result.put("cannotDeleteIds", cannotDeleteIds);
        result.put("canDeleteCount", canDeleteIds.size());
        result.put("cannotDeleteCount", cannotDeleteIds.size());

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> importUserBankData(List<SysUserBankEntity> userBankList) {
        Assert.notEmpty(userBankList, "导入数据列表不能为空");

        Map<String, Object> result = new HashMap<>();
        int successCount = 0;
        int failedCount = 0;
        List<String> errorMessages = new ArrayList<>();

        for (SysUserBankEntity userBank : userBankList) {
            try {
                create(userBank);
                successCount++;
            } catch (Exception e) {
                failedCount++;
                errorMessages.add("导入失败: " + e.getMessage());
                log.error("导入用户银行关系失败", e);
            }
        }

        result.put("successCount", successCount);
        result.put("failedCount", failedCount);
        result.put("errorMessages", errorMessages);

        log.info("导入用户银行关系数据完成，成功: {}, 失败: {}", successCount, failedCount);

        return result;
    }

    @Override
    public List<Map<String, Object>> exportUserBankData(SysUserBankPagination sysUserBankPagination) {
        List<SysUserBankEntity> userBanks = getList(sysUserBankPagination);
        List<Map<String, Object>> exportData = new ArrayList<>();

        for (SysUserBankEntity userBank : userBanks) {
            Map<String, Object> data = new HashMap<>();
            data.put("id", userBank.getId());
            data.put("manId", userBank.getManId());
            data.put("bankId", userBank.getBankId());
            data.put("bankName", userBank.getBankName());
            data.put("deptAddress", userBank.getDeptAddress());
            data.put("homeAddress", userBank.getHomeAddress());
            data.put("addrType", userBank.getAddrType());
            data.put("limitMoney", userBank.getLimitMoney());
            data.put("point", userBank.getPoint());
            data.put("note", userBank.getNote());
            data.put("cardType", userBank.getCardType());
            data.put("createdAt", userBank.getCreatedAt());

            exportData.add(data);
        }

        return exportData;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SysUserBankEntity copyUserBank(String id, String newManId, String newBankId) {
        Assert.hasText(id, "源用户银行关系ID不能为空");
        Assert.hasText(newManId, "新持卡人ID不能为空");
        Assert.hasText(newBankId, "新银行ID不能为空");

        SysUserBankEntity sourceUserBank = getInfo(id);
        if (sourceUserBank == null) {
            throw new RuntimeException("源用户银行关系不存在");
        }

        // 检查新的关系是否已存在
        if (checkRelationExists(newManId, newBankId, null)) {
            throw new RuntimeException("目标持卡人与银行的关系已存在");
        }

        SysUserBankEntity newUserBank = new SysUserBankEntity();
        newUserBank.setManId(newManId);
        newUserBank.setBankId(newBankId);
        newUserBank.setDeptAddress(sourceUserBank.getDeptAddress());
        newUserBank.setHomeAddress(sourceUserBank.getHomeAddress());
        newUserBank.setAddrType(sourceUserBank.getAddrType());
        newUserBank.setLimitMoney(0); // 新关系初始额度为0
        newUserBank.setPoint(0.0); // 新关系初始积分为0
        newUserBank.setNote(sourceUserBank.getNote());
        newUserBank.setCardType(sourceUserBank.getCardType());

        create(newUserBank);

        log.info("复制用户银行关系成功，源ID: {}, 新持卡人ID: {}, 新银行ID: {}", id, newManId, newBankId);

        return newUserBank;
    }

    @Override
    public Map<String, Object> getUserBankDetailWithRelated(String id) {
        Assert.hasText(id, "用户银行关系ID不能为空");

        SysUserBankEntity userBank = getInfo(id);
        if (userBank == null) {
            throw new RuntimeException("用户银行关系不存在");
        }

        Map<String, Object> detail = new HashMap<>();
        detail.put("userBank", userBank);

        // TODO: 添加关联信息，如信用卡列表等
        detail.put("creditCards", new ArrayList<>());
        detail.put("relatedStats", new HashMap<>());

        return detail;
    }

    @Override
    @Cached(name = CACHE_NAME, key = "bank:#bankId", cacheType = CacheType.LOCAL, expire = 120)
    public List<SysUserBankEntity> getListByBankId(String bankId) {
        Assert.hasText(bankId, "银行ID不能为空");

        QueryWrapper<SysUserBankEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(SysUserBankEntity::getBankId, bankId)
                .orderByDesc(SysUserBankEntity::getCreatedAt);

        return this.list(queryWrapper);
    }

    @Override
    public Map<String, Object> getLimitSummaryByManId(String manId) {
        Assert.hasText(manId, "持卡人ID不能为空");

        List<SysUserBankEntity> userBanks = getListByManId(manId);

        Map<String, Object> summary = new HashMap<>();
        int totalLimit = userBanks.stream()
                .mapToInt(ub -> ub.getLimitMoney() != null ? ub.getLimitMoney() : 0)
                .sum();

        summary.put("totalLimit", totalLimit);
        summary.put("totalLimitYuan", totalLimit / 100.0); // 转换为元
        summary.put("bankCount", userBanks.size());
        summary.put("avgLimit", userBanks.size() > 0 ? totalLimit / userBanks.size() : 0);

        return summary;
    }

    @Override
    public Map<String, Object> getPointsSummaryByManId(String manId) {
        Assert.hasText(manId, "持卡人ID不能为空");

        List<SysUserBankEntity> userBanks = getListByManId(manId);

        Map<String, Object> summary = new HashMap<>();
        double totalPoints = userBanks.stream()
                .mapToDouble(ub -> ub.getPoint() != null ? ub.getPoint() : 0.0)
                .sum();

        summary.put("totalPoints", totalPoints);
        summary.put("bankCount", userBanks.size());
        summary.put("avgPoints", userBanks.size() > 0 ? totalPoints / userBanks.size() : 0.0);

        return summary;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchSetMergeAccount(List<String> ids, Boolean mergeAccount) {
        Assert.notEmpty(ids, "用户银行关系ID列表不能为空");
        Assert.notNull(mergeAccount, "合并账户设置不能为空");

        // 根据合并账户设置确定卡片类型
        Integer cardType = mergeAccount ?
                SysUserBankEntity.CardType.MERGED_ACCOUNT_SEPARATE_REPAY :
                SysUserBankEntity.CardType.SEPARATE_ACCOUNT_SEPARATE_REPAY;

        UpdateWrapper<SysUserBankEntity> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda()
                .in(SysUserBankEntity::getId, ids)
                .set(SysUserBankEntity::getCardType, cardType);

        boolean result = this.update(updateWrapper);
        int count = result ? ids.size() : 0;

        if (result) {
            log.info("批量设置合并账户成功，设置数量: {}, 合并账户: {}", count, mergeAccount);
        }

        return count;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchSetSeparateRepayment(List<String> ids, Boolean separateRepayment) {
        Assert.notEmpty(ids, "用户银行关系ID列表不能为空");
        Assert.notNull(separateRepayment, "分别还款设置不能为空");

        // 根据分别还款设置确定卡片类型
        Integer cardType = separateRepayment ?
                SysUserBankEntity.CardType.MERGED_ACCOUNT_SEPARATE_REPAY :
                SysUserBankEntity.CardType.MERGED_ACCOUNT_MERGED_REPAY;

        UpdateWrapper<SysUserBankEntity> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda()
                .in(SysUserBankEntity::getId, ids)
                .set(SysUserBankEntity::getCardType, cardType);

        boolean result = this.update(updateWrapper);
        int count = result ? ids.size() : 0;

        if (result) {
            log.info("批量设置分别还款成功，设置数量: {}, 分别还款: {}", count, separateRepayment);
        }

        return count;
    }

    @Override
    public Map<String, Object> getUserBankRelatedStats(String id) {
        Assert.hasText(id, "用户银行关系ID不能为空");

        Map<String, Object> stats = new HashMap<>();

        // TODO: 实现关联统计逻辑，如关联的信用卡数量、交易数量等
        stats.put("creditCardCount", 0);
        stats.put("transactionCount", 0);
        stats.put("lastTransactionDate", null);

        return stats;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int syncManName(String manId, String manName) {
        Assert.hasText(manId, "持卡人ID不能为空");
        Assert.hasText(manName, "持卡人名称不能为空");

        // 注意：SysUserBankEntity中没有manName字段，这里只是记录日志
        // 如果需要同步持卡人名称，需要在实体类中添加相应字段

        QueryWrapper<SysUserBankEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(SysUserBankEntity::getManId, manId);
        int count = (int) this.count(queryWrapper);

        log.info("同步持卡人名称，持卡人ID: {}, 持卡人名称: {}, 关联记录数: {}", manId, manName, count);

        return count;
    }

    @Override
    public List<Map<String, Object>> getDuplicateUserBanks() {
        // TODO: 实现查找重复用户银行关系的逻辑
        List<Map<String, Object>> duplicates = new ArrayList<>();

        return duplicates;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int cleanDuplicateUserBanks() {
        List<Map<String, Object>> duplicates = getDuplicateUserBanks();
        int cleanCount = 0;

        // TODO: 实现清理重复关系的逻辑

        log.info("清理重复用户银行关系完成，清理数量: {}", cleanCount);

        return cleanCount;
    }
}
