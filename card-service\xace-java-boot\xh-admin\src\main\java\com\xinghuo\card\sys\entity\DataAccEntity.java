package com.xinghuo.card.sys.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


/**
 * 个人账号	苏宁，京东 存在子账号表。在电商账号中处理
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2022-12-02
 */
@Data
@TableName("data_acc")
public class DataAccEntity implements Serializable {

    /**m dev
     * ID
     */
    @TableId("ID")
    private String id;


    /**
     * 管理用户ID
     */
    @TableField("USER_ID")
    private String userId;


    /**
     *
     */
    @TableField("TYPE")
    private Integer type;


    /**
     *
     */
    @TableField("PARENT_ID")
    private String parentId;


    /**
     *
     */
    @TableField("NAME")
    private String name;

    /**
     * 常用标记
     */
    @TableField("FAV_MARK")
    private Integer favMark;


    /**
     * 拼音
     */
    @TableField("PY")
    private String py;


    /**
     * 1-表示隐藏  0-表示正常
     */
    @TableField("HIDE_FLAG")
    private Boolean hideFlag;


    /**
     * 建立时间
     */
    @TableField("BUILD_DATE")
    private Date buildDate;


    /**
     *
     */
    @TableField("MAN_ID")
    private String manId;


    /**
     * 当前余额
     */
    @TableField("BALANCE")
    private BigDecimal balance;


    /**
     *
     */
    @TableField("QDATE")
    private Date qdate;


    /**
     *
     */
    @TableField("QBALANCE")
    private BigDecimal qbalance;


    /**
     * 排序
     */
    @TableField("LIST_ORDER")
    private Integer listOrder;


    /**
     * 备注
     */
    @TableField("NOTE")
    private String note;


    /**
     * 创建人
     */
    @TableField("CREATE_BY")
    private String createBy;


    /**
     * 创建时间
     */
    @TableField("CREATE_TIME")
    private Date createTime;


    /**
     * 最后修改人
     */
    @TableField("UPDATE_BY")
    private String updateBy;


    /**
     * 最后修改时间
     */
    @TableField("UPDATE_TIME")
    private Date updateTime;

}
