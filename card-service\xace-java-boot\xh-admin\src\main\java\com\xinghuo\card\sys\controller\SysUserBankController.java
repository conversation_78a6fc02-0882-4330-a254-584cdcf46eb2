package com.xinghuo.card.sys.controller;

import com.xinghuo.admin.util.GeneraterSwapUtil;
import com.xinghuo.card.sys.entity.SysUserBankEntity;
import com.xinghuo.card.sys.model.sysuserbank.DataSysUserBankForm;
import com.xinghuo.card.sys.model.sysuserbank.DataSysUserBankPagination;
import com.xinghuo.card.sys.model.sysuserbank.DataSysUserBankVO;
import com.xinghuo.card.sys.model.sysuserbank.SysUserBankPagination;
import com.xinghuo.card.sys.service.SysUserBankService;
import com.xinghuo.common.base.ActionResult;
import com.xinghuo.common.base.UserInfo;
import com.xinghuo.common.base.vo.ListVO;
import com.xinghuo.common.base.vo.PaginationVO;
import com.xinghuo.common.exception.DataException;
import com.xinghuo.common.util.UserProvider;
import com.xinghuo.common.util.core.BeanCopierUtils;
import com.xinghuo.common.util.core.DateXhUtil;
import com.xinghuo.common.util.core.RandomUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

/**
 * 用户银行信息控制器
 * 用于管理用户与银行的关联关系，包括地址信息、额度汇总、积分等
 * 支持合并账户管理和分别还款设置
 *
 * <AUTHOR> Assistant
 * @version V1.0.0
 * @date 2024-12-29
 */
@Slf4j
@RestController
@Tag(name = "用户银行信息管理", description = "用户银行信息管理")
@RequestMapping("/api/card/sys/userbank")
public class SysUserBankController {

    @Autowired
    private SysUserBankService sysUserBankService;

    @Autowired
    private UserProvider userProvider;

    @Autowired
    private GeneraterSwapUtil generaterSwapUtil;

    /**
     * 获取用户银行关系列表
     *
     * @param dataSysUserBankPagination 分页查询参数
     * @return 用户银行关系列表
     */
    @Operation(summary = "获取用户银行关系列表")
    @PostMapping("/getList")
    public ActionResult list(@RequestBody SysUserBankPagination dataSysUserBankPagination) throws IOException {
        List<SysUserBankEntity> data = sysUserBankService.getList(dataSysUserBankPagination);
        List<DataSysUserBankVO> listVOs = BeanCopierUtils.copyList(data, DataSysUserBankVO.class);
        
        // 处理数据转换和补充
        for (DataSysUserBankVO vo : listVOs) {
            enrichUserBankVO(vo);
        }
        
        PaginationVO page = BeanCopierUtils.copy(dataSysUserBankPagination, PaginationVO.class);
        return ActionResult.page(listVOs, page);
    }

    /**
     * 根据持卡人ID获取银行关系列表
     *
     * @param manId 持卡人ID
     * @return 银行关系列表
     */
    @Operation(summary = "根据持卡人ID获取银行关系列表")
    @GetMapping("/man/{manId}")
    public ActionResult<ListVO<DataSysUserBankVO>> getListByManId(@PathVariable("manId") String manId) {
        List<SysUserBankEntity> data = sysUserBankService.getListByManId(manId);
        List<DataSysUserBankVO> listVOs = BeanCopierUtils.copyList(data, DataSysUserBankVO.class);
        
        // 处理数据转换
        for (DataSysUserBankVO vo : listVOs) {
            enrichUserBankVO(vo);
        }
        
        ListVO<DataSysUserBankVO> vo = new ListVO<>();
        vo.setList(listVOs);
        return ActionResult.success(vo);
    }

    /**
     * 根据银行ID获取用户关系列表
     *
     * @param bankId 银行ID
     * @return 用户关系列表
     */
    @Operation(summary = "根据银行ID获取用户关系列表")
    @GetMapping("/bank/{bankId}")
    public ActionResult<ListVO<DataSysUserBankVO>> getListByBankId(@PathVariable("bankId") String bankId) {
        List<SysUserBankEntity> data = sysUserBankService.getListByBankId(bankId);
        List<DataSysUserBankVO> listVOs = BeanCopierUtils.copyList(data, DataSysUserBankVO.class);
        
        // 处理数据转换
        for (DataSysUserBankVO vo : listVOs) {
            enrichUserBankVO(vo);
        }
        
        ListVO<DataSysUserBankVO> vo = new ListVO<>();
        vo.setList(listVOs);
        return ActionResult.success(vo);
    }

    /**
     * 获取用户银行关系详情
     *
     * @param id 用户银行关系ID
     * @return 用户银行关系详情
     */
    @GetMapping("/{id}")
    @Operation(summary = "获取用户银行关系详情")
    public ActionResult<DataSysUserBankVO> info(@PathVariable("id") String id) {
        SysUserBankEntity entity = sysUserBankService.getInfo(id);
        if (entity == null) {
            return ActionResult.fail("用户银行关系不存在");
        }
        
        DataSysUserBankVO vo = BeanCopierUtils.copy(entity, DataSysUserBankVO.class);
        enrichUserBankVO(vo);
        
        return ActionResult.success(vo);
    }

    /**
     * 表单信息(详情页)
     *
     * @param id 用户银行关系ID
     * @return 用户银行关系详情
     */
    @Operation(summary = "表单信息(详情页)")
    @GetMapping("/detail/{id}")
    public ActionResult<DataSysUserBankVO> detailInfo(@PathVariable("id") String id) {
        SysUserBankEntity entity = sysUserBankService.getInfo(id);
        if (entity == null) {
            return ActionResult.fail("用户银行关系不存在");
        }
        
        DataSysUserBankVO vo = BeanCopierUtils.copy(entity, DataSysUserBankVO.class);
        enrichUserBankVO(vo);
        
        return ActionResult.success(vo);
    }

    /**
     * 创建用户银行关系
     *
     * @param dataSysUserBankForm 用户银行关系表单
     * @return 操作结果
     */
    @PostMapping
    @Operation(summary = "创建用户银行关系")
    public ActionResult create(@RequestBody @Valid DataSysUserBankForm dataSysUserBankForm) throws DataException {
        String mainId = RandomUtil.snowId();
        UserInfo userInfo = userProvider.get();
        
        dataSysUserBankForm.setCreateBy(userInfo.getUserId());
        dataSysUserBankForm.setCreateTime(DateXhUtil.date());
        
        SysUserBankEntity entity = BeanCopierUtils.copy(dataSysUserBankForm, SysUserBankEntity.class);
        entity.setId(mainId);
        
        sysUserBankService.create(entity);
        return ActionResult.success("创建成功");
    }

    /**
     * 更新用户银行关系
     *
     * @param id                      用户银行关系ID
     * @param dataSysUserBankForm 用户银行关系表单
     * @return 操作结果
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新用户银行关系")
    public ActionResult update(@PathVariable("id") String id, @RequestBody @Valid DataSysUserBankForm dataSysUserBankForm) throws DataException {
        UserInfo userInfo = userProvider.get();
        SysUserBankEntity entity = sysUserBankService.getInfo(id);
        
        if (entity != null) {
            SysUserBankEntity updateEntity = BeanCopierUtils.copy(dataSysUserBankForm, SysUserBankEntity.class);

            
            sysUserBankService.update(id, updateEntity);
            return ActionResult.success("更新成功");
        } else {
            return ActionResult.fail("更新失败，数据不存在");
        }
    }

    /**
     * 删除用户银行关系
     *
     * @param id 用户银行关系ID
     * @return 操作结果
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除用户银行关系")
    public ActionResult delete(@PathVariable("id") String id) {
        SysUserBankEntity entity = sysUserBankService.getInfo(id);
        if (entity != null) {
            sysUserBankService.delete(entity);
            return ActionResult.success("删除成功");
        } else {
            return ActionResult.fail("删除失败，数据不存在");
        }
    }

    /**
     * 批量删除用户银行关系
     *
     * @param idList 用户银行关系ID列表
     * @return 操作结果
     */
    @DeleteMapping("/batch")
    @Operation(summary = "批量删除用户银行关系")
    public ActionResult batchDelete(@RequestBody @NotEmpty List<String> idList) {
        int count = sysUserBankService.batchDelete(idList);
        return ActionResult.success("批量删除成功，共删除" + count + "条记录");
    }

    /**
     * 检查用户银行关系是否已存在
     *
     * @param manId  持卡人ID
     * @param bankId 银行ID
     * @param id     排除的ID
     * @return 检查结果
     */
    @GetMapping("/checkRelation")
    @Operation(summary = "检查用户银行关系是否已存在")
    public ActionResult<Boolean> checkRelationExists(
            @Parameter(description = "持卡人ID") @RequestParam String manId,
            @Parameter(description = "银行ID") @RequestParam String bankId,
            @Parameter(description = "排除的ID") @RequestParam(required = false) String id) {
        boolean exists = sysUserBankService.checkRelationExists(manId, bankId, id);
        return ActionResult.success(!exists); // 返回是否可用（不存在则可用）
    }

    /**
     * 更新总额度
     *
     * @param id 用户银行关系ID
     * @return 操作结果
     */
    @PutMapping("/{id}/updateLimit")
    @Operation(summary = "更新总额度")
    public ActionResult updateLimitMoney(@PathVariable("id") String id) {
        boolean result = sysUserBankService.updateLimitMoney(id);
        return result ? ActionResult.success("总额度更新成功") : ActionResult.fail("总额度更新失败");
    }

    /**
     * 更新积分
     *
     * @param id    用户银行关系ID
     * @param point 新的积分数
     * @return 操作结果
     */
    @PutMapping("/{id}/updatePoint")
    @Operation(summary = "更新积分")
    public ActionResult updatePoint(@PathVariable("id") String id, @RequestParam Double point) {
        boolean result = sysUserBankService.updatePoint(id, point);
        return result ? ActionResult.success("积分更新成功") : ActionResult.fail("积分更新失败");
    }

    /**
     * 获取用户银行关系统计信息
     *
     * @param manId 持卡人ID
     * @return 统计信息
     */
    @GetMapping("/statistics/{manId}")
    @Operation(summary = "获取用户银行关系统计信息")
    public ActionResult<Object> getUserBankStatistics(@PathVariable("manId") String manId) {
        Object statistics = sysUserBankService.getUserBankStatistics(manId);
        return ActionResult.success(statistics);
    }

    /**
     * 丰富用户银行关系VO对象
     * 添加地址类型名称、信用卡类型名称、创建人名称等信息
     *
     * @param vo 用户银行关系VO对象
     */
    private void enrichUserBankVO(DataSysUserBankVO vo) {
        // 地址类型名称转换
        if ("1".equals(vo.getAddrType())) {
            vo.setAddrTypeName("单位地址");
            vo.setCurrentAddress(vo.getDeptAddress());
        } else if ("2".equals(vo.getAddrType())) {
            vo.setAddrTypeName("家庭地址");
            vo.setCurrentAddress(vo.getHomeAddress());
        } else {
            vo.setAddrTypeName("未设置");
            vo.setCurrentAddress("");
        }
        
        // 信用卡类型名称转换
        if (vo.getCardType() != null) {
            switch (vo.getCardType()) {
                case 1:
                    vo.setCardTypeName("合并账户合并还款");
                    break;
                case 2:
                    vo.setCardTypeName("合并账户分别还款");
                    break;
                case 3:
                    vo.setCardTypeName("分别账户分别还款");
                    break;
                default:
                    vo.setCardTypeName("默认类型");
                    break;
            }
        } else {
            vo.setCardTypeName("未设置");
        }
        
        // 总额度转换为元单位
        if (vo.getLimitMoney() != null) {
            BigDecimal limitMoneyYuan = new BigDecimal(vo.getLimitMoney())
                    .divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
            vo.setLimitMoneyYuan(limitMoneyYuan.doubleValue());
        }
        
        // 创建人和更新人名称转换
        vo.setCreateByName(generaterSwapUtil.userSelectValue(vo.getCreateBy()));
        vo.setUpdateByName(generaterSwapUtil.userSelectValue(vo.getUpdateBy()));
    }
}
