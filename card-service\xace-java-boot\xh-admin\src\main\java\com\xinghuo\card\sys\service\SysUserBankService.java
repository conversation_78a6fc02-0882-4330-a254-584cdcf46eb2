package com.xinghuo.card.sys.service;

import com.alicp.jetcache.anno.CacheInvalidate;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.CacheUpdate;
import com.alicp.jetcache.anno.Cached;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xinghuo.card.sys.entity.SysUserBankEntity;
import com.xinghuo.card.sys.model.sysuserbank.SysUserBankPagination;
import com.xinghuo.common.base.service.BaseService;

import java.util.List;
import java.util.Map;

/**
 * 用户银行信息服务接口
 * 用于管理用户与银行的关联关系，包括地址信息、额度汇总、积分等
 * 支持合并账户管理和分别还款设置
 *
 * <AUTHOR> Assistant
 * @version V1.0.0
 * @date 2024-12-29
 */
public interface SysUserBankService extends BaseService<SysUserBankEntity> {

    String CACHE_NAME = "dataSysUserBank";

    /**
     * 查询分页数据
     *
     * @param sysUserBankPagination 查询对象
     * @return 查询结果
     */
    List<SysUserBankEntity> getList(SysUserBankPagination sysUserBankPagination);

    /**
     * 查询分页或者不分页列表
     *
     * @param sysUserBankPagination 查询对象
     * @param dataType              0:分页 1:不分页
     * @return 查询结果
     */
    List<SysUserBankEntity> getTypeList(SysUserBankPagination sysUserBankPagination, String dataType);

    /**
     * 根据持卡人ID获取银行关系列表
     *
     * @param manId 持卡人ID
     * @return 银行关系列表
     */
    @Cached(name = CACHE_NAME, key = "man:#manId", cacheType = CacheType.LOCAL, expire = 120)
    List<SysUserBankEntity> getListByManId(String manId);

    /**
     * 获取DataSysUserBankEntity详细信息
     *
     * @param id 主键
     * @return 用户银行关系详细信息
     */
    @Cached(name = CACHE_NAME, key = "#id", cacheType = CacheType.LOCAL, expire = 360)
    SysUserBankEntity getInfo(String id);

    /**
     * 根据持卡人ID和银行ID查询关系
     *
     * @param manId  持卡人ID
     * @param bankId 银行ID
     * @return 用户银行关系信息
     */
    @Cached(name = CACHE_NAME, key = "relation:#manId:#bankId", cacheType = CacheType.LOCAL, expire = 360)
    SysUserBankEntity getInfoByManAndBank(String manId, String bankId);

    /**
     * 删除用户银行关系
     *
     * @param entity 删除的对象
     */
    @CacheInvalidate(name = CACHE_NAME, key = "#entity.id")
    void delete(SysUserBankEntity entity);

    /**
     * 新增保存用户银行关系
     *
     * @param entity 新增的对象
     */
    void create(SysUserBankEntity entity);

    /**
     * 修改保存用户银行关系
     *
     * @param id     主键
     * @param entity 修改的对象
     * @return 是否成功
     */
    @CacheUpdate(name = CACHE_NAME, key = "#id", value = "#entity")
    boolean update(String id, SysUserBankEntity entity);

    /**
     * 检查用户银行关系是否已存在
     *
     * @param manId  持卡人ID
     * @param bankId 银行ID
     * @param id     排除的ID（用于更新时检查）
     * @return 是否已存在
     */
    boolean checkRelationExists(String manId, String bankId, String id);

    /**
     * 批量删除用户银行关系
     *
     * @param ids 关系ID列表
     * @return 删除成功的数量
     */
    int batchDelete(List<String> ids);

    /**
     * 更新总额度
     * 根据关联的信用卡自动计算并更新总额度
     *
     * @param id 用户银行关系ID
     * @return 是否成功
     */
    boolean updateLimitMoney(String id);

    /**
     * 更新积分
     *
     * @param id    用户银行关系ID
     * @param point 新的积分数
     * @return 是否成功
     */
    boolean updatePoint(String id, Double point);

    /**
     * 批量更新总额度
     * 根据持卡人ID批量更新其所有银行关系的总额度
     *
     * @param manId 持卡人ID
     * @return 更新成功的数量
     */
    int batchUpdateLimitMoneyByManId(String manId);

    /**
     * 获取持卡人的银行关系统计信息
     *
     * @param manId 持卡人ID
     * @return 统计信息
     */
    @Cached(name = CACHE_NAME, key = "statistics:#manId", cacheType = CacheType.LOCAL, expire = 300)
    Object getUserBankStatistics(String manId);

    /**
     * 同步银行名称
     * 当银行信息更新时，同步更新冗余的银行名称字段
     *
     * @param bankId   银行ID
     * @param bankName 新的银行名称
     * @return 更新成功的数量
     */
    int syncBankName(String bankId, String bankName);

    /**
     * 获取用户银行统计信息
     *
     * @return 统计信息
     */
    @Cached(name = CACHE_NAME, key = "statistics", cacheType = CacheType.LOCAL, expire = 300)
    Map<String, Object> getUserBankStatistics();

    /**
     * 获取银行分布统计
     *
     * @return 银行分布
     */
    @Cached(name = CACHE_NAME, key = "bankDistribution", cacheType = CacheType.LOCAL, expire = 300)
    List<Map<String, Object>> getBankDistribution();

    /**
     * 获取持卡人分布统计
     *
     * @return 持卡人分布
     */
    @Cached(name = CACHE_NAME, key = "manDistribution", cacheType = CacheType.LOCAL, expire = 300)
    List<Map<String, Object>> getManDistribution();

    /**
     * 验证用户银行关系是否可以删除
     * 检查是否有关联的信用卡账户
     *
     * @param id 用户银行关系ID
     * @return 验证结果
     */
    Map<String, Object> validateCanDelete(String id);

    /**
     * 批量验证用户银行关系是否可以删除
     *
     * @param ids 用户银行关系ID列表
     * @return 验证结果
     */
    Map<String, Object> batchValidateCanDelete(List<String> ids);

    /**
     * 导入用户银行关系数据
     *
     * @param userBankList 用户银行关系数据列表
     * @return 导入结果
     */
    Map<String, Object> importUserBankData(List<SysUserBankEntity> userBankList);

    /**
     * 导出用户银行关系数据
     *
     * @param sysUserBankPagination 查询条件
     * @return 导出数据
     */
    List<Map<String, Object>> exportUserBankData(SysUserBankPagination sysUserBankPagination);

    /**
     * 复制用户银行关系
     *
     * @param id        源用户银行关系ID
     * @param newManId  新持卡人ID
     * @param newBankId 新银行ID
     * @return 复制的用户银行关系
     */
    SysUserBankEntity copyUserBank(String id, String newManId, String newBankId);

    /**
     * 获取用户银行关系详细信息（包含关联信息）
     *
     * @param id 用户银行关系ID
     * @return 详细信息
     */
    Map<String, Object> getUserBankDetailWithRelated(String id);

    /**
     * 根据银行ID获取用户银行关系列表
     *
     * @param bankId 银行ID
     * @return 用户银行关系列表
     */
    @Cached(name = CACHE_NAME, key = "bank:#bankId", cacheType = CacheType.LOCAL, expire = 120)
    List<SysUserBankEntity> getListByBankId(String bankId);

    /**
     * 获取用户银行关系的额度汇总
     *
     * @param manId 持卡人ID
     * @return 额度汇总信息
     */
    Map<String, Object> getLimitSummaryByManId(String manId);

    /**
     * 获取用户银行关系的积分汇总
     *
     * @param manId 持卡人ID
     * @return 积分汇总信息
     */
    Map<String, Object> getPointsSummaryByManId(String manId);

    /**
     * 批量设置合并账户
     *
     * @param ids           用户银行关系ID列表
     * @param mergeAccount  是否合并账户
     * @return 更新成功的数量
     */
    int batchSetMergeAccount(List<String> ids, Boolean mergeAccount);

    /**
     * 批量设置分别还款
     *
     * @param ids               用户银行关系ID列表
     * @param separateRepayment 是否分别还款
     * @return 更新成功的数量
     */
    int batchSetSeparateRepayment(List<String> ids, Boolean separateRepayment);

    /**
     * 获取用户银行关系关联统计
     *
     * @param id 用户银行关系ID
     * @return 关联统计信息
     */
    Map<String, Object> getUserBankRelatedStats(String id);

    /**
     * 同步持卡人名称
     * 当持卡人信息更新时，同步更新冗余的持卡人名称字段
     *
     * @param manId   持卡人ID
     * @param manName 新的持卡人名称
     * @return 更新成功的数量
     */
    int syncManName(String manId, String manName);

    /**
     * 获取重复的用户银行关系
     * 检查是否存在相同持卡人和银行的重复关系
     *
     * @return 重复关系列表
     */
    List<Map<String, Object>> getDuplicateUserBanks();

    /**
     * 清理重复的用户银行关系
     *
     * @return 清理数量
     */
    int cleanDuplicateUserBanks();
}
