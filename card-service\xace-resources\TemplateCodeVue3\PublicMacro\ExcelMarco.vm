#macro(CreateExcelFields $html,$index)
    #set($vModel = ${html.vModel})
    #set($config = ${html.config})
    #set($xhkey = "${config.xhKey}")
    /** ${config.label} **/
    @JSONField(name = "${vModel}")
    @Excel(name = "${config.label}",orderNum = "1", isImportField = "true" #if(${context.nameAgain.contains($vModel)}) ,fixedIndex =${index} #end)
    #if(${xhkey}=='inputNumber' || ${xhkey}=='calculate' )
        #if($!{fieLdsModel.formColumnModel.fieLdsModel.precision})
    private BigDecimal ${vModel};

        #else
    private Integer ${vModel};

        #end
    #elseif(${xhkey}=='slider'|| ${xhkey}=='rate')
    private Integer ${vModel};

    #elseif(${xhkey}=='modifyTime' || ${xhkey}=='createTime')
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date ${vModel};

    #elseif(${xhkey}=='datePicker')
        #set($pattern = "yyyy-MM-dd")
        #if(${html.format})
            #set($pattern = "${html.format}")
        #end
    @JsonFormat(pattern = "$pattern",timezone = "GMT+8")
    private String ${vModel};

    #else
    private String ${vModel};

    #end
#end
