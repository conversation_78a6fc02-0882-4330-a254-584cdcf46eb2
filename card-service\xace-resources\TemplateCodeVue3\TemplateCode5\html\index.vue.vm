<template>
    <div class="xh-content-wrapper bg-white">
        <FlowParser @register="registerFlowParser" @reload="getFlowOptions()" />
        <BasicModal v-bind="$attrs" @register="registerFlowListModal" title="请选择流程" :footer="null"
                    :width="400" destroyOnClose class="xh-flow-list-modal">
            <div class="template-list">
                <ScrollContainer>
                    <div class="template-item" v-for="item in flowList" :key="item.id"
                         @click="selectFlow(item)">
                        {{ item.fullName }}
                    </div>
                </ScrollContainer>
            </div>
        </BasicModal>
    </div>
</template>

<script lang="ts" setup>
    import {onMounted, reactive, toRefs} from 'vue';
    import {getFlowByFormId} from '/@/api/workFlow/formDesign';
    import {getFlowList} from '/@/api/workFlow/flowEngine';
    import {useMessage} from '/@/hooks/web/useMessage';
    import {useModal} from '/@/components/Modal';
    import {usePopup} from '/@/components/Popup';

    interface State {
        flowList: any[];
        flowItem: any;
        formFlowId: string;
    }

    const { createMessage } = useMessage();
    const [registerFlowParser, { openPopup: openFlowParser }] = usePopup();
    const [registerFlowListModal, { openModal: openFlowListModal, closeModal: closeFlowListModal }] = useModal();
    const state = reactive<State>({
        flowList: [],
        flowItem: {},
        formFlowId: '',
    });
    const { flowList } = toRefs(state);

    function getFlowOptions() {
        getFlowList(state.formFlowId, '1').then((res) => {
            const flowList = res.data;
            state.flowList = flowList;
            if (state.flowItem.id) return selectFlow(state.flowItem);
            if (!flowList.length) return createMessage.error('流程不存在');
            if (flowList.length === 1) return selectFlow(flowList[0]);
            openFlowListModal(true);
        });
    }
    function selectFlow(item) {
        closeFlowListModal();
        state.flowItem = item;
        const data = {
            id: '',
            flowId: item.id,
            opType: '-1',
            hideCancelBtn: true,
        };
        openFlowParser(true, data);
    }
    function init() {
        getFlowByFormId("${context.moduleId}").then((res) => {
            const flowId = res.data && res.data.id;
            state.formFlowId = flowId;
            getFlowOptions();
        });
    }

    onMounted(() => {
        init();
    });
</script>
