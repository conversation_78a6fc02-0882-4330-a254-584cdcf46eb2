package com.xinghuo.card.budget.model.budgetitem;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 预算子项视图对象
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2024-06-28
 */
@Data
public class FinBudgetItemVO {

    /**
     * 主键ID
     */
    private String id;

    /**
     * 关联预算ID
     */
    private String budgetId;

    /**
     * 预算项类型: CATEGORY-分类, ACCOUNT-账户
     */
    private String itemType;

    /**
     * 预算项类型名称
     */
    private String itemTypeName;

    /**
     * 目标ID
     */
    private String targetId;

    /**
     * 目标名称
     */
    private String targetName;

    /**
     * 预算金额
     */
    private BigDecimal budgetedAmount;

    /**
     * 当前已支出金额
     */
    private BigDecimal currentSpent;

    /**
     * 剩余金额
     */
    private BigDecimal remainingAmount;

    /**
     * 支出百分比
     */
    private BigDecimal spentPercentage;

    /**
     * 预警阈值百分比
     */
    private BigDecimal alertThreshold;

    /**
     * 是否已预警
     */
    private Boolean isAlerted;

    /**
     * 是否超支
     */
    private Boolean isOverBudget;

    /**
     * 预警状态: NORMAL-正常, WARNING-预警, EXCEEDED-超支
     */
    private String alertStatus;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}