<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xinghuo.card.sys.dao.SysManMapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.xinghuo.card.sys.entity.SysManEntity">
        <id column="id" property="id" jdbcType="VARCHAR"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="sex" property="sex" jdbcType="VARCHAR"/>
        <result column="birthday" property="birthday" jdbcType="TIMESTAMP"/>
        <result column="list_order" property="listOrder" jdbcType="INTEGER"/>
        <result column="create_by" property="createBy" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_by" property="updateBy" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础字段列表 -->
    <sql id="Base_Column_List">
        id, name, sex, birthday, list_order, create_by, create_time, update_by, update_time
    </sql>

    <!-- 获取下一个排序序号 -->
    <select id="getNextListOrder" resultType="java.lang.Integer">
        SELECT COALESCE(MAX(list_order), 0) + 1
        FROM data_sys_man
        WHERE list_order IS NOT NULL
    </select>

    <!-- 检查持卡人代称是否唯一 -->
    <select id="checkNameUnique" resultType="int">
        SELECT COUNT(1)
        FROM data_sys_man
        WHERE name = #{name}
        <if test="id != null and id != ''">
            AND id != #{id}
        </if>
    </select>

    <!-- 批量更新排序序号 -->
    <update id="batchUpdateListOrder">
        <foreach collection="list" item="item" separator=";">
            UPDATE data_sys_man
            SET list_order = #{item.listOrder},
                update_time = NOW()
            WHERE id = #{item.id}
        </foreach>
    </update>

    <!-- 获取持卡人统计信息 -->
    <select id="getManStatistics" resultType="map">
        SELECT 
            COUNT(1) as totalCount,
            COUNT(CASE WHEN sex = '1' THEN 1 END) as maleCount,
            COUNT(CASE WHEN sex = '2' THEN 1 END) as femaleCount,
            AVG(YEAR(CURDATE()) - YEAR(birthday)) as avgAge,
            MIN(create_time) as earliestCreateTime,
            MAX(create_time) as latestCreateTime
        FROM data_sys_man
    </select>

    <!-- 根据性别统计持卡人数量 -->
    <select id="getManCountBySex" resultType="map">
        SELECT 
            sex,
            COUNT(1) as count,
            CASE 
                WHEN sex = '1' THEN '男性'
                WHEN sex = '2' THEN '女性'
                ELSE '未知'
            END as sexName
        FROM data_sys_man
        GROUP BY sex
        ORDER BY sex
    </select>

    <!-- 获取生日提醒列表 -->
    <select id="getBirthdayRemindList" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM data_sys_man
        WHERE birthday IS NOT NULL
        AND (
            (MONTH(birthday) = MONTH(CURDATE()) AND DAY(birthday) BETWEEN DAY(CURDATE()) AND DAY(DATE_ADD(CURDATE(), INTERVAL #{days} DAY)))
            OR 
            (MONTH(birthday) = MONTH(DATE_ADD(CURDATE(), INTERVAL #{days} DAY)) AND DAY(birthday) &lt;= DAY(DATE_ADD(CURDATE(), INTERVAL #{days} DAY)))
        )
        ORDER BY MONTH(birthday), DAY(birthday)
    </select>

    <!-- 根据年龄范围查询持卡人 -->
    <select id="getManByAgeRange" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM data_sys_man
        WHERE birthday IS NOT NULL
        <if test="minAge != null">
            AND YEAR(CURDATE()) - YEAR(birthday) >= #{minAge}
        </if>
        <if test="maxAge != null">
            AND YEAR(CURDATE()) - YEAR(birthday) &lt;= #{maxAge}
        </if>
        ORDER BY birthday DESC
    </select>

</mapper>
