package com.xinghuo.card.flow.model.datafloworder;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 订单管理
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2022-11-27
 */
@Data
public class DataFlowOrderVO {

    @Schema(description = "主键")
    private String orderId;

    @Schema(description = "店铺")
    @JsonProperty("accId")
    private String accId;

    @Schema(description = "采购细目")
    @JsonProperty("orderItem")
    private String orderItem;

    @Schema(description = "金额")
    @JsonProperty("amount")
    private String amount;

    @Schema(description = "订单编号")
    @JsonProperty("orderNo")
    private String orderNo;

    @Schema(description = "订单日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @JsonProperty("orderDate")
    private Date orderDate;

    @Schema(description = "快递编号")
    @JsonProperty("expressNo")
    private String expressNo;

    @Schema(description = "目的地")
    @JsonProperty("dest")
    private String dest;

    @Schema(description = "状态")
    @JsonProperty("formStatus")
    private String formStatus;

    @Schema(description = "签收日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @JsonProperty("recDate")
    private Date recDate;

    @Schema(description = "预定日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @JsonProperty("prePayDate")
    private Date prePayDate;

    @Schema(description = "定金账户")
    @JsonProperty("prePayAccId")
    private String prePayAccId;

    @Schema(description = "定金金额")
    @JsonProperty("prePayAmount")
    private String prePayAmount;

    @Schema(description = "支付方式")
    @JsonProperty("payType")
    private String payType;

    @Schema(description = "支付卡片")
    @JsonProperty("payAccId")
    private String payAccId;

    @Schema(description = "支付费用")
    @JsonProperty("payAmount")
    private String payAmount;

    @Schema(description = "苏宁卡")
    @JsonProperty("payLp1")
    private String payLp1;

    @Schema(description = "云钻")
    @JsonProperty("payLp2")
    private String payLp2;

    @Schema(description = "铜板")
    @JsonProperty("payLp3")
    private String payLp3;

    @Schema(description = "银行支付立减")
    @JsonProperty("payYh")
    private String payYh;

    @Schema(description = "返钻")
    @JsonProperty("backLp2")
    private String backLp2;

    @Schema(description = "支付备注")
    @JsonProperty("payNote")
    private String payNote;

    @Schema(description = "备注")
    @JsonProperty("note")
    private String note;

    @Schema(description = "结算账户")
    @JsonProperty("backAccId")
    private String backAccId;

    @Schema(description = "回款金额")
    @JsonProperty("backAmount")
    private String backAmount;

    @Schema(description = "回款日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @JsonProperty("backDate")
    private Date backDate;

    @Schema(description = "最后创建人")
    @JsonProperty("updateBy")
    private String updateBy;

    @Schema(description = "最后修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonProperty("updateTime")
    private Date updateTime;

    @Schema(description = "创建人员")
    @JsonProperty("createBy")
    private String createBy;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonProperty("createTime")
    private Date createTime;

}
