##详情-表单生成
#macro(DetailFormRendering)
    #foreach($fieLdsModel in ${context.form})
        #set($xhKey = "${fieLdsModel.xhKey}")
        #set($isEnd = "${fieLdsModel.isEnd}")
        #set($formModel = ${fieLdsModel.formModel})
        #set($config= $formModel.config)
        #set($span=$config.span)
        #set($outermost = ${formModel.outermost})
        #set($borderType = ${formModel.borderType})
        #set($borderColor = ${formModel.borderColor})
        #set($borderWidth = ${formModel.borderWidth})
        #set($pcShow = $config.pc)
        #if(${xhKey}=='row' && $pcshow == true)
            #if(${isEnd}=='0')
            <a-col :span="${formModel.span}" class="ant-col-item">
            <a-row :gutter="#if(${context.formStyle}=='word-form')0#else${context.gutter}#end">
            #else
            </a-row>
            </a-col>
            #end
        #elseif(${xhKey}=='card' && $pcshow == true)
            #if(${isEnd}=='0')
            <a-col  #if(${span}) :span="${span}" #else :span="24" #end class="ant-col-item">
            <a-card class="mb-20"   #if(${formModel.shadow}=='hover') hoverable #end>
                #if(${formModel.header})
                    <template #title>${formModel.header}#if(${formModel.header} && ${config.tipLabel})<BasicHelp text="${config.tipLabel}" />#end</template>
                #end
            #else
            </a-card>
            </a-col>
            #end
        #elseif(${xhKey}=='tab' && $pcshow == true)
            #set($tabs = "a-tabs")
            #if(${outermost}=='1')
                #set($tabs = "a-tab-pane")
            #end
            #if(${isEnd}=='0')
                #if(${outermost}=='0')
                <a-col :span="${formModel.span}" class="ant-col-item">
                    <${tabs}  v-model:activeKey="state.${formModel.model}" #if($formModel.type)type="${formModel.type}"#end tabPosition="${formModel.tabPosition}" class="mb-20" >
                #else
                    <${tabs}  tab="${formModel.title}" key="${formModel.name}" forceRender>
                #end
            #else
                #if(${outermost}=='0')
                </${tabs}>
                </a-col>
                #else
                </${tabs} >
                #end
            #end
        #elseif(${xhKey}=='tableGrid' || ${xhKey}=='tableGridTd' || ${xhKey}=='tableGridTr')
            #set($tabs = "tbody")
            #set($tableGrid = "table")
            #if(${xhKey}=='tableGridTr')
                #set($tabs = "tr")
            #elseif(${xhKey}=='tableGridTd')
                #set($tabs = "")
                #if(${config.merged}==false)
                    #set($tabs = "td")
                #end
            #end
            #if(${config.pc}==true)
                #if(${isEnd}=='0')
                    #if(${xhKey}=='tableGrid')
                    <${tableGrid} class="table-grid-box" :style='{"--borderType":"${borderType}","--borderColor":"${borderColor}","--borderWidth":"${borderWidth}px"}'>
                    #end
                    #if($tabs)
                    <${tabs}#if(${config.colspan}) colspan="${config.colspan}"#end#if(${config.rowspan}) rowspan="${config.rowspan}"#end>
                    #end
                #else
                    #if($tabs)
                    </${tabs}>
                    #end
                    #if(${xhKey}=='tableGrid')
                    </${tableGrid}>
                    #end
                #end
            #end
        #elseif(${xhKey}=='groupTitle' || ${xhKey}=='text'|| ${xhKey} == 'button' || ${xhKey} == 'link' || ${xhKey} == 'alert'|| ${xhKey} == 'divider')
            #if($pcshow== true)
            <a-col :span="${span}" class="ant-col-item">
                <a-form-item>
                    <${config.tag}
                    #if($formModel.style) :style='${formModel.style}'#end
                    #if($formModel.href) href = "$formModel.href"#end
                    #if($formModel.target) target = "$formModel.target"#end
                    #if($formModel.showIcon) :show-icon= "$formModel.showIcon"#end
                    #if($formModel.align) align="${formModel.align}" #end
                    #if($formModel.disabled) :disabled="${formModel.disabled}" #end
                    #if($formModel.buttonText)  buttonText="${formModel.buttonText}"#end
                    #if($formModel.type) type="${formModel.type}" #end
                    #if($formModel.textStyle) :textStyle='${formModel.textStyle}'#end
                    #if($formModel.contentPosition) contentPosition="${formModel.contentPosition}" #end
                    #if(${xhKey} == 'alert' && $!{formModel.closable}) :closable= "$formModel.closable" #end
                    #if($formModel.title) title ="${formModel.title}" #end
                    #if($formModel.closeText) closeText ="${formModel.closeText}" #end
                    #if($formModel.description) description ="${formModel.description}" #end
                    #if($formModel.helpMessage) helpMessage ="${formModel.helpMessage}" #end
                    #if($formModel.content) content ="$formModel.content" #end>
                </${config.tag}>
                </a-form-item>
            </a-col>
            #end
        #elseif(${xhKey}=='collapse' && $pcshow == true)
            #set($collapse = "a-collapse")
            #if(${outermost}=='1')
                #set($collapse = "a-collapse-panel")
            #end
            #if(${isEnd}=='0')
                #if(${outermost}=='0')
                <a-col :span="${formModel.span}" class="ant-col-item">
                    <${collapse} ghost expandIconPosition="right"  :accordion="${formModel.accordion}" v-model:activeKey="state.${formModel.model}" class="mb-20">
                #else
                    <${collapse} header="${formModel.title}" key="${formModel.name}" forceRender>
                #end
            #else
                #if(${outermost}=='0')
                </${collapse}>
                </a-col>
                #else
                </${collapse}>
                #end
            #end
        #elseif(${xhKey}=='mast' || ${xhKey}=='mastTable')
            #DetailMastTable(${xhKey})
        #elseif($xhKey == 'table')
            #DetailChildTable()
        #end
    #end
#end
##详情-主副标签生成
#macro(DetailMastTable $tableType)
    #set($html = $fieLdsModel.formColumnModel.fieLdsModel)
    #set($beforeVmodel =${html.vModel})
    ##    副表参数
    #if($tableType=='mastTable')
        #set($html = $fieLdsModel.formMastTableModel.mastTable.fieLdsModel)
        #set($beforeVmodel =${fieLdsModel.formMastTableModel.vModel})
    #end
    #set($vModel = "${html.vModel}")
    #set($mastModel="${context.formModel}.${beforeVmodel}")
    #set($config = $html.config)
    #set($mastKey = "${config.xhKey}")
    #set($show = $config.noShow)
    #set($pcshow = $config.pc)
    #set($startTime=${html.startTime})
    #set($endTime=${html.endTime})
##    时间处理
    #if(${mastKey}=='datePicker'||${mastKey}=='timePicker')
##            #GetStartAndEndTime($mastKey,$config,$html,$startTime,$endTime)
    #end
    #if($show == false && $pcshow == true)
    <a-col :span="${config.span}" class="ant-col-item" #if(${context.columnData.useFormPermission}) #if(${vModel}) v-if="hasFormP('${beforeVmodel}')"
        #elseif($mastKey == 'relationFormAttr' || $mastKey == 'popupAttr') v-if="hasFormP('${html.relationField}')" #end  #end >
        <a-form-item #if($config.showLabel == true)   #if($config.labelWidth && ${context.labelPosition}!="top") :labelCol="{ style: { width: '${config.labelWidth}px' } }"#end
            #else :labelCol="{ style: { width: '0px' } }"#end #if($vModel) name="${beforeVmodel}" #end>
            <template #label>${config.label}#if(${config.label} && $config.tipLabel)<BasicHelp text="${config.tipLabel}" />#end</template>
            #DetailFieldTag($mastKey,$html,$config,$mastModel,$beforeVmodel,-1)
        </a-form-item>
    </a-col>
    #end
#end
##  详情-生成字段标签
#macro(DetailFieldTag $mastKey,$html,$config,$mastModel,$beforeVmodel,$index)
    #if($DetailTag.contains($mastKey))
    <${config.tag}  #if($vModel)  v-model:value="${mastModel}"#end
        #if($mastKey!='text')
            #if($html.placeholder) placeholder="${html.placeholder}" #end
        #else
            #if($config.defaultValue) value="${config.defaultValue}"#end
        #end
        #if($html.maxlength) :maxlength="${html.maxlength}" #end disabled
        #if($mastKey =='uploadFile' || $mastKey =='uploadImg' || $mastKey =='inputNumber'  || $mastKey =='calculate') detailed #end
        #if($html.readonly == true ) readonly #end
        #if($html.clearable == true ) allowClear #end
        #if($html.prefixIcon) prefix-icon='${html.prefixIcon}' #end
        #if($html.suffixIcon) suffix-icon='${html.suffixIcon}' #end
        #if($html.style) :style='${html.style}'#end
        #if($html.showWordLimit == true ) ${html.showWordLimit} #end
        #if($html.size) size="${html.size}" #end
        #if($html.min) :min="${html.min}" #end
        #if($html.max) :max="${html.max}" #end
        #if($html.count) :count="${html.count}" #end
        #if($html.type) type="${html.type}" #end
        #if($html.autoSize) :autoSize='${html.autoSize}' #end
        #if($html.step) :step="${html.step}" #end
        #if($html.precision) :precision="${html.precision}" #end
        #if($html.stepstrictly==true) stepstrictly #end
        #if($html.textStyle) :textStyle='${html.textStyle}' #end
        #if($html.lineHeight) :lineHeight="${html.lineHeight}" #end
        #if($html.fontSize) :fontSize="${html.fontSize}" #end
        #if($html.controls) :controls="${html.controls}" #end
        #if($html.showChinese) :showChinese="${html.showChinese}" #end
        #if($html.showPassword) show-password #end
        #if($html.filterable || $html.filterable=='false') :showSearch='${html.filterable}' #end
        #if($html.multiple) :multiple="${html.multiple}" #end
        #if($html.separator) separator="${html.separator}" #end
        #if($html.isrange==true) is-range #end
        #if($html.rangeseparator) range-separator="${html.rangeseparator}" #end
        #if($html.startplaceholder) start-placeholder="${html.startplaceholder}" #end
        #if($html.endplaceholder) end-placeholder="${html.endplaceholder}" #end
        #if($html.format) format="${html.format}" #end
        #if($html.colorformat) color-format="${html.colorformat}" #end
        #if($html.valueformat) value-format="${html.valueformat}" #end
        #if($html.activetext) active-text="${html.activetext}" #end
        #if($html.inactivetext) inactive-text="${html.inactivetext}" #end
        #if($html.activecolor) active-color="${html.activecolor}" #end
        #if($html.inactivecolor) inactive-color="${html.inactivecolor}" #end
        #if($html.activevalue) :active-value="${html.activevalue}" #end
        #if($html.inactivevalue) :inactive-value="${html.inactivevalue}" #end
        #if($html.pickeroptions) :picker-options='${html.pickeroptions}'#end
        #if($html.showScore == true ) show-score #end
        #if($html.showText == true ) show-text #end
        #if($html.allowhalf == true ) allow-half #end
        #if($html.showAlpha == true ) show-alpha #end
        #if($html.showStops == true ) show-stops #end
        #if($html.range == true ) range #end
        #if($html.showTip == true ) :showTip="${html.showTip}" #end
        #if($html.accept) accept="${html.accept}" #end
        #if($html.fileSize) :fileSize="${html.fileSize}" #end
        #if($html.sizeUnit) sizeUnit="${html.sizeUnit}" #end
        #if($html.limit) :limit="${html.limit}" #end
        #if($html.pathType) pathType="${html.pathType}" #end
        #if($html.isAccount) :isAccount="${html.isAccount}" #end
        #if($html.folder) folder="${html.folder}" #end
        #if($html.buttonText) buttonText="${html.buttonText}" #end
        #if($html.contentposition) content-position="${html.contentposition}" #end
        #if($html.isAmountChinese) isAmountChinese #end
        #if($html.thousands) thousands #end
        #if($html.addonAfter) addonAfter="${html.addonAfter}" #end
        #if($html.addonBefore) addonBefore="${html.addonBefore}" #end
        #if($html.level || $html.level=='0') :level=${html.level} #end >
    </${config.tag}>
    #else
        #if(${mastKey} == 'relationFormAttr' || ${mastKey} == 'popupAttr')
            #if(${html.isStorage} == 0)
                #if($index=="index")
                    #set($mastModel = "record.${html.relationField}_${html.showField}")
                #else
                    #set($mastModel = "${context.formModel}.${html.relationField}_${html.showField}")
                #end

            #end
        #end
        #if($dataType=='static')
        <p>{{ ${mastModel} }} </p>
        #else
            #if(${mastKey} == 'relationForm')
            <p class="link-text" @click="toDetail('${html.modelId}', ${mastModel}_id)">{{ ${mastModel} }}</p>
            #elseif(${mastKey} == 'input')
            <p>#if($html.slot.prepend)${html.slot.prepend}#end{{${mastModel}}}#if($html.slot.append)${html.slot.append}#end</p>
            #elseif(${mastKey} == 'editor')
##          副文本内放标签
            <div v-html="${mastModel}"></div>
            #else
            <p>{{${mastModel}}}</p>
            #end
        #end
    #end
#end
##详情-子表生成
#macro(DetailChildTable)
#set($child = $fieLdsModel.childList)
#set($aliasname = "")
#foreach($children in ${context.children})
    #if(${children.tableModel}==${child.tableModel})
        #set($aliasname = "${children.aliasLowName}")
        #set($aliasName = "${children.aliasUpName}")
    #end
#end
<a-col :span="${child.span}" class="ant-col-item">
<a-form-item>
    #if($child.showTitle== true)
        <XhGroupTitle content="${child.label}" :bordered="false" helpMessage="$child.tipLabel" />
    #end
    <a-table :data-source="dataForm.${child.tableModel}" :columns="${aliasname}Columns" size="small" :pagination="false" :scroll="{ x: 'max-content' }">
        <template #headerCell="{ column }">
            <span class="required-sign" v-if="column.required">*</span>
            {{ column.title }}
            <BasicHelp :text="column.tipLabel" v-if="column.tipLabel" />
        </template>
##    子表字段
        <template #bodyCell="{ column, index, record }">
            <template v-if="column.key === 'index'">{{ index + 1 }}</template>
            #foreach($itemModel in ${child.childList})
                #set($fieLdsModel = ${itemModel.fieLdsModel})
                #set($config = ${fieLdsModel.config})
                #set($mastKey = "${config.xhKey}")
                #set($beforeVmodel ="${aliasname}${fieLdsModel.vModel}")
                #set($mastModel="record.${fieLdsModel.vModel}")
                <template v-if="column.key === #if($!{fieLdsModel.vModel}) '${fieLdsModel.vModel}' #else '${config.formId}' #end">
##              子表标签生成
                 #DetailFieldTag($mastKey,$fieLdsModel,$config,$mastModel,$beforeVmodel,'index')
                </template>
            #end
        </template>
        ##    子表合计
        #if($child.showSummary)
            #set($childSummary=true)
            <template #summary v-if="dataForm.${child.tableModel}?.length">
                <a-table-summary fixed>
                    <a-table-summary-row>
                        <a-table-summary-cell :index="0">合计</a-table-summary-cell>
                        <a-table-summary-cell v-for="(item, index) in get${aliasName}ColumnSum" :key="index" :index="index + 1">{{ item }}</a-table-summary-cell>
                        <a-table-summary-cell :index="get${aliasName}ColumnSum.length + 1"></a-table-summary-cell>
                    </a-table-summary-row>
                </a-table-summary>
            </template>
        #end
    </a-table>
</a-form-item>
</a-col>
#end
##  子表字段对象列表生成
#macro(DetailChildTableColumns)
    ##  子表列表字段属性
    #foreach($itemModel in ${context.children})
        const ${itemModel.aliasLowName}Columns: any[] = computed(() => {
            let list = [
                #set($childList = ${itemModel.childList})
                #foreach($html in ${childList})
                    #set($fieLdsModel = ${html.fieLdsModel})
                    #set($config = ${fieLdsModel.config})
                {
                title: '${config.label}',
                dataIndex: #if($!{fieLdsModel.vModel}) '${fieLdsModel.vModel}' #else '${config.formId}' #end,
                key: #if($!{fieLdsModel.vModel}) '${fieLdsModel.vModel}' #else '${config.formId}' #end,
                    #if($!{config.columnWidth})
                width: $!{config.columnWidth},
                    #end
                tipLabel: #if($!{config.tipLabel}) "${config.tipLabel}" #else '' #end,
                    #set($vModelRequired="#if($!{fieLdsModel.vModel})${itemModel.aliasLowName}List-${fieLdsModel.vModel}#else${itemModel.aliasLowName}List-${config.formId}#end")
                required: #if($context.isFlow)judgeRequired('$vModelRequired'), #else ${config.required},#end
                    #if($!{fieLdsModel.thousands}==true)
                thousands: ${fieLdsModel.thousands},
                    #end
                },
                #end
            ];
            #if($context.isFlow)
            list = list.filter(o => judgeShow('${itemModel.tableModel}-' + o.dataIndex));
            #else
                #if(${context.columnData.useFormPermission})list = list.filter(o => hasFormP('${itemModel.tableModel}-' + o.dataIndex)); #end
            #end
            const indexColumn = { title: '序号', dataIndex: 'index', key: 'index', align: 'center', width: 50 };
            return [indexColumn, ...list];
        });
        ##  子表合计参数
        #if($itemModel.showSummary)
        //合计方法
        const get${itemModel.aliasUpName}ColumnSum = computed(() => {
            const sums: any[] = [];
            const summaryField: any[] = #if(${itemModel.summaryField}) ${itemModel.summaryField} #else [] #end;
            const useThousands = key => unref(${itemModel.aliasLowName}Columns).some(o => o.key === key && o.thousands);
            const isSummary = key => summaryField.includes(key);
            const list = unref(${itemModel.aliasLowName}Columns).filter(o => o.key !== 'index' && o.key !== 'action');
            list.forEach((column, index) => {
                let sumVal = state.dataForm.${itemModel.tableModel}.reduce((sum, d) => sum + getCmpValOfRow(d, column.key, summaryField || []), 0);
                if (!isSummary(column.key)) sumVal = '';
                sumVal = Number.isNaN(sumVal) ? '' : sumVal;
                const realVal = sumVal && !Number.isInteger(Number(sumVal)) ? Number(sumVal).toFixed(2) : sumVal;
                sums[index] = useThousands(column.key) ? thousandsFormat(realVal) : realVal;
            });
            return sums;
        });
        #end
    #end
#end

