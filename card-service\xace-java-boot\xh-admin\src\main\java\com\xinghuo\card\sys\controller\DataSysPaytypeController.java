package com.xinghuo.card.sys.controller;

import com.xinghuo.admin.util.GeneraterSwapUtil;
import com.xinghuo.card.sys.entity.DataSysPaytypeEntity;
import com.xinghuo.card.sys.model.datasyspaytype.DataSysPaytypeForm;
import com.xinghuo.card.sys.model.datasyspaytype.DataSysPaytypeModel;
import com.xinghuo.card.sys.model.datasyspaytype.DataSysPaytypePagination;
import com.xinghuo.card.sys.model.datasyspaytype.DataSysPaytypeVO;
import com.xinghuo.card.sys.service.DataSysPaytypeService;
import com.xinghuo.common.base.ActionResult;
import com.xinghuo.common.base.UserInfo;
import com.xinghuo.common.base.vo.ListVO;
import com.xinghuo.common.exception.DataException;
import com.xinghuo.common.util.UserProvider;
import com.xinghuo.common.util.core.BeanCopierUtils;
import com.xinghuo.common.util.core.DateXhUtil;
import com.xinghuo.common.util.core.RandomUtil;
import com.xinghuo.common.util.tree.SumTree;
import com.xinghuo.common.util.tree.TreeDotUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

/**
 * 收支类型管理
 *
 * <AUTHOR>
 * @version V1.0.0
 * date 2022-11-14
 */
@Slf4j
@RestController
@Tag(name = "收支明细管理", description = "收支明细管理")
@RequestMapping("/api/card/sys/paytype")
public class DataSysPaytypeController {

    @Autowired
    private GeneraterSwapUtil generaterSwapUtil;

    @Autowired
    private UserProvider userProvider;

    @Autowired
    private DataSysPaytypeService dataSysPaytypeService;


    /**
     * 列表
     *
     * @param dataSysPaytypePagination
     * @return
     */
    @Operation(summary = "列表")
    @GetMapping
    public ActionResult list( DataSysPaytypePagination dataSysPaytypePagination) throws IOException {
        List<DataSysPaytypeEntity> data = dataSysPaytypeService.getList(dataSysPaytypePagination);
//处理id字段转名称，若无需转或者为空可删除
        for (DataSysPaytypeEntity entity : data) {
//            entity.setType( generaterSwapUtil.getDicName(String.valueOf(entity.getType()), "1009903952115712"));
            entity.setCreateBy(generaterSwapUtil.userSelectValue(entity.getCreateBy()));
        }
        List<DataSysPaytypeModel> list = BeanCopierUtils.copyList(data, DataSysPaytypeModel.class);

        List<SumTree<DataSysPaytypeModel>> typeList = TreeDotUtils.convertListToTreeDot(list);

        List<DataSysPaytypeVO> typevo = BeanCopierUtils.copyList(typeList, DataSysPaytypeVO.class);
        ListVO vo = new ListVO();
        vo.setList(typevo);
        return ActionResult.success(vo);
    }


    @Operation(summary = "获取收支列表(下拉框)")
    @GetMapping("/Selector/{id}")
    public ActionResult<ListVO<DataSysPaytypeVO>> treeView(@PathVariable("id") String id) {
        List<DataSysPaytypeEntity> data = dataSysPaytypeService.getList(new DataSysPaytypePagination());
        if (!"0".equals(id)) {
            data.remove(dataSysPaytypeService.getInfo(id));
        }
        List<DataSysPaytypeModel> list = BeanCopierUtils.copyList(data, DataSysPaytypeModel.class);
        List<SumTree<DataSysPaytypeModel>> typeList = TreeDotUtils.convertListToTreeDotFilter(list);
//        List<DataSysPaytypeVO> typevo = JsonUtil.getJsonToList(typeList, DataSysPaytypeVO.class);
        ListVO vo = new ListVO();
        vo.setList(typeList);
        return ActionResult.success(vo);
    }

    /**
     * 创建
     *
     * @param dataSysPaytypeForm
     * @return
     */
    @Operation(summary = "新增")
    @PostMapping
    public ActionResult create(@RequestBody @Valid DataSysPaytypeForm dataSysPaytypeForm) throws DataException {
        String mainId = RandomUtil.snowId();
        UserInfo userInfo = userProvider.get();
        dataSysPaytypeForm.setCreateBy(userInfo.getUserId());
        dataSysPaytypeForm.setCreateTime(DateXhUtil.now());
        DataSysPaytypeEntity entity = BeanCopierUtils.copy(dataSysPaytypeForm, DataSysPaytypeEntity.class);
        entity.setId(mainId);
        dataSysPaytypeService.save(entity);
        return ActionResult.success("创建成功");
    }

    /**
     * 信息
     *
     * @param id
     * @return
     */
    @Operation(summary = "查询单条")
    @GetMapping("/{id}")
    public ActionResult<DataSysPaytypeVO> info(@PathVariable("id") String id) {
        DataSysPaytypeEntity entity = dataSysPaytypeService.getInfo(id);
        DataSysPaytypeVO vo = BeanCopierUtils.copy(entity, DataSysPaytypeVO.class);
        vo.setCreateBy(generaterSwapUtil.userSelectValue(vo.getCreateBy()));
        return ActionResult.success(vo);
    }

    /**
     * 表单信息(详情页)
     *
     * @param id
     * @return
     */
    @Operation(summary = "表单信息(详情页)")
    @GetMapping("/detail/{id}")
    public ActionResult<DataSysPaytypeVO> detailInfo(@PathVariable("id") String id) {
        DataSysPaytypeEntity entity = dataSysPaytypeService.getInfo(id);
        DataSysPaytypeVO vo =BeanCopierUtils.copy(entity, DataSysPaytypeVO.class);
        //添加到详情表单对象中
//        vo.setType(generaterSwapUtil.getDicName(vo.getType(), "1009903952115712"));
        vo.setCreateBy(generaterSwapUtil.userSelectValue(vo.getCreateBy()));
        return ActionResult.success(vo);
    }

    /**
     * 更新
     *
     * @param id
     * @return
     */
    @Operation(summary = "修改")
    @PutMapping("/{id}")
    public ActionResult update(@PathVariable("id") String id, @RequestBody @Valid DataSysPaytypeForm dataSysPaytypeForm) throws DataException {
        UserInfo userInfo = userProvider.get();
        DataSysPaytypeEntity entity = dataSysPaytypeService.getInfo(id);
        if (entity != null) {
            DataSysPaytypeEntity subentity = BeanCopierUtils.copy(dataSysPaytypeForm, DataSysPaytypeEntity.class);
            subentity.setCreateBy(entity.getCreateBy());
            subentity.setCreateTime(entity.getCreateTime());
            dataSysPaytypeService.update(id, subentity);
            return ActionResult.success("更新成功");
        } else {
            return ActionResult.fail("更新失败，数据不存在");
        }
    }

    /**
     * 删除
     *
     * @param id
     * @return
     */
    @Operation(summary = "删除")
    @DeleteMapping("/{id}")
    public ActionResult delete(@PathVariable("id") String id) {
        DataSysPaytypeEntity entity = dataSysPaytypeService.getInfo(id);
        if (entity != null) {
            dataSysPaytypeService.delete(entity);
        }
        return ActionResult.success("删除成功");
    }
}
