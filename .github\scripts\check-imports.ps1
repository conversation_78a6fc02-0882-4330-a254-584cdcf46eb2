# 导入规范检查脚本
# 用于检查项目中是否有使用错误的导入路径

param(
    [string]$ProjectPath = ".",
    [switch]$Fix = $false
)

Write-Host "🔍 检查导入规范..." -ForegroundColor Yellow

# 需要检查的错误导入模式
$ErrorPatterns = @(
    @{
        Pattern = "import javax\.validation\."
        Replacement = "import jakarta.validation."
        Description = "Bean Validation 注解"
    },
    @{
        Pattern = "import javax\.servlet\."
        Replacement = "import jakarta.servlet."
        Description = "Servlet API"
    },
    @{
        Pattern = "import javax\.persistence\."
        Replacement = "import jakarta.persistence."
        Description = "JPA 注解"
    },
    @{
        Pattern = "import javax\.ws\.rs\."
        Replacement = "import jakarta.ws.rs."
        Description = "JAX-RS 注解"
    },
    @{
        Pattern = "import javax\.json\."
        Replacement = "import jakarta.json."
        Description = "JSON-B API"
    },
    @{
        Pattern = "import com\.xinghuo\.common\.base\.Pagination;"
        Replacement = "import com.xinghuo.common.base.model.Pagination;"
        Description = "分页基类导入路径"
    }
)

$ErrorCount = 0
$FixedCount = 0

# 查找所有 Java 文件
$JavaFiles = Get-ChildItem -Path $ProjectPath -Recurse -Filter "*.java" | Where-Object {
    $_.FullName -notlike "*\target\*" -and
    $_.FullName -notlike "*\build\*" -and
    $_.FullName -notlike "*\.git\*"
}

Write-Host "📁 找到 $($JavaFiles.Count) 个 Java 文件" -ForegroundColor Green

foreach ($File in $JavaFiles) {
    $Content = Get-Content -Path $File.FullName -Raw
    $FileHasErrors = $false
    $ModifiedContent = $Content

    foreach ($Pattern in $ErrorPatterns) {
        if ($Content -match $Pattern.Pattern) {
            $ErrorCount++
            $FileHasErrors = $true

            $RelativePath = $File.FullName.Replace((Get-Location).Path, "").TrimStart('\')
            Write-Host "❌ $RelativePath" -ForegroundColor Red
            Write-Host "   错误: 使用了 $($Pattern.Description) 的旧包名" -ForegroundColor Red

            # 显示具体的错误行
            $Lines = $Content -split "`n"
            for ($i = 0; $i -lt $Lines.Length; $i++) {
                if ($Lines[$i] -match $Pattern.Pattern) {
                    Write-Host "   第 $($i + 1) 行: $($Lines[$i].Trim())" -ForegroundColor Yellow
                }
            }

            if ($Fix) {
                $ModifiedContent = $ModifiedContent -replace $Pattern.Pattern, $Pattern.Replacement
                $FixedCount++
                Write-Host "   ✅ 已自动修复" -ForegroundColor Green
            } else {
                Write-Host "   💡 建议修改为: $($Pattern.Replacement)" -ForegroundColor Cyan
            }
            Write-Host ""
        }
    }

    # 如果启用了修复模式且文件有错误，则写回文件
    if ($Fix -and $FileHasErrors -and $ModifiedContent -ne $Content) {
        Set-Content -Path $File.FullName -Value $ModifiedContent -NoNewline
    }
}

# 输出总结
Write-Host "📊 检查完成!" -ForegroundColor Green
Write-Host "   检查文件数: $($JavaFiles.Count)" -ForegroundColor White
Write-Host "   发现错误数: $ErrorCount" -ForegroundColor $(if ($ErrorCount -eq 0) { "Green" } else { "Red" })

if ($Fix -and $FixedCount -gt 0) {
    Write-Host "   修复错误数: $FixedCount" -ForegroundColor Green
}

if ($ErrorCount -gt 0 -and -not $Fix) {
    Write-Host ""
    Write-Host "💡 提示: 使用 -Fix 参数可以自动修复这些问题" -ForegroundColor Cyan
    Write-Host "   例如: .\check-jakarta-imports.ps1 -Fix" -ForegroundColor Cyan
}

if ($ErrorCount -eq 0) {
    Write-Host "✅ 所有文件都符合导入规范!" -ForegroundColor Green
    exit 0
} else {
    Write-Host "❌ 发现不符合导入规范的文件，请修复后再提交代码" -ForegroundColor Red
    exit 1
}
