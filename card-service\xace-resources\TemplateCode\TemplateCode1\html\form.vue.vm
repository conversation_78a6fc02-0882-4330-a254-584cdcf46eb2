#set($pKeyName = "${context.pKeyName}")
#set($mastTableList = $context.mastTable)
#macro(form $key $html $formModel $showModel)
	#set($model = ${html.vModel})
	#set($slot = ${html.slot})
	#set($prop=${model})
	#set($vModel="${formModel}.${model}")
	#set($config = $html.config)
	#set($xhkey = "${config.xhKey}")
	#set($show = $config.noShow)
	#set($mastModel="${vModel}")
	#set($placeholder = ${html.placeholder})
	#set($showList = ${model})
	#set($showJudge= "v-if="""+"judgeShow('"+${showList}+"')"+"""")
	#set($showCol="el-col")
	#set($item="xh-form-tip-item")
	#set($tableTemplate="")
	#set($relationField="")
	#set($showWrite="")
	#set($field="")
	#set($rowIndex="")
	#set($formData="")
	#set($templateJson="")
	#set($childoptions="")
	#set($ableRelationIds="")
	#set($startTime="")
	#set($endTime="")
	#set($dataType="${config.dataType}")
	#set($dataOptions="${childoptions}${model}Options")
	#if($xhkey=='userSelect' && ${html.relationField})
		#set($ableRelationIds="${context.formModel}.${html.relationField}")
		#if($html.relationChild)
			#set($ableRelationIds="scope.row.${html.relationField}")
		#end
	#end
	#if($xhkey=='date' || $xhkey=='time')
		#set($startRelationField="''")
		#if($config.startRelationField)
			#set($startRelationField="${context.formModel}.${config.startRelationField}")
			#if($config.startChild)
				#set($startRelationField="scope.row.${config.startRelationField}")
			#end
		#end
		#set($startTimeValue="#if(${config.startTimeValue})'${config.startTimeValue}'#else''#end")
		#set($startTimeType="#if(${config.startTimeType})${config.startTimeType}#else''#end")
		#set($startTimeTarget="#if(${config.startTimeTarget})${config.startTimeTarget}#else''#end")
		#set($endRelationField="''")
		#if($config.endRelationField)
			#set($endRelationField="${context.formModel}.${config.endRelationField}")
			#if($config.endChild)
				#set($endRelationField="scope.row.${config.endRelationField}")
			#end
		#end
		#set($endTimeValue="#if(${config.endTimeValue})'${config.endTimeValue}'#else''#end")
		#set($endTimeType="#if(${config.endTimeType})${config.endTimeType}#else''#end")
		#set($endTimeTarget="#if(${config.endTimeTarget})${config.endTimeTarget}#else''#end")

		#set($startTime="dateTime(${config.startTimeRule},${startTimeType},${startTimeTarget},${startTimeValue},${startRelationField})")
		#set($endTime="dateTime(${config.endTimeRule},${endTimeType},${endTimeTarget},${endTimeValue},${endRelationField})")
		#if($xhkey=='time')
			#set($startTime="time(${config.startTimeRule},${startTimeType},${startTimeTarget},${startTimeValue},'${html.format}',${startRelationField})")
			#set($endTime="time(${config.endTimeRule},${endTimeType},${endTimeTarget},${endTimeValue},'${html.format}',${endRelationField})")
		#end
	#end
	#if($xhkey=='popupAttr' || $xhkey=='relationFormAttr')
		#set($relationField = "relationField='"+${html.relationField}+"'")
		#set($showJudge = "")
		#if(${html.relationField})
			#set($showJudge = "v-if="""+"judgeShow('"+${html.relationField}+"')"+"""")
		#end
		#set($showList = "")
	#end
	#if($xhkey=='relationForm' || $xhkey=='popupSelect'|| $xhkey=='popupTableSelect'|| $xhkey=='autoComplete')
		#set($relationField = "relationField='"+${html.relationField}+"'")
		#set($field="field='"+${model}+"'")
		#set($rowIndex="null")
		#set($formData="${context.formModel}")
		#set($templateJson="interfaceRes.${model}")
	#end
	#set($fileSize="")
	#if($xhkey=='uploadFz' || $xhkey=='uploadImg')
		#set($fileSize="null")
		#if($html.fileSize)
			#set($fileSize="${html.fileSize}")
		#end
	#end
	#if($key=='table')
		#set($showList="")
		#set($showWrite=":disabled=""judgeWrite('"+${showModel}+"List')||judgeWrite('"+${showModel}+"List-"+${model}+"')""")
		#set($prop="")
		#set($showCol="")
		#set($showJudge= "v-if="""+"judgeShow('"+${showModel}+"List-"+${model}+"')"+"""")
		#set($item="el-table-column")
		#set($tableTemplate="template")
		#set($mastModel="scope.row."+"${model}")
		#set($childoptions="${showModel}")
		#if($xhkey=='popupAttr' || $xhkey=='relationFormAttr')
			#set($relationField = ":relationField=""'"+${html.relationField}+"'+scope."+'$'+"index""")
			#set($showJudge = "")
			#if(${html.relationField})
				#set($showJudge= "v-if="""+"judgeShow('"+${showModel}+"List-"+${html.relationField}+"')"+"""")
			#end
			#set($showWrite="")
		#end
		#if($xhkey=='relationForm' || $xhkey=='popupSelect' || $xhkey=='popupTableSelect'|| $xhkey=='autoComplete')
			#set($relationField = "relationField='"+${html.relationField}+"'")
			#set($field = ":field=""'"+${model}+"'+scope."+'$'+"index""")
			#set($rowIndex="scope."+'$'+"index")
			#set($formData="${context.formModel}")
			#set($templateJson="interfaceRes.${showModel}${model}")
		#end
		#if($dataType=='dynamic')
			#set($dataOptions="scope.row.${model}Options")
		#else
			#set($dataOptions="${childoptions}${model}Options")
		#end
	#end
	#if(${showList})
		#set($showWrite=":disabled=""judgeWrite('"+${showList}+"')""")
	#end
	#if($xhkey=='createUser' || $xhkey=='createTime' || $xhkey=='modifyUser' || $xhkey=='modifyTime' || $xhkey=='currOrganize' || $xhkey=='currDept' || $xhkey=='currPosition' || $xhkey=='billRule')
		#set($placeholder = "系统自动生成")
	#end
	#set($columnOptions="")
	#if($xhkey=='popupSelect' || $xhkey=='relationForm' || $xhkey=='popupTableSelect')
		#set($columnOptions="${childoptions}${model}"+"ColumnOptions")
	#end
	#if($show == false  && ${config.pc}==true)
			#if($showCol)
			<el-col #if(${showJudge}) :span="${config.span}" ${showJudge} #end>
			#end
				<${item} #if($config.tipLabel) tipLabel="${config.tipLabel}" #end
					#if($config.showLabel && $config.showLabel == true)
					#set($childIndex='-1')
					#set($childModel="${model}")
					#if($config.label) label="${config.label}" #end
					#if(${showJudge}) ${showJudge} #end
					#if(${config.columnWidth}) width="${config.columnWidth}" #end
					#if($config.labelWidth) label-width="${config.labelWidth}px"#end #else label-width="0"#end
					#if($model) prop="${model}" #end>
					#if($tableTemplate)
					#if($model)
					<template slot="header">
        				<span class="required-sign"  v-if="judgeRequired('${showModel}List-${model}')">*</span>${config.label}
						#if(${config.tipLabel})
						<span slot="label">
                      		<el-tooltip placement="top" content='${config.tipLabel}'>
                        		<a class='el-icon-warning-outline'></a>
                      		</el-tooltip>
                    	</span>
						#end
      				</template>
					#end
					#set($childIndex='scope.'+'$'+'index')
					#set($childModel="${showModel}-${model}")
					<template slot-scope="scope">
					#end
					<${config.tag} #if($model)  v-model="${mastModel}" @change="changeData('${childModel}',${childIndex})" #end
					#if($slot.prepend) prepend="${slot.prepend}" #end
					#if($slot.append) append="${slot.append}" #end
					#if($rowIndex) :rowIndex="${rowIndex}" #end
					#if($formData) :formData="${formData}" #end
					#if($templateJson) :templateJson="${templateJson}" #end
					#if($ableRelationIds) :ableRelationIds = "${ableRelationIds}" #end
					#if($startTime) :startTime="${startTime}" #end
					#if($endTime) :endTime="${endTime}" #end
					#if($config.isStorage) isStorage="${config.isStorage}" #end
					#if($placeholder) placeholder="${placeholder}" #end
					#if($html.selectType) selectType="${html.selectType}" #end
					#if($html.ableIds) :ableIds="ableAll.${showModel}${model}ableIds" #end
					#if($html.ableDepIds) :ableDepIds="ableAll.${showModel}${model}ableDepIds" #end
					#if($html.ablePosIds) :ablePosIds="ableAll.${showModel}${model}ablePosIds" #end
					#if($html.ableUserIds) :ableUserIds="ableAll.${showModel}${model}ableUserIds" #end
					#if($html.ableRoleIds) :ableRoleIds="ableAll.${showModel}${model}ableRoleIds" #end
					#if($html.ableGroupIds) :ableGroupIds="ableAll.${showModel}${model}ableGroupIds" #end
					#if($showWrite) $showWrite #end
					#if($html.optionType) optionType="${html.optionType}" #end
					#if($html.tipText) tipText="${html.tipText}" #end
					#if($html.total) :total="${html.total}" #end
					#if($html.precision) :precision="${html.precision}" #end
					#if($html.direction) direction="${html.direction}" #end
					#if($html.isAmountChinese) isAmountChinese #end
					#if($html.thousands) thousands #end
					#if($html.addonAfter) addonAfter="${html.addonAfter}" #end
					#if($html.addonBefore) addonBefore="${html.addonBefore}" #end
					#if($html.controlsPosition) controlsPosition="${html.controlsPosition}" #end
					#if($html.hasPage) hasPage #end
					#if($html.propsValue) propsValue="${html.propsValue}" #end
					#if($html.popupWidth) popupWidth="${html.popupWidth}" #end
					#if($html.popupTitle) popupTitle="${html.popupTitle}" #end
					#if($html.popupType) popupType="${html.popupType}" #end
					#if($relationField) ${relationField} #end
					#if($field) ${field} #end
					#if($html.showField) showField="${html.showField}" #end
					#if($html.modelId) modelId="${html.modelId}" #end
					#if($html.interfaceId) interfaceId="${html.interfaceId}" #end
					#if($html.pageSize) :pageSize="${html.pageSize}" #end
					#if($columnOptions) :columnOptions="${columnOptions}" #end
					#if($html.maxlength) :maxlength="${html.maxlength}" #end
					#if($html.readonly) readonly #end
					#if($html.clearable) clearable #end
					#if($html.prefixicon) prefix-icon='${html.prefixicon}' #end
					#if($html.suffixicon) suffix-icon='${html.suffixicon}' #end
					#if($html.style) :style='${html.style}'#end
					#if($html.showWordLimit) ${html.showWordLimit} #end
					#if($html.size) size="${html.size}" #end
					#if($html.min) :min="${html.min}" #end
					#if($html.max) :max="${html.max}" #end
					#if($html.type) type="${html.type}" #end
					#if($html.autosize) :autosize='${html.autosize}' #end
					#if($html.step) :step="${html.step}" #end
					#if($html.stepstrictly) stepstrictly #end
					#if($html.textStyle) :textStyle='${html.textStyle}' #end
					#if($html.lineHeight) :lineHeight="${html.lineHeight}" #end
					#if($html.fontSize) :fontSize="${html.fontSize}" #end
					#if($html.controlsposition) controls-position='${html.controlsposition}' #end
					#if($html.showChinese) :showChinese="${html.showChinese}" #end
					#if($html.showPassword) show-password #end
					#if($html.filterable) filterable #end
					#if($html.multiple) :multiple="${html.multiple}" #end
					#if($html.separator) separator="${html.separator}" #end
					#if($html.isrange) is-range #end
					#if($html.rangeseparator) range-separator="${html.rangeseparator}" #end
					#if($html.startplaceholder) start-placeholder="${html.startplaceholder}" #end
					#if($html.endplaceholder) end-placeholder="${html.endplaceholder}" #end
					#if($html.format) format="${html.format}" #end
					#if($html.colorformat) color-format="${html.colorformat}" #end
					#if($html.valueformat) valueFormat="${html.valueformat}" #end
					#if($html.activetext) active-text="${html.activetext}" #end
					#if($html.inactivetext) inactive-text="${html.inactivetext}" #end
					#if($html.activecolor) active-color="${html.activecolor}" #end
					#if($html.inactivecolor) inactive-color="${html.inactivecolor}" #end
					#if($html.activevalue) :active-value="${html.activevalue}" #end
					#if($html.inactivevalue) :inactive-value="${html.inactivevalue}" #end
					#if($html.pickeroptions) :picker-options='${html.pickeroptions}'#end
					#if($html.showScore) show-score #end
					#if($html.showText) show-text #end
					#if($html.allowhalf) allow-half #end
					#if($html.showAlpha) show-alpha #end
					#if($html.showStops) show-stops #end
					#if($html.range) range #end
					#if($html.showTip) showTip #end
					#if($html.accept) accept="${html.accept}" #end
					#if($fileSize) :fileSize="${fileSize}"#end
					#if($html.sizeUnit) sizeUnit="${html.sizeUnit}" #end
					#if($html.limit) :limit="${html.limit}" #end
					#if($html.pathType) pathType="${html.pathType}" #end
					#if($html.isAccount) :isAccount="${html.isAccount}" #end
					#if($html.folder) folder="${html.folder}" #end
					#if($html.buttonText) buttonText="${html.buttonText}" #end
					#if($html.contentposition) content-position="${html.contentposition}" #end
					#if($html.level) :level="${html.level}" #elseif(${html.level}==0) :level="${html.level}" #end
					#if($xhkey=='cascader' || $xhkey=='treeSelect' || $xhkey=='checkbox'||$xhkey=='radio'||$xhkey=='select') :options="${dataOptions}" :props="${childoptions}${model}Props" #end>
					#if($xhkey!='checkbox' && $xhkey!='radio' && $xhkey!='select')
						#if($html.slot.prepend)
						<template slot="prepend">${html.slot.prepend}</template>
						#end
						#if($html.slot.append)
						<template slot="append">${html.slot.append}</template>
						#end
					#end
					</${config.tag}>
					#if($tableTemplate)
					</template>
					#end
				</${item}>
				#if($showCol)
			</el-col>
				#end
	#end
#end
<template>
	<div>
		<el-row :gutter="${context.gutter}">
		<el-form ref="${context.formModel}" :model="${context.formModel}" :rules="dataRule" size="${context.size}" label-width="${context.labelWidth}px" label-position="${context.labelPosition}" #if($context.disabled == true ) :disabled="true" #else :disabled="setting.readonly" #end>
		<template v-if="!loading && formOperates">
		#foreach($fieLdsModel in ${context.form})
			#set($xhkey = "${fieLdsModel.xhKey}")
			#set($isEnd = "${fieLdsModel.isEnd}")
			#set($formModel = ${fieLdsModel.formModel})
			#set($config=$formModel.config)
			#set($span=$config.span)
			#set($outermost = ${formModel.outermost})
			#set($borderType = ${formModel.borderType})
			#set($borderColor = ${formModel.borderColor})
			#set($borderWidth = ${formModel.borderWidth})
			#if($xhkey=='row')
				#if(${config.pc}==true)
				#if(${isEnd}=='0')
			<el-col :span="${span}">
			<el-row :gutter="${context.gutter}">
				#else
			</el-row>
			</el-col>
					#end
				#end
			#elseif($xhkey=='card')
				#if(${config.pc}==true)
				#if(${isEnd}=='0')
			<el-card class="mb-20" shadow ="${formModel.shadow}" header="${formModel.header}">
					#if(${config.tipLabel} && ${formModel.header})
			<div slot="header">
				<span slot="label">${formModel.header}
					<el-tooltip placement="top" content='${config.tipLabel}'>
                  		<a class='el-icon-warning-outline'></a>
                	</el-tooltip>
				</span>
			</div>
					#end
				#else
			</el-card>
					#end
				#end
			#elseif($xhkey=='tab')
				#set($tabs = "el-tabs")
				#if(${outermost}=='1')
				#set($tabs = "el-tab-pane")
				#end
				#if(${config.pc}==true)
				#if(${isEnd}=='0')
					#if(${outermost}=='0')
			<el-col :span="${span}">
			<${tabs}  v-model="${formModel.model}" #if($formModel.type)type="${formModel.type}"#end tab-position="${formModel.tabPosition}" class="mb-20">
					#else
			<${tabs}  label="${formModel.title}">
					#end
				#else
					#if(${outermost}=='0')
			</${tabs}>
			</el-col>
					#else
			</${tabs} >
					#end
				#end
				#end
			#elseif($xhkey=='tableGrid' || $xhkey=='tableGridTd' || $xhkey=='tableGridTr')
				#set($tabs = "tbody")
				#set($tableGrid = "table")
				#if($xhkey=='tableGridTr')
					#set($tabs = "tr")
				#elseif($xhkey=='tableGridTd')
					#set($tabs = "")
                    #if(${config.merged}==false)
                    #set($tabs = "td")
                    #end
				#end
				#if(${config.pc}==true)
				    #if(${isEnd}=='0')
                        #if($xhkey=='tableGrid')
            <${tableGrid} class="table-grid-box" :style='{"--borderType":"${borderType}","--borderColor":"${borderColor}","--borderWidth":"${borderWidth}px"}'>
                        #end
                        #if($tabs)
            <${tabs}#if(${config.colspan}) colspan="${config.colspan}"#end#if(${config.rowspan}) rowspan="${config.rowspan}"#end>
                        #end
                    #else
                        #if($tabs)
            </${tabs}>
                        #end
                        #if($xhkey=='tableGrid')
            </${tableGrid}>
                        #end
				    #end
				#end
			#elseif($xhkey=='groupTitle' || $xhkey=='divider' || $xhkey=='XHText' || $xhkey=='button'|| $xhkey=='link' || $xhkey=='alert')
				#set($content=$formModel.content)
				#set($defaultName=${formModel.slot.defaultName})
				#if(${config.pc}==true)
			<el-col :span="${span}" >
			<xh-form-tip-item>
			<${config.tag} #if($formModel.style) :style='${formModel.style}'#end
			#if($formModel.title) title='${formModel.title}'#end
			#if($formModel.description) description="${formModel.description}" #end
			#if($formModel.closeText) closeText="${formModel.closeText}" #end
			#if($formModel.tagIcon) tagIcon='${formModel.tagIcon}'#end
			#if($formModel.showIcon) showIcon #end
			#if($formModel.closable) closable #end
			#if($formModel.target) target='${formModel.target}'#end
			#if($formModel.href) href='${formModel.href}'#end
			#if($formModel.buttonText) buttonText='${formModel.buttonText}'#end
			#if($formModel.align) align='${formModel.align}'#end
			#if($formModel.type) type='${formModel.type}'#end
			#if($formModel.textStyle) :textStyle='${formModel.textStyle}'#end
			#if($config.defaultValue) value="${config.defaultValue}"#end
			#if($formModel.tipLabel) tipLabel="${formModel.tipLabel}"#end
			#if($content) content="${content}"#end
			#if($formModel.contentposition)  content-position="${formModel.contentposition}" #end>
			#if(${defaultName})
			$!{defaultName}
			#end
			</${config.tag}>
			</xh-form-tip-item>
			</el-col>
				#end
			#elseif($xhkey=='collapse')
				#set($collapse = "el-collapse")
				#if(${outermost}=='1')
					#set($collapse = "el-collapse-item")
				#end
				#if(${config.pc}==true)
				#if(${isEnd}=='0')
					#if(${outermost}=='0')
			<el-col :span="${span}">
			<${collapse} :accordion="${formModel.accordion}" v-model="${formModel.model}" class="mb-20">
					#else
			<${collapse} title="${formModel.title}" name="${formModel.name}">
					#end
				#else
					#if(${outermost}=='0')
			</${collapse}>
			</el-col>
					#else
			</${collapse}>
					#end
				#end
				#end
			#elseif($xhkey=='mast')
				#set($html = $fieLdsModel.formColumnModel.fieLdsModel)
				#set($formModel = "${context.formModel}")
				#set($showModel="")
				#form('mast' $html $formModel $showModel)
			#elseif($xhkey=='mastTable')
				#set($mastTableModel = $fieLdsModel.formMastTableModel)
				#set($html = $mastTableModel.mastTable.fieLdsModel)
				#set($formModel="${context.formModel}")
				#set($showModel="")
				#form('mastTable' $html $formModel $showModel)
			#elseif($xhkey=='table')
			#set($child = $fieLdsModel.childList)
			#set($showSummary = ${child.showSummary})
			#set($className = "")
			#foreach($children in ${context.children})
				#if(${children.tableModel}==${child.tableModel})
				#set($className = "${children.className.substring(0,1).toLowerCase()}${children.className.substring(1).toLowerCase()}")
				#end
			#end
			#set($showJudge=
				"v-if="""+"judgeShow('"+${className}+"List')"+"""")
			#set($showWrite="")
			#set($showTableWrite="!judgeWrite('"+${className}+"List')""")
			#set($showTableJudge= "v-if="""+${showTableWrite}+"")
			<el-col :span="${child.span}"  #if(${showJudge}) ${showJudge} #end>
			<xh-form-tip-item label-width="0">
					#if($child.showTitle== true)
			<div class="XH-common-title">
			<h2>${child.label}#if($child.tipLabel)<el-tooltip placement="top" content='${child.tipLabel}'><a class='el-icon-warning-outline'></a></el-tooltip>#end</h2>
			</div>
					#end
			<el-table :data="${context.formModel}.${className}List" size='mini' #if($showSummary==true) show-summary :summary-method="${className}Summaries"#end>
			<el-table-column type="index" width="50" label="序号" align="center" />
						#foreach($childListAll in ${child.childList})
							#set($html = $childListAll.fieLdsModel)
							#set($formModel="${context.formModel}.${className}List[i]")
							#set($showModel="${className}")
							#form('table' $html $formModel $showModel)
						#end
			<el-table-column label="操作" width="50"  #if($showTableJudge) $showTableJudge#end>
			<template slot-scope="scope">
			<el-button size="mini" type="text" class="XH-table-delBtn" @click="del${className}List(scope.$index)">删除</el-button>
			</template>
			</el-table-column>
			</el-table>
			<div class="table-actions" @click="add${className}List()" #if($showTableJudge) $showTableJudge#end>
			<el-button type="text" icon="el-icon-plus">$!{child.actionText}</el-button>
			</div>
			</xh-form-tip-item>
			</el-col>
			#end
		#end
		</template>
		</el-form>
		</el-row>
		<UserBox v-if="userBoxVisible" ref="userBox" @submit="submit" />
		<SelectDialog v-if="selectDialogVisible" :config="addTableConf" :formData="dataForm" ref="selectDialog" @select="addForSelect" @close="selectDialogVisible=false"/>
	</div>
</template>
#macro(appableAll $fieLdsModel,$childList)
	#set($html = $fieLdsModel)
	#set($vModel = "${childList}${html.vModel}")
	#set($config = $html.config)
	#set($dataType = "${config.dataType}")
	#if(${html.ableIds})
		${vModel}ableIds:${html.ableIds},
	#end
	#if(${html.ableDepIds})
		${vModel}ableDepIds:${html.ableDepIds},
	#end
	#if(${html.ablePosIds})
		${vModel}ablePosIds:${html.ablePosIds},
	#end
	#if(${html.ableUserIds})
		${vModel}ableUserIds:${html.ableUserIds},
	#end
	#if(${html.ableRoleIds})
		${vModel}ableRoleIds:${html.ableRoleIds},
	#end
	#if(${html.ableGroupIds})
		${vModel}ableGroupIds:${html.ableGroupIds},
	#end
#end
#macro(formRule $fieLdsModel)
	#set($html = $fieLdsModel)
	#set($vModel = "${html.vModel}")
	#set($config = $html.config)
	#set($label = "${config.label}")
	#set($listSize=$!{config.regList})
	#set($defaultValue=${config.defaultValue})
	#set($defaultValueSize=$!{config.defaultValue})
	#set($trigger = ${config.trigger})
	#if(${trigger.substring(0,1)}!='[')
		#set($trigger = "'"+ ${config.trigger}+ "'")
	#end
	#if(!$config.defaultValue && $config.defaultValue==[])
		#set($messages='请至少选择一个')
	#elseif(${config.defaultValue} && (${defaultValueSize} || $defaultValueSize.size()>0))
		#set($messages='请至少选择一个')
	#elseif($html.placeholder)
		#set($messages=${html.placeholder})
	#else
		#set($messages='不能为空')
	#end
	#if($vModel)
		#if($config.required==true|| (${listSize} && $listSize.size()>0))
					${vModel}: [
			#if($config.required==true)
						{
							required: true,
							message: '${label}$!{messages}',
							trigger: ${trigger}
						},
			#end
			#if($listSize.size()>0)
				#foreach($regList in ${config.regList})
						{
							pattern: ${regList.pattern},
							message: '${label}${regList.message}',
							trigger: ${trigger}
						},
				#end
			#end
					],
		#end
	#end
#end
#macro(list $fieLdsModel,$childList)
	#set($html = $fieLdsModel)
	#set($vModel = "${childList}${html.vModel}")
	#set($config = $html.config)
	#set($xhkey = "$config.xhKey")
	#if(${config.dataType}=='dictionary'||${config.dataType}=='dynamic')
				${vModel}Options:[],
	#elseif(${config.dataType} == "static")
		#if($html.slot.options)
                ${vModel}Options:${html.slot.options},
		#elseif($html.options)
                ${vModel}Options:${html.options},
		#end
	#end
	#if($html.props)
				#set($propsModel = ${html.props.props})
				${vModel}Props:{"label":"${propsModel.label}","value":"${propsModel.value}","multiple":${propsModel.multiple},"children":"${propsModel.children}"},
	#end
	#if($config.props)
				${vModel}Props:{"label":"${config.props.label}","value":"${config.props.value}"},
	#end
	#if($xhkey=='relationForm' || $xhkey=='popupSelect'|| $xhkey=='popupTableSelect')
				${vModel}ColumnOptions:[
		#foreach($columnOptions in  ${html.columnOptions})
					{
						"label":"${columnOptions.label}",
						"value":"${columnOptions.value}"
					},
		#end
				],
	#end
#end
#macro(options $fieLdsModel,$childList)
	#set($html = $fieLdsModel)
	#set($vModel = "${childList}${html.vModel}")
	#set($config = $html.config)
	#set($dataType = "${config.dataType}")
	#if(${dataType}=='dictionary' || ${dataType}=='dynamic')
			    this.get${vModel}Options()
	#end
#end
#macro(optionsData $fieLdsModel,$childList)
	#set($html = $fieLdsModel)
	#set($vModel = "${childList}${html.vModel}")
	#set($config = $html.config)
	#set($dataType = "${config.dataType}")
	#set($xhkey="${config.xhKey}")
	#set($changeDataFormType = "1")
	#set($changeDataFormData = "${html.vModel}")
	#if($childList)
		#set($changeDataFormData = "${childList}"+"List")
		#set($changeDataFormType = "2")
	#end
	#set($defaultValue='""')
	#if($!config.valueType=='String')
		#set($defaultValue="'$!{config.defaultValue}'")
	#elseif($!config.valueType=='undefined')
		#set($defaultValue='""')
	#else
		#set($defaultValue=$!{config.defaultValue})
	#end
	#if(${dataType}=='dictionary')
            get${vModel}Options() {
                getDictionaryDataSelector('${config.dictionaryType}').then(res => {
                    this.${vModel}Options = res.data.list
                })
            },
	#elseif(${dataType}=='dynamic')
            get${vModel}Options() {
				const index = this.childIndex
				let templateJsonList = JSON.parse(JSON.stringify(this.interfaceRes.${vModel}))
				for (let i = 0; i < templateJsonList.length; i++) {
					let json = templateJsonList[i];
					if(json.relationField){
						let relationFieldAll = json.relationField.split("-");
						let val = json.defaultValue;
						if(relationFieldAll.length>1 && index>-1){
							val = this.${context.formModel}[relationFieldAll[0]+'List']&&this.${context.formModel}[relationFieldAll[0]+'List'].length?this.${context.formModel}[relationFieldAll[0]+'List'][index][relationFieldAll[1]]:''
						}else {
							val = this.${context.formModel}[relationFieldAll]
						}
						json.defaultValue = val
					}
				}
				let template ={
					paramList:templateJsonList
				}
				getDataInterfaceRes('${config.propsUrl}',template).then(res => {
					let data = res.data
					this.${vModel}Options = data
					#if($childList)
					if(index==-1) return
					this.${context.formModel}.${childList}List[index].${html.vModel}Options =data
					#end
					this.changeDataFormData(${changeDataFormType},'${changeDataFormData}','${html.vModel}',index,${defaultValue})
                })
            },
	#elseif(${html.vModel})
			get${vModel}Options() {
				const index = this.childIndex
				this.changeDataFormData(${changeDataFormType},'${changeDataFormData}','${html.vModel}',index,${defaultValue})
			},
	#end
#end
#macro(faceRes $fieLdsModel,$childList)
	#set($html = $fieLdsModel)
	#set($vModel = "${childList}${html.vModel}")
	#set($config = $html.config)
	#set($dataType = "${config.dataType}")
					${vModel}:[
						#foreach($templateJson in ${config.templateJson})
						{
							fieldName:"${templateJson.fieldName}",
							field:"${templateJson.field}",
							defaultValue:"${templateJson.defaultValue}",
							xhKey:"$!{templateJson.xhKey}",
							dataType:"${templateJson.dataType}",
							id:"${templateJson.id}",
							required:"${templateJson.required}",
							relationField:"${templateJson.relationField}",
						},
						#end
					],
#end
#macro(dataList $key $fieLdsModel $formModel $tableModel $fieldModel $vModel)
	#set($model="${formModel}.${vModel}")
	#set($field="${formModel}.${fieldModel}")
	#if($key=='mastTable')
		#set($field="${formModel}.${tableModel}.${fieldModel}")
	#end
	#set($html = $fieLdsModel)
	#set($config = $html.config)
	#set($xhkey = "${config.xhKey}")
	#if($xhkey=='checkbox' || $xhkey=='timeRange' || $xhkey=='dateRange' || $xhkey=='address' || $xhkey=='cascader')
				${field} = Array.isArray(${model})? JSON.stringify(${model}):'[]'
	#elseif($xhkey=='comSelect' || $xhkey=="uploadFz" || $xhkey=="uploadImg")
				${field} = Array.isArray(${model})? JSON.stringify(${model}):'[]'
	#elseif($xhkey=='userSelect' || $xhkey=='usersSelect' || $xhkey=='select' || $xhkey=='depSelect' || $xhkey=='posSelect' || $xhkey=='popupTableSelect'|| $xhkey=='groupSelect'|| $xhkey=='roleSelect' || $xhkey=='treeSelect')
		#if(${html.multiple}=='true')
				${field} = Array.isArray(${model})? JSON.stringify(${model}):'[]'
		#else
				${field} = ${model}
		#end
	#else
		#if($key=='mastTable')
				${field} = ${model}
		#end
	#end
#end
#macro(dataInfo $key $fieLdsModel $formModel $tableModel $fieldModel $vModel)
	#set($model="${formModel}.${vModel}")
	#set($field="${formModel}.${fieldModel}")
	#if($key=='mastTable')
		#set($field="${formModel}.${tableModel}.${fieldModel}")
	#end
	#set($html = $fieLdsModel)
	#set($config = $html.config)
	#set($xhkey = "${config.xhKey}")
	#if($xhkey=='checkbox' || $xhkey=='timeRange' || $xhkey=='dateRange' || $xhkey=='address' || $xhkey=='cascader')
				${model} = ${field}? JSON.parse(${field}):[]
	#elseif($xhkey=='comSelect' || $xhkey=="uploadFz" || $xhkey=="uploadImg")
				${model} = ${field}? JSON.parse(${field}):[]
	#elseif($xhkey=='userSelect' ||  $xhkey=='usersSelect' ||$xhkey=='select' || $xhkey=='depSelect' || $xhkey=='posSelect' || $xhkey=='popupTableSelect'|| $xhkey=='groupSelect'|| $xhkey=='roleSelect' || $xhkey=='treeSelect')
		#if(${html.multiple}=='true')
				${model} = ${field}? JSON.parse(${field}):[]
		#else
				${model} = ${field}
		#end
	#elseif($xhkey=="switch" || $xhkey=="slider")
				${model} = ${field}? Number(${field}):0
	#elseif($xhkey=="date")
				${model} = ${field}? Number(${field}):${field}
	#else
		#if($key=='mastTable')
				${model} = ${field}
		#end
	#end
#end
#macro(formInitDefaultData $key $fieLdsModel $formModel)
	#set($html = $fieLdsModel)
	#set($eachFiled = "${html.vModel}")
	#set($config = $html.config)
	#set($xhkey = "${config.xhKey}")
	#set($defaultCurrent=${config.defaultCurrent})
	#set($multiple=${html.multiple})
	#set($selectType = ${config.xhKey})
	#set($ableDepIds = ${html.ableDepIds})
	#set($ableUserIds = ${html.ableUserIds})
	#set($ableGroupIds = ${html.ableGroupIds})
	#set($ableRoleIds = ${html.ableRoleIds})
	#set($ablePosIds = ${html.ablePosIds})
	#set($format=${html.format})
	#set($timeFormat = "this.xh.toDate")
	#if($defaultCurrent)
		#if($xhkey=='date')
			${formModel}.${eachFiled} = new Date().getTime()
		#elseif($xhkey=='time')
			${formModel}.${eachFiled} = ${timeFormat}(new Date(),"${format}")
		#elseif($xhkey=='depSelect')
			if(this.userInfo.departmentId != null && this.userInfo.departmentId != '') {
				#if($selectType=='all')
					#if($multiple == true)
				${formModel}.${eachFiled} = [this.userInfo.departmentId]
					#else
				${formModel}.${eachFiled} = this.userInfo.departmentId
					#end
				#else
				let ableDepIds = ${ableDepIds}
				if (ableDepIds instanceof Array && ableDepIds.length > 0 && ableDepIds.includes(this.userInfo.departmentId)) {
					#if($multiple == true)
					${formModel}.${eachFiled} = [this.userInfo.departmentId]
					#else
					${formModel}.${eachFiled} = this.userInfo.departmentId
					#end
				} else if(ableDepIds instanceof Array && ableDepIds.length > 0) {
					getDefaultCurrentValueDepartmentId({departIds:ableDepIds}).then(res => {
						if (res.data.departmentId != null && res.data.departmentId != '') {
					#if($multiple == true)
							${formModel}.${eachFiled} = [this.userInfo.departmentId]
					#else
							${formModel}.${eachFiled} = this.userInfo.departmentId
					#end
						}
					})
				}
				#end
			}
		#elseif($xhkey=='comSelect')
			if(this.userInfo.organizeIdList instanceof Array && this.userInfo.organizeIdList.length > 0) {
				#if($multiple == true)
				${formModel}.${eachFiled} = [this.userInfo.organizeIdList]
				#else
				${formModel}.${eachFiled} = this.userInfo.organizeIdList
				#end
			}
		#elseif($xhkey=='userSelect')
			#if($selectType=='all')
				#if($multiple == true)
			${formModel}.${eachFiled} = [this.userInfo.userId]
				#else
			${formModel}.${eachFiled} = this.userInfo.userId
				#end
			#elseif($selectType=='custom')
			if(this.userInfo.userId != null && this.userInfo.userId != '') {
				let ableUserIds = ${ableUserIds}
				let ableDepIds = ${ableDepIds}
				let ableGroupIds = ${ableGroupIds}
				let ableRoleIds = ${ableRoleIds}
				let ablePosIds = ${ablePosIds}
				if (ableUserIds instanceof Array && ableUserIds.length > 0 && ableUserIds.includes(this.userInfo.userId)) {
					#if($multiple == true)
					${formModel}.${eachFiled} = [this.userInfo.userId]
					#else
					${formModel}.${eachFiled} = this.userInfo.userId
					#end
				}else if((ableUserIds instanceof Array && ableUserIds.length > 0)
					|| (ableDepIds instanceof Array && ableDepIds.length > 0)
					|| (ableGroupIds instanceof Array && ableGroupIds.length > 0)
					|| (ableRoleIds instanceof Array && ableRoleIds.length > 0)
					|| (ablePosIds instanceof Array && ablePosIds.length > 0)) {
					getDefaultCurrentValueUserId({
						departIds:ableDepIds,
						groupIds:ableGroupIds,
						roleIds:ableRoleIds,
						userIds:ableUserIds,
						positionIds:ablePosIds
					}).then(res => {
						if (res.data.userId != null && res.data.userId != '') {
							#if($multiple == true)
							${formModel}.${eachFiled} = [this.userInfo.userId]
							#else
							${formModel}.${eachFiled} = this.userInfo.userId
							#end
						}
					})
				}
			}
			#end
		#end
	#end
#end

<script>
    import {mapGetters} from "vuex";
    import SelectDialog from '@/components/SelectDialog'
    import comMixin from '@/views/workFlow/workFlowForm/mixin';
    import {
        getBeforeData,
        getBeforeTime,
        getDateDay,
        getLaterData,
        getLaterTime
    } from '@/components/Generator/utils/index.js'
    import {thousandsFormat} from "@/components/Generator/utils/index"

    export default {
		mixins: [comMixin],
		components: {SelectDialog},
		props: [],
		data() {
			return {
				selectDialogVisible: false,
				tableKey: '',
				addTableConf:{},
				loading: false,
				visible: false,
				setting:{},
				eventType: '',
				userBoxVisible:false,
      			tableRequiredData: {},
				${context.formModel}: {
					${pKeyName}:'',
					flowId: '',
					#foreach($fieLdsModel in ${context.fields})
						#set($html = $fieLdsModel.formColumnModel.fieLdsModel)
						#set($vModel = "${html.vModel}")
						#set($config = $html.config)
						#if($vModel)
							#if($!config.valueType=='String')
					$!{vModel} : "$!{config.defaultValue}",
							#elseif($!config.valueType=='undefined')
					$!{vModel} : '',
							#else
					$!{vModel} : $!{config.defaultValue},
							#end
						#end
					#end
					#foreach($masetkey in $mastTableList.entrySet())
						#set($tableModel = $masetkey.key)
					$tableModel:{
						#set($fieldsAll = $masetkey.value)
						#foreach($fieLdsModel in ${fieldsAll})
							#set($mastTableModel = $fieLdsModel.formMastTableModel)
							#set($html = $fieLdsModel.formMastTableModel.mastTable.fieLdsModel)
							#set($vModel = "${mastTableModel.field}")
							#set($config = $html.config)
							#if($vModel)
								#if($!config.valueType=='String')
						$!{vModel} : "$!{config.defaultValue}",
								#elseif($!config.valueType=='undefined')
						$!{vModel} : '',
								#else
						$!{vModel} : $!{config.defaultValue},
								#end
							#end
						#end
					},
					#set($fieldsAll = $masetkey.value)
						#foreach($fieLdsModel in ${fieldsAll})
							#set($mastTableModel = $fieLdsModel.formMastTableModel)
							#set($html = $fieLdsModel.formMastTableModel.mastTable.fieLdsModel)
							#set($vModel = "${mastTableModel.vModel}")
							#set($config = $html.config)
							#if($vModel)
								#if($!config.valueType=='String')
					$!{vModel} : "$!{config.defaultValue}",
								#elseif($!config.valueType=='undefined')
					$!{vModel} : '',
								#else
					$!{vModel} : $!{config.defaultValue},
								#end
							#end
						#end
					#end
					#foreach($child in ${context.children})
						#set($className = "${child.className.toLowerCase()}")
					${className}List:[],
					#end
				},
				#foreach($fieLdsModel in ${context.form})
					#set($xhkey = "${fieLdsModel.xhKey}")
					#set($formModel = ${fieLdsModel.formModel})
					#set($outermost = ${formModel.outermost})
					#set($isEnd = "${fieLdsModel.isEnd}")
					#if(${isEnd}=='0')
						#if($xhkey=='collapse')
							#if(${outermost}=='0')
				${formModel.model}:${formModel.active},
							#end
						#end
						#if($xhkey=='tab')
							#if(${outermost}=='0')
				${formModel.model}:'${formModel.active}',
							#end
						#end
					#end
				#end
				dataRule:
				{
					#foreach($fields in ${context.fields})
						#set($fieLdsModel = $fields.formColumnModel.fieLdsModel)
						#formRule($fieLdsModel)
					#end
                    #foreach($masetkey in $mastTableList.entrySet())
						#set($fieldsAll = $masetkey.value)
						#foreach($fields in ${fieldsAll})
							#set($fieLdsModel = $fields.formMastTableModel.mastTable.fieLdsModel)
							#formRule($fieLdsModel)
						#end
					#end
				},
				#foreach($fields in ${context.fields})
					#set($fieLdsModel = $fields.formColumnModel.fieLdsModel)
					#list($fieLdsModel,'')
				#end
				#foreach($masetkey in $mastTableList.entrySet())
					#set($fieldsAll = $masetkey.value)
					#foreach($fields in ${fieldsAll})
						#set($fieLdsModel = $fields.formMastTableModel.mastTable.fieLdsModel)
						#list($fieLdsModel,'')
					#end
				#end
				#foreach($child in ${context.children})
					#set($className = "${child.className.toLowerCase()}")
					#foreach($childList in ${child.childList})
						#set($fieLdsModel = $childList.fieLdsModel)
						#list($fieLdsModel,$className)
					#end
				#end
				interfaceRes:{
					#foreach($html in ${context.fields})
						#set($fieLdsModel = $html.formColumnModel.fieLdsModel)
						#faceRes($fieLdsModel,'')
					#end
					#foreach($masetkey in $mastTableList.entrySet())
						#set($fieldsAll = $masetkey.value)
						#foreach($html in ${fieldsAll})
							#set($fieLdsModel = $html.formMastTableModel.mastTable.fieLdsModel)
							#faceRes($fieLdsModel,'')
						#end
					#end
					#foreach($child in ${context.children})
						#set($className = "${child.className.toLowerCase()}")
						#foreach($childList in ${child.childList})
							#set($fieLdsModel = $childList.fieLdsModel)
							#faceRes($fieLdsModel,$className)
						#end
					#end
				},
				regList:{
					#foreach($child in ${context.children})
						#set($className = "${child.className.toLowerCase()}")
					${className}List:{
							#foreach($html in ${child.childList})
								#set($fieLdsModel = ${html.fieLdsModel})
								#set($vModel = "${fieLdsModel.vModel}")
								#set($config = ${fieLdsModel.config})
								#set($listSize=$!{config.regList})
								#set($label = "${config.label}")
								#if($vModel)
						${vModel}: [
									#if($listSize.size()>0)
										#foreach($regList in ${config.regList})
							{
								pattern: ${regList.pattern},
								message: '${label}${regList.message}',
							},
										#end
									#end
						],
								#end
							#end
					},
					#end
				},
				ableAll:{
					#foreach($html in ${context.fields})
						#set($fieLdsModel = $html.formColumnModel.fieLdsModel)
						#appableAll($fieLdsModel,'')
					#end
					#foreach($masetkey in $mastTableList.entrySet())
						#set($fieldsAll = $masetkey.value)
						#foreach($html in ${fieldsAll})
							#set($fieLdsModel = $html.formMastTableModel.mastTable.fieLdsModel)
							#appableAll($fieLdsModel,'')
						#end
					#end
					#foreach($child in ${context.children})
						#set($className = "${child.className.toLowerCase()}")
						#foreach($childList in ${child.childList})
							#set($fieLdsModel = $childList.fieLdsModel)
							#appableAll($fieLdsModel,$className)
						#end
					#end
				},
				childIndex:-1,
				dataValue:{},
				isEdit:false,
			}
		},
		computed: {
		    ...mapGetters(['userInfo']),
    		formOperates() {
      			return this.setting.formOperates;
    		}
  		},
        watch: {},
		created() {
			this.dataValue = JSON.parse(JSON.stringify(this.${context.formModel}))
        },
		mounted() {},
		methods: {
			changeData(model, index) {
				this.isEdit = false
				this.childIndex = index
				let modelAll = model.split("-");
				let faceMode = "";
				for (let i = 0; i < modelAll.length; i++) {
					faceMode += modelAll[i];
				}
				for (let key in this.interfaceRes) {
					if (key != faceMode) {
						let faceReList = this.interfaceRes[key]
						for (let i = 0; i < faceReList.length; i++) {
							if (faceReList[i].relationField == model) {
								let options = 'get' + key + 'Options';
								if(this[options]){
									this[options]()
								}
								this.changeData(key, index)
							}
						}
					}
				}
			},
			changeDataFormData(type, data, model,index,defaultValue) {
				if(!this.isEdit) {
					if (type == 2) {
						for (let i = 0; i < this.${context.formModel}[data].length; i++) {
							if (index == -1) {
								this.${context.formModel}[data][i][model] = defaultValue
							} else if (index == i) {
								this.${context.formModel}[data][i][model] = defaultValue
							}
						}
					} else {
						this.${context.formModel}[data] = defaultValue
					}
				}
			},
			goBack() {
				this.$emit('refresh')
			},
			selfInit(){
				this.dataAll()
			},
            //初始化默认数据
            initDefaultData() {
				#foreach($html in ${context.fields})
					#set($fieLdsModel = $html.formColumnModel.fieLdsModel)
					#set($formModel='this.'+${context.formModel})
					#formInitDefaultData('mast',$fieLdsModel,${formModel})
				#end
				#foreach($masetkey in $mastTableList.entrySet())
					#set($fieldsAll = $masetkey.value)
					#foreach($html in ${fieldsAll})
						#set($fieLdsModel = $html.formMastTableModel.mastTable.fieLdsModel)
						#set($formModel='this.'+${context.formModel})
						#formInitDefaultData('mastTable',$fieLdsModel,${formModel})
					#end
				#end
            },
			selfGetInfo(dataForm){
				this.dataInfo(dataForm)
			},
			beforeSubmit(){
				const _data =this.dataList()
				return _data
			},
			exist() {
				let title=[]
				let isOk = true
				let _regList = this.regList
				for (let k in _regList) {
					for(let n in _regList[k]){
						for(let i=0;i<_regList[k][n].length;i++){
							let childData = this.${context.formModel}[k]
							childData.forEach((item, index) => {
								if(item[n] && !eval(_regList[k][n][i].pattern).test(item[n])){
									title.push(_regList[k][n][i].message)
								}
							})
						}
					}
				}
				if (title.length > 0) {
					this.$message({
						message: title[0],
						type: 'error',
						duration: 1000
					})
					isOk = false
				}
				return isOk;
			},
			dataAll(){
			    #foreach($html in ${context.fields})
			        #set($fieLdsModel = $html.formColumnModel.fieLdsModel)
			        #options($fieLdsModel,'')
			    #end
				#foreach($masetkey in $mastTableList.entrySet())
					#set($fieldsAll = $masetkey.value)
					#foreach($html in ${fieldsAll})
						#set($fieLdsModel = $html.formMastTableModel.mastTable.fieLdsModel)
						#options($fieLdsModel,'')
					#end
				#end
			    #foreach($child in ${context.children})
					#set($className = "${child.className.toLowerCase()}")
			        #foreach($childList in ${child.childList})
			            #set($fieLdsModel = $childList.fieLdsModel)
						#options($fieLdsModel,$className)
			        #end
			    #end
				this.initDefaultData()
			},
			dateTime(timeRule, timeType, timeTarget, timeValueData, dataValue) {
			  let timeDataValue = null;
			  let timeValue = Number(timeValueData)
			  if (timeRule) {
				if (timeType == 1) {
				  timeDataValue = timeValue
				} else if (timeType == 2) {
				  timeDataValue = dataValue
				} else if (timeType == 3) {
				  timeDataValue = new Date().getTime()
				} else if (timeType == 4) {
				  let previousDate = '';
				  if (timeTarget == 1 || timeTarget == 2) {
					previousDate = getDateDay(timeTarget, timeType, timeValue)
					timeDataValue = new Date(previousDate).getTime()
				  } else if (timeTarget == 3) {
					previousDate = getBeforeData(timeValue)
					timeDataValue = new Date(previousDate).getTime()
				  } else {
					timeDataValue = getBeforeTime(timeTarget, timeValue).getTime()
				  }
				} else if (timeType == 5) {
				  let previousDate = '';
				  if (timeTarget == 1 || timeTarget == 2) {
					previousDate = getDateDay(timeTarget, timeType, timeValue)
					timeDataValue = new Date(previousDate).getTime()
				  } else if (timeTarget == 3) {
					previousDate = getLaterData(timeValue)
					timeDataValue = new Date(previousDate).getTime()
				  } else {
					timeDataValue = getLaterTime(timeTarget, timeValue).getTime()
				  }
				}
			  }
			  return timeDataValue;
			},
			time(timeRule, timeType, timeTarget, timeValue, formatType, dataValue) {
			  let format = formatType == 'HH:mm' ? 'HH:mm:00' : formatType
			  let timeDataValue = null
			  if (timeRule) {
				if (timeType == 1) {
				  timeDataValue = timeValue || '00:00:00'
				  if (timeDataValue.split(':').length == 3) {
					timeDataValue = timeDataValue
				  } else {
					timeDataValue = timeDataValue + ':00'
				  }
				} else if (timeType == 2) {
				  timeDataValue = dataValue
				} else if (timeType == 3) {
				  timeDataValue = this.xh.toDate(new Date(), format)
				} else if (timeType == 4) {
				  let previousDate = '';
				  previousDate = getBeforeTime(timeTarget, timeValue)
				  timeDataValue = this.xh.toDate(previousDate, format)
				} else if (timeType == 5) {
				  let previousDate = '';
				  previousDate = getLaterTime(timeTarget, timeValue)
				  timeDataValue = this.xh.toDate(previousDate, format)
				}
			  }
			  return timeDataValue;
			},
            #foreach($fields in ${context.fields})
                #set($fieLdsModel = $fields.formColumnModel.fieLdsModel)
				#optionsData($fieLdsModel,'')
            #end
			#foreach($masetkey in $mastTableList.entrySet())
				#set($fieldsAll = $masetkey.value)
				#foreach($fields in ${fieldsAll})
					#set($fieLdsModel = $fields.formMastTableModel.mastTable.fieLdsModel)
					#optionsData($fieLdsModel,'')
				#end
			#end
            #foreach($child in ${context.children})
				#set($className = "${child.className.toLowerCase()}")
				#set($showSummary = ${child.showSummary})
				#set($summaryField = ${child.summaryField})
				#if($showSummary==true)
			${className}Summaries(param) {
				const summaryField = ${summaryField}
				const { columns, data } = param;
				const sums = [];
				columns.forEach((column, index) => {
					if (index === 0) {
						sums[index] = '合计';
						return;
					}
					if (!summaryField.includes(column.property)) {
						sums[index] = '';
						return;
					}
					const values = data.map(item => Number(item[column.property]));
					if (!values.every(value => isNaN(value))) {
						sums[index] = values.reduce((prev, curr) => {
							const value = Number(curr);
							if (!isNaN(value)) {
								return prev + curr;
							} else {
								return prev;
							}
						}, 0).toFixed(2);
						const thousandsField = ${child.thousandsField};
						if(thousandsField.includes(column.property)){
							sums[index] = thousandsFormat(sums[index]);
						}
					} else {
						sums[index] = '';
					}
				});
				return sums
			},
				#end
                #foreach($childList in ${child.childList})
					#set($className = "${child.className.toLowerCase()}")
                    #set($fieLdsModel = $childList.fieLdsModel)
					#optionsData($fieLdsModel,$className)
                #end
            #end
			#foreach($child in ${context.children})
				#set($className = "${child.className.toLowerCase()}")
			add${className}List(){
				this.tableKey = '${className}List';
				#if($child.addType==1)
            	let _addTableConf =${child.addTableConf}
            	this.addTableConf = _addTableConf
				this.selectDialogVisible = true
      			this.$nextTick(() => {
      				#set($conf='$'+"refs.selectDialog.init()")
        			this.${conf}
      			})
				return
            	#end
				let item = {
					#foreach($childData in ${child.childList})
						#set($fieLdsModel = ${childData.fieLdsModel})
                        #set($vModel = "${fieLdsModel.vModel}")
						#set($config = ${fieLdsModel.config})
						#set($dataType = "${config.dataType}")
						#if($vModel)
							#if($!config.valueType=='String')
					$!{vModel} : "$!{config.defaultValue}",
							#elseif($!config.valueType=='undefined')
					$!{vModel} : '',
							#else
					$!{vModel} : $!{config.defaultValue},
							#end
							#if(${dataType}=='dictionary' || ${dataType}=='dynamic')
					${vModel}Options:[],
							#end
						#end
                    #end
				}
				this.${context.formModel}.${className}List.push(item)
				#foreach($childData in ${child.childList})
					#set($fieLdsModel = ${childData.fieLdsModel})
					#set($formModel='this.'+${context.formModel}+"."+${className}+"List[this."+${context.formModel}+"."+${className}+"List.length - 1]")
					#formInitDefaultData('table',$fieLdsModel,${formModel})
				#end
				this.childIndex=this.${context.formModel}.${className}List.length-1
					#foreach($childList in ${child.childList})
						#set($fieLdsModel = $childList.fieLdsModel)
						#set($vModel = "${fieLdsModel.vModel}")
						#set($field = "${fieLdsModel.vModel}")
						#set($config = ${fieLdsModel.config})
						#set($dataType = "${config.dataType}")
						#if(${dataType}=='dynamic')
				this.get${className}${vModel}Options()
						#end
					#end
				this.childIndex = -1
			},
			del${className}List(index) {
			    this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
                  type: 'warning'
                }).then(() => {
				  this.${context.formModel}.${className}List.splice(index, 1);
				  }).catch(() => {
                });
			},
			#end
            dataList(){
				var _data = JSON.parse(JSON.stringify(this.${context.formModel}));
				#foreach($html in ${context.fields})
					#set($fieLdsModel = $html.formColumnModel.fieLdsModel)
					#set($vModel = "${fieLdsModel.vModel}")
					#set($field = "${fieLdsModel.vModel}")
					#set($formModel = "_data")
					#set($tableModel = "")
					#if($vModel)
						#dataList('mast' $fieLdsModel $formModel $tableModel $field $vModel)
					#end
				#end
				#foreach($masetkey in $mastTableList.entrySet())
					#set($tableModel = $masetkey.key)
					#set($fieldsAll = $masetkey.value)
					#foreach($html in ${fieldsAll})
						#set($mastTableModel = $html.formMastTableModel)
						#set($fieLdsModel = $mastTableModel.mastTable.fieLdsModel)
						#set($vModel = "${mastTableModel.vModel}")
						#set($field = "${mastTableModel.field}")
						#set($formModel = "_data")
						#if($vModel)
							#dataList('mastTable' $fieLdsModel $formModel $tableModel $field $vModel)
						#end
					#end
				#end
				#foreach($child in ${context.children})
					#set($className = "${child.className.toLowerCase()}")
				for(let i=0;i<_data.${className}List.length;i++){
				var _list = _data.${className}List[i];
					#foreach($childList in ${child.childList})
						#set($fieLdsModel = $childList.fieLdsModel)
						#set($vModel = "${fieLdsModel.vModel}")
						#set($field = "${fieLdsModel.vModel}")
						#set($formModel = "_list")
						#set($tableModel = "")
						#if($vModel)
							#dataList('table' $fieLdsModel $formModel $tableModel $field $vModel)
						#end
					#end
				}
				#end
				return _data;
			},
			dataInfo(dataAll){
				let _dataAll =dataAll
				_dataAll.id = _dataAll.${pKeyName}
				#foreach($html in ${context.fields})
					#set($fieLdsModel = $html.formColumnModel.fieLdsModel)
					#set($vModel = "${fieLdsModel.vModel}")
					#set($field = "${fieLdsModel.vModel}")
					#set($formModel = "_dataAll")
					#set($tableModel = "")
					#if($vModel)
						#dataInfo('mast' $fieLdsModel $formModel $tableModel $field $vModel)
					#end
				#end
				#foreach($masetkey in $mastTableList.entrySet())
					#set($tableModel = $masetkey.key)
					#set($fieldsAll = $masetkey.value)
					#foreach($html in ${fieldsAll})
						#set($mastTableModel = $html.formMastTableModel)
						#set($fieLdsModel = $html.formMastTableModel.mastTable.fieLdsModel)
						#set($vModel = "${mastTableModel.vModel}")
						#set($field = "${mastTableModel.field}")
						#set($formModel = "_dataAll")
						#if($vModel)
							#dataInfo('mastTable' $fieLdsModel $formModel $tableModel $field $vModel)
						#end
					#end
				#end
				#foreach($child in ${context.children})
					#set($className = "${child.className.toLowerCase()}")
				for(let i=0;i<_dataAll.${className}List.length;i++){
				var _list = _dataAll.${className}List[i];
					#foreach($childList in ${child.childList})
						#set($fieLdsModel = $childList.fieLdsModel)
						#set($vModel = "${fieLdsModel.vModel}")
						#set($field = "${fieLdsModel.vModel}")
						#set($config = ${fieLdsModel.config})
						#set($dataType = "${config.dataType}")
						#set($formModel = "_list")
						#set($tableModel = "")
						#if($vModel)
							#dataInfo('table' $fieLdsModel $formModel $tableModel $field $vModel)
						#end
						#if(${dataType}=='dictionary' || ${dataType}=='dynamic')
						_list.${vModel}Options=[]
						#end
					#end
				}
				#end
				this.${context.formModel}=_dataAll
				this.isEdit = true
				this.dataAll()
				#foreach($child in ${context.children})
					#set($className = "${child.className.toLowerCase()}")
				for(let i=0;i<_dataAll.${className}List.length;i++){
					this.childIndex = i
						#foreach($childList in ${child.childList})
							#set($fieLdsModel = $childList.fieLdsModel)
							#set($vModel = "${fieLdsModel.vModel}")
							#set($field = "${fieLdsModel.vModel}")
							#set($config = ${fieLdsModel.config})
							#set($dataType = "${config.dataType}")
							#if(${dataType}=='dynamic')
					this.get${className}${vModel}Options()
							#end
						#end
				}
				#end
				this.childIndex=-1
			},
			addForSelect(data){
				for (let i = 0; i < data.length; i++) {
        			let item = data[i]
        			this.${context.formModel}[this.tableKey].push(item)
      			}
			},
		},
	}

</script>
