// 表单的beforeSubmit 默认函数
export const defaultBeforeSubmitFunc =
  '({ formData, setFormData, setShowOrHide, setRequired, setDisabled, request, extraParams, useCustomDialog }) => {\n    return new Promise((resolve, reject) => {\n        // 在此编写代码\n        \n        // 继续执行\n        resolve()\n    })\n}';

// 表单的onLoad、afterSubmit的默认函数
export const defaultOnLoadFunc =
  '({ formData, setFormData, setShowOrHide, setRequired, setDisabled, request, extraParams, useCustomDialog }) => {\n    // 在此编写代码\n    \n}';

// 表单组件的change、blur等默认函数
export const defaultWdigetFunc =
  '({ data, formData, setFormData, setShowOrHide, setRequired, setDisabled, request, getFieldOptions, setFieldOptions, extraParams, useCustomDialog }) => {\n    // 在此编写代码\n    \n}';

const escapeSpace: (prop: string) => string = str => {
  const reg = /(\s{2,}|\n{2,})/g;
  str = str.replace(reg, match => {
    if (match.includes(' ')) {
      return '';
    } else {
      return '\n';
    }
  });
  return str;
};

const deleteComment: (prop: string) => string = str => {
  const noSingleLineCommentStr = str.replace(/\/\/.*$/gm, '');
  const noMultiLineCommentStr = noSingleLineCommentStr.replace(/\/\*[\s\S]*?\*\//g, '');
  return noMultiLineCommentStr;
};
export const compareFuncStr: (prop1: string, prop2: string, iVoid?: boolean) => boolean = (funcStr1 = '', funcStr2 = '', iVoid = false) => {
  if (!iVoid) {
    if (funcStr1.trim() === '' || funcStr2.trim() === '') {
      return true;
    }
  }
  if (funcStr1 === funcStr2) {
    return true;
  }
  const reg = /=>\s*/;
  let parts1 = funcStr1.split(reg);
  let parts2 = funcStr2.split(reg);
  // 兼容多个箭头
  if (parts1.length > 2) {
    const temp = parts1.slice(1).join('\n');
    parts1 = [parts1[0], temp];
  }
  if (parts2.length > 2) {
    const temp = parts2.slice(1).join('\n');
    parts2 = [parts2[0], temp];
  }
  if (parts1.length === 2 && parts2.length === 2) {
    let str1 = parts1[1];
    let str2 = parts2[1];
    str1 = deleteComment(str1);
    str2 = deleteComment(str2);
    return escapeSpace(str1) === escapeSpace(str2);
  }
  return false;
};

export function setBtnWarnClass(s1: string, s2: string) {
  if (compareFuncStr(s1, s2)) {
    return '';
  } else {
    return 'dynamic-script-form-item';
  }
}
