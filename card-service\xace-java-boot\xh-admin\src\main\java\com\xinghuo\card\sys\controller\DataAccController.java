package com.xinghuo.card.sys.controller;

import com.xinghuo.card.sys.entity.DataAccEntity;
import com.xinghuo.card.sys.model.dataacc.DataAccModel;
import com.xinghuo.card.sys.model.dataacc.DataAccVO;
import com.xinghuo.card.sys.service.DataAccService;
import com.xinghuo.common.base.ActionResult;
import com.xinghuo.common.base.vo.ListVO;
import com.xinghuo.common.util.core.BeanCopierUtils;
import com.xinghuo.common.util.json.JsonXhUtil;
import com.xinghuo.common.util.tree.SumTree;
import com.xinghuo.common.util.tree.TreeDotUtils;
import com.xinghuo.system.base.entity.DictionaryDataEntity;
import com.xinghuo.system.base.service.DictionaryDataService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

/**
 * 个人账号
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2022-12-02
 */
@Slf4j
@RestController
@Tag(name = "账号管理", description = "账号")
@RequestMapping("/api/card/sys/acc")
public class DataAccController {
    @Autowired
    private DataAccService dataAccService;

    @Autowired
    private DictionaryDataService dictionaryDataService;

    /**
     * 列表
     *
     * @return
     */
    @Operation(summary = "账号树形列表")
    @GetMapping("/selector")
    public ActionResult treeView(boolean showNormalAcc) throws IOException {

        List<DataAccEntity> data = dataAccService.getList(showNormalAcc);
        List<DictionaryDataEntity> data2 = dictionaryDataService.getList("1010038124763136");
        for (DictionaryDataEntity item : data2) {
            DataAccEntity da = new DataAccEntity();
            da.setId(item.getEnCode());
            da.setName(item.getFullName());
            da.setParentId("-1");
            //da.setFavMark(1);
            data.add(da);
        }
        //处理id字段转名称，若无需转或者为空可删除
        for (DataAccEntity entity : data) {
            if (StringUtils.isBlank(entity.getParentId()) && entity.getType() != null) {
                entity.setParentId(entity.getType().toString());
            }
        }

        List<DataAccModel> voListVO = BeanCopierUtils.copyList(data, DataAccModel.class);
        List<SumTree<DataAccModel>> sumTrees = TreeDotUtils.convertListToTreeDot(voListVO);
        List<DataAccVO> list = BeanCopierUtils.copyList(sumTrees, DataAccVO.class);
        List<DataAccVO> listVo = new LinkedList<>();
        for (DataAccVO dataAccVO : list) {
            if ("-1".equals(dataAccVO.getParentId())) {
                listVo.add(dataAccVO);
            }
        }

        ListVO<DataAccVO> vo = new ListVO<>();
        vo.setList(listVo);
        return ActionResult.success(vo);

    }


    @Operation(summary = "账号选择列表")
    @GetMapping("/select")
    public ActionResult select() {

        List<DataAccEntity> data = dataAccService.getSelectList();
        //对取到的数据包含id，name ，转换成 List MAP对象， name 用fullname
        List<Map<String,String>> list = new LinkedList<>();
        data.stream().map(item -> {
            Map<String, String> map = new HashMap<>();
            map.put("id", item.getId());
            map.put("fullName", item.getName());
            return map;
        }).forEach(item -> list.add(item));
        return ActionResult.success(list);
    }



    }
