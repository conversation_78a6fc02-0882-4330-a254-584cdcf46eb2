import { defHttp } from '/@/utils/http/axios';

enum Api {
  Prefix = '/api/card/acc',
}

// 获取账户列表
export function getDataAccList() {
  return defHttp.get({ url: Api.Prefix + '/list' });
}

// 获取账户选择器数据
export function getDataAccSelector() {
  return defHttp.get({ url: Api.Prefix + '/selector' });
}

// 获取账户详情
export function getDataAccDetail(id: string) {
  return defHttp.get({ url: Api.Prefix + `/${id}` });
}
