import { defHttp } from '/@/utils/http/axios';

enum Api {
  Prefix = '/api/card/bill',
}

// 获取最新账单
export function getLatestBill(cardAccId: string) {
  return defHttp.get({ url: Api.Prefix + `/latest/${cardAccId}` });
}

// 获取历史账单
export function getHistoryBills(cardAccId: string, limit = 12) {
  return defHttp.get({ url: Api.Prefix + `/history/${cardAccId}`, params: { limit } });
}

// 获取账单详情
export function getBillDetail(billId: string) {
  return defHttp.get({ url: Api.Prefix + `/${billId}` });
}

// 更新还款状态
export function updatePaymentStatus(billId: string, paymentAmount: number) {
  return defHttp.post({ url: Api.Prefix + `/${billId}/payment`, params: { paymentAmount } });
}

// 手动生成账单
export function generateBill(cardAccId: string) {
  return defHttp.post({ url: Api.Prefix + `/generate/${cardAccId}` });
}

// 获取还款提醒
export function getPaymentReminders() {
  return defHttp.get({ url: Api.Prefix + '/reminders' });
}

// 检查账单逾期状态
export function checkOverdue(billId: string) {
  return defHttp.get({ url: Api.Prefix + `/${billId}/overdue` });
}
