import { defHttp } from '/@/utils/http/axios';

enum Api {
  Prefix = '/api/card/flowtype',
}

// 获取分类列表
export function getFlowTypeList() {
  return defHttp.get({ url: Api.Prefix + '/list' });
}

// 获取分类选择器数据
export function getFlowTypeSelector() {
  return defHttp.get({ url: Api.Prefix + '/selector' });
}

// 获取分类详情
export function getFlowTypeDetail(id: string) {
  return defHttp.get({ url: Api.Prefix + `/${id}` });
}
