package com.xinghuo.admin.aop;

import cn.dev33.satoken.context.SaHolder;
import com.xinghuo.common.base.UserInfo;
import com.xinghuo.common.database.util.NotTenantPluginHolder;
import com.xinghuo.common.util.ConfigValueUtil;
import com.xinghuo.common.util.UserProvider;
import com.xinghuo.common.util.context.DataSourceContextHolder;
import com.xinghuo.common.util.core.StrXhUtil;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023-10-05
 */
@Slf4j
@Aspect
@Component
@Order(1)
public class DataSourceBindAspect {


    @Autowired
    private ConfigValueUtil configValueUtil;

    @Pointcut("((execution(* com.xinghuo.*.controller.*.*(..)) || execution(* com.xinghuo.*.*.controller.*.*(..))) " +
            "&& !execution(* com.xinghuo.oauth.controller.LoginController.login(..)))" +
            "|| execution(* com.xinghuo.message.websocket.WebSocket.*(..))")
    public void bindDataSource() {

    }

    /**
     * NoDataSourceBind 不需要绑定数据库的注解
     *
     * @param pjp
     * @return
     * @throws Throwable
     */
    @Around("bindDataSource() && !@annotation(com.xinghuo.common.annotation.NoDataSourceBind)")
    public Object doAroundService(ProceedingJoinPoint pjp) throws Throwable {
        if (configValueUtil.isMultiTenancy()) {
            if (StrXhUtil.isEmpty(DataSourceContextHolder.getDatasourceId()) || StrXhUtil.isEmpty(DataSourceContextHolder.getDatasourceName())) {
                UserInfo userInfo = UserProvider.getUser();
                String url = null;
                try {
                    url = SaHolder.getRequest().getRequestPath();
                } catch (Exception ee) {
                }
                log.error("租户" + userInfo.getTenantId() + "数据库不存在, URL: {}, TOKEN: {}", url, userInfo.getToken());
                return null;
            }
            return pjp.proceed();
        }
        Object obj = pjp.proceed();
        return obj;
    }


    /**
     * NoDataSourceBind 不需要绑定数据库的注解 加入不切租户库标记
     *
     * @param pjp
     * @return
     * @throws Throwable
     */
    @Around("bindDataSource() && @annotation(com.xinghuo.common.annotation.NoDataSourceBind)")
    public Object doAroundService2(ProceedingJoinPoint pjp) throws Throwable {
        try {
            NotTenantPluginHolder.setNotSwitchAlwaysFlag();
            //Filter中提前设置租户信息, 不需要切库的方法进行清除切库
            DataSourceContextHolder.clearDatasourceType();
            return pjp.proceed();
        } finally {
            NotTenantPluginHolder.clearNotSwitchAlwaysFlag();
        }
    }
}
