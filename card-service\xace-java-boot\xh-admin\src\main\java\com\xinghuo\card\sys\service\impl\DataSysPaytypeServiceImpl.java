package com.xinghuo.card.sys.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xinghuo.card.sys.dao.DataSysPaytypeMapper;
import com.xinghuo.card.sys.entity.DataSysPaytypeEntity;
import com.xinghuo.card.sys.model.datasyspaytype.DataSysPaytypePagination;
import com.xinghuo.card.sys.service.DataSysPaytypeService;
import com.xinghuo.common.util.UserProvider;
import com.xinghuo.common.util.core.StrXhUtil;
import com.xinghuo.common.util.extra.ServletUtil;
import com.xinghuo.permission.model.authorize.AuthorizeConditionModel;
import com.xinghuo.permission.service.AuthorizeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 收支类型管理
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2022-11-14
 */
@Service
public class DataSysPaytypeServiceImpl extends ServiceImpl<DataSysPaytypeMapper, DataSysPaytypeEntity> implements DataSysPaytypeService {

    @Autowired
    private UserProvider userProvider;

    @Autowired
    private AuthorizeService authorizeService;

    @Override
    public List<DataSysPaytypeEntity> getList(DataSysPaytypePagination dataSysPaytypePagination) {
        return getListByType(dataSysPaytypePagination, "0");
    }

    @Override
    public List<DataSysPaytypeEntity> getTypeList(DataSysPaytypePagination dataSysPaytypePagination, String dataType) {
        return getListByType(dataSysPaytypePagination, dataType);
    }

    private List<DataSysPaytypeEntity> getListByType(DataSysPaytypePagination dataSysPaytypePagination, String dataType) {
        List<String> allIdList = new ArrayList();
        int total = 0;
        int dataSysPaytypeNum = 0;
        QueryWrapper<DataSysPaytypeEntity> dataSysPaytypeQueryWrapper = new QueryWrapper<>();
        boolean pcPermission = false;
        boolean appPermission = false;
        boolean isPc = "pc".equals(ServletUtil.getHeader("xh-origin"));
        if (isPc && pcPermission) {
            if (!userProvider.get().getIsAdministrator()) {
                Object dataSysPaytypeObj = authorizeService.getCondition(new AuthorizeConditionModel(dataSysPaytypeQueryWrapper, dataSysPaytypePagination.getMenuId(), "data_sys_paytype"));
                if (ObjectUtil.isEmpty(dataSysPaytypeObj)) {
                    return new ArrayList<>();
                } else {
                    dataSysPaytypeQueryWrapper = (QueryWrapper<DataSysPaytypeEntity>) dataSysPaytypeObj;
                    dataSysPaytypeNum++;
                }
            }
        }
        if (!isPc && appPermission) {
            if (!userProvider.get().getIsAdministrator()) {
                Object dataSysPaytypeObj = authorizeService.getCondition(new AuthorizeConditionModel(dataSysPaytypeQueryWrapper, dataSysPaytypePagination.getMenuId(), "data_sys_paytype"));
                if (ObjectUtil.isEmpty(dataSysPaytypeObj)) {
                    return new ArrayList<>();
                } else {
                    dataSysPaytypeQueryWrapper = (QueryWrapper<DataSysPaytypeEntity>) dataSysPaytypeObj;
                    dataSysPaytypeNum++;
                }
            }
        }
        if (StrXhUtil.isNotEmpty(dataSysPaytypePagination.getType())) {
            dataSysPaytypeNum++;
            dataSysPaytypeQueryWrapper.lambda().eq(DataSysPaytypeEntity::getType, dataSysPaytypePagination.getType());
        }
        if (StrXhUtil.isNotEmpty(dataSysPaytypePagination.getName())) {
            dataSysPaytypeNum++;
            dataSysPaytypeQueryWrapper.lambda().like(DataSysPaytypeEntity::getName, dataSysPaytypePagination.getName());
        }
        if (allIdList.size() > 0) {
            dataSysPaytypeQueryWrapper.lambda().in(DataSysPaytypeEntity::getId, allIdList);
        }

        dataSysPaytypeQueryWrapper.lambda().orderByAsc(DataSysPaytypeEntity::getListOrder);


        return this.list(dataSysPaytypeQueryWrapper);

    }

    @Override
    public DataSysPaytypeEntity getInfo(String id) {
        QueryWrapper<DataSysPaytypeEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(DataSysPaytypeEntity::getId, id);
        return this.getOne(queryWrapper);
    }

    @Override
    public void create(DataSysPaytypeEntity entity) {
        this.save(entity);
    }

    @Override
    public boolean update(String id, DataSysPaytypeEntity entity) {
        entity.setId(id);
        return this.updateById(entity);
    }

    @Override
    public void delete(DataSysPaytypeEntity entity) {
        if (entity != null) {
            this.removeById(entity.getId());
        }
    }


}
