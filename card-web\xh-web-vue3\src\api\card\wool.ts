import { defHttp } from '/@/utils/http/axios';

/**
 * 羊毛管理相关API接口
 */

enum Api {
  Prefix = '/api/card/flow/wool',
}

/**
 * 羊毛记录分页查询参数
 */
export interface WoolListParams {
  type?: string;
  srcAccId?: string;
  inDate?: string[];
  keyword?: string;
  current?: number;
  size?: number;
}

/**
 * 羊毛记录表单数据
 */
export interface WoolFormData {
  id?: string;
  type: string;
  srcAccId: string;
  srcPoint: number;
  amount: number;
  acitivity: string;
  goods: string;
  inDate: string;
  inAccId: string;
  flowAmount: number;
  note?: string;
}

/**
 * 获取羊毛记录列表（分页）
 */
export function getWoolList(data: WoolListParams) {
  return defHttp.post({ url: Api.Prefix + '/getList', data });
}

/**
 * 创建羊毛记录
 */
export function createWool(data: WoolFormData) {
  return defHttp.post({ url: Api.Prefix, data });
}

/**
 * 修改羊毛记录
 */
export function updateWool(data: WoolFormData) {
  return defHttp.put({ url: Api.Prefix + '/' + data.id, data });
}

/**
 * 获取羊毛记录详情
 */
export function getWoolInfo(id: string) {
  return defHttp.get({ url: Api.Prefix + '/' + id });
}

/**
 * 删除羊毛记录
 */
export function delWool(id: string) {
  return defHttp.delete({ url: Api.Prefix + '/' + id });
}
