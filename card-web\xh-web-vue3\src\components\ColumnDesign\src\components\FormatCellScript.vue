<template>
  <BasicModal
    v-bind="$attrs"
    @register="registerModal"
    title="格式化单元格"
    :helpMessage="['小程序不支持在线JS脚本', '暂时只支持返回普通文本']"
    :width="1000"
    @ok="handleSubmit"
    @fullscreen-change="fullscreenChange"
    destroyOnClose
    canFullscreen
    class="form-script-modal">
    <div class="form-script-modal-body">
      <div class="main-board">
        <div class="main-board-editor">
          <MonacoEditor :provideHover="provideHover" v-if="isOK" ref="editorRef" v-model="text" />
        </div>
        <div class="main-board-tips">
          <p>支持JavaScript的脚本</p>
          <p>text--单元格文字</p>
          <p>record--行数据</p>
          <p>column--单元格配置信息</p>
          <p>utils--内置工具函数</p>
        </div>
      </div>
    </div>
  </BasicModal>
</template>
<script lang="ts" setup>
  import { ref, reactive } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { MonacoEditor } from '/@/components/CodeEditor';
  import { defaultCellFormat } from '../helper/config';
  import type { languages } from 'monaco-editor/esm/vs/editor/editor.api';

  const emit = defineEmits(['register', 'confirm']);
  const [registerModal, { closeModal }] = useModalInner(init);
  const editorRef = ref(null);
  const text = ref('');
  const key = ref('');
  const funcName = ref('');
  const isOK = ref(false);

  const provideHover = reactive<languages.HoverProvider>({
    // @ts-expect-error
    provideHover(model, position) {
      // const lineword = model.getLineContent(position.lineNumber);
      const word = model.getWordAtPosition(position)?.word;
      let returnValue = {};
      switch (word) {
        case 'record':
          returnValue = {
            contents: [
              {
                value: ['列表行数据', `${word}：Record<string, any>`].join('\n\n'),
              },
            ],
          };
          break;
        case 'column':
          returnValue = {
            contents: [
              {
                value: ['列表列数据', `${word}：Record<string, any>`].join('\n\n'),
              },
            ],
          };
          break;
        default:
          break;
      }
      return returnValue;
    },
  });

  function reloadEditor() {
    isOK.value = false;
    setTimeout(() => {
      isOK.value = true;
    }, 100);
  }
  function init(data) {
    text.value = data.text || defaultCellFormat;
    key.value = data.key;
    funcName.value = data.funcName;
    reloadEditor();
  }
  function fullscreenChange() {
    reloadEditor();
  }
  function handleSubmit() {
    emit('confirm', { text: text.value, key: key.value });
    closeModal();
  }
</script>
