package com.xinghuo.admin.util;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.xinghuo.common.base.model.Pagination;
import com.xinghuo.common.constant.visual.XhKeyConstant;
import com.xinghuo.common.database.model.superQuery.ConditionJsonModel;
import com.xinghuo.common.database.model.superQuery.SuperQueryConditionModel;
import com.xinghuo.common.database.model.superQuery.SuperQueryJsonModel;
import com.xinghuo.common.util.context.SpringContext;
import com.xinghuo.common.util.core.DateXhUtil;
import com.xinghuo.common.util.core.StrXhUtil;
import com.xinghuo.common.util.json.JsonXhUtil;
import com.xinghuo.permission.model.authorize.AuthorizeConditionModel;
import com.xinghuo.permission.service.AuthorizeService;
import org.springframework.util.Assert;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Service服务类Util
 *
 * <AUTHOR>
 * @date 2024-04-07
 */
public class BaseServiceUtil {
    private static final AuthorizeService authorizeService;

    static {
        authorizeService = SpringContext.getBean(AuthorizeService.class);
    }

    /**
     * 数据权限
     */
    public static <T> QueryWrapper<T> getCondition(AuthorizeConditionModel conditionModel) {
        return authorizeService.getCondition(conditionModel);
    }

    /**
     * 构造高级查询内容，目前仅支持主表查询
     */
    public static void advancedSearchQuery(QueryWrapper queryWrapper, Pagination pagination, Class<?> clazz) {
        // 获取TableName注解
        TableName tname = clazz.getAnnotation(TableName.class);
        Assert.notNull(tname, "实体对象缺乏TableName，必须进行配置。");
        String mainTableName = tname.value();
        if (ObjectUtil.isNotEmpty(pagination.getSuperQueryJson())) {
            SuperQueryJsonModel superQueryJsonModel = JsonXhUtil.toBean(pagination.getSuperQueryJson(), SuperQueryJsonModel.class);
            String matchLogic = superQueryJsonModel.getMatchLogic();
            List<ConditionJsonModel> superQueryList = JsonXhUtil.jsonToList(superQueryJsonModel.getConditionJson(), ConditionJsonModel.class);
            for (ConditionJsonModel conditionjson : superQueryList) {
                Map<String, Object> map = JsonXhUtil.stringToMap(conditionjson.getAttr());
                Map<String, Object> configMap = (Map<String, Object>) map.get("__config__");
                String tableName = configMap.get("relationTable") != null ? String.valueOf(configMap.get("relationTable")) : String.valueOf(configMap.get("tableName"));
                if (map.get("multiple") != null) {
                    if (Boolean.parseBoolean(String.valueOf(map.get("multiple"))) && ObjectUtil.isNull(conditionjson.getFieldValue())) {
                        conditionjson.setFieldValue("[]");
                    }
                }
                conditionjson.setTableName(tableName);
            }
            getCondition(new SuperQueryConditionModel(queryWrapper, superQueryList, matchLogic, mainTableName), clazz);
        }
    }


    public static Integer getCondition(SuperQueryConditionModel conditionModel, Class<?> clazz) {
        int num = 0;
        // 根据条件模型获取查询包装器
        QueryWrapper<?> queryWrapper = conditionModel.getObj();
        // 获取查询条件列表
        List<ConditionJsonModel> queryConditionModels = conditionModel.getConditionList();
        // 获取匹配逻辑操作符
        String op = conditionModel.getMatchLogic();
        // 获取表名
        String tableName = conditionModel.getTableName();
        // 用于存储适用的查询条件
        List<ConditionJsonModel> useCondition = new ArrayList<>();

        // 筛选出与表名匹配的查询条件并处理特定字段
        for (ConditionJsonModel queryConditionModel : queryConditionModels) {
            if (queryConditionModel.getTableName().equalsIgnoreCase(tableName)) {
                // 处理字段名包含"com/xinghuo"的情况
                if (queryConditionModel.getField().contains("com/xinghuo")) {
                    String child = queryConditionModel.getField();
                    String s1 = child.substring(child.lastIndexOf("xh_")).replace("xh_", "");
                    queryConditionModel.setField(s1);
                }
                // 处理字段名以"tableField"开头的情况
                if (queryConditionModel.getField().startsWith("tableField")) {
                    String child = queryConditionModel.getField();
                    String s1 = child.substring(child.indexOf("-") + 1);
                    queryConditionModel.setField(s1);
                }
                useCondition.add(queryConditionModel);
            }
        }

        // 如果查询条件列表为空，则直接返回初始数量
        if (queryConditionModels.isEmpty() || useCondition.isEmpty()) {
            return num;
        }
        // 如果有适用的查询条件，则累加数量
        if (useCondition.size() > 0) {
            num += 1;
        }

        // 处理控件，转换为有效查询值
        for (ConditionJsonModel queryConditionModel : useCondition) {
            String xhKey = queryConditionModel.getXhKey();
            String fieldValue = queryConditionModel.getFieldValue();
            // 对空值进行特殊处理，并转换like和notLike操作符
            if (StrXhUtil.isEmpty(fieldValue)) {
                if (xhKey.equals(XhKeyConstant.CASCADER) || xhKey.equals(XhKeyConstant.CHECKBOX) || xhKey.equals(XhKeyConstant.COMSELECT) || xhKey.equals(XhKeyConstant.ADDRESS)) {
                    queryConditionModel.setFieldValue("[]");
                } else {
                    queryConditionModel.setFieldValue("");
                }
                if ("like".equals(queryConditionModel.getSymbol())) {
                    queryConditionModel.setSymbol("==");
                } else if ("notLike".equals(queryConditionModel.getSymbol())) {
                    queryConditionModel.setSymbol("<>");
                }
            }
            // 对特定控件的值进行格式化
            if (xhKey.equals(XhKeyConstant.DATE)) {
                String startTime = "";
                if (StrXhUtil.isNotEmpty(fieldValue)) {
                    startTime = DateXhUtil.formatDate(Long.parseLong(fieldValue));
                }
                queryConditionModel.setFieldValue(startTime);
            } else if (xhKey.equals(XhKeyConstant.CREATETIME) || xhKey.equals(XhKeyConstant.MODIFYTIME)) {
                String startTime = "";
                if (StrXhUtil.isNotEmpty(fieldValue)) {
                    startTime = DateXhUtil.formatDateTime(Long.parseLong(fieldValue));
                }
                queryConditionModel.setFieldValue(startTime);
            } else if (xhKey.equals(XhKeyConstant.CURRORGANIZE)) {
                if (StrXhUtil.isNotEmpty(fieldValue)) {
                    List<String> orgList = JsonXhUtil.jsonToList(fieldValue, String.class);
                    queryConditionModel.setFieldValue(orgList.get(orgList.size() - 1));
                }
            }
        }

        // 构建查询条件
        queryWrapper.and(tw -> {
            for (ConditionJsonModel conditionJsonModel : useCondition) {
                String conditionField = conditionJsonModel.getField();
                String xhKey = conditionJsonModel.getXhKey();
                Field declaredField = null;
                try {
                    declaredField = clazz.getDeclaredField(conditionField);
                } catch (NoSuchFieldException e) {
                    e.printStackTrace();
                }
                declaredField.setAccessible(true);
                String field = declaredField.getAnnotation(TableField.class).value();
                String fieldValue = conditionJsonModel.getFieldValue();
                String symbol = conditionJsonModel.getSymbol();

                // 根据匹配逻辑操作符和操作符类型构建查询条件
                if ("AND".equalsIgnoreCase(op)) {
                    // and逻辑下的条件构建
                    if ("==".equals(symbol)) {
                        // 等于操作
                        tw.and(qw -> {
                            // 处理多选情况
                            List<String> multXh = new ArrayList<>() {{
                                add(XhKeyConstant.CASCADER);
                                add(XhKeyConstant.COMSELECT);
                                add(XhKeyConstant.ADDRESS);
                                add(XhKeyConstant.SELECT);
                                add(XhKeyConstant.TREESELECT);
                            }};
                            if (XhKeyConstant.CHECKBOX.equals(xhKey) || (multXh.contains(xhKey) && conditionJsonModel.isFormMultiple())) {
                                String eavalue = "";
                                if (fieldValue.contains("[")) {
                                    eavalue = "[" + fieldValue + "]";
                                } else {
                                    ArrayNode jarr = JsonXhUtil.createArrayNode();
                                    jarr.add(fieldValue);
                                    eavalue = jarr.toString();
                                }
                                qw.eq(field, eavalue);
                            } else if (!xhKey.equals(XhKeyConstant.NUM_INPUT) && !xhKey.equals(XhKeyConstant.CALCULATE)) {
                                qw.eq(field, fieldValue);
                            } else {
                                if (StrXhUtil.isNotEmpty(fieldValue)) {
                                    qw.eq(field, fieldValue);
                                }
                            }
                            if (StrXhUtil.isEmpty(fieldValue)) {
                                qw.or(ew -> ew.isNull(field));
                            }
                        });
                    } else if (">=".equals(symbol)) {
                        // 大于等于操作
                        tw.ge(field, fieldValue);
                    } else if ("<=".equals(symbol)) {
                        // 小于等于操作
                        tw.and(ew -> {
                            ew.le(field, fieldValue);
                            ew.and(qw -> qw.ne(field, ""));
                        });
                    } else if (">".equals(symbol)) {
                        // 大于操作
                        tw.gt(field, fieldValue);
                    } else if ("<".equals(symbol)) {
                        // 小于操作
                        tw.and(ew -> {
                            ew.lt(field, fieldValue);
                            ew.and(qw -> qw.ne(field, ""));
                        });
                    } else if ("<>".equals(symbol)) {
                        // 不等于操作
                        tw.and(ew -> {
                            ew.ne(field, fieldValue);
                            if (StrXhUtil.isNotEmpty(fieldValue)) {
                                ew.or(qw -> qw.isNull(field));
                            } else {
                                ew.and(qw -> qw.isNotNull(field));
                            }
                        });
                    } else if ("like".equals(symbol)) {
                        // like操作
                        tw.and(ew -> {
                            if (StrXhUtil.isNotEmpty(fieldValue)) {
                                ew.like(field, fieldValue);
                            } else {
                                ew.isNull(field);
                            }
                        });
                    } else if ("notLike".equals(symbol)) {
                        // notLike操作
                        tw.and(ew -> {
                            if (StrXhUtil.isNotEmpty(fieldValue)) {
                                ew.notLike(field, fieldValue);
                                ew.or(qw -> qw.isNull(field));
                            } else {
                                ew.isNotNull(field);
                            }
                        });
                    }
                } else {
                    // or逻辑下的条件构建
                    if ("==".equals(symbol)) {
                        tw.or(qw -> qw.eq(field, fieldValue));
                    } else if (">=".equals(symbol)) {
                        tw.or(qw -> qw.ge(field, fieldValue));
                    } else if ("<=".equals(symbol)) {
                        tw.or(qw -> qw.le(field, fieldValue));
                    } else if (">".equals(symbol)) {
                        tw.or(qw -> qw.gt(field, fieldValue));
                    } else if ("<".equals(symbol)) {
                        tw.or(qw -> qw.lt(field, fieldValue));
                    } else if ("<>".equals(symbol)) {
                        tw.or(qw -> qw.ne(field, fieldValue));
                        if (StrXhUtil.isNotEmpty(fieldValue)) {
                            tw.or(qw -> qw.isNull(field));
                        }
                    } else if ("like".equals(symbol)) {
                        if (StrXhUtil.isNotEmpty(fieldValue)) {
                            tw.or(qw -> qw.like(field, fieldValue));
                        } else {
                            tw.or(qw -> qw.isNull(field));
                        }
                    } else if ("notLike".equals(symbol)) {
                        if (StrXhUtil.isNotEmpty(fieldValue)) {
                            tw.or(qw -> qw.notLike(field, fieldValue));
                            tw.or(qw -> qw.isNull(field));
                        } else {
                            tw.or(qw -> qw.isNotNull(field));
                        }
                    }
                }
            }
        });
        return num;
    }
}
