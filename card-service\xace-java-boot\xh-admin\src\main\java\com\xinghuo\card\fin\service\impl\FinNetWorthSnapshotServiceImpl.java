package com.xinghuo.card.fin.service.impl;

import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xinghuo.card.fin.entity.FinNetWorthSnapshotEntity;
import com.xinghuo.card.fin.mapper.FinNetWorthSnapshotMapper;
import com.xinghuo.card.fin.model.finnetworthsnapshot.FinNetWorthSnapshotPagination;
import com.xinghuo.card.fin.service.FinNetWorthSnapshotService;
import com.xinghuo.common.util.UserProvider;
import com.xinghuo.common.util.core.StrXhUtil;
import com.xinghuo.common.util.core.RandomUtil;
import com.xinghuo.common.util.core.DateXhUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;

/**
 * 净资产快照服务实现类
 * 用于管理用户净资产快照数据，提供净资产变化跟踪和财务分析功能
 *
 * <AUTHOR> Assistant
 * @version V1.0.0
 * @date 2024-12-29
 */
@Slf4j
@Service
public class FinNetWorthSnapshotServiceImpl extends ServiceImpl<FinNetWorthSnapshotMapper, FinNetWorthSnapshotEntity> implements FinNetWorthSnapshotService {

    @Autowired
    private UserProvider userProvider;

    @Override
    public List<FinNetWorthSnapshotEntity> getList(FinNetWorthSnapshotPagination finNetWorthSnapshotPagination) {
        return getListByType(finNetWorthSnapshotPagination, "0");
    }

    @Override
    public List<FinNetWorthSnapshotEntity> getTypeList(FinNetWorthSnapshotPagination finNetWorthSnapshotPagination, String dataType) {
        return getListByType(finNetWorthSnapshotPagination, dataType);
    }

    @Override
    public List<FinNetWorthSnapshotEntity> getSelectList() {
        QueryWrapper<FinNetWorthSnapshotEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .orderByDesc(FinNetWorthSnapshotEntity::getSnapshotDate)
                .orderByDesc(FinNetWorthSnapshotEntity::getCreatedAt);
        return this.list(queryWrapper);
    }

    @Override
    public List<FinNetWorthSnapshotEntity> getListByUserId(String userId) {
        Assert.notEmpty(userId, "用户ID不能为空");
        
        QueryWrapper<FinNetWorthSnapshotEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(FinNetWorthSnapshotEntity::getUserId, userId)
                .orderByDesc(FinNetWorthSnapshotEntity::getSnapshotDate)
                .orderByDesc(FinNetWorthSnapshotEntity::getCreatedAt);
        return this.list(queryWrapper);
    }

    @Override
    public List<FinNetWorthSnapshotEntity> getListByUserIdAndDateRange(String userId, Date startDate, Date endDate) {
        Assert.notEmpty(userId, "用户ID不能为空");
        
        QueryWrapper<FinNetWorthSnapshotEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(FinNetWorthSnapshotEntity::getUserId, userId);
        
        if (startDate != null) {
            queryWrapper.lambda().ge(FinNetWorthSnapshotEntity::getSnapshotDate, startDate);
        }
        if (endDate != null) {
            queryWrapper.lambda().le(FinNetWorthSnapshotEntity::getSnapshotDate, endDate);
        }
        
        queryWrapper.lambda()
                .orderByAsc(FinNetWorthSnapshotEntity::getSnapshotDate);
        
        return this.list(queryWrapper);
    }

    /**
     * 根据类型获取列表数据
     *
     * @param finNetWorthSnapshotPagination 查询参数
     * @param dataType                      数据类型 0:分页 1:不分页
     * @return 查询结果
     */
    private List<FinNetWorthSnapshotEntity> getListByType(FinNetWorthSnapshotPagination finNetWorthSnapshotPagination, String dataType) {
        QueryWrapper<FinNetWorthSnapshotEntity> queryWrapper = new QueryWrapper<>();

        // 构建查询条件
        if (StrXhUtil.isNotEmpty(finNetWorthSnapshotPagination.getUserId())) {
            queryWrapper.lambda().eq(FinNetWorthSnapshotEntity::getUserId, finNetWorthSnapshotPagination.getUserId());
        }
        if (finNetWorthSnapshotPagination.getSnapshotDateStart() != null) {
            queryWrapper.lambda().ge(FinNetWorthSnapshotEntity::getSnapshotDate, finNetWorthSnapshotPagination.getSnapshotDateStart());
        }
        if (finNetWorthSnapshotPagination.getSnapshotDateEnd() != null) {
            queryWrapper.lambda().le(FinNetWorthSnapshotEntity::getSnapshotDate, finNetWorthSnapshotPagination.getSnapshotDateEnd());
        }

        // 排序
        queryWrapper.lambda()
                .orderByDesc(FinNetWorthSnapshotEntity::getSnapshotDate)
                .orderByDesc(FinNetWorthSnapshotEntity::getCreatedAt);

        return this.list(queryWrapper);
    }

    @Override
    public FinNetWorthSnapshotEntity getInfo(String id) {
        Assert.notEmpty(id, "id不能为空");
        QueryWrapper<FinNetWorthSnapshotEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(FinNetWorthSnapshotEntity::getId, id);
        return this.getOne(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(FinNetWorthSnapshotEntity entity) {
        // 自动计算各种比率
        entity.calculateAndSetAllRatios();
        
        this.save(entity);
        log.info("创建净资产快照成功，ID: {}, 用户ID: {}, 净资产: {}", 
                entity.getId(), entity.getUserId(), entity.getNetWorth());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean update(String id, FinNetWorthSnapshotEntity entity) {
        Assert.notEmpty(id, "id不能为空");
        
        // 自动计算各种比率
        entity.calculateAndSetAllRatios();
        
        entity.setId(id);
        boolean result = this.updateById(entity);
        
        if (result) {
            log.info("更新净资产快照成功，ID: {}, 用户ID: {}, 净资产: {}", 
                    id, entity.getUserId(), entity.getNetWorth());
        }
        
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(FinNetWorthSnapshotEntity entity) {
        if (entity != null) {
            this.removeById(entity.getId());
            log.info("删除净资产快照成功，ID: {}, 用户ID: {}", 
                    entity.getId(), entity.getUserId());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchDelete(List<String> ids) {
        Assert.notEmpty(ids, "删除ID列表不能为空");
        
        int count = this.removeBatchByIds(ids) ? ids.size() : 0;
        log.info("批量删除净资产快照完成，删除数量: {}", count);
        
        return count;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public FinNetWorthSnapshotEntity createSnapshot(String userId, Date snapshotDate) {
        Assert.notEmpty(userId, "用户ID不能为空");
        Assert.notNull(snapshotDate, "快照日期不能为空");
        
        // 检查是否已存在同一天的快照
        FinNetWorthSnapshotEntity existingSnapshot = getSnapshotByUserIdAndDate(userId, snapshotDate);
        if (existingSnapshot != null) {
            log.warn("用户{}在{}已存在快照，跳过创建", userId, snapshotDate);
            return existingSnapshot;
        }
        
        // 从各个资产负债模块获取数据
        FinNetWorthSnapshotEntity entity = new FinNetWorthSnapshotEntity();
        entity.setId(RandomUtil.snowId());
        entity.setUserId(userId);
        entity.setSnapshotDate(snapshotDate);
        
        // 这里应该从实际的资产负债模块获取数据
        // 暂时设置默认值
        entity.setTotalAssets(BigDecimal.ZERO);
        entity.setTotalDebts(BigDecimal.ZERO);
        entity.setLiquidAssets(BigDecimal.ZERO);
        entity.setInvestmentAssets(BigDecimal.ZERO);
        entity.setPropertyAssets(BigDecimal.ZERO);
        entity.setMovableAssets(BigDecimal.ZERO);
        entity.setOtherAssets(BigDecimal.ZERO);
        entity.setCreditDebt(BigDecimal.ZERO);
        entity.setLoanDebt(BigDecimal.ZERO);
        entity.setOtherDebt(BigDecimal.ZERO);
        
        entity.setCreatedBy(userProvider.get().getUserId());
        entity.setCreatedAt(DateXhUtil.date());
        
        this.create(entity);
        
        log.info("创建净资产快照成功，用户ID: {}, 快照日期: {}", userId, snapshotDate);
        
        return entity;
    }

    @Override
    public FinNetWorthSnapshotEntity getLatestSnapshotByUserId(String userId) {
        Assert.notEmpty(userId, "用户ID不能为空");
        
        QueryWrapper<FinNetWorthSnapshotEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(FinNetWorthSnapshotEntity::getUserId, userId)
                .orderByDesc(FinNetWorthSnapshotEntity::getSnapshotDate)
                .orderByDesc(FinNetWorthSnapshotEntity::getCreatedAt)
                .last("LIMIT 1");
        
        return this.getOne(queryWrapper);
    }

    @Override
    public FinNetWorthSnapshotEntity getSnapshotByUserIdAndDate(String userId, Date snapshotDate) {
        Assert.notEmpty(userId, "用户ID不能为空");
        Assert.notNull(snapshotDate, "快照日期不能为空");
        
        QueryWrapper<FinNetWorthSnapshotEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(FinNetWorthSnapshotEntity::getUserId, userId)
                .eq(FinNetWorthSnapshotEntity::getSnapshotDate, snapshotDate);
        
        return this.getOne(queryWrapper);
    }

    @Override
    public List<Map<String, Object>> getNetWorthTrend(String userId, Date startDate, Date endDate) {
        Assert.notEmpty(userId, "用户ID不能为空");
        
        List<FinNetWorthSnapshotEntity> snapshots = getListByUserIdAndDateRange(userId, startDate, endDate);
        List<Map<String, Object>> result = new ArrayList<>();
        
        for (FinNetWorthSnapshotEntity snapshot : snapshots) {
            Map<String, Object> item = new HashMap<>();
            item.put("date", snapshot.getSnapshotDate());
            item.put("totalAssets", snapshot.getTotalAssets());
            item.put("totalDebts", snapshot.getTotalDebts());
            item.put("netWorth", snapshot.getNetWorth());
            item.put("growthRate", snapshot.getGrowthRate());
            item.put("assetLiabilityRatio", snapshot.getAssetLiabilityRatio());
            item.put("liquidityRatio", snapshot.getLiquidityRatio());
            result.add(item);
        }
        
        return result;
    }

    @Override
    public Object getSnapshotStatistics() {
        Map<String, Object> statistics = new HashMap<>();
        
        // 总快照数量
        long totalCount = this.count();
        statistics.put("totalCount", totalCount);
        
        // 按用户统计
        QueryWrapper<FinNetWorthSnapshotEntity> userWrapper = new QueryWrapper<>();
        userWrapper.select("USER_ID, COUNT(*) as count")
                .groupBy("USER_ID");
        
        List<Map<String, Object>> userStats = this.listMaps(userWrapper);
        statistics.put("userStats", userStats);
        
        return statistics;
    }

    @Override
    public Object getSnapshotStatisticsByUserId(String userId) {
        Assert.notEmpty(userId, "用户ID不能为空");
        
        Map<String, Object> statistics = new HashMap<>();
        
        QueryWrapper<FinNetWorthSnapshotEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(FinNetWorthSnapshotEntity::getUserId, userId);
        
        long count = this.count(queryWrapper);
        statistics.put("count", count);
        
        // 获取最新快照
        FinNetWorthSnapshotEntity latestSnapshot = getLatestSnapshotByUserId(userId);
        if (latestSnapshot != null) {
            statistics.put("latestNetWorth", latestSnapshot.getNetWorth());
            statistics.put("latestSnapshotDate", latestSnapshot.getSnapshotDate());
            statistics.put("financialHealth", latestSnapshot.getFinancialHealthDescription());
        }
        
        return statistics;
    }

    @Override
    public BigDecimal calculateNetWorthGrowthRate(String userId, Integer months) {
        Assert.notEmpty(userId, "用户ID不能为空");
        
        if (months == null || months <= 0) {
            months = 12; // 默认12个月
        }
        
        // 实现净资产增长率计算逻辑
        QueryWrapper<FinNetWorthSnapshotEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(FinNetWorthSnapshotEntity::getUserId, userId)
                .orderByDesc(FinNetWorthSnapshotEntity::getSnapshotDate)
                .last("LIMIT " + months);

        List<FinNetWorthSnapshotEntity> snapshots = this.list(queryWrapper);

        if (snapshots.size() < 2) {
            log.warn("快照数据不足，无法计算增长率，用户ID: {}", userId);
            return BigDecimal.ZERO;
        }

        // 取最新和最早的快照计算增长率
        FinNetWorthSnapshotEntity latest = snapshots.get(0);
        FinNetWorthSnapshotEntity earliest = snapshots.get(snapshots.size() - 1);

        BigDecimal latestNetWorth = latest.getNetWorth() != null ? latest.getNetWorth() : BigDecimal.ZERO;
        BigDecimal earliestNetWorth = earliest.getNetWorth() != null ? earliest.getNetWorth() : BigDecimal.ZERO;

        if (earliestNetWorth.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }

        BigDecimal growthRate = latestNetWorth.subtract(earliestNetWorth)
                .divide(earliestNetWorth, 4, BigDecimal.ROUND_HALF_UP)
                .multiply(new BigDecimal("100"));

        return growthRate;
    }

    @Override
    public BigDecimal calculateAverageNetWorth(String userId, Integer months) {
        Assert.notEmpty(userId, "用户ID不能为空");
        
        if (months == null || months <= 0) {
            months = 12; // 默认12个月
        }
        
        // 实现平均净资产计算逻辑
        QueryWrapper<FinNetWorthSnapshotEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(FinNetWorthSnapshotEntity::getUserId, userId)
                .orderByDesc(FinNetWorthSnapshotEntity::getSnapshotDate)
                .last("LIMIT " + months);

        List<FinNetWorthSnapshotEntity> snapshots = this.list(queryWrapper);

        if (snapshots.isEmpty()) {
            return BigDecimal.ZERO;
        }

        BigDecimal totalNetWorth = snapshots.stream()
                .map(snapshot -> snapshot.getNetWorth() != null ? snapshot.getNetWorth() : BigDecimal.ZERO)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        return totalNetWorth.divide(new BigDecimal(snapshots.size()), 2, BigDecimal.ROUND_HALF_UP);
    }

    @Override
    public List<Map<String, Object>> getNetWorthRanking(Integer limit) {
        // 实现净资产排行榜逻辑
        if (limit == null || limit <= 0) {
            limit = 10; // 默认前10名
        }

        QueryWrapper<FinNetWorthSnapshotEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .orderByDesc(FinNetWorthSnapshotEntity::getNetWorth)
                .last("LIMIT " + limit);

        List<FinNetWorthSnapshotEntity> topSnapshots = this.list(queryWrapper);
        List<Map<String, Object>> ranking = new ArrayList<>();

        int rank = 1;
        for (FinNetWorthSnapshotEntity snapshot : topSnapshots) {
            Map<String, Object> item = new HashMap<>();
            item.put("rank", rank++);
            item.put("userId", snapshot.getUserId());
            item.put("netWorth", snapshot.getNetWorth());
            item.put("snapshotDate", snapshot.getSnapshotDate());
            ranking.add(item);
        }

        return ranking;
    }

    @Override
    public List<Map<String, Object>> getFinancialHealthDistribution() {
        // 实现财务健康度分布统计逻辑
        List<FinNetWorthSnapshotEntity> allSnapshots = this.list();
        List<Map<String, Object>> distribution = new ArrayList<>();

        Map<String, Integer> healthLevelCount = new HashMap<>();
        healthLevelCount.put("优秀", 0);
        healthLevelCount.put("良好", 0);
        healthLevelCount.put("一般", 0);
        healthLevelCount.put("较差", 0);

        for (FinNetWorthSnapshotEntity snapshot : allSnapshots) {
            BigDecimal netWorth = snapshot.getNetWorth() != null ? snapshot.getNetWorth() : BigDecimal.ZERO;

            String healthLevel;
            if (netWorth.compareTo(new BigDecimal("1000000")) >= 0) {
                healthLevel = "优秀";
            } else if (netWorth.compareTo(new BigDecimal("500000")) >= 0) {
                healthLevel = "良好";
            } else if (netWorth.compareTo(new BigDecimal("100000")) >= 0) {
                healthLevel = "一般";
            } else {
                healthLevel = "较差";
            }

            healthLevelCount.put(healthLevel, healthLevelCount.get(healthLevel) + 1);
        }

        for (Map.Entry<String, Integer> entry : healthLevelCount.entrySet()) {
            Map<String, Object> item = new HashMap<>();
            item.put("healthLevel", entry.getKey());
            item.put("count", entry.getValue());
            distribution.add(item);
        }

        return distribution;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchCreateSnapshots(List<String> userIds, Date snapshotDate) {
        Assert.notEmpty(userIds, "用户ID列表不能为空");
        Assert.notNull(snapshotDate, "快照日期不能为空");
        
        int count = 0;
        for (String userId : userIds) {
            try {
                createSnapshot(userId, snapshotDate);
                count++;
            } catch (Exception e) {
                log.error("为用户{}创建快照失败", userId, e);
            }
        }
        
        log.info("批量创建快照完成，成功数量: {}", count);
        return count;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int autoCreatePeriodicSnapshots(Date snapshotDate) {
        Assert.notNull(snapshotDate, "快照日期不能为空");
        
        // 获取所有用户ID列表
        // TODO: 这里需要从用户表或其他相关表获取用户ID列表
        List<String> userIds = new ArrayList<>();

        // 暂时从现有快照中获取用户ID
        QueryWrapper<FinNetWorthSnapshotEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .select(FinNetWorthSnapshotEntity::getUserId)
                .groupBy(FinNetWorthSnapshotEntity::getUserId);

        List<FinNetWorthSnapshotEntity> existingSnapshots = this.list(queryWrapper);
        for (FinNetWorthSnapshotEntity snapshot : existingSnapshots) {
            userIds.add(snapshot.getUserId());
        }
        
        return batchCreateSnapshots(userIds, snapshotDate);
    }

    @Override
    public Map<String, Object> getAssetAllocationAnalysis(String userId) {
        Assert.notEmpty(userId, "用户ID不能为空");
        
        FinNetWorthSnapshotEntity latestSnapshot = getLatestSnapshotByUserId(userId);
        Map<String, Object> analysis = new HashMap<>();
        
        if (latestSnapshot != null) {
            FinNetWorthSnapshotEntity.AssetAllocation allocation = latestSnapshot.getAssetAllocation();
            analysis.put("liquidRatio", allocation.getLiquidRatio());
            analysis.put("investmentRatio", allocation.getInvestmentRatio());
            analysis.put("propertyRatio", allocation.getPropertyRatio());
            analysis.put("movableRatio", allocation.getMovableRatio());
            analysis.put("otherRatio", allocation.getOtherRatio());
        }
        
        return analysis;
    }

    @Override
    public Map<String, Object> getDebtStructureAnalysis(String userId) {
        Assert.notEmpty(userId, "用户ID不能为空");
        
        FinNetWorthSnapshotEntity latestSnapshot = getLatestSnapshotByUserId(userId);
        Map<String, Object> analysis = new HashMap<>();
        
        if (latestSnapshot != null) {
            FinNetWorthSnapshotEntity.DebtStructure structure = latestSnapshot.getDebtStructure();
            analysis.put("creditRatio", structure.getCreditRatio());
            analysis.put("loanRatio", structure.getLoanRatio());
            analysis.put("otherRatio", structure.getOtherRatio());
        }
        
        return analysis;
    }

    @Override
    public Map<String, Object> getFinancialHealthAnalysis(String userId) {
        Assert.notEmpty(userId, "用户ID不能为空");
        
        FinNetWorthSnapshotEntity latestSnapshot = getLatestSnapshotByUserId(userId);
        Map<String, Object> analysis = new HashMap<>();
        
        if (latestSnapshot != null) {
            analysis.put("healthLevel", latestSnapshot.getHealthLevel().getCode());
            analysis.put("healthDescription", latestSnapshot.getFinancialHealthDescription());
            analysis.put("assetLiabilityRatio", latestSnapshot.getAssetLiabilityRatio());
            analysis.put("liquidityRatio", latestSnapshot.getLiquidityRatio());
        }
        
        return analysis;
    }

    @Override
    public Map<String, Object> getNetWorthChangeAnalysis(String userId, Integer months) {
        Assert.notEmpty(userId, "用户ID不能为空");
        
        // 实现净资产变化分析逻辑
        Map<String, Object> analysis = new HashMap<>();

        if (months == null || months <= 0) {
            months = 12; // 默认12个月
        }

        QueryWrapper<FinNetWorthSnapshotEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(FinNetWorthSnapshotEntity::getUserId, userId)
                .orderByDesc(FinNetWorthSnapshotEntity::getSnapshotDate)
                .last("LIMIT " + months);

        List<FinNetWorthSnapshotEntity> snapshots = this.list(queryWrapper);

        if (snapshots.isEmpty()) {
            analysis.put("message", "暂无快照数据");
            return analysis;
        }

        // 计算变化趋势
        List<Map<String, Object>> trendData = new ArrayList<>();
        BigDecimal maxNetWorth = BigDecimal.ZERO;
        BigDecimal minNetWorth = new BigDecimal("999999999");

        for (FinNetWorthSnapshotEntity snapshot : snapshots) {
            Map<String, Object> point = new HashMap<>();
            point.put("date", snapshot.getSnapshotDate());
            point.put("netWorth", snapshot.getNetWorth());
            point.put("totalAssets", snapshot.getTotalAssets());
            point.put("totalLiabilities", snapshot.getTotalLiabilities());
            trendData.add(point);

            BigDecimal netWorth = snapshot.getNetWorth() != null ? snapshot.getNetWorth() : BigDecimal.ZERO;
            if (netWorth.compareTo(maxNetWorth) > 0) {
                maxNetWorth = netWorth;
            }
            if (netWorth.compareTo(minNetWorth) < 0) {
                minNetWorth = netWorth;
            }
        }

        analysis.put("trendData", trendData);
        analysis.put("maxNetWorth", maxNetWorth);
        analysis.put("minNetWorth", minNetWorth);
        analysis.put("dataPoints", snapshots.size());

        // 计算增长率
        if (snapshots.size() >= 2) {
            BigDecimal growthRate = calculateNetWorthGrowthRate(userId, months);
            analysis.put("growthRate", growthRate);
        }

        return analysis;
    }

    @Override
    public List<Map<String, Object>> exportSnapshotData(String userId, Date startDate, Date endDate) {
        // 实现导出净资产快照数据逻辑
        Assert.notEmpty(userId, "用户ID不能为空");

        QueryWrapper<FinNetWorthSnapshotEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(FinNetWorthSnapshotEntity::getUserId, userId)
                .orderByDesc(FinNetWorthSnapshotEntity::getSnapshotDate);

        // 添加日期范围过滤
        if (startDate != null) {
            queryWrapper.lambda().ge(FinNetWorthSnapshotEntity::getSnapshotDate, startDate);
        }
        if (endDate != null) {
            queryWrapper.lambda().le(FinNetWorthSnapshotEntity::getSnapshotDate, endDate);
        }

        List<FinNetWorthSnapshotEntity> snapshots = this.list(queryWrapper);
        List<Map<String, Object>> exportData = new ArrayList<>();

        for (FinNetWorthSnapshotEntity snapshot : snapshots) {
            Map<String, Object> data = new HashMap<>();
            data.put("快照日期", snapshot.getSnapshotDate());
            data.put("净资产", snapshot.getNetWorth());
            data.put("总资产", snapshot.getTotalAssets());
            data.put("总负债", snapshot.getTotalLiabilities());
            data.put("现金资产", snapshot.getCashAssets());
            data.put("投资资产", snapshot.getInvestmentAssets());
            data.put("固定资产", snapshot.getFixedAssets());
            data.put("其他资产", snapshot.getOtherAssets());
            data.put("短期负债", snapshot.getShortTermLiabilities());
            data.put("长期负债", snapshot.getLongTermLiabilities());
            data.put("创建时间", snapshot.getCreateTime());
            exportData.add(data);
        }

        log.info("导出净资产快照数据，用户ID: {}, 数据条数: {}", userId, exportData.size());

        return exportData;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean syncUserAssetDebtData(String userId) {
        Assert.notEmpty(userId, "用户ID不能为空");
        
        // 实现同步用户资产负债数据逻辑
        log.info("开始同步用户资产负债数据，用户ID: {}", userId);

        try {
            // TODO: 这里需要从各个资产负债模块获取最新数据
            // 1. 从动产资产表获取数据
            // 2. 从不动产资产表获取数据
            // 3. 从债务表获取数据
            // 4. 从其他资产负债表获取数据

            // 创建新的快照
            Date now = new Date();
            FinNetWorthSnapshotEntity snapshot = createSnapshot(userId, now);

            if (snapshot != null) {
                log.info("同步用户资产负债数据成功，用户ID: {}, 快照ID: {}", userId, snapshot.getId());
                return true;
            }

        } catch (Exception e) {
            log.error("同步用户资产负债数据失败，用户ID: {}", userId, e);
        }

        return false;
    }

    @Override
    public List<Map<String, Object>> getNetWorthForecast(String userId, Integer months) {
        // 实现净资产预测逻辑
        Assert.notEmpty(userId, "用户ID不能为空");

        if (months == null || months <= 0) {
            months = 12; // 默认预测12个月
        }

        // 获取历史数据
        QueryWrapper<FinNetWorthSnapshotEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(FinNetWorthSnapshotEntity::getUserId, userId)
                .orderByDesc(FinNetWorthSnapshotEntity::getSnapshotDate)
                .last("LIMIT 12"); // 取最近12个月的数据

        List<FinNetWorthSnapshotEntity> historicalData = this.list(queryWrapper);

        if (historicalData.size() < 3) {
            log.warn("历史数据不足，无法进行预测，用户ID: {}", userId);
            return new ArrayList<>();
        }

        // 简单的线性预测算法
        List<Map<String, Object>> forecast = new ArrayList<>();

        // 计算平均增长率
        BigDecimal totalGrowth = BigDecimal.ZERO;
        for (int i = 0; i < historicalData.size() - 1; i++) {
            BigDecimal current = historicalData.get(i).getNetWorth();
            BigDecimal previous = historicalData.get(i + 1).getNetWorth();

            if (previous != null && previous.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal growth = current.subtract(previous).divide(previous, 4, BigDecimal.ROUND_HALF_UP);
                totalGrowth = totalGrowth.add(growth);
            }
        }

        BigDecimal avgGrowthRate = totalGrowth.divide(new BigDecimal(historicalData.size() - 1), 4, BigDecimal.ROUND_HALF_UP);

        // 生成预测数据
        BigDecimal currentNetWorth = historicalData.get(0).getNetWorth();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(historicalData.get(0).getSnapshotDate());

        for (int i = 1; i <= months; i++) {
            calendar.add(Calendar.MONTH, 1);
            currentNetWorth = currentNetWorth.multiply(BigDecimal.ONE.add(avgGrowthRate));

            Map<String, Object> forecastPoint = new HashMap<>();
            forecastPoint.put("month", i);
            forecastPoint.put("date", calendar.getTime());
            forecastPoint.put("predictedNetWorth", currentNetWorth);
            forecastPoint.put("confidence", "中等"); // 简单的置信度标记

            forecast.add(forecastPoint);
        }

        log.info("生成净资产预测，用户ID: {}, 预测月数: {}", userId, months);

        return forecast;
    }

    @Override
    public Map<String, Object> getFinancialGoalAnalysis(String userId, BigDecimal targetAmount) {
        // 实现财务目标达成分析逻辑
        Assert.notEmpty(userId, "用户ID不能为空");
        Assert.notNull(targetAmount, "目标金额不能为空");

        Map<String, Object> analysis = new HashMap<>();

        // 获取当前净资产
        FinNetWorthSnapshotEntity latestSnapshot = getLatestSnapshot(userId);
        BigDecimal currentNetWorth = latestSnapshot != null && latestSnapshot.getNetWorth() != null
                ? latestSnapshot.getNetWorth() : BigDecimal.ZERO;

        // 计算目标差距
        BigDecimal gap = targetAmount.subtract(currentNetWorth);

        analysis.put("targetAmount", targetAmount);
        analysis.put("currentNetWorth", currentNetWorth);
        analysis.put("gap", gap);

        // 判断目标状态
        String status;
        if (gap.compareTo(BigDecimal.ZERO) <= 0) {
            status = "已达成";
        } else {
            status = "未达成";
        }
        analysis.put("status", status);

        // 计算达成率
        BigDecimal achievementRate = BigDecimal.ZERO;
        if (targetAmount.compareTo(BigDecimal.ZERO) > 0) {
            achievementRate = currentNetWorth.divide(targetAmount, 4, BigDecimal.ROUND_HALF_UP)
                    .multiply(new BigDecimal("100"));
        }
        analysis.put("achievementRate", achievementRate);

        // 预估达成时间（基于历史增长率）
        if (gap.compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal growthRate = calculateNetWorthGrowthRate(userId, 12);
            if (growthRate.compareTo(BigDecimal.ZERO) > 0) {
                // 简单计算：需要多少个月达成目标
                BigDecimal monthlyGrowthRate = growthRate.divide(new BigDecimal("12"), 4, BigDecimal.ROUND_HALF_UP);
                BigDecimal monthsNeeded = gap.divide(currentNetWorth.multiply(monthlyGrowthRate.divide(new BigDecimal("100"), 4, BigDecimal.ROUND_HALF_UP)), 0, BigDecimal.ROUND_UP);

                analysis.put("estimatedMonthsToTarget", monthsNeeded);

                Calendar calendar = Calendar.getInstance();
                calendar.add(Calendar.MONTH, monthsNeeded.intValue());
                analysis.put("estimatedTargetDate", calendar.getTime());
            }
        }

        return analysis;
    }

    @Override
    public Map<String, Object> getYearOverYearComparison(String userId, Integer compareMonths) {
        Assert.notEmpty(userId, "用户ID不能为空");

        if (compareMonths == null || compareMonths <= 0) {
            compareMonths = 12; // 默认12个月
        }

        Map<String, Object> comparison = new HashMap<>();

        // 获取当前时间点的快照
        FinNetWorthSnapshotEntity currentSnapshot = getLatestSnapshot(userId);
        if (currentSnapshot == null) {
            comparison.put("message", "暂无当前快照数据");
            return comparison;
        }

        // 计算去年同期的日期
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(currentSnapshot.getSnapshotDate());
        calendar.add(Calendar.MONTH, -compareMonths);
        Date compareDate = calendar.getTime();

        // 查找去年同期的快照（允许一定的时间误差）
        QueryWrapper<FinNetWorthSnapshotEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(FinNetWorthSnapshotEntity::getUserId, userId)
                .orderByAsc(FinNetWorthSnapshotEntity::getSnapshotDate);

        List<FinNetWorthSnapshotEntity> allSnapshots = this.list(queryWrapper);
        FinNetWorthSnapshotEntity compareSnapshot = null;

        // 找到最接近对比日期的快照
        long minTimeDiff = Long.MAX_VALUE;
        for (FinNetWorthSnapshotEntity snapshot : allSnapshots) {
            long timeDiff = Math.abs(snapshot.getSnapshotDate().getTime() - compareDate.getTime());
            if (timeDiff < minTimeDiff) {
                minTimeDiff = timeDiff;
                compareSnapshot = snapshot;
            }
        }

        if (compareSnapshot == null) {
            comparison.put("message", "暂无同期对比数据");
            return comparison;
        }

        // 计算各项对比数据
        BigDecimal currentNetWorth = currentSnapshot.getNetWorth() != null ? currentSnapshot.getNetWorth() : BigDecimal.ZERO;
        BigDecimal compareNetWorth = compareSnapshot.getNetWorth() != null ? compareSnapshot.getNetWorth() : BigDecimal.ZERO;

        BigDecimal currentAssets = currentSnapshot.getTotalAssets() != null ? currentSnapshot.getTotalAssets() : BigDecimal.ZERO;
        BigDecimal compareAssets = compareSnapshot.getTotalAssets() != null ? compareSnapshot.getTotalAssets() : BigDecimal.ZERO;

        BigDecimal currentLiabilities = currentSnapshot.getTotalLiabilities() != null ? currentSnapshot.getTotalLiabilities() : BigDecimal.ZERO;
        BigDecimal compareLiabilities = compareSnapshot.getTotalLiabilities() != null ? compareSnapshot.getTotalLiabilities() : BigDecimal.ZERO;

        // 计算变化金额和变化率
        BigDecimal netWorthChange = currentNetWorth.subtract(compareNetWorth);
        BigDecimal assetsChange = currentAssets.subtract(compareAssets);
        BigDecimal liabilitiesChange = currentLiabilities.subtract(compareLiabilities);

        BigDecimal netWorthChangeRate = BigDecimal.ZERO;
        BigDecimal assetsChangeRate = BigDecimal.ZERO;
        BigDecimal liabilitiesChangeRate = BigDecimal.ZERO;

        if (compareNetWorth.compareTo(BigDecimal.ZERO) > 0) {
            netWorthChangeRate = netWorthChange.divide(compareNetWorth, 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal("100"));
        }
        if (compareAssets.compareTo(BigDecimal.ZERO) > 0) {
            assetsChangeRate = assetsChange.divide(compareAssets, 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal("100"));
        }
        if (compareLiabilities.compareTo(BigDecimal.ZERO) > 0) {
            liabilitiesChangeRate = liabilitiesChange.divide(compareLiabilities, 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal("100"));
        }

        // 构建对比结果
        comparison.put("compareMonths", compareMonths);
        comparison.put("currentDate", currentSnapshot.getSnapshotDate());
        comparison.put("compareDate", compareSnapshot.getSnapshotDate());

        // 净资产对比
        Map<String, Object> netWorthComparison = new HashMap<>();
        netWorthComparison.put("current", currentNetWorth);
        netWorthComparison.put("compare", compareNetWorth);
        netWorthComparison.put("change", netWorthChange);
        netWorthComparison.put("changeRate", netWorthChangeRate);
        comparison.put("netWorth", netWorthComparison);

        // 总资产对比
        Map<String, Object> assetsComparison = new HashMap<>();
        assetsComparison.put("current", currentAssets);
        assetsComparison.put("compare", compareAssets);
        assetsComparison.put("change", assetsChange);
        assetsComparison.put("changeRate", assetsChangeRate);
        comparison.put("totalAssets", assetsComparison);

        // 总负债对比
        Map<String, Object> liabilitiesComparison = new HashMap<>();
        liabilitiesComparison.put("current", currentLiabilities);
        liabilitiesComparison.put("compare", compareLiabilities);
        liabilitiesComparison.put("change", liabilitiesChange);
        liabilitiesComparison.put("changeRate", liabilitiesChangeRate);
        comparison.put("totalLiabilities", liabilitiesComparison);

        // 财务健康度评估
        String healthTrend = "稳定";
        if (netWorthChangeRate.compareTo(new BigDecimal("10")) > 0) {
            healthTrend = "显著改善";
        } else if (netWorthChangeRate.compareTo(new BigDecimal("5")) > 0) {
            healthTrend = "有所改善";
        } else if (netWorthChangeRate.compareTo(new BigDecimal("-5")) < 0) {
            healthTrend = "有所下降";
        } else if (netWorthChangeRate.compareTo(new BigDecimal("-10")) < 0) {
            healthTrend = "显著下降";
        }
        comparison.put("healthTrend", healthTrend);

        log.info("生成同期对比分析，用户ID: {}, 对比月数: {}, 净资产变化率: {}%", userId, compareMonths, netWorthChangeRate);

        return comparison;
    }
}
