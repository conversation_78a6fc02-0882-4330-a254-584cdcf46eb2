package com.xinghuo.common.swagger;

import io.swagger.v3.oas.annotations.Operation;
import org.springdoc.webmvc.core.SpringWebMvcProvider;
import org.springframework.web.servlet.handler.AbstractHandlerMethodMapping;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping;

import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 自定义的SpringWebMvcProvider扩展，用于检索带有 @Operation 注解的处理方法。
 * <p>
 * SpringWebMvcProvider 类是与 SpringDoc OpenAPI 库相关的类之一。
 * SpringDoc OpenAPI 是一个用于自动生成和公开 API 文档的库，它与 Spring Web MVC 框架集成，帮助开发者以 OpenAPI 规范（前身是 Swagger 规范）自动生成 API 文档。
 * * SpringWebMvcProvider 是 SpringDoc OpenAPI 库中的一个提供者类，用于从 Spring Web MVC 应用程序中收集处理方法（handler methods）的信息，以便将其包含在生成的 OpenAPI 文档中。
 *
 * <AUTHOR>
 * @date 2023-10-05
 */
public class MySpringWebMvcProvider extends SpringWebMvcProvider {

    /**
     * 重写父类方法，获取处理方法的映射
     *
     * @return
     */
    @Override
    public Map<String, RequestMappingHandlerMapping> getHandlerMethods() {
        if (this.handlerMethods == null) {
            // 获取 Spring WebMvc 容器中的所有 RequestMappingHandlerMapping 类型的 Bean
            Map<String, RequestMappingHandlerMapping> beansOfTypeRequestMappingHandlerMapping = applicationContext.getBeansOfType(RequestMappingHandlerMapping.class);

            // 将每个 Bean 转换为 AbstractHandlerMethodMapping 对象，并获取其中的处理方法映射
            this.handlerMethods = beansOfTypeRequestMappingHandlerMapping.values().stream()
                    .map(AbstractHandlerMethodMapping::getHandlerMethods)
                    .map(Map::entrySet)
                    .flatMap(Collection::stream)
                    // 过滤出具有 Operation 注解的处理方法
                    .filter(v -> v.getValue().hasMethodAnnotation(Operation.class))
                    // 将处理方法收集到新的 Map 中，以方法名作为键
                    .collect(Collectors.toMap(
                            // 键：映射的 URL
                            Map.Entry::getKey,
                            // 值：HandlerMethod
                            Map.Entry::getValue,
                            // 合并函数（保留现有值，如果有重复的键）
                            (a1, a2) -> a1,
                            // 使用 LinkedHashMap 以保持插入顺序
                            LinkedHashMap::new
                    ));
        }
        // 返回处理方法的映射
        return this.handlerMethods;
    }
}
