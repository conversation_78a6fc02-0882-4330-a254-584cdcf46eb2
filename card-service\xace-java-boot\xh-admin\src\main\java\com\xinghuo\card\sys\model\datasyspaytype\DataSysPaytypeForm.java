package com.xinghuo.card.sys.model.datasyspaytype;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 收支类型管理
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2022-11-14
 */

@Data
public class DataSysPaytypeForm {

    @Schema(description = "主键")
    private String id;


    @Schema(description = "上级")
    private String parentId;


    @Schema(description = "类型")
    private Integer type;


    @Schema(description = "名称")
    @JsonProperty("fullName")
    private String name;


    @Schema(description = "排序")
    private Integer listOrder;


    @Schema(description = "系统分类")
    private Integer systemFlag;


    @Schema(description = "拼音")
    @JsonProperty("py")
    private String py;


    @Schema(description = "创建人")
    private String createBy;


    @Schema(description = "创建时间")
    private String createTime;


}
