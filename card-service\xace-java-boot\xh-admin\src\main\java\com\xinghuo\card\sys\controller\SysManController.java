package com.xinghuo.card.sys.controller;

import com.xinghuo.admin.util.GeneraterSwapUtil;
import com.xinghuo.card.sys.entity.SysManEntity;
import com.xinghuo.card.sys.model.sysman.DataSysManForm;
import com.xinghuo.card.sys.model.sysman.DataSysManPagination;
import com.xinghuo.card.sys.model.sysman.DataSysManVO;
import com.xinghuo.card.sys.model.sysman.SysManPagination;
import com.xinghuo.card.sys.service.SysManService;
import com.xinghuo.common.base.ActionResult;
import com.xinghuo.common.base.UserInfo;
import com.xinghuo.common.base.vo.ListVO;
import com.xinghuo.common.base.vo.PaginationVO;
import com.xinghuo.common.exception.DataException;
import com.xinghuo.common.util.UserProvider;
import com.xinghuo.common.util.core.BeanCopierUtils;
import com.xinghuo.common.util.core.DateXhUtil;
import com.xinghuo.common.util.core.RandomUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import java.io.IOException;
import java.time.LocalDate;
import java.time.Period;
import java.time.ZoneId;
import java.util.List;

/**
 * 持卡账户管理控制器
 * 用于管理持卡人的基本信息和代称
 * 支持多持卡人管理，每个持卡人有唯一的代称标识
 *
 * <AUTHOR> Assistant
 * @version V1.0.0
 * @date 2024-12-29
 */
@Slf4j
@RestController
@Tag(name = "持卡账户管理", description = "持卡账户管理")
@RequestMapping("/api/card/sys/man")
public class SysManController {

    @Autowired
    private SysManService sysManService;

    @Autowired
    private UserProvider userProvider;

    @Autowired
    private GeneraterSwapUtil generaterSwapUtil;

    /**
     * 获取持卡人列表
     *
     * @param dataSysManPagination 分页查询参数
     * @return 持卡人列表
     */
    @Operation(summary = "获取持卡人列表")
    @PostMapping("/getList")
    public ActionResult list(@RequestBody SysManPagination dataSysManPagination) throws IOException {
        List<SysManEntity> data = sysManService.getList(dataSysManPagination);
        List<DataSysManVO> listVOs = BeanCopierUtils.copyList(data, DataSysManVO.class);
        
        // 处理数据转换和补充
        for (DataSysManVO vo : listVOs) {
            enrichManVO(vo);
        }
        
        PaginationVO page = BeanCopierUtils.copy(dataSysManPagination, PaginationVO.class);
        return ActionResult.page(listVOs, page);
    }

    /**
     * 获取持卡人选择器列表
     *
     * @return 持卡人选择器列表
     */
    @Operation(summary = "获取持卡人选择器列表")
    @GetMapping("/selector")
    public ActionResult<ListVO<DataSysManVO>> selector() {
        List<SysManEntity> data = sysManService.getSelectList();
        List<DataSysManVO> listVOs = BeanCopierUtils.copyList(data, DataSysManVO.class);
        
        // 处理数据转换
        for (DataSysManVO vo : listVOs) {
            enrichManVO(vo);
        }
        
        ListVO<DataSysManVO> vo = new ListVO<>();
        vo.setList(listVOs);
        return ActionResult.success(vo);
    }

    /**
     * 获取持卡人详情
     *
     * @param id 持卡人ID
     * @return 持卡人详情
     */
    @GetMapping("/{id}")
    @Operation(summary = "获取持卡人详情")
    public ActionResult<DataSysManVO> info(@PathVariable("id") String id) {
        SysManEntity entity = sysManService.getInfo(id);
        if (entity == null) {
            return ActionResult.fail("持卡人不存在");
        }
        
        DataSysManVO vo = BeanCopierUtils.copy(entity, DataSysManVO.class);
        enrichManVO(vo);
        
        return ActionResult.success(vo);
    }

    /**
     * 表单信息(详情页)
     *
     * @param id 持卡人ID
     * @return 持卡人详情
     */
    @Operation(summary = "表单信息(详情页)")
    @GetMapping("/detail/{id}")
    public ActionResult<DataSysManVO> detailInfo(@PathVariable("id") String id) {
        SysManEntity entity = sysManService.getInfo(id);
        if (entity == null) {
            return ActionResult.fail("持卡人不存在");
        }
        
        DataSysManVO vo = BeanCopierUtils.copy(entity, DataSysManVO.class);
        enrichManVO(vo);
        
        return ActionResult.success(vo);
    }

    /**
     * 创建持卡人
     *
     * @param dataSysManForm 持卡人表单
     * @return 操作结果
     */
    @PostMapping
    @Operation(summary = "创建持卡人")
    public ActionResult create(@RequestBody @Valid DataSysManForm dataSysManForm) throws DataException {
        String mainId = RandomUtil.snowId();
        SysManEntity entity = BeanCopierUtils.copy(dataSysManForm, SysManEntity.class);
        entity.setId(mainId);
        
        sysManService.create(entity);
        return ActionResult.success("创建成功");
    }

    /**
     * 更新持卡人
     *
     * @param id             持卡人ID
     * @param dataSysManForm 持卡人表单
     * @return 操作结果
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新持卡人")
    public ActionResult update(@PathVariable("id") String id, @RequestBody @Valid DataSysManForm dataSysManForm) throws DataException {
        UserInfo userInfo = userProvider.get();
        SysManEntity entity = sysManService.getInfo(id);
        
        if (entity != null) {
            SysManEntity updateEntity = BeanCopierUtils.copy(dataSysManForm, SysManEntity.class);

            
            sysManService.update(id, updateEntity);
            return ActionResult.success("更新成功");
        } else {
            return ActionResult.fail("更新失败，数据不存在");
        }
    }

    /**
     * 删除持卡人
     *
     * @param id 持卡人ID
     * @return 操作结果
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除持卡人")
    public ActionResult delete(@PathVariable("id") String id) {
        SysManEntity entity = sysManService.getInfo(id);
        if (entity != null) {
            sysManService.delete(entity);
            return ActionResult.success("删除成功");
        } else {
            return ActionResult.fail("删除失败，数据不存在");
        }
    }

    /**
     * 批量删除持卡人
     *
     * @param idList 持卡人ID列表
     * @return 操作结果
     */
    @DeleteMapping("/batch")
    @Operation(summary = "批量删除持卡人")
    public ActionResult batchDelete(@RequestBody @NotEmpty List<String> idList) {
        int count = sysManService.batchDelete(idList);
        return ActionResult.success("批量删除成功，共删除" + count + "条记录");
    }

    /**
     * 检查持卡人代称是否唯一
     *
     * @param name 持卡人代称
     * @param id   排除的ID
     * @return 检查结果
     */
    @GetMapping("/checkName")
    @Operation(summary = "检查持卡人代称是否唯一")
    public ActionResult<Boolean> checkNameUnique(
            @Parameter(description = "持卡人代称") @RequestParam String name,
            @Parameter(description = "排除的ID") @RequestParam(required = false) String id) {
        boolean isUnique = sysManService.checkNameUnique(name, id);
        return ActionResult.success(isUnique);
    }

    /**
     * 更新排序序号
     *
     * @param id        持卡人ID
     * @param listOrder 排序序号
     * @return 操作结果
     */
    @PutMapping("/{id}/listOrder")
    @Operation(summary = "更新排序序号")
    public ActionResult updateListOrder(@PathVariable("id") String id, @RequestParam Integer listOrder) {
        boolean result = sysManService.updateListOrder(id, listOrder);
        return result ? ActionResult.success("排序更新成功") : ActionResult.fail("排序更新失败");
    }

    /**
     * 丰富持卡人VO对象
     * 添加性别名称、年龄、创建人名称等信息
     *
     * @param vo 持卡人VO对象
     */
    private void enrichManVO(DataSysManVO vo) {
        // 性别名称转换
        if ("1".equals(vo.getSex())) {
            vo.setSexName("男");
        } else if ("2".equals(vo.getSex())) {
            vo.setSexName("女");
        } else {
            vo.setSexName("未知");
        }
        
        // 计算年龄
        if (vo.getBirthday() != null) {
            LocalDate birthDate = vo.getBirthday().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
            LocalDate currentDate = LocalDate.now();
            vo.setAge(Period.between(birthDate, currentDate).getYears());
        }
        
        // 创建人和更新人名称转换
        vo.setCreateByName(generaterSwapUtil.userSelectValue(vo.getCreateBy()));
        vo.setUpdateByName(generaterSwapUtil.userSelectValue(vo.getUpdateBy()));
    }
}
