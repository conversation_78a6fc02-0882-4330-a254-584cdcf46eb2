<template>
  <BasicModal
    v-bind="$attrs"
    @register="registerModal"
    title="表单脚本"
    helpMessage="小程序不支持在线JS脚本"
    :width="1000"
    @ok="handleSubmit"
    @fullscreen-change="fullscreenChange"
    destroyOnClose
    canFullscreen
    defaultFullscreen
    class="form-script-modal">
    <div class="form-script-modal-body">
      <div class="left-board">
        <BasicTree ref="leftTreeRef" class="remove-active-tree" :treeData="treeData" defaultExpandAll @select="handleTreeSelect" />
      </div>
      <div class="main-board">
        <div class="main-board-editor">
          <MonacoEditor v-if="isOK" ref="editorRef" :provideHover="provideHover" v-model="text" />
        </div>
        <div class="main-board-tips">
          <p>请从左侧面板选择的字段名，支持JavaScript的脚本</p>
          <p> data--当前组件的选中数据，formData--表单数据</p>
          <p>
            <a @click="showDemo('setFormData')">setFormData</a>--设置表单某个组件数据(prop,value)，
            <a @click="showDemo('setShowOrHide')">setShowOrHide</a>--设置显示或隐藏(prop,value)，
            <a @click="showDemo('setRequired')">setRequired</a>--设置必填项(prop,value)
          </p>
          <p>
            <a @click="showDemo('setDisabled')">setDisabled-</a>-设置禁用项(prop,value)，
            <a @click="showDemo('request')">request</a>--异步请求(url,method,data)，
            <a @click="showDemo('setFieldOptions')">setFieldOptions</a>--设置表单某个组件下拉数据(prop,value)
          </p>
          <p>
            <a @click="showDemo('extraParams')">extraParams</a>--获取menudId、modelId、id、userInfo，
            <a @click="showDemo('useCustomDialog')">useCustomDialog</a>--自定义设置弹窗
          </p>
        </div>
      </div>
    </div>
    <CommonHelp @register="registerHelpModal" />
  </BasicModal>
</template>
<script lang="ts" setup>
  import { ref, unref, reactive } from 'vue';
  import { BasicModal, useModalInner, useModal } from '/@/components/Modal';
  import { BasicTree, TreeActionType } from '/@/components/Tree';
  import { MonacoEditor } from '/@/components/CodeEditor';
  import type { languages } from 'monaco-editor/esm/vs/editor/editor.api';
  import CommonHelp from '/@/components/ScriptApiDoc/commonHelp.vue';

  const props = defineProps(['treeTitle', 'drawingList']);
  const emit = defineEmits(['register', 'confirm']);
  const [registerModal, { closeModal }] = useModalInner(init);
  const [registerHelpModal, { openModal: openHelpModal }] = useModal();
  const editorRef = ref(null);
  const leftTreeRef = ref<Nullable<TreeActionType>>(null);
  const text = ref('');
  const isOK = ref(false);
  const treeData = ref<any[]>([]);

  const provideHover = reactive<languages.HoverProvider>({
    // @ts-expect-error
    provideHover(model, position) {
      // const lineword = model.getLineContent(position.lineNumber);
      const word = model.getWordAtPosition(position)?.word;
      let returnValue = {};
      switch (word) {
        case 'data':
          returnValue = {
            contents: [
              {
                value: ['当前组件的选中数据', `${word}：Record<string, any>`].join('\n\n'),
              },
            ],
          };
          break;
        case 'formData':
          returnValue = {
            contents: [
              {
                value: ['表单数据', `${word}：Record<string, any>`].join('\n\n'),
              },
            ],
          };
          break;
        case 'setFormData':
          returnValue = {
            contents: [
              {
                value: ['设置表单某个组件数据', `${word}：(prop: string, value: any) => void`].join('\n\n'),
              },
            ],
          };
          break;
        case 'setShowOrHide':
          returnValue = {
            contents: [
              {
                value: ['设置显示或隐藏', `${word}：(prop: string, value: boolean) => void`].join('\n\n'),
              },
            ],
          };
          break;
        case 'setRequired':
          returnValue = {
            contents: [
              {
                value: ['设置必填项', `${word}：(prop: string, value: boolean) => void`].join('\n\n'),
              },
            ],
          };
          break;
        case 'setDisabled':
          returnValue = {
            contents: [
              {
                value: ['设置禁用项', `${word}：(prop: string, value: boolean) => void`].join('\n\n'),
              },
            ],
          };
          break;
        case 'request':
          returnValue = {
            contents: [
              {
                value: ['异步请求', `${word}：(url: string, method: 'post' | 'get', data: Record<string, any>) => VAxios`].join('\n\n'),
              },
            ],
          };
          break;
        case 'setFieldOptions':
          returnValue = {
            contents: [
              {
                value: ['设置下拉框选项', `${word}：(prop: string, value: any) => void`].join('\n\n'),
              },
            ],
          };
          break;
        case 'extraParams':
          returnValue = {
            contents: [
              {
                value: [
                  `获取额外的数据 ${word}`,
                  '{',
                  '&nbsp;&nbsp;id: string 表单id,',
                  '&nbsp;&nbsp;menuId: string 菜单id,',
                  '&nbsp;&nbsp;modelId: string 在线表单id,',
                  '&nbsp;&nbsp;userInfo: Record<string, any>, 用户信息',
                  '&nbsp;&nbsp;util: Record<string, any>, 工具函数',
                  '}',
                ].join('\n\n'),
              },
            ],
          };
          break;
        case 'useCustomDialog':
          returnValue = {
            contents: [
              {
                value: [
                  `自定义设置弹窗 ${word}`,
                  `() => {`,
                  `&nbsp;&nbsp;addDialog: (option: Partial<CustomDialogParams&gt;) => string`,
                  `&nbsp;&nbsp;getDialog: (id: string) => CustomDialogParams`,
                  `&nbsp;&nbsp;closeDialog: (id: string) => string`,
                  `}`,
                ].join('\n\n'),
              },
              {
                value: [
                  `interface CustomDialogParams {`,
                  `&nbsp;&nbsp;id: string`,
                  `&nbsp;&nbsp;modelId?: string 在线表单id`,
                  `&nbsp;&nbsp;type?: 1 | 2 | 3 类型`,
                  `&nbsp;&nbsp;title?: string 弹窗标题`,
                  `&nbsp;&nbsp;params?: Record<string, any> 父页面给弹窗页面传递的参数`,
                  `&nbsp;&nbsp;width?: string 弹窗宽度`,
                  `&nbsp;&nbsp;style?: Record<string, any> 弹窗样式`,
                  `&nbsp;&nbsp;formUrl?: string 打开系统表单文件路径（仅限views目录下文件）`,
                  `}`,
                ].join('\n\n'),
              },
            ],
          };
          break;

        default:
          break;
      }
      return returnValue;
    },
  });

  function showDemo(type) {
    openHelpModal(true, type);
    return false;
  }

  function reloadEditor() {
    isOK.value = false;
    setTimeout(() => {
      isOK.value = true;
    }, 100);
  }

  function init(data) {
    text.value = data.text;
    getTreeList();
    reloadEditor();
  }
  function getTreeList() {
    let list: any[] = [];
    const loop = (data, parent?) => {
      if (!data) return;
      if (data.__config__ && data.__config__.xhKey !== 'table' && data.__config__.children && Array.isArray(data.__config__.children)) {
        loop(data.__config__.children, data);
      }
      if (Array.isArray(data)) data.forEach(d => loop(d, parent));
      if (data.__vModel__) {
        if (data.__config__.xhKey === 'table') {
          let item: Recordable = {
            id: data.__vModel__,
            fullName: data.__config__.label,
            children: [],
          };
          let children: any[] = [];
          if (data.__config__.children && Array.isArray(data.__config__.children) && data.__config__.children.length) {
            for (let i = 0; i < data.__config__.children.length; i++) {
              const child = data.__config__.children[i];
              if (child.__vModel__) {
                children.push({ id: data.__vModel__ + '.' + child.__vModel__, fullName: child.__config__.label });
              }
            }
          }
          item.children = children;
          list.push(item);
        } else {
          list.push({ id: data.__vModel__, fullName: data.__config__.label });
        }
      }
    };
    loop(unref(props.drawingList));
    const topItem = {
      id: (+new Date()).toString(),
      fullName: props.treeTitle,
      top: true,
      children: list,
    };
    treeData.value = [topItem];
  }

  function fullscreenChange() {
    reloadEditor();
  }

  function handleTreeSelect(keys) {
    if (!keys.length) return;
    const leftTree = unref(leftTreeRef);
    const selectedNode: any = leftTree?.getSelectedNode(keys[0]);
    if (selectedNode.top || selectedNode.children) return;
    (editorRef.value as any)?.insert(keys[0]);
  }
  function handleSubmit() {
    emit('confirm', text.value);
    closeModal();
  }
</script>
