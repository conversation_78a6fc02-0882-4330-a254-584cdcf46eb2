package com.xinghuo.card.budget.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 预算主表
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2024-06-28
 */
@Data
@TableName("fin_budget")
public class FinBudgetEntity implements Serializable {

    /**
     * 主键ID
     */
    @TableId("ID")
    private String id;

    /**
     * 用户ID, 关联至现有用户体系
     */
    @TableField("USER_ID")
    private String userId;

    /**
     * 预算名称
     */
    @TableField("NAME")
    private String name;

    /**
     * 周期类型：MONTHLY-月度, YEARLY-年度
     */
    @TableField("PERIOD_TYPE")
    private String periodType;

    /**
     * 开始日期
     */
    @TableField("START_DATE")
    private Date startDate;

    /**
     * 结束日期
     */
    @TableField("END_DATE")
    private Date endDate;

    /**
     * 总预算金额 (由子项汇总)
     */
    @TableField("TOTAL_BUDGET_AMOUNT")
    private BigDecimal totalBudgetAmount;

    /**
     * 状态：ACTIVE-激活, ARCHIVED-归档
     */
    @TableField("STATUS")
    private String status;

    /**
     * 创建人
     */
    @TableField("CREATE_BY")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField("CREATE_TIME")
    private Date createTime;

    /**
     * 更新人
     */
    @TableField("UPDATE_BY")
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField("UPDATE_TIME")
    private Date updateTime;

    /**
     * 租户id
     */
    @TableField("f_tenantid")
    private String tenantId;

    /**
     * 流程id
     */
    @TableField("f_flowid")
    private String flowId;
}