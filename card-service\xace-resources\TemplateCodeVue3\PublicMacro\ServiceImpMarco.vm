##通用参数
#parse("PublicMacro/ConstantMarco.vm")
#ConstantParams()
#set($mapObject ="Map<String, Object>")
##创建QueryWrapper
#macro(CreateWrapper)
    #foreach($child in ${allTableNameList})
        #set($ModelName= "${child.table.substring(0,1).toUpperCase()}${child.table.substring(1)}")
        #set($modelName ="${child.table.substring(0,1).toLowerCase()}${child.table.substring(1)}")
        #set($modelQueryWrapper = "${modelName}QueryWrapper")
        #set($modelNum = "${modelName}Num")
    int ${modelNum} =0;
    QueryWrapper<${ModelName}Entity> ${modelQueryWrapper}=new QueryWrapper<>();
    #end
#end
## 树形QueryWrapper
#macro(treeTableLazyLoad)
    #if($treeTable == true&&$treeLazyType==false)
        if (! ${name}Pagination.isHasParam()) {
        if (StringUtil.isEmpty( ${name}Pagination.getTreeParentValue())) {
        ${name}QueryWrapper.lambda().and(t -> t.eq(${Name}Entity::get${parentField}, "[]" )
        .or().isNull(${Name}Entity::get${parentField})
        .or().eq(${Name}Entity::get${parentField}, "" ));
        } else {
        ${name}QueryWrapper.lambda().eq(${Name}Entity::get${parentField},  ${name}Pagination.getTreeParentValue());
        }
        }
    #end
#end
##
#macro(countTableSize)
    #foreach($child in ${allTableNameList})
        #set($modelName ="${child.table.substring(0,1).toLowerCase()}${child.table.substring(1)}")
        #if(${child.tableField})
            #set($relationField = "${child.tableField.substring(0,1).toUpperCase()}${child.tableField.substring(1)}")
            long ${modelName}count = ${modelName}Service.count();
        #end
    #end
#end
##
#macro(listWrapper)
    #foreach($child in ${allTableNameList})
        #set($ModelName= "${child.table.substring(0,1).toUpperCase()}${child.table.substring(1)}")
        #set($modelName ="${child.table.substring(0,1).toLowerCase()}${child.table.substring(1)}")
        #set($modelQueryWrapper = "${modelName}QueryWrapper")
        #set($modelNum = "${modelName}Num")
        #if(${child.tableField})
            #set($relationField = "${child.tableField.substring(0,1).toUpperCase()}${child.tableField.substring(1)}")
            if(${modelNum}>0){
            List<String> ${modelName}IdList = ${modelName}Service.list($modelQueryWrapper).stream().filter(t->StringUtil.isNotEmpty(t.get${relationField}())).map(t->t.get${relationField}()).collect(Collectors.toList());
            long count = ${modelName}Service.count();
            if (count>0){
            intersectionList.add(${modelName}IdList);
            }
            AllIdList.addAll(${modelName}IdList);
            }
            total+=${modelNum};
        #end
    #end
#end
##权限查询条件拼接
#macro(CreateDataPermission $menuIdModel)
    boolean pcPermission = ${pcDataPermisson};
    boolean appPermission = ${appDataPermisson};
    boolean	isPc = "pc".equals(ServletUtil.getHeader("xh-origin"));
    if(isPc && pcPermission){
    if (!userProvider.get().getIsAdministrator()){
    #foreach($child in ${allTableNameList})
        #set($ModelName= "${child.table.substring(0,1).toUpperCase()}${child.table.substring(1)}")
        #set($modelName ="${child.table.substring(0,1).toLowerCase()}${child.table.substring(1)}")
        #set($modelNum = "${modelName}Num")
        #set($initName = "${child.initName}")
        #set($modelQueryWrapper = "${modelName}QueryWrapper")
        Object ${modelName}Obj=authorizeUtil.getCondition(new AuthorizeConditionModel(${modelQueryWrapper},${menuIdModel}.getMenuId(),"$child.initName"));
        if (ObjectUtil.isEmpty(${modelName}Obj)){
        return new ArrayList<>();
        } else {
        ${modelQueryWrapper} = (QueryWrapper<${ModelName}Entity>)${modelName}Obj;
        if( ${modelQueryWrapper}.getExpression().getNormal().size()>0){
        ${modelNum}++;
        }
        }
    #end
    }
    }
    if(!isPc && appPermission){
    if (!userProvider.get().getIsAdministrator()){
    #foreach($child in ${allTableNameList})
        #set($ModelName= "${child.table.substring(0,1).toUpperCase()}${child.table.substring(1)}")
        #set($modelName ="${child.table.substring(0,1).toLowerCase()}${child.table.substring(1)}")
        #set($modelNum = "${modelName}Num")
        #set($modelQueryWrapper = "${modelName}QueryWrapper")
        Object ${modelName}Obj=authorizeUtil.getCondition(new AuthorizeConditionModel(${modelQueryWrapper},${menuIdModel}.getMenuId(),"$child.initName"));
        if (ObjectUtil.isEmpty(${modelName}Obj)){
        return new ArrayList<>();
        } else {
        ${modelQueryWrapper} = (QueryWrapper<${ModelName}Entity>)${modelName}Obj;
        if( ${modelQueryWrapper}.getExpression().getNormal().size()>0){
        ${modelNum}++;
        }
        }


    #end
    }
    }
#end
##普通查询条件拼接
#macro(PaginationSerach)
    #if($groupModels.size()>0)
        if(isPc){
        #foreach($Group in ${groupModels})
            #set($ModelName= "${Group.modelName.substring(0,1).toUpperCase()}${Group.modelName.substring(1)}")
            #set($ModelNameEntity = "${ModelName}Entity")
            #set($modelName ="${Group.modelName.substring(0,1).toLowerCase()}${Group.modelName.substring(1)}")
            #set($modelQueryWrapper ="${modelName}QueryWrapper")
            #set($modelNum = "${modelName}Num")
            #set($ForeignKey =${Group.ForeignKey})
            #PaginationQuery(${Group.searchTypeModelList},${modelQueryWrapper},${ModelNameEntity},${modelNum})
        #end
        }
    #end
    #if($groupAppModels.size()>0)
        if(!isPc){
        #foreach($appGroup in ${groupAppModels})
            #set($ModelName= "${appGroup.modelName.substring(0,1).toUpperCase()}${appGroup.modelName.substring(1)}")
            #set($ModelNameEntity = "${ModelName}Entity")
            #set($modelName ="${appGroup.modelName.substring(0,1).toLowerCase()}${appGroup.modelName.substring(1)}")
            #set($modelQueryWrapper ="${modelName}QueryWrapper")
            #set($modelNum = "${modelName}Num")
            #set($ForeignKey =${appGroup.ForeignKey})
            #PaginationQuery(${appGroup.searchTypeModelList},${modelQueryWrapper},${ModelNameEntity},${modelNum})
        #end
        }
    #end
#end
##普通查询条件拼接-子方法
#macro(PaginationQuery $searchListSizes $queryWrapper $ModelNameEntity,$modelNum)
    #if($searchListSizes)
        #foreach($SearchTypeModel in ${searchListSizes})
            #if($SearchTypeModel.afterVModel)
                #set($vModelName = $SearchTypeModel.afterVModel)
            #else
                #set($vModelName = $SearchTypeModel.id)
            #end
            #set($fieldName = "${vModelName.substring(0,1).toUpperCase()}${vModelName.substring(1)}")
            #set($paginationName ="${SearchTypeModel.id.substring(0,1).toUpperCase()}${SearchTypeModel.id.substring(1)}")
            if(ObjectUtil.isNotEmpty(${name}Pagination.get${paginationName}())){
            ${modelNum}++;

            #if(${SearchTypeModel.searchType}==1)
                #set($xhKey=${SearchTypeModel.xhKey})
##                多选统一处理
                #MultipleHandle("${name}Pagination")
            #elseif(${SearchTypeModel.searchType}==2)
                ${queryWrapper}.lambda().like(${ModelNameEntity}::get${fieldName},${name}Pagination.get${paginationName}());
            #elseif(${SearchTypeModel.searchType}==3)
                List ${fieldName}List =  JsonXhUtil.getJsonToList(${name}Pagination.get${paginationName}(),String.class);
                #if(${SearchTypeModel.xhKey}=="dateTime" || ${SearchTypeModel.xhKey}=='datePicker' || ${SearchTypeModel.xhKey}=="createTime" || ${SearchTypeModel.xhKey}=="modifyTime")
                    Long fir = Long.valueOf(String.valueOf(${fieldName}List.get(0)));
                    Long sec = Long.valueOf(String.valueOf(${fieldName}List.get(1)));

                    ${queryWrapper}.lambda().ge(${ModelNameEntity}::get${fieldName}, new Date(fir))
                    .le(${ModelNameEntity}::get${fieldName}, DateUtil.stringToDate(DateUtil.daFormatYmd(sec) + " 23:59:59"));
                #elseif(${SearchTypeModel.xhKey}=='timePicker')
                    String fir = String.valueOf(${fieldName}List.get(0));
                    String sec = String.valueOf(${fieldName}List.get(1));
                    ${queryWrapper}.lambda().ge(${ModelNameEntity}::get${fieldName}, fir)
                    .le(${ModelNameEntity}::get${fieldName}, sec);
                #elseif(${SearchTypeModel.xhKey}=="inputNumber" || ${SearchTypeModel.xhKey}=="calculate")
                    for(int i=0;i<${fieldName}List.size();i++){
                    String id = String.valueOf(${fieldName}List.get(i));
                    boolean idAll = StringUtil.isNotEmpty(id) && !id.equals("null");
                    if(idAll){
                    BigDecimal b= new BigDecimal(id);
                    if(i==0){
                    ${queryWrapper}.lambda().ge(${ModelNameEntity}::get${fieldName},b);
                    }else{
                    ${queryWrapper}.lambda().le(${ModelNameEntity}::get${fieldName},b);
                    }
                    }
                    }
                #else
                    String fir = String.valueOf(${fieldName}List.get(0));
                    String sec = String.valueOf(${fieldName}List.get(1));
                    ${queryWrapper}.lambda().ge(${ModelNameEntity}::get${fieldName}, fir)
                    .le(${ModelNameEntity}::get${fieldName}, sec);
                #end

            #end

            }

        #end
    #end
#end
##子表普通查询条件拼接
#macro(childPaginationSearch $searchListSizes,$queryWrapper,$ModelNameEntity)
    #if($searchListSizes)
        #foreach($SearchTypeModel in ${searchListSizes})
            #if($SearchTypeModel.afterVModel)
                #set($vModelName = $SearchTypeModel.afterVModel)
            #else
                #set($vModelName = $SearchTypeModel.id)
            #end
            #set($fieldName = "${vModelName.substring(0,1).toUpperCase()}${vModelName.substring(1)}")
            #set($paginationName ="${SearchTypeModel.id.substring(0,1).toUpperCase()}${SearchTypeModel.id.substring(1)}")
            if(ObjectUtil.isNotEmpty(pagination.get${paginationName}())){
            #if(${SearchTypeModel.searchType}==1)
                #set($xhKey=${SearchTypeModel.xhKey})
##                多选统一处理
                #MultipleHandle("pagination")
            #elseif(${SearchTypeModel.searchType}==2)
                ${queryWrapper}.lambda().like(${ModelNameEntity}::get${fieldName},pagination.get${paginationName}());
            #elseif(${SearchTypeModel.searchType}==3)
                List ${fieldName}List = JsonXhUtil.getJsonToList(pagination.get${paginationName}(), String.class);
                #if(${SearchTypeModel.xhKey}=="dateTime" || ${SearchTypeModel.xhKey}=='datePicker' || ${SearchTypeModel.xhKey}=="createTime" || ${SearchTypeModel.xhKey}=="modifyTime")
                    Long fir = Long.valueOf(String.valueOf(${fieldName}List.get(0)));
                    Long sec = Long.valueOf(String.valueOf(${fieldName}List.get(1)));

                    ${queryWrapper}.lambda().ge(${ModelNameEntity}::get${fieldName}, new Date(fir))
                    .le(${ModelNameEntity}::get${fieldName}, DateUtil.stringToDate(DateUtil.daFormatYmd(sec) + " 23:59:59"));
                #elseif(${SearchTypeModel.xhKey}=='timePicker')
                    String fir = String.valueOf(${fieldName}List.get(0));
                    String sec = String.valueOf(${fieldName}List.get(1));
                    ${queryWrapper}.lambda().ge(${ModelNameEntity}::get${fieldName}, fir)
                    .le(${ModelNameEntity}::get${fieldName}, sec);
                #elseif(${SearchTypeModel.xhKey}=="inputNumber" || ${SearchTypeModel.xhKey}=="calculate")
                    for(int i=0;i<${fieldName}List.size();i++){
                    String id = String.valueOf(${fieldName}List.get(i));
                    boolean idAll = StringUtil.isNotEmpty(id) && !id.equals("null");
                    if(idAll){
                    BigDecimal b= new BigDecimal(id);
                    if(i==0){
                    ${queryWrapper}.lambda().ge(${ModelNameEntity}::get${fieldName},b);
                    }else{
                    ${queryWrapper}.lambda().le(${ModelNameEntity}::get${fieldName},b);
                    }
                    }
                    }
                #else
                    String fir = String.valueOf(${fieldName}List.get(0));
                    String sec = String.valueOf(${fieldName}List.get(1));
                    ${queryWrapper}.lambda().ge(${ModelNameEntity}::get${fieldName}, fir)
                    .le(${ModelNameEntity}::get${fieldName}, sec);
                #end

            #end
            }

        #end
    #end

#end
##多选统一处理
#macro(MultipleHandle $pagination)
    #if(${multipleUnit.contains(${xhKey})})
        List<String> idList = new ArrayList<>();
        try {
        String[][] ${vModelName} = JsonXhUtil.toBean(${pagination}.get${paginationName}(),String[][].class);
        for(int i=0;i<${vModelName}.length;i++){
        if(${vModelName}[i].length>0){
        idList.add(JsonXhUtil.toJSONString(Arrays.asList(${vModelName}[i])));
        }
        }
        }catch (Exception e1){
        try {
        List<String> ${vModelName} = JsonXhUtil.getJsonToList(${pagination}.get${paginationName}(),String.class);
        if(${vModelName}.size()>0){
        #if(${multipleTwoUnit.contains(${xhKey})})
            idList.add(JsonXhUtil.toJSONString(${vModelName}));
        #elseif(${xhKey} =='currOrganize')
            idList.add(${vModelName}.get(${vModelName}.size()-1));
        #else
            idList.addAll(${vModelName});
        #end
        }
        }catch (Exception e2){
        idList.add(String.valueOf(${pagination}.get${paginationName}()));
        }
        }
        ${queryWrapper}.lambda().and(t->{
        idList.forEach(tt->{
        t.like(${ModelNameEntity}::get${fieldName}, tt).or();
        });
        });
    #else
        ${queryWrapper}.lambda().eq(${ModelNameEntity}::get${fieldName},${pagination}.get${paginationName}());
    #end
#end
##
#macro(SuperQuery $tableName)
    if (ObjectUtil.isNotEmpty(${tableName}Pagination.getSuperQueryJson())){
    SuperQueryJsonModel superQueryJsonModel = JsonXhUtil.toBean(${tableName}Pagination.getSuperQueryJson(), SuperQueryJsonModel.class);
    String matchLogic = superQueryJsonModel.getMatchLogic();
    List<ConditionJsonModel> superQueryList = JsonXhUtil.getJsonToList(superQueryJsonModel.getConditionJson(), ConditionJsonModel.class);
    for (ConditionJsonModel conditionjson : superQueryList){
    $mapObject map = JsonXhUtil.stringToMap(conditionjson.getAttr());
    $mapObject configMap = JsonXhUtil.stringToMap(map.get("__config__").toString());
    String tableName = configMap.get("relationTable")!=null ? String.valueOf(configMap.get("relationTable")) : String.valueOf(configMap.get("tableName"));
    if (map.get("multiple") != null) {
    if (Boolean.valueOf(String.valueOf(map.get("multiple"))) && ObjectUtil.isNull(conditionjson.getFieldValue())) {
    conditionjson.setFieldValue("[]");
    }
    }
    conditionjson.setTableName(tableName);
    }
    List<String> allSuperList = new ArrayList<>();
    List<List<String>> intersectionSuperList  = new ArrayList<>();



    #foreach($child in ${allTableNameList})
        #set($ModelName= "${child.table.substring(0,1).toUpperCase()}${child.table.substring(1)}")
        #set($modelName ="${child.table.substring(0,1).toLowerCase()}${child.table.substring(1)}")
        #set($modelQueryWrapper = "${modelName}SuperWrapper")
        #set($modelNum = "${modelName}Num1")
        int $modelNum = 0;
        QueryWrapper<${ModelName}Entity> $modelQueryWrapper = new QueryWrapper<>();
        $modelNum = generaterSwapUtil.getCondition(new SuperQueryConditionModel(${modelQueryWrapper},superQueryList,matchLogic,"$child.initName")
        ,new ${ModelName}Entity(),$modelNum);
        #if(${child.tableField})
            #set($relationField = "${child.tableField.substring(0,1).toUpperCase()}${child.tableField.substring(1)}")
            if ($modelNum>0 && ${modelName}count>0){

            List<String> ${modelName}List =${modelName}Service.list($modelQueryWrapper).stream().map(${ModelName}Entity::get${relationField}).collect(Collectors.toList());
            allSuperList.addAll(${modelName}List);
            intersectionSuperList.add(${modelName}List);
            }
        #else
            if ($modelNum>0){
            #if($snowflake)
                List<String> ${modelName}List =this.list($modelQueryWrapper).stream().map(${ModelName}Entity::get${peimaryKeyName}).collect(Collectors.toList());
            #else
                List<String> ${modelName}List =this.list($modelQueryWrapper).stream().map(${ModelName}Entity::getFlowtaskid).collect(Collectors.toList());
            #end
            allSuperList.addAll(${modelName}List);
            intersectionSuperList.add(${modelName}List);
            }
        #end
    #end
    superOp = matchLogic;
    //and or
    if(matchLogic.equalsIgnoreCase("and")){
    allSuperIDlist = generaterSwapUtil.getIntersection(intersectionSuperList);
    }else{
    allSuperIDlist = allSuperList;
    }
    }
#end
##
#macro(ChildSuperQuery $childModelName)
    if (ObjectUtil.isNotEmpty(pagination.getSuperQueryJson())){
    SuperQueryJsonModel superQueryJsonModel = JsonXhUtil.toBean(pagination.getSuperQueryJson(), SuperQueryJsonModel.class);
    String matchLogic = superQueryJsonModel.getMatchLogic();
    List<ConditionJsonModel> superQueryList = JsonXhUtil.getJsonToList(superQueryJsonModel.getConditionJson(), ConditionJsonModel.class);
    for (ConditionJsonModel conditionjson : superQueryList){
    $mapObject map = JsonXhUtil.stringToMap(conditionjson.getAttr());
    $mapObject configMap = JsonXhUtil.stringToMap(map.get("__config__").toString());
    String tableName = configMap.get("relationTable")!=null ? String.valueOf(configMap.get("relationTable")) : String.valueOf(configMap.get("tableName"));
    if (map.get("multiple") != null) {
    if (Boolean.valueOf(String.valueOf(map.get("multiple"))) && ObjectUtil.isNull(conditionjson.getFieldValue())) {
    conditionjson.setFieldValue("[]");
    }
    }
    conditionjson.setTableName(tableName);
    }
    #foreach($child in ${allTableNameList})
        #if(${child.table} == $childModelName)
            #set($ModelName= "${child.table.substring(0,1).toUpperCase()}${child.table.substring(1)}")
            #set($modelName ="${child.table.substring(0,1).toLowerCase()}${child.table.substring(1)}")
            #set($modelQueryWrapper = "${modelName}QueryWrapper")
            $modelNum = generaterSwapUtil.getCondition(new SuperQueryConditionModel(${modelQueryWrapper},superQueryList,matchLogic,"$child.initName")
            ,new ${ModelName}Entity(),$modelNum);
        #end
    #end
    }
#end
##
#macro(ChildDataPermission $menuIdModel)
    if(isPc && pcPermission){
    if (!userProvider.get().getIsAdministrator()){
    #foreach($child in ${allTableNameList})
        #if($menuIdModel == $child.table)
            #set($ModelName= "${child.table.substring(0,1).toUpperCase()}${child.table.substring(1)}")
            #set($modelName ="${child.table.substring(0,1).toLowerCase()}${child.table.substring(1)}")
            #set($modelNum = "${modelName}Num")
            #set($initName = "${child.initName}")
            #set($modelQueryWrapper = "${modelName}QueryWrapper")

            Object ${modelName}Obj=authorizeUtil.getCondition(new AuthorizeConditionModel(${modelQueryWrapper},pagination.getMenuId(),"$child.initName"));
            if (ObjectUtil.isEmpty(${modelName}Obj)){

            } else {
            ${modelQueryWrapper} = (QueryWrapper<${ModelName}Entity>)${modelName}Obj;
            if( ${modelQueryWrapper}.getExpression().getNormal().size()>0){
            ${modelNum}++;
            }
            }
        #end
    #end
    }
    }
    if(!isPc && appPermission){
    if (!userProvider.get().getIsAdministrator()){
    #foreach($child in ${allTableNameList})
        #if($menuIdModel == $child.table)
            #set($ModelName= "${child.table.substring(0,1).toUpperCase()}${child.table.substring(1)}")
            #set($modelName ="${child.table.substring(0,1).toLowerCase()}${child.table.substring(1)}")
            #set($modelNum = "${modelName}Num")
            #set($modelQueryWrapper = "${modelName}QueryWrapper")
            Object ${modelName}Obj=authorizeUtil.getCondition(new AuthorizeConditionModel(${modelQueryWrapper},pagination.getMenuId(),"$child.initName"));
            if (ObjectUtil.isEmpty(${modelName}Obj)){

            } else {
            ${modelQueryWrapper} = (QueryWrapper<${ModelName}Entity>)${modelName}Obj;
            if( ${modelQueryWrapper}.getExpression().getNormal().size()>0){
            ${modelNum}++;
            }
            }
        #end

    #end
    }
    }
#end
##########################以上为宏调用，以下为实际方法##########################
##  列表接口
#macro(GetTypeList)
    @Override
    public List<${Entity}> getList(${Name}Pagination ${name}Pagination){
        return getTypeList(${name}Pagination,${name}Pagination.getDataType());
    }
    /** 列表查询 */
    @Override
    public List<${Entity}> getTypeList(${Name}Pagination ${name}Pagination,String dataType){
    String userId=userProvider.get().getUserId();
    List<String> AllIdList =new ArrayList();
    List<List<String>> intersectionList =new ArrayList<>();
    int total=0;
    #CreateWrapper()
##树形，异步父级查询条件
    #treeTableLazyLoad()
    #countTableSize()
    #if($superQuery)
        List<String> allSuperIDlist = new ArrayList<>();
        String superOp ="";
        #SuperQuery(${name})
    #end
    #CreateDataPermission("${name}Pagination")

    #if (${hasSub} ==true && ( ${hasFilter} == true ||${hasAppFilter} == true ) )
        // 子副表过滤条件
        List<String> filterIds = new ArrayList<>();
        String tableName;
        $mapObject newtabMap=${Name}Constant.TABLEFIELDKEY.entrySet()
        .stream().collect( Collectors.toMap(e->e.getValue(),e->e.getKey()));
        #foreach($child in ${allTableNameList})
        #set($ModelName= "${child.table.substring(0,1).toUpperCase()}${child.table.substring(1)}")
        #set($modelName ="${child.table.substring(0,1).toLowerCase()}${child.table.substring(1)}")
        #set($modelNum = "${modelName}Num1")
        #if(${child.tableField})
        #set($relationField = "${child.tableField.substring(0,1).toUpperCase()}${child.tableField.substring(1)}")
        #if(${child.tableTag}!="main")
        tableName="${modelName}";
        tableName=newtabMap.get(tableName)==null?tableName:newtabMap.get(tableName).toString();
        generaterSwapUtil.wrapperHandle(${Name}Constant.getColumnData(), ${Name}Constant.getAppColumnData(), ${modelName}QueryWrapper,${ModelName}Entity.class,"${child.tableTag}",tableName);
        #end
        List<String> ${modelName}FilterList = ${modelName}Service.list(${modelName}QueryWrapper).stream().map(${ModelName}Entity::get${relationField}).collect(Collectors.toList());
        #if(${foreach.index}==1)
        filterIds = ${modelName}FilterList;
        #else
        filterIds.retainAll(${modelName}FilterList);
        #end
        #end
        #end
        if(filterIds.size()==0 && !generaterSwapUtil.onlyMainFilter(${Name}Constant.getColumnData(), ${Name}Constant.getAppColumnData())){
            return new ArrayList<>();
        }
        List<String> finalFilterIds = filterIds;
        ${name}QueryWrapper.lambda().and(t -> t.in(${Entity}::get${peimaryKeyName}, finalFilterIds ));

    #end
    #PaginationSerach()
    #listWrapper()
    List<String> intersection = generaterSwapUtil.getIntersection(intersectionList);
    if (total>0){
    if (intersection.size()==0){
    intersection.add("xhNullList");
    }
    #if($snowflake)
        ${QueryWrapper}.lambda().in(${Entity}::get${peimaryKeyName}, intersection);
    #else
        ${QueryWrapper}.lambda().in(${Entity}::getFlowtaskid, intersection);
    #end
    }
    //是否有高级查询
    #if($superQuery)
        if (StringUtil.isNotEmpty(superOp)){
        if (allSuperIDlist.size()==0){
        allSuperIDlist.add("xhNullList");
        }
        List<String> finalAllSuperIDlist = allSuperIDlist;
        #if($snowflake)
            ${QueryWrapper}.lambda().and(t->t.in(${Entity}::get${peimaryKeyName}, finalAllSuperIDlist));
        #else
            ${QueryWrapper}.lambda().and(t->t.in(${Entity}::getFlowtaskid, finalAllSuperIDlist));
        #end
        }
    #end

    #if($logicalDelete)
        //假删除标志
        ${name}QueryWrapper.lambda().isNull(${Name}Entity::getDeletemark);
    #end

    //排序
    if(StringUtil.isEmpty(${name}Pagination.getSidx())){
    #if(${defaultSidx})
        #set($model = "${defaultSidx.substring(0,1).toUpperCase()}${defaultSidx.substring(1)}")
        #set($Sort = "${sort.substring(0,1).toUpperCase()}${sort.substring(1)}")
        ${QueryWrapper}.lambda().orderBy${Sort}(${Entity}::get${model});
    #else
        ${QueryWrapper}.lambda().orderByDesc(${Entity}::get${peimaryKeyName});
    #end
    }else{
    try {
    String sidx = ${name}Pagination.getSidx();
    String[] strs= sidx.split("_name");
    ${Entity} ${name}Entity = new ${Entity}();
    Field declaredField = ${name}Entity.getClass().getDeclaredField(strs[0]);
    declaredField.setAccessible(true);
    String value = declaredField.getAnnotation(TableField.class).value();
    ${QueryWrapper}="asc".equals(${name}Pagination.getSort().toLowerCase())?${QueryWrapper}.orderByAsc(value):${QueryWrapper}.orderByDesc(value);
    } catch (NoSuchFieldException e) {
    e.printStackTrace();
    }
    }
    #if (${hasFilter} == true || ${hasAppFilter} == true )
        //主表过滤条件
        if(${QueryWrapper}.getSqlSegment().startsWith(" ORDER")){
        ${QueryWrapper}.apply(" 1=1 ");
        generaterSwapUtil.wrapperHandle(${Name}Constant.getColumnData(), ${Name}Constant.getAppColumnData(), ${QueryWrapper}, getEntityClass(), "main", "${name}");
        }else{
            ${QueryWrapper}.and(t->{
            generaterSwapUtil.wrapperHandle(${Name}Constant.getColumnData(), ${Name}Constant.getAppColumnData(), t, getEntityClass(), "main", "${name}");
            });
        }
    #end

    if("0".equals(dataType)){
    if((total>0 && AllIdList.size()>0) || total==0){
    Page<${Entity}> page=new Page<>(${name}Pagination.getCurrentPage(), ${name}Pagination.getPageSize());
    IPage<${Entity}> userIPage=this.page(page, ${QueryWrapper});
    return ${name}Pagination.setData(userIPage.getRecords(),userIPage.getTotal());
    }else{
    List<${Entity}> list = new ArrayList();
    return ${name}Pagination.setData(list, list.size());
    }
    }else{
    return this.list(${QueryWrapper});
    }
    }
#end
##  增删改查接口
#macro(CrudMethod)
    @Override
    public ${Entity} getInfo(String ${peimaryKeyname}){
        QueryWrapper<${Entity}> queryWrapper=new QueryWrapper<>();
    #if($snowflake)
        queryWrapper.lambda().eq(${Entity}::get${peimaryKeyName},${peimaryKeyname});
    #else
        queryWrapper.lambda().and(
        t->t.eq(${Entity}::get${peimaryKeyName},${peimaryKeyname})
        .or().eq(${Entity}::getFlowtaskid, ${peimaryKeyname})
        );
    #end
        return this.getOne(queryWrapper);
    }
    @Override
    public void create(${Entity} entity){
        this.save(entity);
    }
    @Override
    public boolean update(String ${peimaryKeyname}, ${Entity} entity){
        return this.updateById(entity);
    }
    @Override
    public void delete(${Entity} entity){
        if(entity!=null){
            this.removeById(entity.get${peimaryKeyName}());
        }
    }
#end
##  副表方法
#macro(MastTableMethod)
#foreach($child in ${columnTableHandle})
    /** ${child.modelUpName}副表方法 */
    @Override
    public ${child.modelUpName}Entity get${child.modelUpName}(String id){
        QueryWrapper<${child.modelUpName}Entity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(${child.modelUpName}Entity::get${child.relationUpField}, id);
        return ${child.modelLowName}Service.getOne(queryWrapper);
    }
#end
#end
##  子表方法
#macro(ChildMethod)
#foreach($grid in ${childTableHandle})
    #if($isList)
    /** ${grid.aliasUpName}子表方法 */
    @Override
    public List<${grid.aliasUpName}Entity> get${grid.aliasUpName}List(String id,${Name}Pagination ${name}Pagination){
        #set($tablefield = "${grid.tablefield.substring(0,1).toUpperCase()}${grid.tablefield.substring(1)}")
        $mapObject newtabMap=${Name}Constant.TABLEFIELDKEY.entrySet()
        .stream().collect( Collectors.toMap(e->e.getValue(),e->e.getKey()));
        String tableName="${grid.aliasLowName}";
        tableName=newtabMap.get(tableName)==null?tableName:newtabMap.get(tableName).toString();
        QueryWrapper<${grid.aliasUpName}Entity> queryWrapper = new QueryWrapper<>();
        queryWrapper = ${grid.aliasLowName}Service.getChild(${name}Pagination,queryWrapper);
        queryWrapper.lambda().eq(${grid.aliasUpName}Entity::get${tablefield}, id);
        generaterSwapUtil.wrapperHandle(${Name}Constant.getColumnData(), ${Name}Constant.getAppColumnData(), queryWrapper,${grid.aliasUpName}Entity.class,"sub",tableName);
        return ${grid.aliasLowName}Service.list(queryWrapper);
    }

    #end
    /** ${grid.aliasUpName}子表方法 */
    @Override
    public List<${grid.aliasUpName}Entity> get${grid.aliasUpName}List(String id){
        #set($tablefield = "${grid.tablefield.substring(0,1).toUpperCase()}${grid.tablefield.substring(1)}")
        QueryWrapper<${grid.aliasUpName}Entity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(${grid.aliasUpName}Entity::get${tablefield}, id);
        return ${grid.aliasLowName}Service.list(queryWrapper);
    }
#end
#end
##  表单验证
#macro(CheckForm)
    /** 验证表单唯一字段，正则，非空 */
    @Override
    public String checkForm(${Name}Form form,int i) {
        int total = 0;
        boolean isUp =StringUtil.isNotEmpty(form.get${peimaryKeyName}()) && !form.get${peimaryKeyName}().equals("0");
        String id="";
        String countRecover = "";
        if (isUp){
        #if($snowflake)
            id = form.get${peimaryKeyName}();
        #else
            id = this.getInfo(form.getId()).getFlowtaskid();
        #end
        }
##      主表字段验证
        //主表字段验证
        #foreach($mastField in ${mastTableHandle})
            #set($Field = $mastField)
            #set($config = $Field.config)
            #set($unique = $config.unique)
            #set($required = $config.required)
            #set($xhKey = $config.xhKey)
            #set($vModel = ${Field.vModel})
            #set($upName = "${vModel.substring(0,1).toUpperCase()}${vModel.substring(1)}")
            #if($xhKey == 'input' && $required ==true)
                if(StringUtil.isEmpty(form.get${upName}())){
                    return "${config.label}不能为空";
                }
            #end
            #if($xhKey == 'input' && $unique ==true)
                if(StringUtil.isNotEmpty(form.get${upName}())){
                    form.set${upName}(form.get${upName}().trim());
                    QueryWrapper<${Name}Entity> ${vModel}Wrapper=new QueryWrapper<>();
                    ${vModel}Wrapper.lambda().eq(${Name}Entity::get${upName},form.get${upName}());
                    #if($logicalDelete)
                        //假删除标志
                        ${vModel}Wrapper.lambda().isNull(${Name}Entity::getDeletemark);
                    #end
                    if (isUp){
                    #if($snowflake)
                        ${vModel}Wrapper.lambda().ne(${Name}Entity::get${peimaryKeyName}, id);
                    #else
                        ${vModel}Wrapper.lambda().ne(${Name}Entity::getFlowtaskid, id);
                    #end
                    }
                    if((int) this.count(${vModel}Wrapper)>0){
                        total ++;
                        countRecover = "${config.label}不能重复";
                    }
                }
            #end
##          正则判断
                #RegMethod($xhKey,"form.get${upName}()",$config)
        #end
##      副表字段验证
        #if(${columnTableHandle.size()}>0)
        //副表字段验证
        #foreach($cl in  ${columnTableHandle})
        #set($columnTableName = "${cl.modelUpName}")
            #foreach($clModel in ${cl.fieLdsModelList})
            #set($model = "${clModel.field.substring(0,1).toUpperCase()}${clModel.field.substring(1).toLowerCase()}")
            #set($fullName = "${clModel.vModel.substring(0,1).toUpperCase()}${clModel.vModel.substring(1).toLowerCase()}")
            #set($config =  ${clModel.mastTable.fieLdsModel.config})
            #set($key =  ${clModel.mastTable.fieLdsModel.config.xhKey})
            #set($unique = $clModel.mastTable.fieLdsModel.config.unique)
            #set($required = $clModel.mastTable.fieLdsModel.config.required)
            #if($key =='input' && $required ==true)
                if(StringUtil.isEmpty(form.get${fullName}())){
                    return "${clModel.mastTable.fieLdsModel.config.label}不能为空";;
                }
            #end
            #if($key =='input' && $unique ==true)
                if(StringUtil.isNotEmpty(form.get${fullName}())){
                    form.set${fullName}(form.get${fullName}().trim());
                    QueryWrapper<${columnTableName}Entity> ${columnTableName}${model}Wrapper=new QueryWrapper<>();
                    ${columnTableName}${model}Wrapper.lambda().eq(${columnTableName}Entity::get${model},form.get${fullName}());
                    if (isUp){
                        ${columnTableName}${model}Wrapper.lambda().ne(${columnTableName}Entity::get${cl.relationUpField}, id);
                    }
                    if((int) ${cl.modelLowName}Service.count(${columnTableName}${model}Wrapper)>i){
                        countRecover = "${clModel.mastTable.fieLdsModel.config.label}不能重复";
                        total ++;
                    }
                }
            #end
##          正则判断
                #RegMethod($key,"form.get${fullName}()",$config)
            #end
        #end
        #end
##      子表字段验证
        #if(${childTableHandle.size()}>0)
        //子表字段验证
        #foreach($grid in ${childTableHandle})
            #set($tablefield = "${grid.tablefield.substring(0,1).toUpperCase()}${grid.tablefield.substring(1)}")
            #set($listTableModel = "${grid.aliasUpName}")
            if (form.get${listTableModel}List()!=null){
            #foreach($xhkey in ${grid.childList})
                #if(${xhkey.fieLdsModel.vModel} != '')
                    #set($config = ${xhkey.fieLdsModel.config})
                    #set($key = ${xhkey.fieLdsModel.config.xhKey})
                    #set($model = "${xhkey.fieLdsModel.vModel.substring(0,1).toUpperCase()}${xhkey.fieLdsModel.vModel.substring(1)}")
                    #set($unique = $xhkey.fieLdsModel.config.unique)
                    #set($required = $xhkey.fieLdsModel.config.required)
                    #if($key =='input')
                        for(${grid.aliasUpName}Model t : form.get${grid.aliasUpName}List()){
                            if(StringUtil.isNotEmpty(t.get${model}())){
                                t.set${model}(t.get${model}().trim());
                            }
                        #if($required ==true)
                            else{
                                return "${grid.label}-${xhkey.fieLdsModel.config.label}不能为空";
                            }
                        #end
##                      子表正则
                            #RegMethod($key,"t.get${model}()",$config)
                        }
                    #end
                    #if($key =='input' && $unique ==true)
                        QueryWrapper<${grid.aliasUpName}Entity> ${grid.aliasLowName}${model}Wrapper = new QueryWrapper<>();
                        List<String> ${model}List = form.get${grid.aliasUpName}List().stream().filter(f->StringUtil.isNotEmpty(f.get${model}())).map(f -> f.get${model}()).collect(Collectors.toList());
                        HashSet<String> ${model}Set = new HashSet<>(${model}List);
                        if(${model}Set.size() != ${model}List.size()){
                        countRecover = "${grid.label}-${xhkey.fieLdsModel.config.label}不能重复";
                        }
                        if(${model}List.size()>0){
                        for (String ${model} : ${model}List) {
                        ${grid.aliasLowName}${model}Wrapper.lambda().eq(${grid.aliasUpName}Entity::get${model}, ${model});
                        if(isUp){
                        ${grid.aliasLowName}${model}Wrapper.lambda().ne(${grid.aliasUpName}Entity::get${tablefield},id);
                        }
                        if((int) ${grid.aliasLowName}Service.count(${grid.aliasLowName}${model}Wrapper) > 0){
                        countRecover = "${grid.label}-${xhkey.fieLdsModel.config.label}不能重复";
                        total ++;
                        }
                        }
                        }
                    #end
                #end
            #end
            }
        #end
        #end
        return countRecover;
    }
#end
##  正则验证方法
#macro(RegMethod $xhKey,$param $config)
    #if($xhKey == 'input' && ${config.regList} && ${config.regList.size()}>0)
    if(StringUtil.isNotEmpty($param)){
    #foreach($regItem in ${config.regList})
        #set($pattern=$regItem.pattern.replace("\","\\").replace("/",""))
        if(!Pattern.compile("$pattern").matcher(String.valueOf($param)).matches()){
        return "${regItem.message}";
        }
    #end
    }
    #end
#end
##  子表过滤方法
#macro(childPaginationSerach $tableName)
    boolean pcPermission = ${pcDataPermisson};
    boolean appPermission = ${appDataPermisson};
    boolean	isPc = "pc".equals(ServletUtil.getHeader("xh-origin"));
    #if($groupModels.size()>0)
        if(isPc){
        #foreach($Group in ${groupModels})
            #if($tableName.toLowerCase() == "${Group.modelName.toLowerCase()}")
                #set($ModelName= "${Group.modelName.substring(0,1).toUpperCase()}${Group.modelName.substring(1)}")
                #set($ModelNameEntity = "${ModelName}Entity")
                #set($modelName ="${Group.modelName.substring(0,1).toLowerCase()}${Group.modelName.substring(1)}")
                #set($modelQueryWrapper ="${modelName}QueryWrapper")
                #set($modelNum = "${modelName}Num")
                int ${modelName}Num = 0;
                #childPaginationSearch(${Group.searchTypeModelList},${modelQueryWrapper},${ModelNameEntity})
                #ChildSuperQuery($Group.modelName)
                #ChildDataPermission($Group.modelName)
            #end
        #end
        }
    #end
    #if($groupAppModels.size()>0)
        if(!isPc){
        #foreach($appGroup in ${groupAppModels})
            #if($tableName == "${appGroup.modelName.toLowerCase()}")
                #set($ModelName= "${appGroup.modelName.substring(0,1).toUpperCase()}${appGroup.modelName.substring(1)}")
                #set($ModelNameEntity = "${ModelName}Entity")
                #set($modelName ="${appGroup.modelName.substring(0,1).toLowerCase()}${appGroup.modelName.substring(1)}")
                #set($modelQueryWrapper ="${modelName}QueryWrapper")
                #set($modelNum = "${modelName}Num")
                int ${modelName}Num = 0;
                #childPaginationSearch(${appGroup.searchTypeModelList},${modelQueryWrapper},${ModelNameEntity})
                #ChildSuperQuery($appGroup.modelName)
                #ChildDataPermission($appGroup.modelName)
            #end
        #end
        }
    #end
#end
