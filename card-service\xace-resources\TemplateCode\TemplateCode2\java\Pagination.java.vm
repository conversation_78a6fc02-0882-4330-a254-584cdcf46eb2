#set($name = "${context.className.substring(0,1).toUpperCase()}${context.className.substring(1)}")
package ${context.package}.model.$!{name.toLowerCase()};

 #set($searchTypeList=${context.searchTypeList})

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import com.xinghuo.common.base.Pagination;
import java.util.List;
import com.xinghuo.obsolete.util.generater.DataSwapUtil;

/**
 *
 * ${context.genInfo.description}
 * @版本： ${context.genInfo.version}
 * @版权： ${context.genInfo.copyright}
 * @作者： ${context.genInfo.createUser}
 * @日期： ${context.genInfo.createDate}
 */
@Data
public class $!{name}Pagination extends Pagination {

	private String selectKey;

	private String json;

	private String dataType;

	private String superQueryJson;

#foreach($search in ${searchTypeList})
        #set($searchType = ${search.searchType})
        #set($xhKey= ${search.xhKey})
        #set($vModel =${search.vModel})
        #set($lowModel = "${vModel.substring(0,1).toLowerCase()}${vModel.substring(1)}")
        #set($upModel = "${vModel.substring(0,1).toUpperCase()}${vModel.substring(1)}")
        #set($label = ${search.label})

        /** ${label} */
		@JsonProperty("$vModel")
    #if(${searchType}==3)
        private Object ${lowModel};
    #else
        #if(${xhKey}=='select' || ${xhKey}=='depSelect' || ${xhKey} =='roleSelect' || ${xhKey} =='userSelect'
        || ${xhKey}=='usersSelect' || ${xhKey} =='comSelect' || ${xhKey} =='posSelect' || ${xhKey} =='groupSelect'
        || ${xhKey}=='address' || ${xhKey} =='cascader' || ${xhKey} =='currOrganize')
        private List ${lowModel};
        public void set${upModel}(Object ${lowModel}) {
            this.${lowModel} = DataSwapUtil.convertToList(${lowModel});
        }
        #else
        private String ${lowModel};
        #end
##        #if($search.multiple =='true')
##            private List ${lowModel};
##        #else
##            #if(${xhKey}=='address' || ${xhKey}=='cascader' || ${xhKey} =='comSelect' || ${xhKey} =='currOrganize')
##            private List ${lowModel};
##            #else
##            private String ${lowModel};
##            #end
##        #end
    #end
#end
    #if($context.hasTree)
        #set($treeSearch = ${context.treeRelation})
        #set($xhKey= ${treeSearch.xhKey})
        #set($vModel =${treeSearch.vModel})
        #set($lowModel = "${vModel.substring(0,1).toLowerCase()}${vModel.substring(1)}")
        #set($upModel = "${vModel.substring(0,1).toUpperCase()}${vModel.substring(1)}")
        #set($label = ${treeSearch.label})

			/** ${label} */
		@JsonProperty("$vModel")
        #if(${searchType}==3)
				private Object ${lowModel};
        #else
            #if(${xhKey}=='select' || ${xhKey}=='depSelect' || ${xhKey} =='roleSelect' || ${xhKey} =='userSelect'
            || ${xhKey}=='usersSelect' || ${xhKey} =='comSelect' || ${xhKey} =='posSelect' || ${xhKey} =='groupSelect'
            || ${xhKey}=='address' || ${xhKey} =='cascader' || ${xhKey} =='currOrganize')
                private Object ${lowModel};
##                public void set${upModel}(Object ${lowModel}) {
##                    this.${lowModel} = DataSwapUtil.convertToList(${lowModel});
##                }
            #else
                private String ${lowModel};
            #end
##            #if(${xhKey}=='address' || ${xhKey}=='cascader' || ${xhKey} =='comSelect' || ${xhKey} =='currOrganize')
##                private List ${lowModel};
##            #else
##                private String ${lowModel};
##            #end
        #end
    #end
	/**
    * 菜单id
    */
	private String menuId;
#if(${context.treeTable} == true)
    /**
    * 树形异步父级字段传值
    */
    private String treeParentValue;
    /**
    * 是否有参数
    */
    private  boolean hasParam=false;
#end

    private String moduleId;
}
