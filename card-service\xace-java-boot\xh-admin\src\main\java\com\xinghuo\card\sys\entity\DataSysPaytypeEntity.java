package com.xinghuo.card.sys.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;


/**
 * 收支类型表
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2022-11-14
 */
@Data
@TableName("data_sys_paytype")
public class DataSysPaytypeEntity  implements Serializable {

    /**
     *
     */
    @TableId("ID")
    private String id;


    /**
     * 名称
     */
    @TableField("NAME")
    private String name;


    /**
     * 父ID
     */
    @TableField("PARENT_ID")
    private String parentId;


    /**
     * 类型 1-收入，2-支出 3-转账
     */
    @TableField("TYPE")
    private Integer type;


    /**
     * 系统分类
     */
    @TableField("IS_SYSTEM")
    private Integer systemFlag;


    /**
     *
     */
    @TableField("LIST_ORDER")
    private Integer listOrder;


    /**
     * 拼音
     */
    @TableField("PY")
    private String py;


    /**
     * 创建人
     */
    @TableField("CREATE_BY")
    private String createBy;


    /**
     * 创建时间
     */
    @TableField("CREATE_TIME")
    private Date createTime;


    /**
     * 最后修改人
     */
    @TableField("UPDATE_BY")
    private String updateBy;


    /**
     * 最后修改时间
     */
    @TableField("UPDATE_TIME")
    private Date updateTime;

}
