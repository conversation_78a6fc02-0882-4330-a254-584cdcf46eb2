package com.xinghuo.card.flow.model.datawoolwool;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.DecimalMin;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 羊毛记录表
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2022-12-12
 */
@Data
public class DataWoolWoolForm {

    @Schema(description = "主键")
    private String id;


    @Schema(description = "类型")
    @JsonProperty("type")
    private String type;


    @Schema(description = "活动账户")
    @JsonProperty("srcAccId")
    private String srcAccId;


    @Schema(description = "积分", required = true)
    @JsonProperty("srcPoint")
    @NotNull(message = "积分不能为空")
    @DecimalMin(value = "0", message = "积分不能小于0")
    private BigDecimal srcPoint;

    @Schema(description = "折算金额", required = true)
    @JsonProperty("amount")
    @NotNull(message = "折算金额不能为空")
    @DecimalMin(value = "0.01", message = "折算金额必须大于0")
    private BigDecimal amount;


    @Schema(description = "活动名称")
    @JsonProperty("acitivity")
    private String acitivity;


    @Schema(description = "物品名称")
    @JsonProperty("goods")
    private String goods;


    @Schema(description = "入账日期")
    @JsonProperty("inDate")
    private Date inDate;


    @Schema(description = "入账账户")
    @JsonProperty("inAccId")
    private String inAccId;


    @Schema(description = "入账金额", required = true)
    @JsonProperty("flowAmount")
    @NotNull(message = "入账金额不能为空")
    @DecimalMin(value = "0.01", message = "入账金额必须大于0")
    private BigDecimal flowAmount;


    @Schema(description = "备注")
    @JsonProperty("note")
    private String note;


}
