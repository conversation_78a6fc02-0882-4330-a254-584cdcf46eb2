<template>
  <BasicPopup
    v-if="type == DialogEnum.FULLSCREEN"
    v-bind="$attrs"
    @register="registerPopup"
    :title="title"
    :cancelText="'关闭'"
    destroyOnClose
    :closeFunc="onClose">
    <div class="p-10px" :style="{ margin: '0 auto', width: '100%' }">
      <Item :prop-model-id="modelId" :params="params" :width="width" :style="style" :formUrl="formUrl" />
    </div>
  </BasicPopup>
  <BasicDrawer
    v-else-if="type == DialogEnum.DRAWER"
    :showOkBtn="false"
    v-bind="$attrs"
    @register="registerDrawer"
    :title="title"
    :width="width"
    showFooter
    :cancelText="'关闭'"
    :closeFunc="onClose">
    <div class="p-10px">
      <Item :prop-model-id="modelId" :params="params" :style="style" :formUrl="formUrl" />
    </div>
  </BasicDrawer>
  <BasicModal
    v-else-if="type == DialogEnum.MODAL"
    :showOkBtn="false"
    @register="registerModal"
    v-bind="$attrs"
    :title="title"
    :width="width"
    :minHeight="100"
    :cancelText="'关闭'"
    :closeFunc="onClose">
    <Item :prop-model-id="modelId" :params="params" :style="style" :formUrl="formUrl" />
  </BasicModal>
</template>
<script lang="ts" setup>
  import { reactive, toRefs, nextTick } from 'vue';
  import { BasicPopup, usePopup } from '/@/components/Popup';
  import { BasicModal, useModal } from '/@/components/Modal';
  import { BasicDrawer, useDrawer } from '/@/components/Drawer';
  import Item from './item.vue';
  import { DialogEnum } from '/@/components/CustomDialog/types';
  import type { CustomDialogParams } from '/@/components/CustomDialog/types';

  defineOptions({ name: 'CustomDialog' });
  const props = defineProps(['did']);

  const [registerPopup, { openPopup, closePopup }] = usePopup();
  const [registerModal, { openModal, closeModal }] = useModal();
  const [registerDrawer, { openDrawer, closeDrawer }] = useDrawer();
  const state = reactive<CustomDialogParams>({
    id: '',
    modelId: '',
    type: DialogEnum.FULLSCREEN,
    title: '',
    params: {},
    width: '520px',
    style: {},
    formUrl: '',
  });
  const { title, modelId, params, width, style, formUrl, type } = toRefs(state);

  function init(data: CustomDialogParams) {
    state.type = data.type ?? state.type;
    state.modelId = data.modelId ?? state.modelId;
    state.title = data.title ?? state.title;
    state.params = data.params ?? state.params;
    state.width = data.width ?? state.width;
    state.style = data.style ?? state.style;
    state.formUrl = data.formUrl ?? state.formUrl;
    state.id = data.id;
    nextTick(() => {
      openForm();
    });
  }
  function openForm() {
    if (state.type == DialogEnum.FULLSCREEN) return openPopup();
    if (state.type == DialogEnum.DRAWER) return openDrawer();
    openModal();
  }
  function close() {
    if (state.type == DialogEnum.FULLSCREEN) return closePopup();
    if (state.type == DialogEnum.DRAWER) return closeDrawer();
    closeModal();
  }
  async function onClose() {
    return true;
  }

  defineExpose({ init, id: props.did, close });
</script>
