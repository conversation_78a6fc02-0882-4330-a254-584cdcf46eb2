#set($name = "${context.className.substring(0,1).toLowerCase()}${context.className.substring(1)}")
#set($pKeyName =${context.pKeyName.toLowerCase()})

#set($peimaryKeyName = "${pKeyName.substring(0,1).toUpperCase()}${pKeyName.substring(1)}")
#set($peimaryKeyname = "${pKeyName.substring(0,1).toLowerCase()}${pKeyName.substring(1)}")

package ${context.package}.model.$!{name.toLowerCase()};

import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.List;
import java.util.Map;
/**
 *
 * ${context.genInfo.description}
 * @版本： ${context.genInfo.version}
 * @版权： ${context.genInfo.copyright}
 * @作者： ${context.genInfo.createUser}
 * @日期： ${context.genInfo.createDate}
 */
@Data
public class $!{context.className}GroupVO{
	private String ${peimaryKeyname};

	private String ${context.groupField}_name;

	private List<Map<String ,Object>> children;
}
