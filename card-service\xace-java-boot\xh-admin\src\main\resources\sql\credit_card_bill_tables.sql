-- 信用卡账单管理相关表结构
-- 创建时间：2024-01-01
-- 说明：用于信用卡账单管理和账单周期自动生成功能

-- 1. 信用卡账单表
CREATE TABLE `credit_card_bill` (
  `ID` varchar(50) NOT NULL COMMENT '主键ID',
  `USER_ID` varchar(50) NOT NULL COMMENT '用户ID',
  `CARD_ACC_ID` varchar(50) DEFAULT NULL COMMENT '信用卡账户ID',
  `BANK_CODE` varchar(20) DEFAULT NULL COMMENT '银行代码',
  `BANK_NAME` varchar(100) DEFAULT NULL COMMENT '银行名称',
  `CARD_NUMBER` varchar(50) DEFAULT NULL COMMENT '信用卡号（脱敏）',
  `BILL_START_DATE` datetime DEFAULT NULL COMMENT '账单周期开始日期',
  `BILL_END_DATE` datetime DEFAULT NULL COMMENT '账单周期结束日期',
  `DUE_DATE` datetime DEFAULT NULL COMMENT '还款到期日',
  `CURRENT_AMOUNT` decimal(18,2) DEFAULT NULL COMMENT '本期应还金额',
  `MINIMUM_AMOUNT` decimal(18,2) DEFAULT NULL COMMENT '最低还款金额',
  `CONSUMPTION_AMOUNT` decimal(18,2) DEFAULT NULL COMMENT '本期消费金额',
  `PAYMENT_AMOUNT` decimal(18,2) DEFAULT NULL COMMENT '本期还款金额',
  `AVAILABLE_CREDIT` decimal(18,2) DEFAULT NULL COMMENT '可用额度',
  `CREDIT_LIMIT` decimal(18,2) DEFAULT NULL COMMENT '信用额度',
  `BILL_STATUS` int(2) DEFAULT '0' COMMENT '账单状态：0-未出账单，1-已出账单，2-已还款，3-逾期',
  `PAYMENT_STATUS` int(2) DEFAULT '0' COMMENT '还款状态：0-未还款，1-部分还款，2-已还清',
  `BILL_DAY` int(2) DEFAULT NULL COMMENT '账单日（每月几号出账单）',
  `REPAYMENT_DAY` int(2) DEFAULT NULL COMMENT '还款日（每月几号还款）',
  `INTEREST_FREE_DAYS` int(3) DEFAULT NULL COMMENT '免息期天数',
  `AUTO_GENERATE` tinyint(1) DEFAULT '1' COMMENT '是否自动生成账单：0-否，1-是',
  `PREVIOUS_BALANCE` decimal(18,2) DEFAULT NULL COMMENT '上期账单余额',
  `NOTE` text COMMENT '备注',
  `CREATE_BY` varchar(50) DEFAULT NULL COMMENT '创建人',
  `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间',
  `UPDATE_BY` varchar(50) DEFAULT NULL COMMENT '最后修改人',
  `UPDATE_TIME` datetime DEFAULT NULL COMMENT '最后修改时间',
  `DELETE_FLAG` int(2) DEFAULT '0' COMMENT '删除标记：0-正常，1-已删除',
  `LIST_ORDER` int(11) DEFAULT '0' COMMENT '排序字段',
  PRIMARY KEY (`ID`),
  KEY `idx_user_id` (`USER_ID`),
  KEY `idx_card_acc_id` (`CARD_ACC_ID`),
  KEY `idx_bank_code` (`BANK_CODE`),
  KEY `idx_bill_date` (`BILL_START_DATE`, `BILL_END_DATE`),
  KEY `idx_due_date` (`DUE_DATE`),
  KEY `idx_bill_status` (`BILL_STATUS`),
  KEY `idx_payment_status` (`PAYMENT_STATUS`),
  KEY `idx_bill_day` (`BILL_DAY`),
  KEY `idx_create_time` (`CREATE_TIME`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='信用卡账单表';

-- 2. 信用卡配置表
CREATE TABLE `credit_card_config` (
  `ID` varchar(50) NOT NULL COMMENT '主键ID',
  `USER_ID` varchar(50) NOT NULL COMMENT '用户ID',
  `ACC_ID` varchar(50) NOT NULL COMMENT '关联账户ID（对应DataAccEntity的ID）',
  `CARD_NAME` varchar(100) NOT NULL COMMENT '信用卡名称',
  `BANK_CODE` varchar(20) DEFAULT NULL COMMENT '银行代码',
  `BANK_NAME` varchar(100) DEFAULT NULL COMMENT '银行名称',
  `CARD_NUMBER` varchar(50) DEFAULT NULL COMMENT '信用卡号（脱敏显示）',
  `CARD_LAST_FOUR` varchar(4) DEFAULT NULL COMMENT '信用卡号后四位',
  `CREDIT_LIMIT` decimal(18,2) DEFAULT NULL COMMENT '信用额度',
  `BILL_DAY` int(2) NOT NULL COMMENT '账单日（每月几号出账单，1-31）',
  `REPAYMENT_DAY` int(2) NOT NULL COMMENT '还款日（每月几号还款，1-31）',
  `INTEREST_FREE_DAYS` int(3) DEFAULT '20' COMMENT '免息期天数',
  `AUTO_GENERATE_BILL` tinyint(1) DEFAULT '1' COMMENT '是否启用自动生成账单：0-否，1-是',
  `PAYMENT_REMINDER` tinyint(1) DEFAULT '1' COMMENT '是否启用还款提醒：0-否，1-是',
  `REMINDER_DAYS` int(2) DEFAULT '3' COMMENT '提醒提前天数',
  `CARD_STATUS` int(2) DEFAULT '1' COMMENT '卡片状态：0-停用，1-正常，2-冻结，3-注销',
  `CARD_TYPE` int(2) DEFAULT '1' COMMENT '卡片类型：1-普通卡，2-金卡，3-白金卡，4-钻石卡',
  `OPEN_DATE` datetime DEFAULT NULL COMMENT '开卡日期',
  `EXPIRE_DATE` datetime DEFAULT NULL COMMENT '到期日期',
  `ANNUAL_FEE` decimal(18,2) DEFAULT NULL COMMENT '年费',
  `FREE_ANNUAL_FEE` tinyint(1) DEFAULT '0' COMMENT '是否免年费：0-否，1-是',
  `NOTE` text COMMENT '备注',
  `CREATE_BY` varchar(50) DEFAULT NULL COMMENT '创建人',
  `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间',
  `UPDATE_BY` varchar(50) DEFAULT NULL COMMENT '最后修改人',
  `UPDATE_TIME` datetime DEFAULT NULL COMMENT '最后修改时间',
  `DELETE_FLAG` int(2) DEFAULT '0' COMMENT '删除标记：0-正常，1-已删除',
  `LIST_ORDER` int(11) DEFAULT '0' COMMENT '排序字段',
  PRIMARY KEY (`ID`),
  UNIQUE KEY `uk_acc_id` (`ACC_ID`),
  KEY `idx_user_id` (`USER_ID`),
  KEY `idx_bank_code` (`BANK_CODE`),
  KEY `idx_bill_day` (`BILL_DAY`),
  KEY `idx_repayment_day` (`REPAYMENT_DAY`),
  KEY `idx_card_status` (`CARD_STATUS`),
  KEY `idx_auto_generate` (`AUTO_GENERATE_BILL`),
  KEY `idx_create_time` (`CREATE_TIME`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='信用卡配置表';
