package com.xinghuo.visualdev.portal.controller;

import com.xinghuo.common.base.ActionResult;
import com.xinghuo.common.base.vo.ListVO;
import com.xinghuo.common.util.json.JsonXhUtil;
import com.xinghuo.extend.service.EmailReceiveService;
import com.xinghuo.message.entity.MessageEntity;
import com.xinghuo.message.model.NoticeModel;
import com.xinghuo.message.service.MessageService;
import com.xinghuo.permission.entity.UserEntity;
import com.xinghuo.permission.service.UserService;
import com.xinghuo.system.base.entity.DictionaryDataEntity;
import com.xinghuo.system.base.entity.DictionaryTypeEntity;
import com.xinghuo.system.base.service.DictionaryDataService;
import com.xinghuo.system.base.service.DictionaryTypeService;
import com.xinghuo.visualdev.portal.model.*;
import com.xinghuo.workflow.engine.entity.FlowTaskEntity;
import com.xinghuo.workflow.engine.model.flowtask.FlowTaskListModel;
import com.xinghuo.workflow.engine.service.FlowDelegateService;
import com.xinghuo.workflow.engine.service.FlowTaskService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.LinkedList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * xh-visualdev-portal迁移到xh-admin
 * <p>
 * Portal 主页控制器
 * 根据实际项目情况修改
 *
 * <AUTHOR>
 * date 2023-10-05
 */
@Tag(name = "主页控制器", description = "Home")
@RestController
@RequestMapping("api/visualdev/Dashboard")
public class DashboardController {
    @Autowired
    private FlowTaskService flowTaskService;

    @Autowired
    private FlowDelegateService flowDelegateService;

    @Autowired
    private MessageService messageService;

    @Autowired
    private EmailReceiveService emailReceiveService;

    @Autowired
    private UserService userService;

    @Autowired
    private DictionaryDataService dictionaryDataService;

    @Autowired
    private DictionaryTypeService dictionaryTypeService;

    /**
     * 获取我的待办
     *
     * @return
     */
    @Operation(summary = "获取我的待办")
    @PostMapping("/FlowTodoCount")
    public ActionResult getFlowTodoCount(@RequestBody FlowTodo flowTodo) {
        FlowTodoCountVO vo = new FlowTodoCountVO();
        List<FlowTaskEntity> toBeReviewedList = flowTaskService.getWaitList();
        if (flowTodo.getToBeReviewedType().size() > 0) {
            toBeReviewedList = toBeReviewedList.stream().filter(t -> flowTodo.getToBeReviewedType().contains(t.getFlowCategory())).collect(Collectors.toList());
        }
        vo.setToBeReviewed(toBeReviewedList.size());
        vo.setEntrust(flowDelegateService.getList().size());
        List<FlowTaskListModel> flowDoneList = flowTaskService.getTrialList();
        if (flowTodo.getFlowDoneType().size() > 0) {
            flowDoneList = flowDoneList.stream().filter(t -> flowTodo.getFlowDoneType().contains(t.getFlowCategory())).collect(Collectors.toList());
        }
        vo.setFlowDone(flowDoneList.size());
        List<FlowTaskListModel> flowCirculate = flowTaskService.getCirculateList();
        if (flowTodo.getFlowCirculateType().size() > 0) {
            flowCirculate = flowCirculate.stream().filter(t -> flowTodo.getFlowCirculateType().contains(t.getFlowCategory())).collect(Collectors.toList());
        }
        vo.setFlowCirculate(flowCirculate.size());
        return ActionResult.success(vo);
    }

    /**
     * 获取通知公告
     *
     * @return
     */
    @Operation(summary = "获取通知公告")
    @PostMapping("/Notice")
    public ActionResult getNotice(@RequestBody NoticeModel noticeModel) {
        List<MessageEntity> list = messageService.getDashboardNoticeList(noticeModel.getTypeList());
        List<UserEntity> userList = userService.getUserName(list.stream().map(MessageEntity::getCreatorUser).collect(Collectors.toList()));
        DictionaryTypeEntity dictionaryTypeEntity = dictionaryTypeService.getInfoByEnCode("NoticeType");
        List<DictionaryDataEntity> noticeType = dictionaryDataService.getDicList(dictionaryTypeEntity.getId());
        list.forEach(t -> {
            // 处理是否过期
            if (t.getExpirationTime() != null) {
                // 已发布的情况下
                if (t.getEnabledMark() == 1) {
                    if (t.getExpirationTime().getTime() < System.currentTimeMillis()) {
                        t.setEnabledMark(2);
                    }
                }
            }
            DictionaryDataEntity dictionaryDataEntity = noticeType.stream().filter(notice -> notice.getEnCode().equals(t.getCategory())).findFirst().orElse(new DictionaryDataEntity());
            t.setCategory(dictionaryDataEntity.getFullName());
            UserEntity user = userList.stream().filter(ul -> ul.getId().equals(t.getCreatorUser())).findFirst().orElse(null);
            t.setCreatorUser(user != null ? user.getRealName() : "");
            if (t.getEnabledMark() != null && t.getEnabledMark() != 0) {
                UserEntity entity = userService.getInfo(t.getLastModifyUserId());
                t.setLastModifyUserId(entity != null ? entity.getRealName() : "");
            }
        });
        List<NoticeVO> data = JsonXhUtil.jsonToList(list, NoticeVO.class);
        data.forEach(t -> {
            t.setReleaseTime(t.getLastModifyTime());
            t.setReleaseUser(t.getLastModifyUserId());
        });
        ListVO<NoticeVO> voList = new ListVO<>();
        voList.setList(data);
        return ActionResult.success(voList);
    }

    /**
     * 获取未读邮件
     *
     * @return
     */
    @Operation(summary = "获取未读邮件")
    @GetMapping("/Email")
    public ActionResult getEmail() {
        List<EmailVO> list = JsonXhUtil.jsonToList(emailReceiveService.getDashboardReceiveList(), EmailVO.class);
        ListVO<EmailVO> voList = new ListVO<>();
        voList.setList(list);
        return ActionResult.success(voList);
    }

    /**
     * 获取待办事项
     *
     * @return
     */
    @Operation(summary = "获取待办事项")
    @GetMapping("/FlowTodo")
    public ActionResult getFlowTodo() {
        List<FlowTaskListModel> taskList = flowTaskService.getDashboardAllWaitList();
        List<FlowTodoVO> list = new LinkedList<>();
        for (FlowTaskListModel taskEntity : taskList) {
            FlowTodoVO vo = JsonXhUtil.jsonDeepCopy(taskEntity, FlowTodoVO.class);
            vo.setTaskNodeId(taskEntity.getThisStepId());
            vo.setTaskOperatorId(taskEntity.getId());
            vo.setType(2);
            list.add(vo);
        }
        ListVO voList = new ListVO<>();
        voList.setList(list);
        return ActionResult.success(voList);
    }

    /**
     * 获取我的待办事项
     *
     * @return
     */
    @Operation(summary = "获取我的待办事项")
    @GetMapping("/MyFlowTodo")
    public ActionResult getMyFlowTodo() {
        List<MyFlowTodoVO> list = JsonXhUtil.jsonToList(flowTaskService.getWaitList(), MyFlowTodoVO.class);
        ListVO<MyFlowTodoVO> voList = new ListVO<>();
        voList.setList(list);
        return ActionResult.success(voList);
    }
}
