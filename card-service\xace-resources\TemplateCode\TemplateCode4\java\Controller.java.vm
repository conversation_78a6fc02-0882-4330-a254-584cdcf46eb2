package ${package.Controller};
    #set($serviceName = "${table.serviceName.substring(0,1).toLowerCase()}${table.serviceName.substring(1)}")
    #set($Name = "${genInfo.className.substring(0,1).toUpperCase()}${genInfo.className.substring(1)}")
    #set($name = "${genInfo.className.substring(0,1).toLowerCase()}${genInfo.className.substring(1)}")
    #set($packName = "${genInfo.className.toLowerCase()}")
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import com.xinghuo.common.base.ActionResult;
import com.xinghuo.common.base.UserInfo;
import com.xinghuo.common.exception.DataException;
import com.xinghuo.example.util.GeneraterSwapUtil;
import com.xinghuo.permission.entity.UserEntity;
#if(${DS})
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
#else
import org.springframework.transaction.annotation.Transactional;
#end
import ${modulePackageName}.model.${packName}.*;
    #foreach($subfield in ${child})
import ${package.Entity}.${subfield.className}Entity;
    #end
    #foreach($cl in  ${columnChildren})
import ${package.Service}.${cl.modelUpName}Service;
import ${package.Entity}.${cl.modelUpName}Entity;
    #end
import com.xinghuo.common.util.*;
import com.xinghuo.common.hutool.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import ${package.Entity}.${Name}Entity;
import ${package.Service}.${table.serviceName};
    #foreach($tableModel in ${child})
                #set($ChildName="${tableModel.className.substring(0,1).toUpperCase()}${tableModel.className.substring(1)}")
                #set($childName="${tableModel.className.substring(0,1).toLowerCase()}${tableModel.classNames.substring(1)}")
import ${package.Entity}.${ChildName}Entity;
import ${package.Service}.${ChildName}Service;
    #end
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import javax.validation.Valid;


/**
 *
 * ${genInfo.description}
 * @版本： ${genInfo.version}
 * @版权： ${genInfo.copyright}
 * @作者： ${genInfo.createUser}
 * @日期： ${genInfo.createDate}
 */
@Slf4j
@RestController
@Tag(name = "${genInfo.description}" , description = "${module}")
#if(${isCloud}=="cloud")
@RequestMapping("/${genInfo.className}")
#else
@RequestMapping("/api/${module}/${genInfo.className}")
#end
public class ${table.controllerName} {

    #set($peimaryKeyName = "${pKeyName.substring(0,1).toUpperCase()}${pKeyName.substring(1).toLowerCase()}")
    #set($peimaryKeyname = "${pKeyName.substring(0,1).toLowerCase()}${pKeyName.substring(1).toLowerCase()}")

    @Autowired
    private UserProvider userProvider;

    @Autowired
    private GeneraterSwapUtil generaterSwapUtil;

    @Autowired
    private ${table.serviceName} ${serviceName};

    #foreach($tableModel in ${child})
        #set($ChildName="${tableModel.className.substring(0,1).toUpperCase()}${tableModel.className.substring(1)}")
        #set($childName="${tableModel.className.substring(0,1).toLowerCase()}${tableModel.className.substring(1)}")
    @Autowired
    private ${ChildName}Service ${childName}Service;
    #end

    #foreach($cl in  ${columnChildren})
    @Autowired
    private ${cl.modelUpName}Service ${cl.modelLowName}Service;
    #end

    /**
     * 创建
     *
     * @param ${name}CrForm
     * @return
     */
    @PostMapping
    #if(${DS})
    @DSTransactional
    #else
    @Transactional
    #end
    @Operation(summary = "创建")
    public ActionResult create(@RequestBody @Valid ${Name}CrForm ${name}CrForm) throws DataException {
        String b =  ${serviceName}.checkForm(${name}CrForm,0);
		if (StringUtil.isNotEmpty(b)){
		    return ActionResult.fail(b + "不能重复");
		}
        String mainId =RandomUtil.snowId();

        UserInfo userInfo=userProvider.get();
		UserEntity userEntity = generaterSwapUtil.getUser(userInfo.getUserId());
        #set($peimaryKeyName="${pKeyName.substring(0,1).toUpperCase()}${pKeyName.substring(1)}")
    #foreach($field in ${system})
        #set($model = "${field.vModel.substring(0,1).toUpperCase()}${field.vModel.substring(1)}")
        #set($key = ${field.config.xhKey})
        #if(${key}=='createUser')
        ${name}CrForm.set${model}(userInfo.getUserId());
        #elseif(${key}=='createTime')
        ${name}CrForm.set${model}(DateUtil.getNow());
        #elseif(${key}=='currOrganize')
        ${name}CrForm.set${model}(StringUtil.isEmpty(userInfo.getDepartmentId()) ? userInfo.getOrganizeId() : userInfo.getDepartmentId());
        #elseif(${key}=='currPosition')
            ${name}CrForm.set${model}(userEntity.getPositionId());
        #elseif(${key}=='billRule')
        ${name}CrForm.set${model}(generaterSwapUtil.getBillNumber("${field.config.rule}", false));
        #end
    #end
        ${Name}Entity entity = JsonXhUtil.toBean(${name}CrForm, ${Name}Entity.class);
        #if($snowflake)
            entity.set${peimaryKeyName}(mainId);
        #else
            entity.setFlowtaskid(mainId);
            entity.set${peimaryKeyName}(0);
        #end
                ${name}Service.save(entity);
        #foreach($grid in ${child})
            #set($list = "${grid.className.substring(0,1).toUpperCase()}${grid.className.substring(1).toLowerCase()}")
            #set($serviceName = "${grid.className.substring(0,1).toLowerCase()}${grid.className.substring(1)}")
                List<${grid.className}Entity> ${grid.className}List = JsonXhUtil.getJsonToList(${name}CrForm.get${list}List(),${grid.className}Entity.class);

                for(${grid.className}Entity entitys : ${grid.className}List){
            #set($tableField = "${grid.tableField.substring(0,1).toUpperCase()}${grid.tableField.substring(1)}")
            #set($relationField = "${grid.relationField.substring(0,1).toUpperCase()}${grid.relationField.substring(1)}")
            #set($chidKeyName = "${grid.chidKeyName.substring(0,1).toUpperCase()}${grid.chidKeyName.substring(1)}")
            #if($snowflake)
                entitys.set${chidKeyName}(RandomUtil.snowId());
                entitys.set${tableField}(entity.get${relationField}());
            #else
                entitys.set${chidKeyName}(0);
                entitys.set${tableField}(entity.getFlowtaskid());
            #end
            #foreach($xhkey in ${grid.childList})
               #if(${xhkey.fieLdsModel.vModel} != '')

                #set($key = ${xhkey.fieLdsModel.config.xhKey})
                #set($rule = ${xhkey.fieLdsModel.config.rule})
                #set($model = "${xhkey.fieLdsModel.vModel.substring(0,1).toUpperCase()}${xhkey.fieLdsModel.vModel.substring(1)}")
                #if(${key}=='createUser')
					entitys.set${model}(userInfo.getUserId());
                #elseif(${key}=='createTime')
					entitys.set${model}(DateXhUtil.date());
                #elseif(${key}=='currOrganize')
					entitys.set${model}(StringUtil.isEmpty(userInfo.getDepartmentId()) ? userInfo.getOrganizeId() : userInfo.getDepartmentId());
                #elseif(${key}=='currPosition')
				entitys.set${model}(userEntity.getPositionId());
                #elseif(${key}=='billRule')
				entitys.set${model}(generaterSwapUtil.getBillNumber("${rule}",false));
                #end
               #end
            #end
                ${serviceName}Service.save(entitys);
            }
        #end

#if(${columnChildren.size()}>0)
		//副表
    #foreach($cl in  ${columnChildren})
        #set($mainField = $cl.mainField)
        #set($mainUpId = "${mainField.substring(0,1).toUpperCase()}${mainField.substring(1)}")
               #set($oracleName = "${cl.modelName.substring(0,1).toUpperCase()}${cl.modelName.substring(1).toLowerCase()}")
        ${cl.modelName}Entity  ${cl.tableName}entity = JsonXhUtil.toBean(${name}CrForm.get${oracleName}(), ${cl.modelName}Entity.class);
        ${cl.tableName}entity.set${cl.relationUpField}(entity.get${cl.mainUpKey}());
				//自动生成的字段
        #foreach($clModel in ${cl.fieLdsModelList})
            #set($model = "${clModel.field.substring(0,1).toUpperCase()}${clModel.field.substring(1)}")
            #set($xhkey =  ${clModel.mastTable.fieLdsModel.config.xhKey})
            #if(${xhkey}=='createUser')
                ${cl.tableName}entity.set${model}(userInfo.getUserId());
            #elseif(${xhkey}=='createTime')
                ${cl.tableName}entity.set${model}(DateXhUtil.date());
            #elseif(${xhkey}=='currOrganize')
                ${cl.tableName}entity.set${model}(StringUtil.isEmpty(userInfo.getDepartmentId()) ? userInfo.getOrganizeId() : userInfo.getDepartmentId());
            #elseif(${xhkey}=='currPosition')
                       ${cl.tableName}entity.set${model}(userEntity.getPositionId());
            #elseif(${xhkey}=='billRule')
                #set($rule = ${clModel.mastTable.fieLdsModel.config.rule})
            ${cl.tableName}entity.set${model}(generaterSwapUtil.getBillNumber("$!{rule}",false));
            #end
        #end
               #if($snowflake)
                   ${cl.tableName}entity.set${cl.relationUpField}(entity.get${cl.mainUpKey}());
                   ${cl.tableName}entity.set${mainUpId}(mainId);
               #else
                   ${cl.tableName}entity.set${mainUpId}(0);
                   ${cl.tableName}entity.set${cl.relationUpField}(entity.getFlowtaskid());
               #end
        ${cl.modelLowName}Service.save(${cl.tableName}entity);
    #end
#end
        return ActionResult.success("保存成功");
    }
}
