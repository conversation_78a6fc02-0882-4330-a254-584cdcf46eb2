<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xinghuo.card.budget.dao.FinBudgetItemMapper">

    <!-- 预算子项实体ResultMap -->
    <resultMap id="FinBudgetItemResult" type="com.xinghuo.card.budget.entity.FinBudgetItemEntity">
        <id property="id" column="ID"/>
        <result property="budgetId" column="BUDGET_ID"/>
        <result property="itemType" column="ITEM_TYPE"/>
        <result property="targetId" column="TARGET_ID"/>
        <result property="targetName" column="TARGET_NAME"/>
        <result property="budgetedAmount" column="BUDGETED_AMOUNT"/>
        <result property="currentSpent" column="CURRENT_SPENT"/>
        <result property="alertThreshold" column="ALERT_THRESHOLD"/>
        <result property="isAlerted" column="IS_ALERTED"/>
        <result property="createBy" column="CREATE_BY"/>
        <result property="createTime" column="CREATE_TIME"/>
        <result property="updateBy" column="UPDATE_BY"/>
        <result property="updateTime" column="UPDATE_TIME"/>
    </resultMap>

    <!-- 分页查询预算子项列表 -->
    <select id="getList" resultMap="FinBudgetItemResult">
        SELECT 
            bi.ID, bi.BUDGET_ID, bi.ITEM_TYPE, bi.TARGET_ID, bi.TARGET_NAME,
            bi.BUDGETED_AMOUNT, bi.CURRENT_SPENT, bi.ALERT_THRESHOLD, bi.IS_ALERTED,
            bi.CREATE_BY, bi.CREATE_TIME, bi.UPDATE_BY, bi.UPDATE_TIME
        FROM fin_budget_item bi
        LEFT JOIN fin_budget b ON bi.BUDGET_ID = b.ID
        <where>
            <if test="finBudgetItemPagination.budgetId != null and finBudgetItemPagination.budgetId != ''">
                AND bi.BUDGET_ID = #{finBudgetItemPagination.budgetId}
            </if>
            <if test="finBudgetItemPagination.itemType != null and finBudgetItemPagination.itemType != ''">
                AND bi.ITEM_TYPE = #{finBudgetItemPagination.itemType}
            </if>
            <if test="finBudgetItemPagination.targetName != null and finBudgetItemPagination.targetName != ''">
                AND bi.TARGET_NAME LIKE CONCAT('%', #{finBudgetItemPagination.targetName}, '%')
            </if>
            <if test="finBudgetItemPagination.isAlerted != null">
                AND bi.IS_ALERTED = #{finBudgetItemPagination.isAlerted}
            </if>
            <if test="finBudgetItemPagination.userId != null and finBudgetItemPagination.userId != ''">
                AND b.USER_ID = #{finBudgetItemPagination.userId}
            </if>
        </where>
        ORDER BY 
            <choose>
                <when test="finBudgetItemPagination.sidx != null and finBudgetItemPagination.sidx != ''">
                    ${finBudgetItemPagination.sidx} ${finBudgetItemPagination.sort}
                </when>
                <otherwise>
                    bi.CREATE_TIME DESC
                </otherwise>
            </choose>
    </select>

    <!-- 根据预算ID获取子项列表 -->
    <select id="getItemsByBudgetId" resultMap="FinBudgetItemResult">
        SELECT 
            ID, BUDGET_ID, ITEM_TYPE, TARGET_ID, TARGET_NAME,
            BUDGETED_AMOUNT, CURRENT_SPENT, ALERT_THRESHOLD, IS_ALERTED,
            CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME
        FROM fin_budget_item
        WHERE BUDGET_ID = #{budgetId}
        ORDER BY CREATE_TIME ASC
    </select>

    <!-- 获取需要预警的预算项 -->
    <select id="getItemsNeedAlert" resultMap="FinBudgetItemResult">
        SELECT 
            bi.ID, bi.BUDGET_ID, bi.ITEM_TYPE, bi.TARGET_ID, bi.TARGET_NAME,
            bi.BUDGETED_AMOUNT, bi.CURRENT_SPENT, bi.ALERT_THRESHOLD, bi.IS_ALERTED,
            bi.CREATE_BY, bi.CREATE_TIME, bi.UPDATE_BY, bi.UPDATE_TIME
        FROM fin_budget_item bi
        INNER JOIN fin_budget b ON bi.BUDGET_ID = b.ID
        WHERE b.USER_ID = #{userId}
          AND b.STATUS = 'ACTIVE'
          AND bi.IS_ALERTED = 0
          AND bi.BUDGETED_AMOUNT > 0
          AND (bi.CURRENT_SPENT / bi.BUDGETED_AMOUNT * 100) >= bi.ALERT_THRESHOLD
        ORDER BY (bi.CURRENT_SPENT / bi.BUDGETED_AMOUNT * 100) DESC
    </select>

    <!-- 批量更新预算项的当前支出金额 -->
    <update id="updateCurrentSpentByBudgetId">
        UPDATE fin_budget_item bi
        INNER JOIN fin_budget b ON bi.BUDGET_ID = b.ID
        SET bi.CURRENT_SPENT = (
            CASE 
                WHEN bi.ITEM_TYPE = 'CATEGORY' THEN (
                    SELECT COALESCE(SUM(df.AMOUT), 0)
                    FROM data_flow df
                    WHERE df.TYPE = bi.TARGET_ID
                      AND df.TRANS_TYPE = '2'  -- 支出
                      AND df.FLOW_DATE BETWEEN b.START_DATE AND b.END_DATE
                )
                WHEN bi.ITEM_TYPE = 'ACCOUNT' THEN (
                    SELECT COALESCE(SUM(df.AMOUT), 0)
                    FROM data_flow df
                    WHERE df.ACC_ID = bi.TARGET_ID
                      AND df.TRANS_TYPE = '2'  -- 支出
                      AND df.FLOW_DATE BETWEEN b.START_DATE AND b.END_DATE
                )
                ELSE 0
            END
        ),
        bi.UPDATE_TIME = NOW()
        WHERE bi.BUDGET_ID = #{budgetId}
    </update>

</mapper>