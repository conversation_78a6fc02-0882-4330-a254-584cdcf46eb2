package com.xinghuo.admin.util;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.xinghuo.common.annotation.XhField;
import com.xinghuo.common.database.model.entity.DbLinkEntity;
import com.xinghuo.common.exception.DataException;
import com.xinghuo.common.exception.WorkFlowException;
import com.xinghuo.common.util.UserProvider;
import com.xinghuo.common.util.core.StrXhUtil;
import com.xinghuo.common.util.json.JsonXhUtil;
import com.xinghuo.form.dao.FlowFormDataMapper;
import com.xinghuo.form.service.FlowFormService;
import com.xinghuo.form.util.FlowFormDataUtil;
import com.xinghuo.form.util.FormCheckUtils;
import com.xinghuo.form.util.FormPublicUtils;
import com.xinghuo.permission.constant.PermissionConst;
import com.xinghuo.permission.entity.OrganizeEntity;
import com.xinghuo.permission.entity.PositionEntity;
import com.xinghuo.permission.entity.UserEntity;
import com.xinghuo.permission.entity.UserRelationEntity;
import com.xinghuo.permission.service.*;
import com.xinghuo.system.base.entity.DictionaryDataEntity;
import com.xinghuo.system.base.service.*;
import com.xinghuo.visualdev.base.entity.VisualdevEntity;
import com.xinghuo.visualdev.base.model.filter.RuleInfo;
import com.xinghuo.visualdev.base.service.FilterService;
import com.xinghuo.visualdev.base.service.VisualdevService;
import com.xinghuo.visualdev.onlinedev.service.VisualDevInfoService;
import com.xinghuo.visualdev.onlinedev.service.VisualdevModelDataService;
import com.xinghuo.visualdev.onlinedev.util.onlineDevUtil.OnlineSwapDataUtils;
import com.xinghuo.workflow.engine.entity.FlowTaskEntity;
import com.xinghuo.workflow.engine.entity.FlowTemplateJsonEntity;
import com.xinghuo.workflow.engine.service.FlowTaskService;
import com.xinghuo.workflow.engine.service.FlowTemplateJsonService;
import com.xinghuo.workflow.engine.service.FlowTemplateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 数据转换(代码生成器用)
 *
  * <AUTHOR>
 * @date 2023-10-05
 */
@Component
public class GeneraterSwapUtil {



    @Autowired
    private FilterService filterService;

    @Autowired
    private PositionService positionService;

    @Autowired
    private UserService userService;

    @Autowired
    private VisualdevService visualdevService;

    @Autowired
    private VisualDevInfoService visualDevInfoService;

    @Autowired
    private DataInterfaceService dataInterfaceService;

    @Autowired
    private VisualdevModelDataService visualdevModelDataService;

    @Autowired
    private ProvinceService provinceService;

    @Autowired
    private DictionaryDataService dictionaryDataService;

    @Autowired
    private BillRuleService billRuleService;

    @Autowired
    private FlowTaskService flowTaskService;

    @Autowired
    private GroupService groupService;

    @Autowired
    private RoleService roleService;

    @Autowired
    private UserProvider userProvider;

    @Autowired
    private DbLinkService dbLinkService;

    @Autowired
    private FlowTemplateJsonService flowTemplateJsonService;

    @Autowired
    private UserRelationService userRelationService;

    @Autowired
    private FlowFormDataMapper flowFormDataMapper;

    @Autowired
    private OnlineSwapDataUtils swapDataUtils;

    @Autowired
    private FlowFormDataUtil flowFormDataUtil;

    @Autowired
    private DbLinkService dblinkService;

    @Autowired
    private FlowTemplateService flowTemplateService;

    @Autowired
    private FlowFormService flowFormService;

    @Autowired
    private FormCheckUtils formCheckUtils;

    public final String regEx = "[\\[\\]\"]";

    /**
     * 日期时间戳字符串转换
     *
     * @param date
     * @param format
     * @return
     */
    public String dateSwap(String date, String format) {
        if (StrXhUtil.isNotEmpty(date)) {
            DateTimeFormatter ftf = DateTimeFormatter.ofPattern(format);
            if (date.contains(",")) {
                String[] dates = date.split(",");
                long time1 = Long.parseLong(dates[0]);
                long time2 = Long.parseLong(dates[1]);
                String value1 = ftf.format(LocalDateTime.ofInstant(Instant.ofEpochMilli(time1), ZoneId.systemDefault()));
                String value2 = ftf.format(LocalDateTime.ofInstant(Instant.ofEpochMilli(time2), ZoneId.systemDefault()));
                return value1 + "至" + value2;
            }
            long time = Long.parseLong(date);
            String value = ftf.format(LocalDateTime.ofInstant(Instant.ofEpochMilli(time), ZoneId.systemDefault()));
            return value;
        }
        return date;
    }







    /**
     * 岗位id转名称
     *
     * @param id
     * @return
     */
    public String posSelectValue(String id) {
        if (StrXhUtil.isNotEmpty(id)) {
            PositionEntity positionApiInfo = positionService.getInfo(id);
            if (ObjectUtil.isNotEmpty(positionApiInfo)) {
                return positionApiInfo.getFullName();
            }
            return id;
        }
        return " ";
    }


    /**
     * 用户id转名称
     *
     * @param id
     * @return
     */
    public String userSelectValue(String id) {
        if (StrXhUtil.isNotEmpty(id)) {
            UserEntity userEntity = userService.getInfo(id);
            if (ObjectUtil.isNotEmpty(userEntity)) {
                return userEntity.getRealName() + "/" + userEntity.getAccount();
            }
            return id;
        }
        return "";
    }




    /**
     * 获取数据字典数据
     *
     * @param feild
     * @return
     */
    public String getDicName(String feild, String dictionaryTypeId) {
        if (StrXhUtil.isNotEmpty(feild)) {
            //去除中括号以及双引号
            feild = feild.replaceAll(regEx, "");
            //判断多选框
            String[] feilds = feild.split(",");
            if (feilds.length > 1) {
                StringBuilder feildsValue = new StringBuilder();
                DictionaryDataEntity dictionaryDataEntity;
                for (String feil : feilds) {
                    dictionaryDataEntity = dictionaryDataService.getSwapInfo(feil, dictionaryTypeId);
                    if (dictionaryDataEntity != null) {
                        feildsValue.append(dictionaryDataEntity.getFullName() + ",");
                    } else {
                        feildsValue.append(feil + ",");
                    }
                }
                String finalValue;
                if (StrXhUtil.isEmpty(feildsValue) || feildsValue.equals("")) {
                    finalValue = feildsValue.toString();
                } else {
                    finalValue = feildsValue.substring(0, feildsValue.length() - 1);
                }
                return finalValue;
            }
            DictionaryDataEntity dictionaryDataentity = dictionaryDataService.getSwapInfo(feild, dictionaryTypeId);
            if (dictionaryDataentity != null) {
                return dictionaryDataentity.getFullName();
            }
            return feild;
        }
        if (StrXhUtil.isNotEmpty(feild)) {
            List<DictionaryDataEntity> dicList = dictionaryDataService.getDicList(dictionaryTypeId);
        }
        return feild;
    }

    /**
     * 获取数据字典数据-
     *
     * @param feild
     * @param keyName id或encode
     * @return
     */
    public String getDicName(String feild, String dictionaryTypeId, String keyName, boolean isMultiple, String separator) {
        Object dataConversion = "";
        if (StrXhUtil.isNotEmpty(feild)) {
            List<DictionaryDataEntity> dicList = dictionaryDataService.getDicList(dictionaryTypeId);
            Map<String, Object> idMap = new HashMap<>(dicList.size());
            Map<String, Object> enCodeMap = new HashMap<>(dicList.size());
            for (DictionaryDataEntity dd : dicList) {
                idMap.put(dd.getId(), dd.getFullName());
                enCodeMap.put(dd.getEnCode(), dd.getFullName());
            }
            if (StrXhUtil.isNotEmpty(separator)) {
                separator = "/";
            }
            if ("enCode".equals(keyName)) {
                dataConversion = FormPublicUtils.getDataConversion(enCodeMap, feild, isMultiple, separator);
            } else {
                dataConversion = FormPublicUtils.getDataConversion(idMap, feild, isMultiple, separator);
            }
        }
        return dataConversion.toString();
    }


    /**
     * 树转成list
     **/
    private void treeToList(String id, String fullName, String children, JSONArray data, List<Map<String, Object>> result) {
        if (data != null) {
            for (int i = 0; i < data.size(); i++) {
                JSONObject ob = data.getJSONObject(i);
                Map<String, Object> tree = new HashMap<>(16);
                tree.put(id, String.valueOf(ob.get(id)));
                tree.put(fullName, String.valueOf(ob.get(fullName)));
                result.add(tree);
                if (ob.get(children) != null) {
                    JSONArray childArray = ob.getJSONArray(children);
                    treeToList(id, fullName, children, childArray, result);
                }
            }
        }
    }

    /**
     * 生成单据规则
     *
     * @param encode
     * @param isCache
     * @return
     * @throws DataException
     */
    public String getBillNumber(String encode, Boolean isCache) throws DataException {
        return billRuleService.getBillNumber(encode, isCache);
    }

    /**
     * 功能流程 获取可视化实体
     *
     * @param visualId
     * @return
     */
    public VisualdevEntity getVisualEntity(String visualId) {
        VisualdevEntity info = visualdevService.getInfo(visualId);
        if (info != null) {
            return info;
        }
        return new VisualdevEntity();
    }

    public UserEntity getUser(String userId) {
        return userService.getInfo(userId);
    }

    /**
     * 获取流程任务
     *
     * @param id
     * @param columns
     * @return
     */
    public FlowTaskEntity getInfoSubmit(String id, SFunction<FlowTaskEntity, ?>... columns) {
        return flowTaskService.getInfoSubmit(id, columns);
    }

    public void deleteFlowTask(FlowTaskEntity flowTaskEntity) throws WorkFlowException {
        flowTaskService.delete(flowTaskEntity);
    }

    public void hasFlowTemplate(String flowId) throws WorkFlowException {
        boolean hasFlow = StrXhUtil.isEmpty(flowId);
        if (hasFlow) {
            throw new WorkFlowException("该功能未配置流程不可用");
        }
        FlowTemplateJsonEntity info = flowTemplateJsonService.getInfo(flowId);
    }



    public List<String> getIntersection(List<List<String>> lists) {
        if (lists == null || lists.size() == 0) {
            return new ArrayList<>();
        }
        ArrayList<List<String>> arrayList = new ArrayList<>(lists);
        for (int i = 0; i < arrayList.size(); i++) {
            List<String> list = arrayList.get(i);
            if (list == null || list.size() == 0) {
                return new ArrayList<>();
            }
        }
        List<String> intersection = arrayList.get(0);
        for (int i = 0; i < arrayList.size(); i++) {
            List<String> list = arrayList.get(i);
            intersection.retainAll(arrayList.get(i));
        }
        return intersection;
    }



    /**
     * 时间是否在范围内
     *
     * @param xhField
     * @param parse
     * @return
     */
    private boolean timeInRange(XhField xhField, Date parse) {
        boolean flag = true;
        if (StrXhUtil.isNotEmpty(xhField.startTime())) {
            Long startTime = Long.parseLong(xhField.startTime());
            flag = parse.after(new Date(startTime));
        }
        if (flag && StrXhUtil.isNotEmpty(xhField.endTime())) {
            Long endTime = Long.parseLong(xhField.endTime());
            flag = parse.before(new Date(endTime));
        }
        return flag;
    }


    private List<String> checkOptionsControl(boolean multiple, Map<String, Object> insMap, String vModel, String label, Map<String, Object> cacheMap, List<String> valueList, StringJoiner errInfo) {
        boolean error = false;
        if (!multiple) {
            //非多选填入多选值
            if (valueList.size() > 1) {
                error = true;
                errInfo.add(label + "非多选");
            }
        }
        List<String> dataList = new ArrayList<>();
        if (!error) {
            boolean errorHapen = false;
            for (String va : valueList) {
                Object vo = cacheMap.get(va);
                if (vo == null) {
                    errorHapen = true;
                } else {
                    dataList.add(vo.toString());
                }

            }
            if (errorHapen) {
                errInfo.add(label + "值不正确");
            } else {
                insMap.put(vModel, !multiple ? dataList.get(0) : JsonXhUtil.getObjectToString(dataList));
            }
        }
        return dataList;
    }

    /**
     * 递归查询
     *
     * @param label
     * @param value
     * @param Children
     * @param data
     * @param options
     */
    public static void getOptions(String label, String value, String Children, JSONArray data, List<Map<String, Object>> options) {
        for (int i = 0; i < data.size(); i++) {
            JSONObject ob = data.getJSONObject(i);
            Map<String, Object> tree = new HashMap<>(16);
            tree.put(value, String.valueOf(ob.get(value)));
            tree.put(label, String.valueOf(ob.get(label)));
            options.add(tree);
            if (ob.get(Children) != null) {
                JSONArray childrenArray = ob.getJSONArray(Children);
                getOptions(label, value, Children, childrenArray, options);
            }
        }
    }





    @DS("")
    public List<RuleInfo> getFilterCondition(String id) {
        return filterService.getCondition(id);
    }

    public static List convertToList(Object obj) {
        return OnlineSwapDataUtils.convertToList(obj);
    }

    public static String convertValueToString(String obj, boolean mult, boolean isOrg) {
        return OnlineSwapDataUtils.convertValueToString(obj, mult, isOrg);
    }

    /**
     * 获取数据连接
     *
     * @param dbLink
     * @return
     */
    public DbLinkEntity getDataSource(String dbLink) {
        QueryWrapper<DbLinkEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(DbLinkEntity::getFullName, dbLink);
        return dblinkService.getOne(queryWrapper);
    }




    public static String loop(List<Map> options, String oneData, String key, String label) {
        for (int i = 0; i < options.size(); i++) {
            if (options.get(i).get(key).equals(oneData)) {
                return options.get(i).get(label).toString();
            } else if (options.get(i).get("children") != null) {
                List<Map> children = JsonXhUtil.jsonToList(options.get(i).get("children"), Map.class);
                String loop = loop(children, oneData, key, label);
                if (loop != null) {
                    return loop;
                }
            }
        }
        return null;
    }






    public static List getList(Field field, Object object) {
        List resultList = new ArrayList<>();
        if (object != null) {
            try {
                Class clzz = object.getClass();
                //反射调用获取到list的size方法来获取到集合的大小
                Method sizeMethod = clzz.getDeclaredMethod("size");
                if (!sizeMethod.isAccessible()) {
                    sizeMethod.setAccessible(true);
                }
                //集合长度
                int size = (int) sizeMethod.invoke(object);
                //循环遍历获取到数据
                for (int i = 0; i < size; i++) {
                    //反射获取到list的get方法
                    Method getMethod = clzz.getDeclaredMethod("get" , int.class);
                    //调用get方法获取数据
                    if (!getMethod.isAccessible()) {
                        getMethod.setAccessible(true);
                    }
                    Object invoke = getMethod.invoke(object, i);
                    resultList.add(invoke);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return resultList;

    }







}
