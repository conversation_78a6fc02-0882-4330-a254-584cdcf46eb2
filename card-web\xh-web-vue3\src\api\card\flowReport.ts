import { defHttp } from '/@/utils/http/axios';

enum Api {
  Prefix = '/api/card/report',
}

// 获取统计概要
export function getStatisticsSummary(data: any) {
  return defHttp.post({ url: Api.Prefix + '/summary', data });
}

// 获取收支统计
export function getIncomeExpenseStatistics(data: any) {
  return defHttp.post({ url: Api.Prefix + '/income-expense', data });
}

// 获取分类统计
export function getCategoryStatistics(data: any) {
  return defHttp.post({ url: Api.Prefix + '/category', data });
}

// 获取趋势分析
export function getTrendAnalysis(data: any) {
  return defHttp.post({ url: Api.Prefix + '/trend', data });
}

// 获取卡片统计
export function getCardStatistics(data: any) {
  return defHttp.post({ url: Api.Prefix + '/card', data });
}

// 获取流水明细
export function getFlowDetails(data: any) {
  return defHttp.post({ url: Api.Prefix + '/details', data });
}

// 获取完整报表数据
export function getCompleteReport(data: any) {
  return defHttp.post({ url: Api.Prefix + '/complete', data });
}

// 获取快速统计数据
export function getQuickStatistics() {
  return defHttp.get({ url: Api.Prefix + '/quick' });
}

// 获取热门分类排行
export function getTopCategories(data: any) {
  return defHttp.post({ url: Api.Prefix + '/top-categories', data });
}

// 获取月度对比数据
export function getMonthlyComparison(currentMonth: string, compareMonths = 6) {
  return defHttp.post({ 
    url: Api.Prefix + '/monthly-comparison', 
    params: { currentMonth, compareMonths } 
  });
}
