package com.xinghuo.card.bill.controller;

import com.xinghuo.card.bill.entity.CreditCardBillEntity;
import com.xinghuo.card.bill.service.CreditCardBillService;
import com.xinghuo.common.base.ActionResult;
import com.xinghuo.common.base.UserInfo;
import com.xinghuo.common.base.vo.ListVO;
import com.xinghuo.common.util.UserProvider;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * 信用卡账单控制器
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2024-01-01
 */
@Slf4j
@Tag(name = "信用卡账单管理", description = "CreditCardBill")
@RestController
@RequestMapping("/api/card/bill")
public class CreditCardBillController {

    @Autowired
    private CreditCardBillService creditCardBillService;

    @Autowired
    private UserProvider userProvider;

    /**
     * 获取指定信用卡的最新账单
     */
    @Operation(summary = "获取最新账单")
    @GetMapping("/latest/{cardAccId}")
    public ActionResult<CreditCardBillEntity> getLatestBill(@PathVariable("cardAccId") String cardAccId) {
        CreditCardBillEntity bill = creditCardBillService.getLatestBill(cardAccId);
        return ActionResult.success(bill);
    }

    /**
     * 获取指定信用卡的历史账单列表
     */
    @Operation(summary = "获取历史账单")
    @GetMapping("/history/{cardAccId}")
    public ActionResult<ListVO<CreditCardBillEntity>> getHistoryBills(
            @PathVariable("cardAccId") String cardAccId,
            @RequestParam(value = "limit", defaultValue = "12") int limit) {
        
        List<CreditCardBillEntity> bills = creditCardBillService.getHistoryBills(cardAccId, limit);
        ListVO<CreditCardBillEntity> listVO = new ListVO<>();
        listVO.setList(bills);
        return ActionResult.success(listVO);
    }

    /**
     * 获取账单详情
     */
    @Operation(summary = "获取账单详情")
    @GetMapping("/{billId}")
    public ActionResult<CreditCardBillEntity> getBillDetail(@PathVariable("billId") String billId) {
        CreditCardBillEntity bill = creditCardBillService.getById(billId);
        if (bill == null) {
            return ActionResult.fail("账单不存在");
        }
        return ActionResult.success(bill);
    }

    /**
     * 更新还款状态
     */
    @Operation(summary = "更新还款状态")
    @PostMapping("/{billId}/payment")
    public ActionResult updatePaymentStatus(
            @PathVariable("billId") String billId,
            @RequestParam("paymentAmount") BigDecimal paymentAmount) {
        
        if (paymentAmount == null || paymentAmount.compareTo(BigDecimal.ZERO) <= 0) {
            return ActionResult.fail("还款金额必须大于0");
        }

        boolean success = creditCardBillService.updatePaymentStatus(billId, paymentAmount);
        if (success) {
            return ActionResult.success("还款状态更新成功");
        } else {
            return ActionResult.fail("还款状态更新失败");
        }
    }

    /**
     * 手动生成账单
     */
    @Operation(summary = "手动生成账单")
    @PostMapping("/generate/{cardAccId}")
    public ActionResult generateBill(@PathVariable("cardAccId") String cardAccId) {
        try {
            // 这里需要先获取信用卡配置，然后生成账单
            // 为了简化，暂时返回成功消息
            return ActionResult.success("账单生成请求已提交");
        } catch (Exception e) {
            log.error("手动生成账单失败", e);
            return ActionResult.fail("生成账单失败：" + e.getMessage());
        }
    }

    /**
     * 获取需要还款提醒的账单
     */
    @Operation(summary = "获取还款提醒")
    @GetMapping("/reminders")
    public ActionResult<ListVO<CreditCardBillEntity>> getPaymentReminders() {
        List<CreditCardBillEntity> bills = creditCardBillService.getBillsNeedReminder();
        ListVO<CreditCardBillEntity> listVO = new ListVO<>();
        listVO.setList(bills);
        return ActionResult.success(listVO);
    }

    /**
     * 检查账单是否逾期
     */
    @Operation(summary = "检查账单逾期状态")
    @GetMapping("/{billId}/overdue")
    public ActionResult<Boolean> checkOverdue(@PathVariable("billId") String billId) {
        CreditCardBillEntity bill = creditCardBillService.getById(billId);
        if (bill == null) {
            return ActionResult.fail("账单不存在");
        }
        
        boolean isOverdue = creditCardBillService.isOverdue(bill);
        return ActionResult.success(isOverdue);
    }
}
