#set($pKeyName = "${context.pKeyName}")
#set($mastTableList = $context.mastTable)
#macro(code7 $key $html $formModel $showModel)
	#set($model = "${html.vModel}")
	#set($slot = ${html.slot})
	#set($prop="${html.vModel}")
	#set($config = $html.config)
	#set($placeholder = "${html.placeholder}")
	#set($show = ${config.noShow})
	#set($vmodel="${formModel}.${model}")
	#set($modelProps="")
	#set($modelOptions="")
	#set($modelUpload="")
	#set($modelColumnOptions="")
	#set($xhkey="${config.xhKey}")
	#set($tag="")
	#set($list="${model}")
	#set($type=${html.type})
	#set($placeholder = ${html.placeholder})
	#set($showList = ${model})
	#set($showJudge= "v-if="""+"judgeShow('"+${showList}+"')"+"""")
	#set($relationField="")
	#set($relationModel = "")
	#set($roleOrgroup = "")
	#set($showWrite = "")
	#set($required = ".${prop}")
	#set($childoptions="")
	#set($inputalign="")
	#set($ableRelationIds="")
	#set($rowIndex="")
	#set($formData="")
	#set($templateJson="")
	#set($startTime="")
	#set($endTime="")
	#set($dataType="${config.dataType}")
	#if($xhkey=='userSelect' && ${html.relationField})
		#set($ableRelationIds="${context.formModel}.${html.relationField}")
		#if($html.relationChild)
			#set($ableRelationIds="${context.formModel}.${showModel}List[i].${html.relationField}")
		#end
	#end
	#if($xhkey=='datePicker' || $xhkey=='timePicker')
		#set($startRelationField="''")
		#if($config.startRelationField)
			#set($startRelationField="${context.formModel}.${config.startRelationField}")
			#if($config.startChild)
				#set($startRelationField="${context.formModel}.${showModel}List[i].${config.startRelationField}")
			#end
		#end
		#set($startTimeValue="#if(${config.startTimeValue})'${config.startTimeValue}'#else''#end")
		#set($startTimeType="#if(${config.startTimeType})${config.startTimeType}#else''#end")
		#set($startTimeTarget="#if(${config.startTimeTarget})${config.startTimeTarget}#else''#end")
		#set($endRelationField="''")
		#if($config.endRelationField)
			#set($endRelationField="${context.formModel}.${config.endRelationField}")
			#if($config.endChild)
				#set($endRelationField="${context.formModel}.${showModel}List[i].${config.endRelationField}")
			#end
		#end
		#set($endTimeValue="#if(${config.endTimeValue})'${config.endTimeValue}'#else''#end")
		#set($endTimeType="#if(${config.endTimeType})${config.endTimeType}#else''#end")
		#set($endTimeTarget="#if(${config.endTimeTarget})${config.endTimeTarget}#else''#end")

		#set($startTime="dateTime(${config.startTimeRule},${startTimeType},${startTimeTarget},${startTimeValue},${startRelationField})")
		#set($endTime="dateTime(${config.endTimeRule},${endTimeType},${endTimeTarget},${endTimeValue},${endRelationField})")
		#if($xhkey=='timePicker')
			#set($startTime="time(${config.startTimeRule},${startTimeType},${startTimeTarget},${startTimeValue},'${html.format}',${startRelationField})")
			#set($endTime="time(${config.endTimeRule},${endTimeType},${endTimeTarget},${endTimeValue},'${html.format}',${endRelationField})")
		#end
	#end
	#set($configLabel="${config.label}")
	#if($xhkey=='roleSelect' || $xhkey=='groupSelect')
		#set($roleOrgroup = "vModel='"+${prop}+"'")
	#end
	#if($xhkey=='relationForm' || $xhkey=='popupSelect'|| $xhkey=='popupTableSelect'|| $xhkey=='autoComplete')
		#set($relationField = "relationField='"+${html.relationField}+"'")
		#set($relationModel = "vModel='"+${prop}+"'")
		#set($rowIndex="null")
		#set($formData="${context.formModel}")
		#set($templateJson="interfaceRes.${model}")
	#end
	#if($xhkey=='popupAttr' || $xhkey=='relationFormAttr')
		#set($relationField = "relationField='"+${html.relationField}+"'")
		#set($showJudge = "")
		#if(${html.relationField})
			#set($showJudge = "v-if="""+"judgeShow('"+${html.relationField}+"')"+"""")
		#end
		#set($showList = "")
	#end
	#if($key=='table')
		#set($showList="")
		#set($showWrite=":disabled=""judgeWrite('"+${showModel}+"List')||judgeWrite('"+${showModel}+"List-"+${model}+"')""")
		#set($prop="")
		#set($required = "['${showModel}List-${model}']")
		#set($showJudge= "v-if="""+"judgeShow('"+${showModel}+"List-"+${model}+"')"+"""")
		#set($childoptions="${showModel}")
		#if($xhkey=='relationForm' || $xhkey=='popupSelect'|| $xhkey=='popupTableSelect'|| $xhkey=='autoComplete')
			#set($relationModel = ":vModel=""'"+${model}+"'+i""")
			#set($rowIndex="i")
			#set($formData="${context.formModel}")
			#set($templateJson="interfaceRes.${showModel}${model}")
		#end
		#if($xhkey=='popupAttr' || $xhkey=='relationFormAttr')
			#set($relationField = ":relationField=""'"+${html.relationField}+"'+i""")
			#set($showJudge = "")
			#if(${html.relationField})
				#set($showJudge= "v-if="""+"judgeShow('"+${showModel}+"List-"+${html.relationField}+"')"+"""")
			#end
			#set($showWrite="")
		#end
	#end
	#if(${showList})
		#set($showWrite=":disabled=""judgeWrite('"+${showList}+"')""")
	#end
	#if($xhkey=='createUser' || $xhkey=='createTime' || $xhkey=='modifyUser' || $xhkey=='modifyTime' || $xhkey=='currOrganize' || $xhkey=='currDept' || $xhkey=='currPosition' || $xhkey=='billRule')
		#set($placeholder = "系统自动生成")
		#set($showWrite="disabled")
	#end
	#if($xhkey=='input' || $xhkey=='textarea' || $xhkey=='modifyUser' || $xhkey=='modifyTime' || $xhkey=='billRule')
		#set($tag = "u-input")
		#if($xhkey=='textarea')
			#set($type = "textarea")
		#end
		#set($inputalign="right")
	#elseif($xhkey=='createUser' || $xhkey=='createTime' || $xhkey=='currOrganize' || $xhkey=='currDept' || $xhkey=='currPosition' )
		#set($tag = "xh-open-data")
	#elseif($xhkey=='inputNumber')
		#set($tag = "xh-number-box")
	#elseif($xhkey=='slider')
		#set($tag = "u-"+$xhkey)
	#elseif($xhkey=='switch' ||$xhkey=='radio' || $xhkey=='checkbox' || $xhkey=='select' || $xhkey=='cascader' || $xhkey=='rate')
		#set($tag = "xh-"+$xhkey)
	#elseif($xhkey=='areaSelect')
		#set($tag = "xh-city-select")
	#elseif($xhkey=='treeSelect')
		#set($tag = "xh-tree-select")
	#elseif($xhkey=='colorPicker')
		#set($tag = "")
	#elseif($xhkey=='editor')
		#set($configLabel="")
		#set($tag = "xh-editor")
	#elseif($xhkey=='popupTableSelect')
		#set($tag = "xh-table-select")
	#elseif($xhkey=='groupSelect')
		#set($tag = "xh-group-select")
	#elseif($xhkey=='roleSelect')
		#set($tag = "xh-role-select")
	#elseif($xhkey=='uploadImg')
		#set($tag = "xh-upload")
		#set($modelUpload="${vmodel}")
	#elseif($xhkey=='uploadFile')
		#set($tag = "xh-file")
	#elseif($xhkey=='popupSelect')
		#set($tag = "xh-popup-select")
		#set($type = "popup")
	#elseif($xhkey=='relationForm')
		#set($tag = "xh-relation-select")
		#set($type = "relation")
	#elseif($xhkey=='datePicker' || $xhkey=='timePicker')
		#set($tag = "xh-date-time")
		#set($type = "date")
		#if($xhkey=='timePicker')
			#set($type = "time")
		#end
	#elseif($xhkey=='userSelect')
		#set($tag = "xh-user-select")
	#elseif($xhkey=='usersSelect')
		#set($tag = "xh-user-choice")
	#elseif($xhkey=='organizeSelect' || $xhkey=='depSelect' || $xhkey=='posSelect' )
		#set($tag = "xh-postordep-select")
		#if($xhkey=='organizeSelect')
			#set($type = "organize")
			#set($tag = "xh-com-select")
		#elseif($xhkey=='depSelect')
			#set($type = "department")
		#elseif($xhkey=='posSelect')
			#set($type = "position")
		#end
	#elseif($xhkey=='popupAttr')
		#set($tag = "xh-relation-attr")
		#set($type = "popupAttr")
	#elseif($xhkey=='relationFormAttr')
		#set($tag = "xh-relation-attr")
		#set($type = "relationFormAttr")
	#elseif($xhkey=='autoComplete')
		#set($tag = "xh-auto-complete")
	#end
	#if($xhkey=='checkbox' || $xhkey=='select' || $xhkey=='cascader'|| $xhkey=='radio' || $xhkey=='treeSelect')
		#set($modelProps="${childoptions}${list}Props")
		#set($modelOptions="${childoptions}${list}Options")
		#if(${childoptions} && ${dataType}=='dynamic')
			#set($modelOptions="${context.formModel}.${childoptions}List[i].${list}Options")
		#end
	#end
	#if($xhkey=='relationForm' || $xhkey=='popupSelect'|| $xhkey=='popupTableSelect')
		#set($modelColumnOptions="${childoptions}${list}ColumnOptions")
	#end
	#set($end ="/"+ ${tag})
	#if($show == false && ${config.app}==true)
		#if($tag)
			<view class="u-p-l-20 u-p-r-20 form-item-box">
			<u-form-item #if($config.showLabel && $config.showLabel == true)
                #if($configLabel) label="${configLabel}"#end
				#if($config.tipLabel && $xhkey!='editor' && $configLabel) left-icon="question-circle-fill" @clickIcon="clickIcon('${configLabel}','${config.tipLabel}')" :left-icon-style="{'color':'#a0acb7'}" #end
				#if($showJudge) ${showJudge}#end
				#if($model) :required="requiredList${required}"#end
                #if($config.labelWidth) label-width="${config.labelWidth}"#end #else label-width="0"#end
                #if($prop) prop="${prop}" #end>
				<${tag} #if($model)v-model="${vmodel}"#if(${showWrite}) ${showWrite} #end#end
				#if($model)
					#if($key=='table')
				@change="changeData('${showModel}-${model}',i)"
				#else
				@change="changeData('${model}',-1)"
					#end
				#end
				#if($xhkey=='uploadFile') :list="${vmodel}" #end
				#if($slot.prepend) prepend="${slot.prepend}" #end
				#if($slot.append) append="${slot.append}" #end
				#if($rowIndex) :rowIndex="${rowIndex}" #end
				#if($formData) :formData="${formData}" #end
				#if($templateJson) :templateJson="${templateJson}" #end
				#if($html.allowhalf) allow-half #end
				#if($ableRelationIds) :ableRelationIds = "${ableRelationIds}" #end
				#if($startTime) :startTime="${startTime}" #end
				#if($endTime) :endTime="${endTime}" #end
				#if($html.isStorage || $html.isStorage=='0') isStorage="${html.isStorage}" #end
				#if($inputalign) input-align='right' #end
				#if($html.prefixIcon) prefix-icon="${html.prefixIcon}" #end
				#if($html.suffixIcon) suffix-icon="${html.suffixIcon}" #end
				#if($html.selectType) selectType="${html.selectType}" #end
				#if($html.ableIds) :ableIds="ableAll.${showModel}${model}ableIds" #end
				#if($html.ableDepIds) :ableDepIds="ableAll.${showModel}${model}ableDepIds" #end
				#if($html.ablePosIds) :ablePosIds="ableAll.${showModel}${model}ablePosIds" #end
				#if($html.ableUserIds) :ableUserIds="ableAll.${showModel}${model}ableUserIds" #end
				#if($html.ableRoleIds) :ableRoleIds="ableAll.${showModel}${model}ableRoleIds" #end
				#if($html.ableGroupIds) :ableGroupIds="ableAll.${showModel}${model}ableGroupIds" #end
				#if($html.format) format="${html.format}" #end
				#if($html.showLevel) showLevel="${html.showLevel}" #end
				#if($html.optionType) optionType="${html.optionType}" #end
				#if($html.tipText) tipText="${html.tipText}" #end
				#if($html.total) :total="${html.total}" #end
				#if($html.precision) :precision="${html.precision}" #end
				#if($html.direction) direction="${html.direction}" #end
				#if($html.isAmountChinese) isAmountChinese #end
				#if($html.thousands) thousands #end
				#if($html.addonAfter) addonAfter="${html.addonAfter}" #end
				#if($html.addonBefore) addonBefore="${html.addonBefore}" #end
				#if($html.controls) :controls="${html.controls}" #end
				#if($html.hasPage) hasPage #end
				#if($html.clearable) clearable #end
				#if($html.filterable) filterable #end
				#if($html.propsValue) propsValue="${html.propsValue}" #end
				#if($html.popupWidth) popupWidth="${html.popupWidth}" #end
				#if($html.popupTitle) popupTitle="${html.popupTitle}" #end
				#if($html.popupType) popupType="${html.popupType}" #end
				#if($relationField) ${relationField} #end
				#if($relationModel) ${relationModel} #end
				#if($roleOrgroup) ${roleOrgroup} #end
				#if($html.showField) showField="${html.showField}" #end
				#if($modelColumnOptions)  :columnOptions="${modelColumnOptions}"#end
				#if($html.modelId) modelId="${html.modelId}" #end
				#if($html.interfaceId) interfaceId="${html.interfaceId}" #end
				#if($html.pageSize) :pageSize="${html.pageSize}" #end
				#if($html.sizeUnit) sizeUnit="${html.sizeUnit}" #end
				#if($html.accept) accept="${html.accept}" #end
				#if($html.fileSize) :fileSize="${html.fileSize}" #end
				#if($html.limit) :limit="${html.limit}" #end
				#if($html.pathType) pathType="${html.pathType}" #end
                #if($html.isAccount) :isAccount="${html.isAccount}" #end
                #if($html.folder) folder="${html.folder}" #end
				#if($placeholder) placeholder="${placeholder}"#end
                #if($modelProps)  :props="${modelProps}"#end
                #if($modelOptions) :options="${modelOptions}"#end
				#if($modelUpload) :value="${modelUpload}"#end
                #if($html.multiple) :multiple="${html.multiple}"#end
                #if($html.maxlength) :maxlength="${html.maxlength}"#end
                #if($html.min) :min="${html.min}"#end
				#if($html.level || ${html.level}==0) :level="${html.level}" #end
				#if($html.count) :max="${html.count}"#end
				#if($html.max) :max="${html.max}"#end
				#if($type) type="${type}"#end
                #if($html.step) :step="${html.step}"#end
                #if($html.textStyle) :textStyle='${html.textStyle}'#end
                #if($html.style) :style='${html.style}'#end
                #if($html.readonly) readonly#end
                #if($xhkey == 'slider' ) style="width: 100%;"#end
                #if($html.contentPosition) contentPosition="${html.contentPosition}"#end>
				</${tag}>
			</u-form-item>
			</view>
		#end
	#end
#end
<template>
    <view class="xh-wrap xh-wrap-workflow" v-if="!loading">
        <u-form :model="${context.formModel}" :rules="${context.formRules}" ref="${context.formModel}" :errorType="['toast']"
				#set($position='left')
				#if(${context.labelPosition}=='top')
					#set($position='top')
				#end
				#set($align='left')
				#if(${context.labelPosition}=='right')
					#set($align='right')
				#end
                label-position="${position}" label-align="${align}" :label-width="labelwidth" class="xh-form">
            #foreach($fieLdsModel in ${context.form})
                #set($xhkey = "${fieLdsModel.xhKey}")
                #set($isEnd = "${fieLdsModel.isEnd}")
				#set($formModel = ${fieLdsModel.formModel})
				#set($config=$formModel.config)
				#set($span=$config.span)
				#set($outermost = ${formModel.outermost})
				#set($header = ${formModel.header})
                #if($xhkey=='card' || $xhkey=='row')
					#if(${config.app}==true)
					#if(${isEnd}=='0')
            <view class="xh-card">
						#if($header)
			<view class="xh-card-cap u-line-1">${header}#if(${config.tipLabel})<u-icon name="question-circle-fill" class="u-m-l-10" color="#a0acb7" @click="clickIcon('${header}','${config.tipLabel}')"/>#end</view>
						#end
					#else
			</view>
					#end
					#end
				#elseif($xhkey=='collapse')
					#set($collapse = "u-collapse")
					#if(${outermost}=='1')
						#set($collapse = "u-collapse-item")
					#end
					#if(${config.app}==true)
						#if(${isEnd}=='0')
							#if(${outermost}=='0')
				<${collapse} ref="${formModel.model}Current" :accordion="${formModel.accordion}" v-model="${formModel.model}Current">
							#else
				<${collapse} title="${formModel.title}" :open="${formModel.model}Current.includes('${formModel.name}')" name="${formModel.name}" class="collapse-item">
							#end
						#else
				</${collapse}>
						#end
					#end
				#elseif($xhkey=="groupTitle" || $xhkey=="divider" || $xhkey=='text' || $xhkey=='button' || $xhkey=='link' || $xhkey=='alert')
					#set($defaultName="")
					#set($tag="")
					#set($divider ="")
					#set($itemBox = "form-item-box")
					#if($xhkey=="groupTitle")
						#set($tag = "xh-group")
						#set($itemBox="")
					#elseif($xhkey=="divider")
						#set($divider ="half-width=""200"" height=""80""")
						#set($tag = "u-"+$xhkey)
						#set($defaultName="$formModel.content")
					#elseif($xhkey=='text')
						#set($tag= "xh-text")
					#elseif($xhkey=="button")
						#set($tag= "xh-button")
					#elseif($xhkey=="link")
						#set($tag= "xh-link")
					#elseif($xhkey=="alert")
						#set($tag= "xh-alert-tips")
					#end
					#if(${config.app}==true)
			<view class="u-p-l-20 u-p-r-20 ${itemBox}">
			<u-form-item>
				<${tag} #if($formModel.style) :style='${formModel.style}'#end
				#if($formModel.title || $formModel.title=='') title='${formModel.title}'#end
				#if($formModel.description) description="${formModel.description}" #end
				#if($formModel.closeText) closeText="${formModel.closeText}" #end
				#if($formModel.tagIcon) tagIcon='${formModel.tagIcon}'#end
				#if($formModel.showIcon) showIcon #end
				#if($formModel.closable) closable #end
				#if($formModel.target) target='${formModel.target}'#end
				#if($formModel.href) href='${formModel.href}'#end
				#if($formModel.buttonText) buttonText='${formModel.buttonText}'#end
				#if($formModel.align) align='${formModel.align}'#end
				#if($formModel.type) type='${formModel.type}'#end
				#if($formModel.textStyle) :textStyle='${formModel.textStyle}'#end
				#if($config.defaultValue) value="${config.defaultValue}"#end
				#if($formModel.helpMessage && ${formModel.content}) tipLabel="${formModel.helpMessage}" @groupIcon="clickIcon('${formModel.content}','${formModel.helpMessage}')" #end
				#if($divider) ${divider}#end
				#if($formModel.content) value="${formModel.content}" content="${formModel.content}"#end
				#if($formModel.contentPosition) contentPosition="${formModel.contentPosition}" #end>
					#if(${defaultName})
					$!{defaultName}
					#end
				</${tag}>
			</u-form-item>
			</view>
					#end
				#elseif($xhkey=='tab')
					#set($tabModel = ${formModel.model})
					#set($tabNum = ${formModel.childNum})
					#if(${config.app}==true)
					#if(${isEnd}=='0')
						#if(${outermost}=='0')
			<view prop="${formModel.model}">
			<u-tabs ref="${formModel.model}Current"  :is-scroll="false" :list="${tabModel}Data" name="title" :current="${tabModel}Current-1" @change="${tabModel}"/>
			<view>
						#else
			<view v-show="${tabNum}+1 == ${tabModel}Current">
						#end
					#else
						#if(${outermost}=='0')
			</view>
			</view>
						#else
			</view>
						#end
					#end
					#end
				#elseif($xhkey=='mastTable')
					#set($mastTableModel = $fieLdsModel.formMastTableModel)
					#set($html = $mastTableModel.mastTable.fieLdsModel)
					#set($formModel="${context.formModel}")
					#set($showModel="")
					#code7('mastTable' $html $formModel $showModel)
				#elseif($xhkey=='mast')
                    #set($html = $fieLdsModel.formColumnModel.fieLdsModel)
					#set($formModel = "${context.formModel}")
					#set($showModel="")
					#code7('mast' $html $formModel $showModel)
			    #elseif($xhkey=='table')
                    #set($child = $fieLdsModel.childList)
					#set($childApp=$child.app)
					#set($className = "")
                    #foreach($children in ${context.children})
                        #if(${children.tableModel}==${child.tableModel})
                            #set($className = "${children.className.toLowerCase()}")
                        #end
                    #end
					#set($showJudge= "v-if="""+"judgeShow('"+${className}+"List')"+"""")
					#set($showTableWrite="!judgeWrite('"+${className}+"List')")
					#if($childApp == true)
            <view class="xh-table"#if(${showJudge}) $showJudge#end>
			<view class="xh-table-item" v-for="(item,i) in ${context.formModel}.${className}List" :key="i">
			<view class="xh-table-item-title u-flex u-row-between">
						#if($child.showTitle== true)
			<text class="xh-table-item-title-num">${child.label}({{i+1}})#if(${child.tipLabel})<u-icon name="question-circle-fill" class="u-m-l-10" color="#a0acb7" @click="clickIcon('${child.label}','${child.tipLabel}')"/>#end</text>
                        #end
						#if($child.addType==0)
						#set($num="1")
						#else
						#set($num="0")
						#end
			<view class="xh-table-item-title-action" v-if="#if($showTableWrite) ${showTableWrite} && #end${context.formModel}.${className}List.length>${num}" @click="del${className}List(i)">删除</view>
			</view>
						#foreach($childListAll in ${child.childList})
							#set($html = $childListAll.fieLdsModel)
							#set($formModel="${context.formModel}.${className}List[i]")
							#set($showModel="${className}")
							#code7('table' $html $formModel $showModel)
						#end
			</view>
			<view class="xh-table-addBtn" @click="add${className}List" #if(${showTableWrite}) v-if="$showTableWrite"#end>
			<u-icon name="plus" color="#2979ff"></u-icon>$!{child.actionText}<span v-if="${context.formModel}.${className}List.length==0">${child.label}</span>
			</view>
			#set($showSummary = $child.showSummary)
			#if($showSummary)
			<view class="xh-table-item">
				<view class="xh-table-item-title u-flex u-row-between">
					<text class="xh-table-item-title-num">${child.label}合计</text>
				</view>
				<view class="u-p-l-20 u-p-r-20 form-item-box">
				<u-form-item v-for="(item,i) in ${className}()" :key="i" :label="item.name">
					<p>{{item.val}}</p>
				</u-form-item>
				</view>
			</view>
			#end
            </view>
					#end
				#end
            #end
        </u-form>
		<u-modal v-model="show" :content="content" width='70%' border-radius="16" :content-style="{fontSize: '28rpx',padding: '20rpx',lineHeight: '44rpx',textAlign: 'left'}"
		 :titleStyle="{padding: '20rpx'}" :confirm-style="{height: '80rpx',lineHeight: '80rpx'}" :title="title" confirm-text="确定">
		</u-modal>
    </view>
</template>
#macro(appableAll $fieLdsModel,$childList)
	#set($html = $fieLdsModel)
	#set($vModel = "${childList}${html.vModel}")
	#set($config = $html.config)
	#set($dataType = "${config.dataType}")
	#if(${html.ableIds})
		${vModel}ableIds:${html.ableIds},
	#end
	#if(${html.ableDepIds})
		${vModel}ableDepIds:${html.ableDepIds},
	#end
	#if(${html.ablePosIds})
		${vModel}ablePosIds:${html.ablePosIds},
	#end
	#if(${html.ableUserIds})
		${vModel}ableUserIds:${html.ableUserIds},
	#end
	#if(${html.ableRoleIds})
		${vModel}ableRoleIds:${html.ableRoleIds},
	#end
	#if(${html.ableGroupIds})
		${vModel}ableGroupIds:${html.ableGroupIds},
	#end
#end
#macro(appRule $fieLdsModel)
	#set($html = $fieLdsModel)
	#set($vModel = "${html.vModel}")
	#set($config = $html.config)
	#set($label = "${config.label}")
	#set($xhkey = "${config.xhKey}")
	#set($listSize=$!{config.regList})
	#set($defaultValue=${config.defaultValue})
	#set($defaultValueSize=$!{config.defaultValue})
	#set($trigger = ${config.trigger})
	#if(${trigger.substring(0,1)}!='[')
		#set($trigger = "'"+ ${config.trigger}+ "'")
	#end
	#if($vModel)
		#if(!$config.defaultValue && $config.defaultValue==[])
			#set($messages='请至少选择一个')
		#elseif(${config.defaultValue} && (${defaultValueSize} || $defaultValueSize.size()>0))
			#set($messages='请至少选择一个')
		#elseif($html.placeholder)
			#set($messages=${html.placeholder})
		#else
			#set($messages='不能为空')
		#end
		#if(${config.required}==true|| (${listSize} && $listSize.size()>0))
					${vModel}: [
			#if($config.required==true)
						{
							required: true,
							message: '${label}$!{messages}',
							#if($xhkey=='checkbox' || $xhkey=='timeRange' || $xhkey=='dateRange' || $xhkey=='areaSelect' || $xhkey=='cascader')
							type:'array',
							#elseif($xhkey=='organizeSelect' || $xhkey=='uploadFile' || $xhkey=="uploadImg")
							type:'array',
							#elseif($xhkey=='userSelect' || $xhkey=='usersSelect' || $xhkey=='select' || $xhkey=='depSelect' || $xhkey=='posSelect' || $xhkey=='popupTableSelect'|| $xhkey=='groupSelect'|| $xhkey=='roleSelect' || $xhkey=='treeSelect')
								#if(${html.multiple}=='true')
							type:'array',
								#end
							#end
						},
			#end
			#if($listSize.size()>0)
				#foreach($regList in ${config.regList})
						{
						pattern: ${regList.pattern},
						message: '${label}${regList.message}',
							#if($xhkey=='checkbox' || $xhkey=='timeRange' || $xhkey=='dateRange' || $xhkey=='areaSelect' || $xhkey=='cascader')
							type:'array',
							#elseif($xhkey=='organizeSelect' || $xhkey=='uploadFile' || $xhkey=="uploadImg")
							type:'array',
							#elseif($xhkey=='userSelect' || $xhkey=='usersSelect' || $xhkey=='select' || $xhkey=='depSelect' || $xhkey=='posSelect' || $xhkey=='popupTableSelect'|| $xhkey=='groupSelect'|| $xhkey=='roleSelect' || $xhkey=='treeSelect')
								#if(${html.multiple}=='true')
								type:'array',
								#end
							#end
						},
				#end
			#end
					],
		#end
	#end
#end
#macro(list $fieLdsModel,$childList)
	#set($html = $fieLdsModel)
	#set($vModel = "${childList}${html.vModel}")
	#set($config = $html.config)
	#set($xhkey = "$config.xhKey")
	#if(${config.dataType}=='dictionary'||${config.dataType}=='dynamic')
				${vModel}Options:[],
	#elseif(${config.dataType} == "static")
		#if($html.slot.options)
                ${vModel}Options:${html.slot.options},
		#elseif($html.options)
                ${vModel}Options:${html.options},
		#end
	#end
	#if($html.props)
				#set($propsModel = ${html.props})
				${vModel}Props:{"label":"${propsModel.label}","value":"${propsModel.value}","multiple":${propsModel.multiple},"children":"${propsModel.children}"},
	#end
	#if($config.props)
				${vModel}Props:{"label":"${config.props.label}","value":"${config.props.value}"},
	#end
	#if($xhkey=='relationForm' || $xhkey=='popupSelect' || $xhkey=='popupTableSelect')
				${vModel}ColumnOptions:[
		#foreach($columnOptions in  ${html.columnOptions})
					{
						"label":"${columnOptions.label}",
						"value":"${columnOptions.value}"
					},
		#end
				],
	#end
#end
#macro(options $fieLdsModel,$childList)
	#set($html = $fieLdsModel)
	#set($vModel = "${childList}${html.vModel}")
	#set($config = $html.config)
	#set($dataType = "${config.dataType}")
	#if(${dataType}=='dictionary' || ${dataType}=='dynamic')
			    this.get${vModel}Options()
	#end
#end
#macro(optionsData $fieLdsModel,$childList)
	#set($html = $fieLdsModel)
	#set($vModel = "${childList}${html.vModel}")
	#set($config = $html.config)
	#set($dataType = "${config.dataType}")
	#set($xhkey="${config.xhKey}")
	#set($changeDataFormType = "1")
	#set($changeDataFormData = "${html.vModel}")
	#if($childList)
		#set($changeDataFormData = "${childList}"+"List")
		#set($changeDataFormType = "2")
	#end
	#set($defaultValue='""')
	#if($!config.valueType=='String')
		#set($defaultValue="'$!{config.defaultValue}'")
	#elseif($!config.valueType=='undefined')
		#set($defaultValue='""')
	#else
		#set($defaultValue=$!{config.defaultValue})
	#end
	#if(${dataType}=='dictionary')
            get${vModel}Options() {
                getDictionaryDataSelector('${config.dictionaryType}').then(res => {
                    this.${vModel}Options = res.data.list
                })
            },
	#elseif(${dataType}=='dynamic')
            get${vModel}Options() {
				const edit = this.isEdit
				const index = this.childIndex
				let templateJsonList = JSON.parse(JSON.stringify(this.interfaceRes.${vModel}))
				for (let i = 0; i < templateJsonList.length; i++) {
					let json = templateJsonList[i];
					if(json.relationField){
						let relationFieldAll = json.relationField.split("-");
						let val = json.defaultValue;
						if(relationFieldAll.length>1 && index>-1){
							val = this.${context.formModel}[relationFieldAll[0]+'List']&&this.${context.formModel}[relationFieldAll[0]+'List'].length?this.${context.formModel}[relationFieldAll[0]+'List'][index][relationFieldAll[1]]:''
						}else {
							val = this.${context.formModel}[relationFieldAll]
						}
						json.defaultValue = val
					}
				}
				let template ={
					paramList:templateJsonList
				}
				getDataInterfaceRes('${config.propsUrl}',template).then(res => {
					let data = res.data
                    this.${vModel}Options = data
					#if($childList)
					if(index==-1) return
					this.${context.formModel}.${childList}List[index].${html.vModel}Options =data
					#end
					this.changeDataFormData(${changeDataFormType},'${changeDataFormData}','${html.vModel}',index,${defaultValue},edit)
                })
            },
	#end
#end
#macro(faceRes $fieLdsModel,$childList)
	#set($html = $fieLdsModel)
	#set($vModel = "${childList}${html.vModel}")
	#set($config = $html.config)
	#set($dataType = "${config.dataType}")
	#if(${vModel})
			${vModel}:[
				#foreach($templateJson in ${config.templateJson})
				{
					fieldName:"${templateJson.fieldName}",
					field:"${templateJson.field}",
					defaultValue:"${templateJson.defaultValue}",
					xhKey:"$!{templateJson.xhKey}",
					dataType:"${templateJson.dataType}",
					id:"${templateJson.id}",
					required:"${templateJson.required}",
					relationField:"${templateJson.relationField}",
				},
				#end
			],
	#end
#end
#macro(dataList $key $fieLdsModel $formModel $tableModel $fieldModel $vModel)
	#set($model="${formModel}.${vModel}")
	#set($field="${formModel}.${fieldModel}")
	#if($key=='mastTable')
		#set($field="${formModel}.${tableModel}.${fieldModel}")
	#end
	#set($html = $fieLdsModel)
	#set($config = $html.config)
	#set($xhkey = "${config.xhKey}")
	#if($xhkey=='checkbox' || $xhkey=='timeRange' || $xhkey=='dateRange' || $xhkey=='areaSelect' || $xhkey=='cascader')
				${field} = Array.isArray(${model})? JSON.stringify(${model}):'[]'
	#elseif($xhkey=='organizeSelect' || $xhkey=='uploadFile' || $xhkey=="uploadImg")
				${field} = Array.isArray(${model})? JSON.stringify(${model}):'[]'
	#elseif($xhkey=='userSelect' || $xhkey=='usersSelect' || $xhkey=='select' || $xhkey=='depSelect' || $xhkey=='posSelect' || $xhkey=='popupTableSelect'|| $xhkey=='groupSelect'|| $xhkey=='roleSelect' || $xhkey=='treeSelect')
		#if(${html.multiple}=='true')
				${field} = Array.isArray(${model})? JSON.stringify(${model}):'[]'
		#else
				${field} = ${model}
		#end
	#else
		#if($key=='mastTable')
				${field} = ${model}
		#end
	#end
#end
#macro(dataInfo $key $fieLdsModel $formModel $tableModel $fieldModel $vModel)
	#set($model="${formModel}.${vModel}")
	#set($field="${formModel}.${fieldModel}")
	#if($key=='mastTable')
		#set($field="${formModel}.${tableModel}.${fieldModel}")
	#end
	#set($html = $fieLdsModel)
	#set($config = $html.config)
	#set($xhkey = "${config.xhKey}")
	#if($xhkey=='checkbox' || $xhkey=='timeRange' || $xhkey=='dateRange' || $xhkey=='areaSelect' || $xhkey=='cascader')
				${model} = ${field}? JSON.parse(${field}):[]
	#elseif($xhkey=='organizeSelect' || $xhkey=='uploadFile' || $xhkey=="uploadImg")
				${model} = ${field}? JSON.parse(${field}):[]
	#elseif($xhkey=='userSelect' || $xhkey=='usersSelect' || $xhkey=='select' || $xhkey=='depSelect' || $xhkey=='posSelect' || $xhkey=='popupTableSelect'|| $xhkey=='groupSelect'|| $xhkey=='roleSelect' || $xhkey=='treeSelect')
		#if(${html.multiple}=='true')
				${model} = ${field}? JSON.parse(${field}):[]
		#else
				${model} = ${field}
		#end
	#elseif($xhkey=="switch" || $xhkey=="slider")
				${model} = ${field}? Number(${field}):0
	#elseif($xhkey=='datePicker')
				${model} = ${field}? Number(${field}):${field}
	#else
		#if($key=='mastTable')
				${model} = ${field}
		#end
	#end
#end
#macro(initDefaultData $key $fieLdsModel $formModel)
	#set($html = $fieLdsModel)
	#set($eachFiled = "${html.vModel}")
	#set($config = $html.config)
	#set($xhkey = "${config.xhKey}")
	#set($defaultCurrent=${config.defaultCurrent})
	#set($multiple=${html.multiple})
	#set($selectType = ${html.selectType})
	#set($ableDepIds = ${html.ableDepIds})
	#set($ableUserIds = ${html.ableUserIds})
	#set($ableGroupIds = ${html.ableGroupIds})
	#set($ableRoleIds = ${html.ableRoleIds})
	#set($ablePosIds = ${html.ablePosIds})
	#set($format=${html.format})
	#set($timeFormat = "this."+'$'+"u.timeFormat")
	#if($defaultCurrent)
		#if($xhkey=='datePicker')
			${formModel}.${eachFiled} = new Date().getTime()
		#elseif($xhkey=='timePicker')
			${formModel}.${eachFiled} = ${timeFormat}(new Date().getTime(),this.formatType['${format}'])
		#elseif($xhkey=='depSelect')
			if(this.userInfo.departmentId != null && this.userInfo.departmentId != '') {
				#if($selectType=='all')
					#if($multiple == true)
				${formModel}.${eachFiled} = [this.userInfo.departmentId]
					#else
				${formModel}.${eachFiled} = this.userInfo.departmentId
					#end
				#else
				let ableDepIds = ${ableDepIds}
				if (ableDepIds instanceof Array && ableDepIds.length > 0 && ableDepIds.includes(this.userInfo.departmentId)) {
					#if($multiple == true)
					${formModel}.${eachFiled} = [this.userInfo.departmentId]
					#else
					${formModel}.${eachFiled} = this.userInfo.departmentId
					#end
				} else if(ableDepIds instanceof Array && ableDepIds.length > 0) {
					getDefaultCurrentValueDepartmentId({departIds:ableDepIds}).then(res => {
						if (res.data.departmentId != null && res.data.departmentId != '') {
					#if($multiple == true)
							${formModel}.${eachFiled} = [this.userInfo.departmentId]
					#else
							${formModel}.${eachFiled} = this.userInfo.departmentId
					#end
						}
					})
				}
				#end
			}
		#elseif($xhkey=='organizeSelect')
			if(this.userInfo.organizeIdList instanceof Array && this.userInfo.organizeIdList.length > 0) {
				#if($multiple == true)
				${formModel}.${eachFiled} = [this.userInfo.organizeIdList]
				#else
				${formModel}.${eachFiled} = this.userInfo.organizeIdList
				#end
			}
		#elseif($xhkey=='userSelect')
			#if($selectType=='all')
				#if($multiple == true)
			${formModel}.${eachFiled} = [this.userInfo.userId]
				#else
			${formModel}.${eachFiled} = this.userInfo.userId
				#end
			#elseif($selectType=='custom')
			if(this.userInfo.userId != null && this.userInfo.userId != '') {
				let ableUserIds = ${ableUserIds}
				let ableDepIds = ${ableDepIds}
				let ableGroupIds = ${ableGroupIds}
				let ableRoleIds = ${ableRoleIds}
				let ablePosIds = ${ablePosIds}
				if (ableUserIds instanceof Array && ableUserIds.length > 0 && ableUserIds.includes(this.userInfo.userId)) {
					#if($multiple == true)
					${formModel}.${eachFiled} = [this.userInfo.userId]
					#else
					${formModel}.${eachFiled} = this.userInfo.userId
					#end
				}else if((ableUserIds instanceof Array && ableUserIds.length > 0)
					|| (ableDepIds instanceof Array && ableDepIds.length > 0)
					|| (ableGroupIds instanceof Array && ableGroupIds.length > 0)
					|| (ableRoleIds instanceof Array && ableRoleIds.length > 0)
					|| (ablePosIds instanceof Array && ablePosIds.length > 0)) {
					getDefaultCurrentValueUserId({
						departIds:ableDepIds,
						groupIds:ableGroupIds,
						roleIds:ableRoleIds,
						userIds:ableUserIds,
						positionIds:ablePosIds
					}).then(res => {
						if (res.data.userId != null && res.data.userId != '') {
							#if($multiple == true)
							${formModel}.${eachFiled} = [this.userInfo.userId]
							#else
							${formModel}.${eachFiled} = this.userInfo.userId
							#end
						}
					})
				}
			}
			#end
		#end
	#end
#end

<script>
    import {getBeforeData, getBeforeTime, getDateDay, getLaterData, getLaterTime} from '@/components/index.js'
    import comMixin from '../mixin'

    export default {
        mixins: [comMixin],
        data(){
            return{
				loading: false,
				tableKey:'',
				primaryKeyPolicy:${context.primaryKeyPolicy},
				${context.formModel}:{
					${pKeyName}:"",
					flowId: '',
					flowtaskid:undefined,
					#foreach($fieLdsModel in ${context.fields})
						#set($html = $fieLdsModel.formColumnModel.fieLdsModel)
						#set($vModel = "${html.vModel}")
						#set($config = $html.config)
						#set($xhkey = "${config.xhKey}")
						#if($vModel)
							#if($!config.valueType=='String')
					$!{vModel} : "$!{config.defaultValue}",
							#elseif($!config.valueType=='undefined')
					$!{vModel} : '',
							#else
					$!{vModel} : $!{config.defaultValue},
							#end
						#end
					#end
					#foreach($masetkey in $mastTableList.entrySet())
						#set($tableModel = $masetkey.key)
					$tableModel:{
						#set($fieldsAll = $masetkey.value)
						#foreach($fieLdsModel in ${fieldsAll})
							#set($mastTableModel = $fieLdsModel.formMastTableModel)
							#set($html = $fieLdsModel.formMastTableModel.mastTable.fieLdsModel)
							#set($vModel = "${mastTableModel.field}")
							#set($config = $html.config)
							#set($xhkey = "${config.xhKey}")
							#if($vModel)
								#if($!config.valueType=='String')
						$!{vModel} : "$!{config.defaultValue}",
								#elseif($!config.valueType=='undefined')
						$!{vModel} : '',
								#else
						$!{vModel} : $!{config.defaultValue},
								#end
							#end
						#end
					},
					#set($fieldsAll = $masetkey.value)
						#foreach($fieLdsModel in ${fieldsAll})
							#set($mastTableModel = $fieLdsModel.formMastTableModel)
							#set($html = $fieLdsModel.formMastTableModel.mastTable.fieLdsModel)
							#set($vModel = "${mastTableModel.vModel}")
							#set($config = $html.config)
							#set($xhkey = "${config.xhKey}")
							#if($vModel)
								#if($!config.valueType=='String')
					$!{vModel} : "$!{config.defaultValue}",
								#elseif($!config.valueType=='undefined')
					$!{vModel} : '',
								#else
					$!{vModel} : $!{config.defaultValue},
								#end
							#end
						#end
					#end
					#foreach($child in ${context.children})
						#set($className = "${child.className.toLowerCase()}")
					${className}List:[],
					#end
				},
				${context.formRules}:{
					#foreach($fields in ${context.fields})
						#set($fieLdsModel = $fields.formColumnModel.fieLdsModel)
						#appRule($fieLdsModel)
					#end
                    #foreach($masetkey in $mastTableList.entrySet())
						#set($fieldsAll = $masetkey.value)
						#foreach($fields in ${fieldsAll})
							#set($fieLdsModel = $fields.formMastTableModel.mastTable.fieLdsModel)
							#appRule($fieLdsModel)
						#end
					#end
				},
				#foreach($fields in ${context.fields})
					#set($fieLdsModel = $fields.formColumnModel.fieLdsModel)
					#list($fieLdsModel,'')
				#end
				#foreach($masetkey in $mastTableList.entrySet())
					#set($fieldsAll = $masetkey.value)
					#foreach($fields in ${fieldsAll})
						#set($fieLdsModel = $fields.formMastTableModel.mastTable.fieLdsModel)
						#list($fieLdsModel,'')
					#end
				#end
				#foreach($child in ${context.children})
					#set($className = "${child.className.toLowerCase()}")
					#foreach($childList in ${child.childList})
						#set($fieLdsModel = $childList.fieLdsModel)
						#list($fieLdsModel,$className)
					#end
				#end
                #foreach($fieLdsModel in ${context.form})
                    #set($xhkey = "${fieLdsModel.xhKey}")
                    #set($isEnd = "${fieLdsModel.isEnd}")
                    #set($formModel = ${fieLdsModel.formModel})
                    #set($model = "${formModel.model}")
                    #set($outermost = ${formModel.outermost})
                    #set($children = ${formModel.children})
                    #if($xhkey=='tab' || $xhkey=='collapse')
                        #if(${isEnd}=='0')
                            #if(${outermost}=='0')
                ${model}Current:${formModel.active},
                ${model}Data:[
                                #foreach($childrenList in $children)
                    {
                        title: "${childrenList.title}"
                    },
                                #end
                ],
                            #end
                        #end
                    #end
                #end
				interfaceRes:{
				#foreach($html in ${context.fields})
					#set($fieLdsModel = $html.formColumnModel.fieLdsModel)
					#faceRes($fieLdsModel,'')
				#end
				#foreach($masetkey in $mastTableList.entrySet())
					#set($fieldsAll = $masetkey.value)
					#foreach($html in ${fieldsAll})
						#set($fieLdsModel = $html.formMastTableModel.mastTable.fieLdsModel)
						#faceRes($fieLdsModel,'')
					#end
				#end
				#foreach($child in ${context.children})
					#set($className = "${child.className.toLowerCase()}")
					#foreach($childList in ${child.childList})
						#set($fieLdsModel = $childList.fieLdsModel)
						#faceRes($fieLdsModel,$className)
					#end
				#end
				},
				regList:{
					#foreach($child in ${context.children})
						#set($className = "${child.className.toLowerCase()}")
					${className}List:{
							#foreach($html in ${child.childList})
								#set($fieLdsModel = ${html.fieLdsModel})
								#set($vModel = "${fieLdsModel.vModel}")
								#set($config = ${fieLdsModel.config})
								#set($listSize=$!{config.regList})
								#set($xhkey = "${config.xhKey}")
								#set($label = "${config.label}")
								#if($vModel)
						${vModel}: [
									#if($listSize.size()>0)
										#foreach($regList in ${config.regList})
							{
								pattern: ${regList.pattern},
								message: '${label}${regList.message}',
							},
										#end
									#end
						],
								#end
							#end
					},
					#end
				},
				ableAll:{
					#foreach($html in ${context.fields})
						#set($fieLdsModel = $html.formColumnModel.fieLdsModel)
						#appableAll($fieLdsModel,'')
					#end
					#foreach($masetkey in $mastTableList.entrySet())
						#set($fieldsAll = $masetkey.value)
						#foreach($html in ${fieldsAll})
							#set($fieLdsModel = $html.formMastTableModel.mastTable.fieLdsModel)
							#appableAll($fieLdsModel,'')
						#end
					#end
					#foreach($child in ${context.children})
						#set($className = "${child.className.toLowerCase()}")
						#foreach($childList in ${child.childList})
							#set($fieLdsModel = $childList.fieLdsModel)
							#appableAll($fieLdsModel,$className)
						#end
					#end
				},
				childIndex:-1,
                labelwidth: ${context.labelWidth}*1.5,
				dataValue:{},
				isEdit:false,
				userInfo:{},
				formatType:{"yyyy":"yyyy","yyyy-MM":"yyyy-mm","yyyy-MM-dd":"yyyy-mm-dd","yyyy-MM-dd HH:mm":"yyyy-mm-dd hh:MM","yyyy-MM-dd HH:mm:ss":"yyyy-mm-dd hh:MM:ss","HH:mm:ss":"hh:MM:ss","HH:mm":"hh:MM"},
				content:'',
				title:'',
				show:false,
            }
        },
        created(){
        	uni.$on('linkPageConfirm', (subVal) => {
				if(this.tableKey){
					for(let i=0;i<subVal.length;i++){
						let t = subVal[i]
						if(this['get'+this.tableKey]){
							this['get'+this.tableKey](t)
						}
					}
					this.childIndex = -1
					this.collapse()
				}
			})
			this.userInfo = uni.getStorageSync('userInfo') || {}
			this.$store.commit('base/UPDATE_RELATION_DATA', {})
			this.dataValue = JSON.parse(JSON.stringify(this.${context.formModel}))
			this.initDefaultData()
        },
        onReady() {
            #set($rulesAll = "this."+${context.formRules})
            this.$refs.${context.formModel}.setRules(${rulesAll});
        },
        watch:{
        	dataForm: {
				handler(val, oldVal) {
				#foreach($child in ${context.children})
					#set($className = "${child.className.toLowerCase()}")
					this.${className}()
				#end
				},
				deep: true
			}
        },
        methods:{
			changeData(model, index) {
				this.isEdit = false
				this.childIndex = index
				let modelAll = model.split("-");
				let faceMode = "";
				for (let i = 0; i < modelAll.length; i++) {
					faceMode += modelAll[i];
				}
				for (let key in this.interfaceRes) {
					if (key != faceMode) {
						let faceReList = this.interfaceRes[key]
						for (let i = 0; i < faceReList.length; i++) {
							if (faceReList[i].relationField == model) {
								let options = 'get' + key + 'Options';
								if(this[options]){
									this[options]()
								}
								this.changeData(key, index)
							}
						}
					}
				}
			},
			changeDataFormData(type, data, model,index,defaultValue, edit) {
				if(!edit) {
					if (type == 2) {
						for (let i = 0; i < this.${context.formModel}[data].length; i++) {
							if (index == -1) {
								this.${context.formModel}[data][i][model] = defaultValue
							} else if (index == i) {
								this.${context.formModel}[data][i][model] = defaultValue
							}
						}
					} else {
						this.${context.formModel}[data] = defaultValue
					}
				}
			},
			clickIcon(label,tipLabel) {
				this.content = tipLabel
				this.title = label
				this.show = true
			},
			exist() {
				let title = []
				let _regList = this.regList
				for (let k in _regList) {
					let childData = this.${context.formModel}[k]
					for(let n in _regList[k]){
						for(let i=0;i<_regList[k][n].length;i++){
							const element = _regList[k][n][i]
							childData.forEach((item, index) => {
								if(item[n] && !eval(element.pattern).test(item[n])){
									title.push(element.message)
								}
							})
						}
					}
				}
				if (title.length > 0) {
					return title[0]
				}
			},
			dataAll(){
			    #foreach($html in ${context.fields})
			        #set($fieLdsModel = $html.formColumnModel.fieLdsModel)
			        #options($fieLdsModel,'')
			    #end
				#foreach($masetkey in $mastTableList.entrySet())
					#set($fieldsAll = $masetkey.value)
					#foreach($html in ${fieldsAll})
						#set($fieLdsModel = $html.formMastTableModel.mastTable.fieLdsModel)
						#options($fieLdsModel,'')
					#end
				#end
			    #foreach($child in ${context.children})
					#set($className = "${child.className.toLowerCase()}")
			        #foreach($childList in ${child.childList})
			            #set($fieLdsModel = $childList.fieLdsModel)
						#options($fieLdsModel,$className)
			        #end
			    #end
				this.collapse()
			},
			dateTime(timeRule, timeType, timeTarget, timeValueData, dataValue) {
			  let timeDataValue = null;
			  let timeValue = Number(timeValueData)
			  if (timeRule) {
				if (timeType == 1) {
				  timeDataValue = timeValueData?timeValue:null
				} else if (timeType == 2) {
				  timeDataValue = dataValue
				} else if (timeType == 3) {
				  timeDataValue = new Date().getTime()
				} else if (timeType == 4) {
				  let previousDate = '';
				  if (timeTarget == 1 || timeTarget == 2) {
					previousDate = getDateDay(timeTarget, timeType, timeValue)
					timeDataValue = new Date(previousDate).getTime()
				  } else if (timeTarget == 3) {
					previousDate = getBeforeData(timeValue)
					timeDataValue = new Date(previousDate).getTime()
				  } else {
					timeDataValue = getBeforeTime(timeTarget, timeValue).getTime()
				  }
				} else if (timeType == 5) {
				  let previousDate = '';
				  if (timeTarget == 1 || timeTarget == 2) {
					previousDate = getDateDay(timeTarget, timeType, timeValue)
					timeDataValue = new Date(previousDate).getTime()
				  } else if (timeTarget == 3) {
					previousDate = getLaterData(timeValue)
					timeDataValue = new Date(previousDate).getTime()
				  } else {
					timeDataValue = getLaterTime(timeTarget, timeValue).getTime()
				  }
				}
			  }
			  return timeDataValue;
			},
			time(timeRule, timeType, timeTarget, timeValue, formatType, dataValue) {
			  let format = formatType == 'HH:mm' ? 'HH:mm:00' : formatType
			  let timeDataValue = null
			  if (timeRule) {
				if (timeType == 1 && timeValue) {
				  timeDataValue = timeValue || '00:00:00'
				  if (timeDataValue.split(':').length == 3) {
					timeDataValue = timeDataValue
				  } else {
					timeDataValue = timeDataValue + ':00'
				  }
				} else if (timeType == 2) {
				  timeDataValue = dataValue
				} else if (timeType == 3) {
				  timeDataValue = this.xh.toDate(new Date(), format)
				} else if (timeType == 4) {
				  let previousDate = '';
				  previousDate = getBeforeTime(timeTarget, timeValue)
				  timeDataValue = this.xh.toDate(previousDate, format)
				} else if (timeType == 5) {
				  let previousDate = '';
				  previousDate = getLaterTime(timeTarget, timeValue)
				  timeDataValue = this.xh.toDate(previousDate, format)
				}
			  }
			  return timeDataValue;
			},
            #foreach($fields in ${context.fields})
                #set($fieLdsModel = $fields.formColumnModel.fieLdsModel)
				#optionsData($fieLdsModel,'')
            #end
			#foreach($masetkey in $mastTableList.entrySet())
				#set($fieldsAll = $masetkey.value)
				#foreach($fields in ${fieldsAll})
					#set($fieLdsModel = $fields.formMastTableModel.mastTable.fieLdsModel)
					#optionsData($fieLdsModel,'')
				#end
			#end
            #foreach($child in ${context.children})
				#set($className = "${child.className.toLowerCase()}")
			${className}(){
				let table = this.${context.formModel}.${className}List
				let summaryField =${child.summaryField}
				let summaryFieldName =${child.summaryFieldName}
				let data ={}
				let thousandsField = ${child.thousandsField}
				for (let i in summaryField) {
					let map = {}
					let val = 0
					for (let j = 0; j < table.length; j++) {
						let summary = table[j][summaryField[i]];
						if (summary) {
							let data = isNaN(summary) ? 0 :	Number(summary)
							val += data
						}
					}
					map.id = summaryField[i];
					map.name = summaryFieldName[summaryField[i]];
					map.val = (thousandsField.includes(summaryField[i]))? Number(val).toLocaleString('zh', {maximumFractionDigits: '2'}):  val;
					data[summaryField[i]]=map;
				}
				return data;
			},
                #foreach($childList in ${child.childList})
					#set($className = "${child.className.toLowerCase()}")
                    #set($fieLdsModel = $childList.fieLdsModel)
					#optionsData($fieLdsModel,$className)
                #end
            #end
            #foreach($fieLdsModel in ${context.form})
                #set($xhkey = "${fieLdsModel.xhKey}")
                #set($isEnd = "${fieLdsModel.isEnd}")
                #set($formModel = ${fieLdsModel.formModel})
                #set($outermost = ${formModel.outermost})
                #if($xhkey=='tab' || $xhkey=='collapse')
                    #if(${isEnd}=='0')
                        #if(${outermost}=='0')
            ${formModel.model}(index) {
                this.${formModel.model}Current = index+1;
				this.collapse()
            },
                        #end
                    #end
                #end
            #end
			selfInit() {
				this.dataAll()
				#foreach($child in ${context.children})
					#set($className = "${child.className.toLowerCase()}")
					#if($child.addType==0)
				this.add${className}List()
					#end
				#end
				this.collapse()
			},
			initDefaultData() {
				#foreach($html in ${context.fields})
					#set($fieLdsModel = $html.formColumnModel.fieLdsModel)
					#set($formModel='this.'+${context.formModel})
					#initDefaultData('mast',$fieLdsModel,${formModel})
				#end
				#foreach($masetkey in $mastTableList.entrySet())
					#set($fieldsAll = $masetkey.value)
					#foreach($html in ${fieldsAll})
						#set($fieLdsModel = $html.formMastTableModel.mastTable.fieLdsModel)
						#set($formModel='this.'+${context.formModel})
						#initDefaultData('mastTable',$fieLdsModel,${formModel})
					#end
				#end
			},
			selfGetInfo(dataForm){
				this.dataInfo(dataForm)
			},
			beforeSubmit(){
				const _data =this.dataList()
				return _data
			},
            #foreach($child in ${context.children})
				#set($className = "${child.className.toLowerCase()}")
            add${className}List(){
				let value={}
            	this.tableKey = '${className}List';
            	#if($child.addType==1)
            	let _addTableConf =${child.addTableConf}
            	let data = {
					addTableConf: _addTableConf,
					formData: this.${context.formModel}
				}
				uni.navigateTo({
					url: '/pages/apply/tableLinkage/index?data=' + encodeURIComponent(JSON.stringify(data))
				})
				return
            	#end
				this.get${className}List(value)
				this.childIndex = -1
				this.collapse()
            },
            del${className}List(index) {
                var that = this
                uni.showModal({
                    title: '提示',
                    content: '确认删除该条信息吗？',
                    success: function(res) {
                    if (res.confirm) {
                        that.${context.formModel}.${className}List.splice(index, 1);
                        that.collapse()
                    }
                  }
                })
            },
			get${className}List(value){
				let item = {
				#foreach($childData in ${child.childList})
					#set($fieLdsModel = ${childData.fieLdsModel})
					#set($vModel = "${fieLdsModel.vModel}")
					#set($config = ${fieLdsModel.config})
					#set($dataType = "${config.dataType}")
					#if($vModel)
						#if($!config.valueType=='String')
					$!{vModel} : "$!{config.defaultValue}",
						#elseif($!config.valueType=='undefined')
					$!{vModel} : '',
						#else
					$!{vModel} : $!{config.defaultValue},
						#end
						#if(${dataType}=='dictionary' || ${dataType}=='dynamic')
					${vModel}Options:[],
						#end
					#end
				#end
				}
				let result = {...item,...value}
				this.${context.formModel}.${className}List.push(result)
				#foreach($childData in ${child.childList})
					#set($fieLdsModel = ${childData.fieLdsModel})
					#set($formModel='this.'+${context.formModel}+"."+${className}+"List[this."+${context.formModel}+"."+${className}+"List.length - 1]")
					#initDefaultData('table',$fieLdsModel,${formModel})
				#end
				this.childIndex=this.${context.formModel}.${className}List.length-1
				this.isEdit = true
				#foreach($childList in ${child.childList})
					#set($fieLdsModel = $childList.fieLdsModel)
					#set($vModel = "${fieLdsModel.vModel}")
					#set($field = "${fieLdsModel.vModel}")
					#set($config = ${fieLdsModel.config})
					#set($dataType = "${config.dataType}")
					#if(${dataType}=='dynamic')
				this.get${className}${vModel}Options()
					#end
				#end
				this.isEdit = false
			},
            #end
			dataList(){
				var _data = JSON.parse(JSON.stringify(this.${context.formModel}));
				return _data;
			},
			dataInfo(dataAll){
				let _dataAll =dataAll
				this.${context.formModel}=_dataAll
				#if($child.addType==0)
					#foreach($child in ${context.children})
						#set($className = "${child.className.toLowerCase()}")
				if(this.${context.formModel}.${className}List.length==0){
					this.add${className}List()
				}
					#end
				#end
				this.isEdit = true
				this.dataAll()
				#foreach($child in ${context.children})
					#set($className = "${child.className.toLowerCase()}")
				for(let i=0;i<_dataAll.${className}List.length;i++){
					this.childIndex = i
					#foreach($childList in ${child.childList})
						#set($fieLdsModel = $childList.fieLdsModel)
						#set($vModel = "${fieLdsModel.vModel}")
						#set($field = "${fieLdsModel.vModel}")
						#set($config = ${fieLdsModel.config})
						#set($dataType = "${config.dataType}")
						#if(${dataType}=='dynamic')
					this.get${className}${vModel}Options()
						#end
					#end
				}
				#end
				this.childIndex=-1
				this.collapse()
			},
			collapse(){
				setTimeout(()=> {
					#foreach($fieLdsModel in ${context.form})
						#set($xhkey = "${fieLdsModel.xhKey}")
						#set($isEnd = "${fieLdsModel.isEnd}")
						#set($formModel = ${fieLdsModel.formModel})
						#set($outermost = ${formModel.outermost})
						#set($config=$formModel.config)
						#if($xhkey=='collapse' || $xhkey=='tab')
							#if(${config.app}==true)
								#if(${isEnd}=='0')
									#if(${outermost}=='0')
					this.$refs.${formModel.model}Current && this.$refs.${formModel.model}Current.init()
									#end
								#end
							#end
						#end
					#end
				}, 1000);
			},
        },
    }

</script>
<style>
	page{
		background-color: #f0f2f6;
	}
</style>
