package ${package.ServiceImpl};

import ${package.Entity}.${table.entityName};
import ${package.Mapper}.${table.mapperName};
import ${package.Service}.${table.serviceName};
import ${superServiceImplClassPackage};
#if(${typeId}=='1')
#set($modelPath = ${modulePackageName}+".model."+${modelPathName})
#set($modelClassName ="${genInfo.className.substring(0,1).toLowerCase()}${genInfo.className.substring(1)}")
import ${modelPath}.${genInfo.className}Form;
#foreach($grid in ${child})
import ${package.Entity}.${grid.className}Entity;
import ${package.Service}.${grid.className}Service;
#end
#foreach($grid in ${tableNameAll})
import ${package.Service}.${grid.className}Service;
import ${package.Entity}.${grid.className}Entity;
#end
#end
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.xinghuo.common.util.JsonUtil;
import com.xinghuo.common.hutool.*;
import com.xinghuo.common.util.DateUtil;
import com.xinghuo.obsolete.util.StringUtil;
import com.xinghuo.common.util.UserProvider;
import com.xinghuo.common.base.UserInfo;
import com.xinghuo.common.exception.WorkFlowException;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.xinghuo.common.database.plugins.DynamicSourceGeneratorInterface;
import com.xinghuo.common.database.model.entity.DbLinkEntity;
import com.xinghuo.common.database.util.DataSourceUtil;
import com.xinghuo.workflow.util.ServiceAllUtil;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import java.util.*;

/**
 *
 * ${genInfo.description}
 * 版本： ${genInfo.version}
 * 版权： ${genInfo.copyright}
 * 作者： ${genInfo.createUser}
 * 日期： ${genInfo.createDate}
 */
@Service
@DS("${DSId}")
public class ${table.serviceImplName} extends ${superServiceImplClass}<${table.mapperName}, ${table.entityName}> implements ${table.serviceName},DynamicSourceGeneratorInterface {

    @Autowired
    private DataSourceUtil dataSourceUtil;
    @Autowired
    private ServiceAllUtil serviceAllUtil;

    #set($modelFormName = "${modelName.substring(0,1).toUpperCase()}${modelName.substring(1)}")
    #if(${typeId}=='1')
    #set($peimaryKeyName="${pKeyName.substring(0,1).toUpperCase()}${pKeyName.substring(1)}")
    @Autowired
    private UserProvider userProvider;
    #foreach($grid in ${child})
    #set($serviceName = "${grid.className.substring(0,1).toLowerCase()}${grid.className.substring(1)}")
    @Autowired
    private ${grid.className}Service ${serviceName}Service;
    #end
    #foreach($grid in $tableNameAll)
        #set($serviceName = "${grid.className.substring(0,1).toLowerCase()}${grid.className.substring(1)}")
    @Autowired
    private ${grid.className}Service ${serviceName}Service;
    #end
    private boolean primaryKeyPolicy = ${primaryKeyPolicy};

    #foreach($grid in ${child})
    @Override
    public List<${grid.className}Entity> Get${grid.className}List(String id){
        #set($serviceName = "${grid.className.substring(0,1).toLowerCase()}${grid.className.substring(1)}")
        #set($tableField = "${grid.tableField.substring(0,1).toUpperCase()}${grid.tableField.substring(1)}")
        QueryWrapper<${grid.className}Entity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(${grid.className}Entity::get${tableField}, id);
        return ${serviceName}Service.list(queryWrapper);
    }

    #end
    #foreach($grid in ${tableNameAll})
    @Override
    public ${grid.className}Entity get${grid.className}(String id){
        #set($serviceName = "${grid.className.substring(0,1).toLowerCase()}${grid.className.substring(1)}")
        #set($tableField = "${grid.tableField.substring(0,1).toUpperCase()}${grid.tableField.substring(1)}")
        QueryWrapper<${grid.className}Entity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(${grid.className}Entity::get${tableField}, id);
        return ${serviceName}Service.getOne(queryWrapper);
    }
    #end

    @Override
    public ${table.entityName} getInfo(String id){
        QueryWrapper<${table.entityName}> queryWrapper = new QueryWrapper<>();
        #if(${primaryKeyPolicy})
        queryWrapper.lambda().eq(${table.entityName}::get${peimaryKeyName}, id);
        #else
        queryWrapper.lambda().eq(${table.entityName}::getFlowtaskid, id);
        #end
        return this.getOne(queryWrapper);
    }

    @Override
    @DSTransactional
    public void create(${table.entityName} entity#foreach($grid in ${child}), List<${grid.className}Entity> ${grid.className}List#end#foreach($grid in ${tableNameAll}), ${grid.className}Entity ${grid.className}#end,String status,String flowId,Map<String, List<String>> candidateList,Map<String,Object> data)throws WorkFlowException{
        String processId = String.valueOf(data.get("flowtaskid"));
        if(primaryKeyPolicy){
            entity.set${peimaryKeyName}(processId);
        }
        this.save(entity);
        #foreach($grid in ${tableNameAll})
            #set($serviceName = "${grid.className.substring(0,1).toLowerCase()}${grid.className.substring(1)}")
            #set($tableField = "${grid.tableField.substring(0,1).toUpperCase()}${grid.tableField.substring(1)}")
            #set($relationField = "${grid.relationField.substring(0,1).toUpperCase()}${grid.relationField.substring(1)}")
            #set($chidKeyName = "${grid.chidKeyName.substring(0,1).toUpperCase()}${grid.chidKeyName.substring(1)}")
        if(primaryKeyPolicy){
            ${grid.className}.set${chidKeyName}(processId);
        }
        ${grid.className}.set${tableField}(processId);
        ${serviceName}Service.save(${grid.className});
        #end
        #foreach($grid in ${child})
            #set($serviceName = "${grid.className.substring(0,1).toLowerCase()}${grid.className.substring(1)}")
        for(${grid.className}Entity entitys : ${grid.className}List){
            #set($tableField = "${grid.tableField.substring(0,1).toUpperCase()}${grid.tableField.substring(1)}")
            #set($relationField = "${grid.relationField.substring(0,1).toUpperCase()}${grid.relationField.substring(1)}")
            #set($chidKeyName = "${grid.chidKeyName.substring(0,1).toUpperCase()}${grid.chidKeyName.substring(1)}")
            if(primaryKeyPolicy){
                entitys.set${chidKeyName}(RandomUtil.snowId());
            }
            entitys.set${tableField}(processId);
            ${serviceName}Service.save(entitys);
        }
        #end
    }

    @Override
    @DSTransactional
    public void update(String id, ${table.entityName} entity#foreach($grid in ${child}), List<${grid.className}Entity> ${grid.className}List#end#foreach($grid in ${tableNameAll}), ${grid.className}Entity ${grid.className}#end,String status,String flowId,Map<String, List<String>> candidateList,Map<String,Object> data)throws WorkFlowException{
        entity.set${peimaryKeyName}(id);
        String processId = primaryKeyPolicy ?  id :String.valueOf(data.get("flowtaskid"));
        boolean update = this.updateById(entity);
        if(update){
            #foreach($grid in ${tableNameAll})
                #set($serviceName = "${grid.className.substring(0,1).toLowerCase()}${grid.className.substring(1)}")
                #set($tableField = "${grid.tableField.substring(0,1).toUpperCase()}${grid.tableField.substring(1)}")
                #set($relationField = "${grid.relationField.substring(0,1).toUpperCase()}${grid.relationField.substring(1)}")
                #set($chidKeyName = "${grid.chidKeyName.substring(0,1).toUpperCase()}${grid.chidKeyName.substring(1)}")
            QueryWrapper<${grid.className}Entity> ${grid.className}queryWrapper = new QueryWrapper<>();
            ${grid.className}queryWrapper.lambda().eq(${grid.className}Entity::get${tableField}, processId);
            ${serviceName}Service.remove(${grid.className}queryWrapper);
            if(primaryKeyPolicy){
                ${grid.className}.set${chidKeyName}(processId);
            }
            ${grid.className}.set${tableField}(processId);
            ${serviceName}Service.save(${grid.className});
            #end
            #foreach($grid in ${child})
                #set($tableField = "${grid.tableField.substring(0,1).toUpperCase()}${grid.tableField.substring(1)}")
                #set($relationField = "${grid.relationField.substring(0,1).toUpperCase()}${grid.relationField.substring(1)}")
                #set($chidKeyName = "${grid.chidKeyName.substring(0,1).toUpperCase()}${grid.chidKeyName.substring(1)}")
                #set($serviceName = "${grid.className.substring(0,1).toLowerCase()}${grid.className.substring(1)}")
            QueryWrapper<${grid.className}Entity> ${grid.className}queryWrapper = new QueryWrapper<>();
            ${grid.className}queryWrapper.lambda().eq(${grid.className}Entity::get${tableField}, processId);
            ${serviceName}Service.remove(${grid.className}queryWrapper);
            for(${grid.className}Entity entitys : ${grid.className}List){
                if(primaryKeyPolicy){
                    entitys.set${chidKeyName}(RandomUtil.snowId());
                }
                entitys.set${tableField}(processId);
                ${serviceName}Service.save(entitys);
            }
            #end
		}else{
            throw new WorkFlowException("当前表单原数据已被调整,请重新进入该页面编辑并提交数据");
        }
    }

	@Override
	public void data(String id,String data) throws WorkFlowException{
        uniqueAll(JsonXhUtil.stringToMap(data),1);
		${modelFormName}Form form =JsonXhUtil.toBean(data,${modelFormName}Form.class );
		${table.entityName} entity = JsonXhUtil.jsonDeepCopy(form, ${modelFormName}Entity.class);
		#foreach($grid in ${child})
			#set($list = "${grid.className.substring(0,1).toUpperCase()}${grid.className.substring(1).toLowerCase()}")
		List<${grid.className}Entity> ${grid.className}List = JsonXhUtil.getJsonToList(form.get${list}List(),${grid.className}Entity.class);
		#end
        ${table.entityName} info = getInfo(id);
		entity.set${peimaryKeyName}(info.get${peimaryKeyName}());
        String taskId = StringUtil.isNotEmpty(form.getFlowtaskid())?form.getFlowtaskid():id;
        boolean update = this.updateById(entity);
        if(update){
            #foreach($grid in ${tableNameAll})
                #set($serviceName = "${grid.className.substring(0,1).toLowerCase()}${grid.className.substring(1)}")
                #set($tableField = "${grid.tableField.substring(0,1).toUpperCase()}${grid.tableField.substring(1)}")
                #set($relationField = "${grid.relationField.substring(0,1).toUpperCase()}${grid.relationField.substring(1)}")
                #set($chidKeyName = "${grid.chidKeyName.substring(0,1).toUpperCase()}${grid.chidKeyName.substring(1)}")
            #set($list = "${grid.className.substring(0,1).toUpperCase()}${grid.className.substring(1).toLowerCase()}")
            ${grid.className}Entity ${grid.className} = JsonXhUtil.jsonDeepCopy(form.get${list}(),${grid.className}Entity.class);
            ${grid.className}.set${chidKeyName}(taskId);
            ${grid.className}.set${tableField}(taskId);
            ${serviceName}Service.updateById(${grid.className});
            #end
            #foreach($grid in ${child})
                #set($tableField = "${grid.tableField.substring(0,1).toUpperCase()}${grid.tableField.substring(1)}")
                #set($relationField = "${grid.relationField.substring(0,1).toUpperCase()}${grid.relationField.substring(1)}")
                #set($chidKeyName = "${grid.chidKeyName.substring(0,1).toUpperCase()}${grid.chidKeyName.substring(1)}")
                #set($serviceName = "${grid.className.substring(0,1).toLowerCase()}${grid.className.substring(1)}")
            QueryWrapper<${grid.className}Entity> ${grid.className}queryWrapper = new QueryWrapper<>();
            ${grid.className}queryWrapper.lambda().eq(${grid.className}Entity::get${tableField}, taskId);
            ${serviceName}Service.remove(${grid.className}queryWrapper);
            for(${grid.className}Entity entitys : ${grid.className}List){
                entitys.set${chidKeyName}(RandomUtil.snowId());
                entitys.set${tableField}(taskId);
                ${serviceName}Service.save(entitys);
            }
            #end
        }else{
            throw new WorkFlowException("更新失败");
        }
	}

    @Override
    public ${modelFormName}Form uniqueAll(Map<String,Object> data,int num)throws WorkFlowException{
        ${modelFormName}Form form =JsonXhUtil.toBean(data,${modelFormName}Form.class );
        Object processId = data.get("flowtaskid");
        int total = 0;
        int result = 0;
        #foreach($mastField in ${system})
            #set($vModel = "${mastField.vModel}")
            #set($config = $mastField.config)
            #set($unique = $config.unique)
            #set($xhKey = $config.xhKey)
            #if($vModel)
                #if($xhKey == 'comInput' && $unique ==true)
                    #set($upName = "${vModel.substring(0,1).toUpperCase()}${vModel.substring(1)}")
        if(StringUtil.isNotEmpty(form.get${upName}())){
            form.set${upName}(form.get${upName}().trim());
            QueryWrapper<${table.entityName}> ${vModel}Wrapper=new QueryWrapper<>();
            ${vModel}Wrapper.lambda().eq(${table.entityName}::get${upName},form.get${upName}());
            total +=this.count(${vModel}Wrapper);
        }
        if(total>num){
            result++;
        }
        total=0;
                #end
            #end
        #end
        #foreach($grid in ${child})
            #set($list = "${grid.className.substring(0,1).toUpperCase()}${grid.className.substring(1).toLowerCase()}")
            #set($serviceName = "${grid.className.substring(0,1).toLowerCase()}${grid.className.substring(1)}")
        List<${grid.className}Entity> ${grid.className}List = JsonXhUtil.getJsonToList(form.get${list}List(),${grid.className}Entity.class);
        Map<String,List<String>> ${grid.className}Map = new HashMap<>();
        for(${grid.className}Entity entitys : ${grid.className}List){
            #foreach($childList in $grid.childList)
                #set($fieLdsModel = ${childList.fieLdsModel})
                #set($childModels = ${fieLdsModel.vModel})
                #if($childModels)
                    #set($config = $fieLdsModel.config)
                    #set($unique = $config.unique)
                    #set($xhKey = $config.xhKey)
                    #if($xhKey == 'comInput' && $unique ==true)
                        #set($upName = "${childModels.substring(0,1).toUpperCase()}${childModels.substring(1)}")
            if(StringUtil.isNotEmpty(entitys.get${upName}())){
                entitys.set${upName}(entitys.get${upName}().trim());
                QueryWrapper<${grid.className}Entity> ${grid.className}${childModels}Wrapper=new QueryWrapper<>();
                ${grid.className}${childModels}Wrapper.lambda().eq(${grid.className}Entity::get${upName},entitys.get${upName}());
                total +=${serviceName}Service.count(${grid.className}${childModels}Wrapper);
                List<String> value = ${grid.className}Map.get("${upName}")!=null?${grid.className}Map.get("${upName}"):new ArrayList<>();
                value.add(entitys.get${upName}());
                ${grid.className}Map.put("${upName}",value);
            }
            List<String> ${upName} = ${grid.className}Map.get("${upName}")!=null?${grid.className}Map.get("${upName}"):new ArrayList<>();
            if(total>num || ${upName}.size() != new HashSet(${upName}).size()){
                result++;
            }
            total=0;
                    #end
                #end
            #end
        }
        #end
        #foreach($grid in ${tableNameAll})
            #set($list = "${grid.className.substring(0,1).toUpperCase()}${grid.className.substring(1).toLowerCase()}")
        ${grid.className}Entity ${grid.className} = JsonXhUtil.jsonDeepCopy(form.get${list}(),${grid.className}Entity.class);
            #set($serviceName = "${grid.className.substring(0,1).toLowerCase()}${grid.className.substring(1)}")
            #foreach($childList in $grid.childList)
                #set($formMastTableModel = $childList.formMastTableModel)
                #set($formModel=${formMastTableModel.mastTable.fieLdsModel})
                #set($config = $formModel.config)
                #set($unique = $config.unique)
                #set($xhKey = $config.xhKey)
                #set($fieldAll = $formMastTableModel.field)
                #if($fieldAll)
                    #if($xhKey == 'comInput' && $unique ==true)
                        #set($upName = "${fieldAll.substring(0,1).toUpperCase()}${fieldAll.substring(1)}")
        if(StringUtil.isNotEmpty(${grid.className}.get${upName}())){
            ${grid.className}.set${upName}(${grid.className}.get${upName}().trim());
            QueryWrapper<${grid.className}Entity> ${grid.className}${fieldAll}Wrapper=new QueryWrapper<>();
            ${grid.className}${fieldAll}Wrapper.lambda().eq(${grid.className}Entity::get${upName},${grid.className}.get${upName}());
            total += ${serviceName}Service.count(${grid.className}${fieldAll}Wrapper);
        }
        if(total>num){
            result++;
        }
        total=0;
                    #end
                #end
            #end
        #end
        boolean unique = result>0;
        if(unique){
            throw new WorkFlowException("单行输入不能重复");
        }
        return form;
    }

    @Override
    public void delete(String id){
        ${table.entityName} entity =getInfo(id);
        this.removeById(entity);
        #foreach($grid in ${tableNameAll})
            #set($serviceName = "${grid.className.substring(0,1).toLowerCase()}${grid.className.substring(1)}")
            #set($tableField = "${grid.tableField.substring(0,1).toUpperCase()}${grid.tableField.substring(1)}")
            #set($relationField = "${grid.relationField.substring(0,1).toUpperCase()}${grid.relationField.substring(1)}")
            #set($chidKeyName = "${grid.chidKeyName.substring(0,1).toUpperCase()}${grid.chidKeyName.substring(1)}")
            #set($list = "${grid.className.substring(0,1).toUpperCase()}${grid.className.substring(1).toLowerCase()}")
        ${grid.className}Entity ${grid.className} = new ${grid.className}Entity();
        ${grid.className}.set${chidKeyName}(id);
        ${serviceName}Service.updateById(${grid.className});
        #end
        #foreach($grid in ${child})
            #set($tableField = "${grid.tableField.substring(0,1).toUpperCase()}${grid.tableField.substring(1)}")
            #set($relationField = "${grid.relationField.substring(0,1).toUpperCase()}${grid.relationField.substring(1)}")
            #set($chidKeyName = "${grid.chidKeyName.substring(0,1).toUpperCase()}${grid.chidKeyName.substring(1)}")
            #set($serviceName = "${grid.className.substring(0,1).toLowerCase()}${grid.className.substring(1)}")
        QueryWrapper<${grid.className}Entity> ${grid.className}queryWrapper = new QueryWrapper<>();
        ${grid.className}queryWrapper.lambda().eq(${grid.className}Entity::get${tableField}, id);
        ${serviceName}Service.remove(${grid.className}queryWrapper);
        #end
    }

    #end

    @Override
    public DataSourceUtil getDataSource() {
        DbLinkEntity info = serviceAllUtil.getDbLink(this.getClass().getAnnotation(DS.class).value());
        return info;
    }

}
