package com.xinghuo.card.bill.controller;

import com.xinghuo.card.bill.service.impl.CreditCardBillServiceImpl;
import com.xinghuo.common.base.ActionResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.util.Date;

/**
 * 信用卡账单管理控制器
 * 提供账单生成的管理功能
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2024-01-01
 */
@Slf4j
@Tag(name = "信用卡账单管理", description = "CreditCardBillManage")
@RestController
@RequestMapping("/api/card/bill/manage")
public class CreditCardBillManageController {

    @Autowired
    private CreditCardBillServiceImpl creditCardBillService;

    /**
     * 手动触发账单生成
     */
    @Operation(summary = "手动触发账单生成")
    @PostMapping("/generate-bills")
    public ActionResult manualGenerateBills() {
        try {
            creditCardBillService.manualGenerateBills();
            return ActionResult.success("账单生成任务已触发");
        } catch (Exception e) {
            log.error("手动触发账单生成失败", e);
            return ActionResult.fail("触发失败：" + e.getMessage());
        }
    }

    /**
     * 为指定日期生成账单
     */
    @Operation(summary = "为指定日期生成账单")
    @PostMapping("/generate-bills-for-date")
    public ActionResult generateBillsForDate(
            @RequestParam("targetDate") @DateTimeFormat(pattern = "yyyy-MM-dd") Date targetDate) {
        
        if (targetDate == null) {
            return ActionResult.fail("目标日期不能为空");
        }

        try {
            creditCardBillService.generateBillsForDate(targetDate);
            return ActionResult.success("指定日期账单生成任务已完成");
        } catch (Exception e) {
            log.error("为指定日期生成账单失败", e);
            return ActionResult.fail("生成失败：" + e.getMessage());
        }
    }

    /**
     * 检查定时任务状态
     */
    @Operation(summary = "检查定时任务状态")
    @GetMapping("/task-status")
    public ActionResult getTaskStatus() {
        // 这里可以返回定时任务的执行状态、下次执行时间等信息
        return ActionResult.success("定时任务正常运行，每天凌晨2点执行");
    }

    /**
     * 获取账单生成统计信息
     */
    @Operation(summary = "获取账单生成统计")
    @GetMapping("/statistics")
    public ActionResult getBillGenerationStatistics() {
        // 这里可以返回账单生成的统计信息
        // 比如今天生成了多少账单、失败了多少等
        return ActionResult.success("统计信息功能待实现");
    }
}
