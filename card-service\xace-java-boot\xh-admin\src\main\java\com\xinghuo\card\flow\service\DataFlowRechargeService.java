package com.xinghuo.card.flow.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xinghuo.card.flow.entity.DataFlowRechargeEntity;
import com.xinghuo.card.flow.model.dataflowrecharge.DataFlowRechargePagination;

import java.util.List;

/**
 * 充值记录服务接口
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2022-11-27
 */
public interface DataFlowRechargeService extends IService<DataFlowRechargeEntity> {

    /**
     * 查询充值记录分页列表
     *
     * @param dataFlowRechargePagination 分页查询参数
     * @return 充值记录列表
     */
    List<DataFlowRechargeEntity> getList(DataFlowRechargePagination dataFlowRechargePagination);

    /**
     * 查询充值记录列表（支持分页和不分页）
     *
     * @param dataFlowRechargePagination 查询参数
     * @param dataType                   数据类型：0-分页，1-不分页
     * @return 充值记录列表
     */
    List<DataFlowRechargeEntity> getTypeList(DataFlowRechargePagination dataFlowRechargePagination, int dataType);

    /**
     * 根据ID获取充值记录详细信息
     *
     * @param id 充值记录ID
     * @return 充值记录实体
     */
    DataFlowRechargeEntity getInfo(String id);

    /**
     * 删除充值记录
     *
     * @param entity 要删除的充值记录实体
     */
    void delete(DataFlowRechargeEntity entity);

    /**
     * 创建充值记录
     *
     * @param entity 充值记录实体
     */
    void create(DataFlowRechargeEntity entity);

    /**
     * 更新充值记录
     *
     * @param id     充值记录ID
     * @param entity 更新的充值记录实体
     * @return 更新是否成功
     */
    boolean update(String id, DataFlowRechargeEntity entity);


}
