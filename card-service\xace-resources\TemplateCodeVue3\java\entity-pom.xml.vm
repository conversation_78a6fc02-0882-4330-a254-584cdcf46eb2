<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>xh-message</artifactId>
        <groupId>com.xinghuo</groupId>
        <version>2.0.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>xh-message-entity</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.xinghuo</groupId>
            <artifactId>xh-system-entity</artifactId>
            <version>${project.version}</version>
        </dependency>
        <!-- 钉钉API的Jar -->
<!--        <dependency>-->
<!--            <groupId>dingtalk-sdk-java</groupId>-->
<!--            <artifactId>taobao-sdk-java</artifactId>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>dingtalk-sdk-java</groupId>-->
<!--            <artifactId>taobao-sdk-java-source</artifactId>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>com.tencentcloudapi</groupId>-->
<!--            <artifactId>tencentcloud-sdk-java</artifactId>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>com.aliyun</groupId>-->
<!--            <artifactId>dysmsapi20170525</artifactId>-->
<!--        </dependency>-->
        <dependency>
            <groupId>com.sun.mail</groupId>
            <artifactId>javax.mail</artifactId>
        </dependency>
        <dependency>
            <groupId>org.dom4j</groupId>
            <artifactId>dom4j</artifactId>
        </dependency>
    </dependencies>

</project>
