package com.xinghuo.card.flow.model.dataflowacc;

import com.xinghuo.common.base.model.Pagination;
import lombok.Data;

import java.util.List;

/**
 * 账号明细
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2022-11-27
 */
@Data
public class DataFlowAccPaginationExportModel extends Pagination {

    /**
     * 选择的key
     */
    private String selectKey;

    /**
     * json
     */
    private String json;

    /**
     * 数据类型
     */
    private String dataType;


    /**
     * 收支类型
     */
    private String type;

    /**
     * 日期
     */
    private List<String> flowDate;
}
