package com.xinghuo.admin;

import com.alicp.jetcache.anno.config.EnableMethodCache;
import org.dromara.x.file.storage.spring.EnableFileStorage;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.context.event.EventListener;
import org.springframework.core.task.TaskExecutor;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.Instant;

/**
 * XhAdmin应用程序入口类
 *
 * <AUTHOR>
 */
@SpringBootApplication(scanBasePackages = {"com.xinghuo"}, exclude = {DataSourceAutoConfiguration.class})
@EnableFileStorage
@EnableScheduling
@EnableAsync
@EnableMethodCache(basePackages = "com.xinghuo")
public class XhAdminApplication extends SpringBootServletInitializer {
    private static final Instant START_TIME = Instant.now();

    /**
     * 主应用程序入口方法
     *
     * @param args 命令行参数
     */
    @SuppressWarnings("AlibabaRemoveCommentedCode")
    public static void main(String[] args) {
        SpringApplication springApplication = new SpringApplication(XhAdminApplication.class);
        // 注册自定义监听器
        // springApplication.addListeners(new XhListener());
        springApplication.run(args);
    }

    @Component
    public class StartupTimePrinter {
        @EventListener(ApplicationReadyEvent.class)
        public void printStartupTime() {
            Duration duration = Duration.between(START_TIME, Instant.now());
            System.out.println("xhAdmin启动完成, 耗时： " + duration.toSeconds() + " s");
        }
    }

//    @Primary
//    @Bean
//    public TaskExecutor primaryTaskExecutor() {
//        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
//        return executor;
//    }
}
