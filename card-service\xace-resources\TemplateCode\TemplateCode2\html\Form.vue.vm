#macro(GetStartAndEndTime $mastKey,$config,$html,$startTime,$endTime)
    #set($startRelationField="''")
    #if($config.startRelationField)
        #set($startRelationField="${context.formModel}.${config.startRelationField}")
        #if($config.startChild)
            #set($startRelationField="scope.row.${config.startRelationField}")
        #end
    #end
    #set($startTimeValue="#if(${config.startTimeValue})'${config.startTimeValue}'#else''#end")
    #set($startTimeType="#if(${config.startTimeType})${config.startTimeType}#else''#end")
    #set($startTimeTarget="#if(${config.startTimeTarget})${config.startTimeTarget}#else''#end")
    #set($endRelationField="''")
    #if($config.endRelationField)
        #set($endRelationField="${context.formModel}.${config.endRelationField}")
        #if($config.endChild)
            #set($endRelationField="scope.row.${config.endRelationField}")
        #end
    #end
    #set($endTimeValue="#if(${config.endTimeValue})'${config.endTimeValue}'#else''#end")
    #set($endTimeType="#if(${config.endTimeType})${config.endTimeType}#else''#end")
    #set($endTimeTarget="#if(${config.endTimeTarget})${config.endTimeTarget}#else''#end")

    #set($startTime="dateTime(${config.startTimeRule},${startTimeType},${startTimeTarget},${startTimeValue},${startRelationField})")
    #set($endTime="dateTime(${config.endTimeRule},${endTimeType},${endTimeTarget},${endTimeValue},${endRelationField})")
    #if($mastKey=='time')
        #set($startTime="time(${config.startTimeRule},${startTimeType},${startTimeTarget},${startTimeValue},'${html.format}',${startRelationField})")
        #set($endTime="time(${config.endTimeRule},${endTimeType},${endTimeTarget},${endTimeValue},'${html.format}',${endRelationField})")
    #end
#end
#set($pKeyName = "${context.pKeyName}")
#set($mastTableList = $context.mastTable)
#set($isSelectDialog = false)
<template>
#if(${context.popupType}=='general')
<el-dialog :title="!${context.formModel}.${pKeyName} ? '新建' :'编辑'"
           :close-on-click-modal="false" append-to-body
           :visible.sync="visible" class="XH-dialog XH-dialog_center" lock-scroll
           width="${context.generalWidth}">
<el-row :gutter="${context.gutter}" class="${context.formStyle}">
#elseif(${context.popupType}=='fullScreen')
<transition name="el-zoom-in-center">
<div class="XH-preview-main">
    <div class="XH-common-page-header">
        <el-page-header @back="goBack"
                        :content="!${context.formModel}.${pKeyName} ? '新建':'编辑'"/>
        <div class="options">
            <el-dropdown class="dropdown" placement="bottom">
                <el-button style="width:70px">
                    更 多<i class="el-icon-arrow-down el-icon--right"></i>
                </el-button>
                <el-dropdown-menu slot="dropdown">
#if(${context.treeTable}!=true && ${context.groupTable} !=true)
                    <template v-if="${context.formModel}.${pKeyName}">
                        <el-dropdown-item @click.native="prev" :disabled='prevDis'>
                            {{'上一条'}}
                        </el-dropdown-item>
                        <el-dropdown-item @click.native="next" :disabled='nextDis'>
                            {{'下一条'}}
                        </el-dropdown-item>
                    </template>
#end
                    <el-dropdown-item type="primary" @click.native="dataFormSubmit(2)"
                                      :loading="continueBtnLoading" :disabled='btnLoading'>
                        {{!${context.formModel}.${pKeyName} ?'确定并新增':'确定并继续'}}</el-dropdown-item>
                </el-dropdown-menu>
            </el-dropdown>
            <el-button type="primary" @click="dataFormSubmit()" :loading="btnLoading" :disabled='continueBtnLoading'>#if(${context.confirmButtonText})${context.confirmButtonText}#else 保 存#end</el-button>
            <el-button @click="goBack">#if(${context.cancelButtonText})${context.cancelButtonText}#else 取 消#end</el-button>
        </div>
    </div>
<el-row :gutter="${context.gutter}" class="${context.formStyle} main" :style="{margin: '0 auto',width: '${context.fullScreenWidth}'}">
#elseif(${context.popupType}=='drawer')
<el-drawer :title="!${context.formModel}.${pKeyName} ? '新建' : '编辑'" :visible.sync="visible"
           :wrapperClosable="false" size="${context.drawerWidth}" append-to-body
           class="XH-common-drawer">
<div class="XH-flex-main">
<div class="dynamicForm ${context.formStyle}">
#end
<el-form ref="${context.formRef}" :model="${context.formModel}" :rules="${context.formRules}" size="${context.size}" label-width="${context.labelWidth}px" label-position="${context.labelPosition}" #if($context.disabled == true ) :disabled="true"  #end>
    <template v-if="!loading">
#foreach($fieLdsModel in ${context.form})
    #set($xhKey = "${fieLdsModel.xhKey}")
    #set($isEnd = "${fieLdsModel.isEnd}")
    #set($formModel = ${fieLdsModel.formModel})
    #set($config=$formModel.config)
    #set($span=$config.span)
    #set($outermost = ${formModel.outermost})
    #set($borderType = ${formModel.borderType})
    #set($borderColor = ${formModel.borderColor})
    #set($borderWidth = ${formModel.borderWidth})
    #set($pcshow = $config.pc)
    #if(${xhKey}=='row' && $pcshow == true)
        #if(${isEnd}=='0')
        <el-col :span="${formModel.span}">
        <el-row :gutter="${context.gutter}">
        #else
        </el-row>
        </el-col>
        #end
    #elseif(${xhKey}=='card' && $pcshow == true)
        #if(${isEnd}=='0')
        <el-col  #if(${span}) :span="${span}" #else :span="24" #end>
        <el-card class="mb-20" shadow ="${formModel.shadow}"  header ="${formModel.header}">
            #if(${config.tipLabel} && ${formModel.header})
              <div slot="header">
				<span slot="label">${formModel.header}
					<el-tooltip placement="top" content='${config.tipLabel}'>
                  		<a class='el-icon-question tooltip-question' ></a>
                	</el-tooltip>
				</span>
              </div>
            #end
        #else
        </el-card>
        </el-col>
        #end
    #elseif(${xhKey}=='tab' && $pcshow == true)
        #set($tabs = "el-tabs")
        #if(${outermost}=='1')
            #set($tabs = "el-tab-pane")
        #end
        #if(${isEnd}=='0')
            #if(${outermost}=='0')
            <el-col :span="${formModel.span}">
                <${tabs}  v-model="${formModel.model}" #if($formModel.type)type="${formModel.type}"#end tab-position="${formModel.tabPosition}" class="mb-20">
            #else
                <${tabs}  label="${formModel.title}">
            #end
        #else
            #if(${outermost}=='0')
            </${tabs}>
                </el-col>
            #else
            </${tabs} >
            #end
        #end
    #elseif(${xhKey}=='tableGrid' || ${xhKey}=='tableGridTd' || ${xhKey}=='tableGridTr')
        #set($tabs = "tbody")
        #set($tableGrid = "table")
        #if(${xhKey}=='tableGridTr')
            #set($tabs = "tr")
        #elseif(${xhKey}=='tableGridTd')
            #set($tabs = "")
            #if(${config.merged}==false)
                #set($tabs = "td")
            #end
        #end
        #if(${config.pc}==true)
            #if(${isEnd}=='0')
                #if(${xhKey}=='tableGrid')
                  <${tableGrid} class="table-grid-box" :style='{"--borderType":"${borderType}","--borderColor":"${borderColor}","--borderWidth":"${borderWidth}px"}'>
                #end
                #if($tabs)
                  <${tabs}#if(${config.colspan}) colspan="${config.colspan}"#end#if(${config.rowspan}) rowspan="${config.rowspan}"#end>
                #end
            #else
                #if($tabs)
                </${tabs}>
                #end
                #if(${xhKey}=='tableGrid')
                </${tableGrid}>
                #end
            #end
        #end
    #elseif(${xhKey}=='groupTitle' || ${xhKey}=='XHText'|| ${xhKey} == 'button' || ${xhKey} == 'link' || ${xhKey} == 'alert')
        #if($pcshow== true)
		#set($content=$formModel.content)
		#set($defaultName=${formModel.slot.defaultName})
	    <el-col :span="${span}" >
		<xh-form-tip-item  label-width="0">
		<${config.tag} #if($formModel.style) :style='${formModel.style}'#end
            #if($formModel.href) href = "$formModel.href"#end
            #if($formModel.target) target = "$formModel.target"#end
            #if($formModel.showIcon) :show-icon= "$formModel.showIcon"#end
            #if($formModel.align) align="${formModel.align}" #end
            #if($formModel.disabled) :disabled="${formModel.disabled}" #end
            #if($formModel.buttonText)  buttonText="${formModel.buttonText}"#end
            #if($formModel.type) type="${formModel.type}" #end
            #if($formModel.textStyle) :textStyle='${formModel.textStyle}'#end
            #if($config.defaultValue) value="${config.defaultValue}"#end
            #if($formModel.contentposition) content-position="${formModel.contentposition}" #end
            #if($formModel.closable==false) :closable= "$formModel.closable" #end
            #if($formModel.title) title ="${formModel.title}" #end
            #if($formModel.closeText) closeText ="${formModel.closeText}" #end
            #if($formModel.description) description ="${formModel.description}" #end
            #if($formModel.tipLabel) tipLabel ="${formModel.tipLabel}" #end
        #if($content) content ="$!{content}" #elseif(${defaultName}) content ="$!{defaultName}"#end>
		</${config.tag}>
	</xh-form-tip-item>
	</el-col>
        #end
    #elseif(${xhKey}=='divider')
        #set($content=$formModel.content)
        #set($defaultName=${formModel.slot.defaultName})
        #if(${config.pc}==true)
            <el-col :span="24"  >
            <${config.tag} #if($formModel.contentposition)  content-position="${formModel.contentposition}" #end>
            #if($content) $!{content} #elseif(${defaultName})$!{defaultName} #end
        </${config.tag}>
            </el-col>
        #end
	#elseif(${xhKey}=='collapse' && $pcshow == true)
        #set($collapse = "el-collapse")
        #if(${outermost}=='1')
            #set($collapse = "el-collapse-item")
        #end
        #if(${isEnd}=='0')
            #if(${outermost}=='0')
            <el-col :span="${formModel.span}">
                <${collapse} :accordion="${formModel.accordion}" v-model="${formModel.model}" class="mb-20">
            #else
                <${collapse} title="${formModel.title}" name="${formModel.name}">
            #end
        #else
            #if(${outermost}=='0')
            </${collapse}>
                </el-col>
            #else
            </${collapse}>
            #end
        #end
    #elseif(${xhKey}=='mast')
        #set($html = $fieLdsModel.formColumnModel.fieLdsModel)
        #set($vModel = "${html.vModel}")
        #set($config = $html.config)
        #set($mastKey = "${config.xhKey}")
        #set($show = $config.noShow)
        #set($pcshow = $config.pc)
        #set($startTime=${html.startTime})
        #set($endTime=${html.endTime})
        #if(${mastKey}=='date'||${mastKey}=='time')
            #GetStartAndEndTime($mastKey,$config,$html,$startTime,$endTime)
        #end
        #if($show == false && $pcshow == true)
            <el-col :span="${config.span}" #if(${context.columnData.useFormPermission}) #if(${vModel}) v-if="xh.hasFormP('${vModel}')"
            #elseif($mastKey == 'relationFormAttr' || $mastKey == 'popupAttr')
                 v-if="xh.hasFormP('${html.relationField}')"
            #end
           #end >
                <xh-form-tip-item #if($config.showLabel && $config.showLabel == true )
                    #if($config.label) label="${config.label}" #end
                    #if($config.labelWidth) label-width="${config.labelWidth}px"#end #else label-width="0"#end
                    #if($vModel) prop="${vModel}" #end  #if(${config.label} && $config.tipLabel) tip-label="${config.tipLabel}" #end>
                    #set($mastModel="${context.formModel}.${vModel}")
                    <${config.tag} #if($vModel)  v-model="${mastModel}" @change="changeData('${vModel}',-1)"  #end
                    #if($mastKey!='XHText')
                        #if($html.placeholder) placeholder="${html.placeholder}" #end
                    #else
                        #if($mastKey == 'relationFormAttr' || $mastKey == 'popupAttr')
                            #if($config.defaultValue) defaultValue="${config.defaultValue}"#end
                        #else
                            #if($config.defaultValue) value="${config.defaultValue}"#end
                        #end
                    #end
                    #if($mastKey== 'cascader')
                        #if($html.showAllLevels)
                            show-all-levels
                        #else
                            :show-all-levels="false"
                        #end
                    #end
                    #if($mastKey =='popupSelect' || $mastKey =='popupTableSelect')
                        :rowIndex="null" :formData="${context.formModel}" :templateJson='${html.templateJson}'
                    #end
                    #if($mastKey== 'uploadFz' || $mastKey== 'uploadImg')
                        #if(${html.fileSize}) :fileSize="${html.fileSize}" #else :fileSize="null" #end  #end
                    #if($html.maxlength) :maxlength="${html.maxlength}" #end
                    #if($html.readonly == true ) readonly #end
                    #if($html.disabled == true ):disabled="${html.disabled}"#end
                    #if($html.clearable == true ) clearable #end
                    #if($html.prefixicon) prefix-icon='${html.prefixicon}' #end
                    #if($html.suffixicon) suffix-icon='${html.suffixicon}' #end
                    #if($html.style) :style='${html.style}'#end
                    #if($html.showWordLimit == true ) ${html.showWordLimit} #end
                    #if($html.size) size="${html.size}" #end
                    #if($html.min) :min="${html.min}" #end
                    #if($html.max) :max="${html.max}" #end
                    #if($html.type) type="${html.type}" #end
                    #if($html.autosize) :autosize='${html.autosize}' #end
                    #if($html.step) :step="${html.step}" #end
                    #if($html.precision) :precision="${html.precision}" #end
                    #if($html.stepstrictly==true) stepstrictly #end
                    #if($html.textStyle) :textStyle='${html.textStyle}' #end
                    #if($html.lineHeight) :lineHeight="${html.lineHeight}" #end
                    #if($html.fontSize) :fontSize="${html.fontSize}" #end
                    #if($html.controlsposition) controls-position='${html.controlsposition}' #end
                    #if($html.showChinese) :showChinese="${html.showChinese}" #end
                    #if($html.showPassword) show-password #end
                    #if($html.filterable==true) filterable #end
                    #if($html.multiple) :multiple="${html.multiple}" #end
                    #if($html.separator) separator="${html.separator}" #end
                    #if($html.isrange==true) is-range #end
                    #if($html.rangeseparator) range-separator="${html.rangeseparator}" #end
                    #if($html.startplaceholder) start-placeholder="${html.startplaceholder}" #end
                    #if($html.endplaceholder) end-placeholder="${html.endplaceholder}" #end
                    #if($html.format) format="${html.format}" #end
                    #if($html.colorformat) color-format="${html.colorformat}" #end
                    #if($html.valueformat) value-format="${html.valueformat}" #end
                    #if($html.activetext) active-text="${html.activetext}" #end
                    #if($html.inactivetext) inactive-text="${html.inactivetext}" #end
                    #if($html.activecolor) active-color="${html.activecolor}" #end
                    #if($html.inactivecolor) inactive-color="${html.inactivecolor}" #end
                    #if($html.activevalue) :active-value="${html.activevalue}" #end
                    #if($html.inactivevalue) :inactive-value="${html.inactivevalue}" #end
                    #if($html.pickeroptions) :picker-options='${html.pickeroptions}'#end
                    #if($html.showScore == true ) show-score #end
                    #if($html.showText == true ) show-text #end
                    #if($html.allowhalf == true ) allow-half #end
                    #if($html.showAlpha == true ) show-alpha #end
                    #if($html.showStops == true ) show-stops #end
                    #if($html.range == true ) range #end
                    #if($html.showTip == true ) :showTip="${html.showTip}" #end
                    #if($html.accept) accept="${html.accept}" #end
                    #if($html.sizeUnit) sizeUnit="${html.sizeUnit}" #end
                    #if($html.limit) :limit="${html.limit}" #end
                    #if($html.pathType) pathType="${html.pathType}" #end
                    #if($html.isAccount) :isAccount="${html.isAccount}" #end
                    #if($html.folder) folder="${html.folder}" #end
                    #if($html.buttonText) buttonText="${html.buttonText}" #end
                    #if($html.contentposition) content-position="${html.contentposition}" #end
                    #if($!html.level || $html.level=='0') :level=${html.level} #end
                    #if($html.isAmountChinese) isAmountChinese #end
                    #if($html.thousands) thousands #end
                    #if($html.addonAfter) addonAfter="${html.addonAfter}" #end
                    #if($html.addonBefore) addonBefore="${html.addonBefore}" #end
                    #if($html.controlsPosition) controlsPosition="${html.controlsPosition}" #end
                    #if($startTime) :startTime="${startTime}" #end
                    #if($endTime) :endTime="${endTime}" #end
                    #if($html.tipText) tipText="${html.tipText}" #end
                    #if($mastKey == 'relationFormAttr' || $mastKey == 'popupAttr')
                        #if($html.relationField) relationField="${html.relationField}" #end
                        #if($html.showField) showField="${html.showField}" #end
                        #if($config.isStorage) isStorage=$config.isStorage #end
                    #end
                    #if($html.selectType) selectType="$html.selectType" #end
                    #if($html.selectType == 'custom')
                        #if($html.ableDepIds) :ableDepIds = 'ableAll.${html.vModel}ableDepIds' #end
                        #if($html.ablePosIds) :ablePosIds = 'ableAll.${html.vModel}ablePosIds' #end
                        #if($html.ableUserIds) :ableUserIds = 'ableAll.${html.vModel}ableUserIds' #end
                        #if($html.ableRoleIds) :ableRoleIds = 'ableAll.${html.vModel}ableRoleIds' #end
                        #if($html.ableGroupIds) :ableGroupIds = 'ableAll.${html.vModel}ableGroupIds' #end
                        #if($html.ableIds) :ableIds = 'ableAll.${html.vModel}ableIds' #end
                    #elseif($html.selectType == 'dep' || $html.selectType == 'pos' || $html.selectType == 'role' || $html.selectType == 'group')
                        #if($html.relationField)
                            :ableRelationIds=" Array.isArray(dataForm.${html.relationField}) ? dataForm.${html.relationField} : [dataForm.${html.relationField}]"
                        #end
                    #end
                    #if($mastKey == 'relationForm') field="${vModel}" modelId ="${html.modelId}"  :columnOptions="${vModel}columnOptions" relationField="${html.relationField}" popupWidth="${html.popupWidth}" #if($html.hasPage) hasPage :pageSize="$html.pageSize" #end  #end
                    #if($mastKey == 'popupSelect' ||$mastKey == 'popupTableSelect')
                        field="${vModel}" interfaceId="${html.interfaceId}" :columnOptions="${vModel}columnOptions" propsValue="${html.propsValue}" relationField="${html.relationField}" popupType="${html.popupType}"
                        #if(${html.popupTitle}) popupTitle="${html.popupTitle}" #end popupWidth="${html.popupWidth}"
                        #if($html.hasPage) hasPage :pageSize="$html.pageSize" #end  #end
                    #if($mastKey=='cascader' || $mastKey=='treeSelect'||$mastKey=='checkbox'||$mastKey=='radio'||$mastKey=='select') :options="${vModel}Options" :props="${vModel}Props"
                      #if(${html.direction}) direction="${html.direction}" #end #if(${html.optionType}) optionType="${html.optionType}" #end  #end
                    #if($mastKey == 'autoComplete')
                        relationField="${html.relationField}"
                        interfaceId="${html.interfaceId}"
                        :templateJson="interfaceRes.${vModel}"
                        :total="${html.total}"
                        :formData="${context.formModel}"
                    #end>
                    #if($mastKey!='checkbox' && $mastKey!='radio' && $mastKey!='select')
                        #if($html.slot.prepend)
                            <template slot="prepend">${html.slot.prepend}</template>
                        #end
                        #if($html.slot.append)
                            <template slot="append">${html.slot.append}</template>
                        #end
                    #end

                </${config.tag}>
                </xh-form-tip-item>
            </el-col>
        #end
    #elseif(${xhKey}=='mastTable')
        #set($beforeVmodel =${fieLdsModel.formMastTableModel.vModel})
        #set($html = $fieLdsModel.formMastTableModel.mastTable.fieLdsModel)
        #set($vModel = "${html.vModel}")
        #set($config = $html.config)
        #set($mastKey = "${config.xhKey}")
        #set($show = $config.noShow)
        #set($pcshow = $config.pc)
        #set($startTime=${html.startTime})
        #set($endTime=${html.endTime})
        #if(${mastKey}=='date'||${mastKey}=='time')
            #GetStartAndEndTime($mastKey,$config,$html,$startTime,$endTime)
        #end
        #if($show == false && $pcshow ==true)
            <el-col :span="${config.span}" #if(${context.columnData.useFormPermission}) #if(${vModel}) v-if="xh.hasFormP('${beforeVmodel}')"
        #elseif($mastKey == 'relationFormAttr' || $mastKey == 'popupAttr')
                    v-if="xh.hasFormP('${html.relationField}')"
            #end
        #end>
                <xh-form-tip-item #if($config.showLabel && $config.showLabel == true)
                    #if($config.label) label="${config.label}" #end
                    #if($config.labelWidth) label-width="${config.labelWidth}px"#end #else label-width="0"#end
                    #if($vModel) prop="${beforeVmodel}" #end  #if(${config.label} && $config.tipLabel) tip-label="${config.tipLabel}" #end>
                    #set($mastModel="${context.formModel}.${beforeVmodel}")
                    <${config.tag}  #if($vModel)  v-model=" ${mastModel}"  @change="changeData('${beforeVmodel}',-1)"  #end
                    #if($mastKey!='XHText')
                        #if($html.placeholder) placeholder="${html.placeholder}" #end
                    #else
                        #if($mastKey == 'relationFormAttr' || $mastKey == 'popupAttr')
                            #if($config.defaultValue) defaultValue="${config.defaultValue}"#end
                        #else
                            #if($config.defaultValue) value="${config.defaultValue}"#end
                        #end
                    #end
                    #if($mastKey== 'cascader')
                        #if($html.showAllLevels)
                            show-all-levels
                        #else
                            :show-all-levels="false"
                        #end
                    #end
                    #if($mastKey =='popupSelect' || $mastKey =='popupTableSelect')
                        :rowIndex="null" :formData="${context.formModel}" :templateJson='${html.templateJson}'
                    #end
                    #if($mastKey== 'uploadFz' || $mastKey== 'uploadImg')
                        #if(${html.fileSize}) :fileSize="${html.fileSize}" #else :fileSize="null" #end  #end
                    #if($html.maxlength) :maxlength="${html.maxlength}" #end
                    #if($html.readonly == true ) readonly #end
                    #if($html.disabled == true ):disabled="${html.disabled}"#end
                    #if($html.clearable == true ) clearable #end
                    #if($html.prefixicon) prefix-icon='${html.prefixicon}' #end
                    #if($html.suffixicon) suffix-icon='${html.suffixicon}' #end
                    #if($html.style) :style='${html.style}'#end
                    #if($html.showWordLimit == true ) ${html.showWordLimit} #end
                    #if($html.size) size="${html.size}" #end
                    #if($html.min) :min="${html.min}" #end
                    #if($html.max) :max="${html.max}" #end
                    #if($html.type) type="${html.type}" #end
                    #if($html.autosize) :autosize='${html.autosize}' #end
                    #if($html.step) :step="${html.step}" #end
                    #if($html.precision) :precision="${html.precision}" #end
                    #if($html.stepstrictly==true) stepstrictly #end
                    #if($html.textStyle) :textStyle='${html.textStyle}' #end
                    #if($html.lineHeight) :lineHeight="${html.lineHeight}" #end
                    #if($html.fontSize) :fontSize="${html.fontSize}" #end
                    #if($html.controlsposition) controls-position='${html.controlsposition}' #end
                    #if($html.showChinese) :showChinese="${html.showChinese}" #end
                    #if($html.showPassword) show-password #end
                    #if($html.filterable==true) filterable #end
                    #if($html.multiple) :multiple="${html.multiple}" #end
                    #if($html.separator) separator="${html.separator}" #end
                    #if($html.isrange==true) is-range #end
                    #if($html.rangeseparator) range-separator="${html.rangeseparator}" #end
                    #if($html.startplaceholder) start-placeholder="${html.startplaceholder}" #end
                    #if($html.endplaceholder) end-placeholder="${html.endplaceholder}" #end
                    #if($html.format) format="${html.format}" #end
                    #if($html.colorformat) color-format="${html.colorformat}" #end
                    #if($html.valueformat) value-format="${html.valueformat}" #end
                    #if($html.activetext) active-text="${html.activetext}" #end
                    #if($html.inactivetext) inactive-text="${html.inactivetext}" #end
                    #if($html.activecolor) active-color="${html.activecolor}" #end
                    #if($html.inactivecolor) inactive-color="${html.inactivecolor}" #end
                    #if($html.activevalue) :active-value="${html.activevalue}" #end
                    #if($html.inactivevalue) :inactive-value="${html.inactivevalue}" #end
                    #if($html.pickeroptions) :picker-options='${html.pickeroptions}'#end
                    #if($html.showScore == true ) show-score #end
                    #if($html.showText == true ) show-text #end
                    #if($html.allowhalf == true ) allow-half #end
                    #if($html.showAlpha == true ) show-alpha #end
                    #if($html.showStops == true ) show-stops #end
                    #if($html.range == true ) range #end
                    #if($html.showTip == true ) :showTip="${html.showTip}" #end
                    #if($html.accept) accept="${html.accept}" #end
                    #if($html.sizeUnit) sizeUnit="${html.sizeUnit}" #end
                    #if($html.limit) :limit="${html.limit}" #end
                    #if($html.pathType) pathType="${html.pathType}" #end
                    #if($html.isAccount) :isAccount="${html.isAccount}" #end
                    #if($html.folder) folder="${html.folder}" #end
                    #if($html.buttonText) buttonText="${html.buttonText}" #end
                    #if($html.contentposition) content-position="${html.contentposition}" #end
                    #if($html.level || $html.level=='0') :level=${html.level} #end
                    #if($html.isAmountChinese) isAmountChinese #end
                    #if($html.thousands) thousands #end
                    #if($html.addonAfter) addonAfter="${html.addonAfter}" #end
                    #if($html.addonBefore) addonBefore="${html.addonBefore}" #end
                    #if($html.controlsPosition) controlsPosition="${html.controlsPosition}" #end
                    #if($startTime) :startTime="${startTime}" #end
                    #if($endTime) :endTime="${endTime}" #end
                    #if($html.tipText) tipText="${html.tipText}" #end
                    #if($mastKey == 'relationFormAttr' || $mastKey == 'popupAttr')
                        #if($html.relationField) relationField="${html.relationField}" #end
                        #if($html.showField) showField="${html.showField}" #end
                        #if($config.isStorage) isStorage=$config.isStorage #end
                    #end
                    #if($html.selectType) selectType="$html.selectType" #end
                    #if($html.selectType == 'custom')
                        #set($vmodelTable =${fieLdsModel.formMastTableModel.table})
                        #set($vmodelFeild =${fieLdsModel.formMastTableModel.field})
                        #if($html.ableDepIds) :ableDepIds = 'ableAll.${beforeVmodel}ableDepIds' #end
                        #if($html.ablePosIds) :ablePosIds = 'ableAll.${beforeVmodel}ablePosIds' #end
                        #if($html.ableUserIds) :ableUserIds = 'ableAll.${beforeVmodel}ableUserIds' #end
                        #if($html.ableRoleIds) :ableRoleIds = 'ableAll.${beforeVmodel}ableRoleIds' #end
                        #if($html.ableGroupIds) :ableGroupIds = 'ableAll.${beforeVmodel}ableGroupIds' #end
                        #if($html.ableIds) :ableIds = 'ableAll.${beforeVmodel}ableIds' #end
                    #elseif($html.selectType == 'dep' || $html.selectType == 'pos' || $html.selectType == 'role' || $html.selectType == 'group')
                        #if($html.relationField)
                            :ableRelationIds=" Array.isArray(dataForm.${html.relationField}) ? dataForm.${html.relationField} : [dataForm.${html.relationField}]"
                        #end
                    #end
                    #if($mastKey == 'relationForm') field="${beforeVmodel}" modelId ="${html.modelId}"  :columnOptions="${beforeVmodel}columnOptions" relationField="${html.relationField}" popupWidth="${html.popupWidth}" #if($html.hasPage) hasPage :pageSize="$html.pageSize" #end  #end
                    #if($mastKey == 'popupSelect' || $mastKey =='popupTableSelect') field="${beforeVmodel}" interfaceId="${html.interfaceId}" :columnOptions="${beforeVmodel}columnOptions" propsValue="${html.propsValue}" relationField="${html.relationField}" popupType="${html.popupType}"
                        #if(${html.popupTitle}) popupTitle="${html.popupTitle}" #end popupWidth="${html.popupWidth}"
                        #if($html.hasPage) hasPage :pageSize="$html.pageSize" #end  #end
                    #if($mastKey=='cascader' || $mastKey=='treeSelect' || $mastKey=='checkbox'||$mastKey=='radio'||$mastKey=='select') :options="${beforeVmodel}Options" :props="${beforeVmodel}Props"
                        #if(${html.direction}) direction="${html.direction}" #end #if(${html.optionType}) optionType="${html.optionType}" #end  #end
                    #if($mastKey == 'autoComplete')
                        relationField="${html.relationField}"
                        interfaceId="${html.interfaceId}"
                        :templateJson="interfaceRes.${beforeVmodel}"
                        :total="${html.total}"
                        :formData="${context.formModel}"
                    #end>
                    #if($mastKey!='checkbox' && $mastKey!='radio' && $mastKey!='select')
                        #if($html.slot.prepend)
                            <template slot="prepend">${html.slot.prepend}</template>
                        #end
                        #if($html.slot.append)
                            <template slot="append">${html.slot.append}</template>
                        #end
                    #end
                </${config.tag}>
                </xh-form-tip-item>
            </el-col>
        #end
    #elseif($xhKey == 'table')
        #set($child = $fieLdsModel.childList)
        #set($className = "")
        #set($TabField = "")
        #foreach($children in ${context.children})
            #if(${children.tableModel}==${child.tableModel})
                #set($TabField = "${children.tableModel}")
                #set($className = "${children.className.substring(0,1).toLowerCase()}${children.className.substring(1).toLowerCase()}")
            #end
        #end
    <el-col :span="${child.span}"  #if(${context.columnData.useFormPermission}) v-if="xh.hasFormP('${TabField}')" #end>
    <xh-form-tip-item label-width="0">
        #if($child.showTitle== true)
            <div class="XH-common-title">
                <h2>${child.label}#if(${child.label} && $child.tipLabel)<el-tooltip placement="top" content='${child.tipLabel}'><a class='el-icon-question tooltip-question'></a></el-tooltip>#end</h2>
            </div>
        #end
    <el-table :data="${context.formModel}.${className}List" size='mini'  #if(${child.showSummary}) show-summary :summary-method="get${className}Summaries" #end >
        <el-table-column type="index" width="50" label="序号" align="center" />
        #foreach($html in ${child.childList})
            #set($htmlChild = $html.fieLdsModel)
            #set($vModel = "${htmlChild.vModel}")
            #set($config = $htmlChild.config)
            #set($tableKey = "${config.xhKey}")
            #set($pcshow = ${config.pc})
            #set($startTime=${htmlChild.startTime})
            #set($endTime=${htmlChild.endTime})
            #if(${tableKey}=='date'||${tableKey}=='time')
                #GetStartAndEndTime($tableKey,$config,$htmlChild,$startTime,$endTime)
            #end
            #if($pcshow == true)
            <el-table-column prop="$!{vModel}" label="${config.label}" #if(${config.columnWidth}) width="${config.columnWidth}" #end
                #if(${context.columnData.useFormPermission})
                    #if(${vModel})  v-if="xh.hasFormP('${TabField}-$!{vModel}')"
                    #elseif($tableKey == 'relationFormAttr' || $tableKey == 'popupAttr')
                             v-if="xh.hasFormP('${TabField}-${htmlChild.relationField}')"
                    #end
                #end>
			#if($config.required || ${config.tipLabel})
				<template slot="header">
                    #if(${config.required})
                      <span class="required-sign">*</span>
                    #end
                    ${config.label}
                    #if(${config.label} && ${config.tipLabel})
                      <span slot="label">
                      		<el-tooltip placement="top" content='${config.tipLabel}'>
                        		<a class='el-icon-question tooltip-question'></a>
                      		</el-tooltip>
                    	</span>
                    #end
				</template>
			#end
                <template slot-scope="scope">
                    <${config.tag} #if($vModel)  v-model="scope.row.${vModel}"    @change="changeData('${className}-${vModel}',scope.$index)" #end
                    #if($tableKey!='XHText')
                        #if($htmlChild.placeholder) placeholder="${htmlChild.placeholder}" #end
                    #else
                        #if($tableKey == 'relationFormAttr' || $tableKey == 'popupAttr')
                            #if($config.defaultValue) defaultValue="${config.defaultValue}"#end
                        #else
                            #if($config.defaultValue) value="${config.defaultValue}"#end
                        #end
                    #end
                    #if($tableKey== 'cascader')
                        #if($htmlChild.showAllLevels)
                            show-all-levels
                        #else
                            :show-all-levels="false"
                        #end
                    #end
                    #if($tableKey =='popupSelect' || $tableKey =='popupTableSelect')
                        :rowIndex="scope.$index" :formData="${context.formModel}" :templateJson='${htmlChild.templateJson}'
                    #end
                    #if($tableKey== 'uploadFz' || $tableKey== 'uploadImg')
                     #if(${htmlChild.fileSize}) :fileSize="${htmlChild.fileSize}" #else :fileSize="null" #end  #end
                    #if($htmlChild.maxlength) :maxlength="${htmlChild.maxlength}" #end
                    #if($htmlChild.readonly == true ) readonly #end
                    #if($htmlChild.disabled == true ):disabled="${htmlChild.disabled}"#end
                    #if($htmlChild.clearable == true ) clearable #end
                    #if($htmlChild.prefixicon) prefix-icon='${htmlChild.prefixicon}' #end
                    #if($htmlChild.suffixicon) suffix-icon='${htmlChild.suffixicon}' #end
                    #if($htmlChild.style) :style='${htmlChild.style}'#end
                    #if($htmlChild.showWordLimit == true ) ${htmlChild.showWordLimit} #end
                    #if($htmlChild.size) size="${htmlChild.size}" #end
                    #if($htmlChild.min) :min="${htmlChild.min}" #end
                    #if($htmlChild.max) :max="${htmlChild.max}" #end
                    #if($htmlChild.type) type="${htmlChild.type}" #end
                    #if($htmlChild.autosize) :autosize='${htmlChild.autosize}' #end
                    #if($htmlChild.step) :step="${htmlChild.step}" #end
                    #if($htmlChild.precision) :precision="${htmlChild.precision}" #end
                    #if($htmlChild.stepstrictly==true) stepstrictly #end
                    #if($htmlChild.textStyle) :textStyle='${htmlChild.textStyle}' #end
                    #if($htmlChild.lineHeight) :lineHeight="${htmlChild.lineHeight}" #end
                    #if($htmlChild.fontSize) :fontSize="${htmlChild.fontSize}" #end
                    #if($htmlChild.controlsposition) controls-position='${htmlChild.controlsposition}' #end
                    #if($htmlChild.showChinese) :showChinese="${htmlChild.showChinese}" #end
                    #if($htmlChild.showPassword) show-password #end
                    #if($htmlChild.filterable==true) filterable #end
                    #if($htmlChild.multiple) :multiple="${htmlChild.multiple}" #end
                    #if($htmlChild.separator) separator="${htmlChild.separator}" #end
                    #if($htmlChild.isrange==true) is-range #end
                    #if($htmlChild.rangeseparator) range-separator="${htmlChild.rangeseparator}" #end
                    #if($htmlChild.startplaceholder) start-placeholder="${htmlChild.startplaceholder}" #end
                    #if($htmlChild.endplaceholder) end-placeholder="${htmlChild.endplaceholder}" #end
                    #if($htmlChild.format) format="${htmlChild.format}" #end
                    #if($htmlChild.colorformat) color-format="${htmlChild.colorformat}" #end
                    #if($htmlChild.valueformat) value-format="${htmlChild.valueformat}" #end
                    #if($htmlChild.activetext) active-text="${htmlChild.activetext}" #end
                    #if($htmlChild.inactivetext) inactive-text="${htmlChild.inactivetext}" #end
                    #if($htmlChild.activecolor) active-color="${htmlChild.activecolor}" #end
                    #if($htmlChild.inactivecolor) inactive-color="${htmlChild.inactivecolor}" #end
                    #if($htmlChild.activevalue) :active-value="${htmlChild.activevalue}" #end
                    #if($htmlChild.inactivevalue) :inactive-value="${htmlChild.inactivevalue}" #end
                    #if($htmlChild.pickeroptions) :picker-options='${htmlChild.pickeroptions}'#end
                    #if($htmlChild.showScore == true ) show-score #end
                    #if($htmlChild.showText == true ) show-text #end
                    #if($htmlChild.allowhalf == true ) allow-half #end
                    #if($htmlChild.showAlpha == true ) show-alpha #end
                    #if($htmlChild.showStops == true ) show-stops #end
                    #if($htmlChild.range == true ) range #end
                    #if($htmlChild.showTip == true ) :showTip="${htmlChild.showTip}" #end
                    #if($htmlChild.accept) accept="${htmlChild.accept}" #end
                    #if($htmlChild.sizeUnit) sizeUnit="${htmlChild.sizeUnit}" #end
                    #if($htmlChild.limit) :limit="${htmlChild.limit}" #end
                    #if($htmlChild.pathType) pathType="${htmlChild.pathType}" #end
                    #if($htmlChild.isAccount) :isAccount="${htmlChild.isAccount}" #end
                    #if($htmlChild.folder) folder="${htmlChild.folder}" #end
                    #if($htmlChild.buttonText) buttonText="${htmlChild.buttonText}" #end
                    #if($htmlChild.contentposition) content-position="${htmlChild.contentposition}" #end
                    #if($htmlChild.isAmountChinese) isAmountChinese #end
                    #if($htmlChild.thousands) thousands #end
                    #if($htmlChild.addonAfter) addonAfter="${htmlChild.addonAfter}" #end
                    #if($htmlChild.addonBefore) addonBefore="${htmlChild.addonBefore}" #end
                    #if($htmlChild.controlsPosition) controlsPosition="${htmlChild.controlsPosition}" #end
                    #if($startTime) :startTime="${startTime}" #end
                    #if($endTime) :endTime="${endTime}" #end
                    #if($htmlChild.tipText) tipText="${htmlChild.tipText}" #end
                    #if($tableKey == 'address')
                        :level=${htmlChild.level}
                    #end
                    #if($tableKey == 'relationFormAttr' || $tableKey == 'popupAttr')
                        #if($htmlChild.relationField) :relationField="'${htmlChild.relationField}'+scope.$index" #end
                        #if($htmlChild.showField) showField="${htmlChild.showField}" #end
                        #if($config.isStorage) isStorage=$config.isStorage #end
                    #end
                    #if($htmlChild.selectType) selectType="$htmlChild.selectType" #end
                    #if($htmlChild.selectType == 'custom')
                        #if($htmlChild.ableDepIds) :ableDepIds = 'ableAll.${className}${vModel}ableDepIds' #end
                        #if($htmlChild.ablePosIds) :ablePosIds = 'ableAll.${className}${vModel}ablePosIds' #end
                        #if($htmlChild.ableUserIds) :ableUserIds = 'ableAll.${className}${vModel}ableUserIds' #end
                        #if($htmlChild.ableRoleIds) :ableRoleIds = 'ableAll.${className}${vModel}ableRoleIds' #end
                        #if($htmlChild.ableGroupIds) :ableGroupIds = 'ableAll.${className}${vModel}ableGroupIds' #end
                        #if($htmlChild.ableIds) :ableIds = 'ableAll.${className}${vModel}ableIds' #end
                    #elseif($htmlChild.selectType == 'dep' || $htmlChild.selectType == 'pos' || $htmlChild.selectType == 'role' || $htmlChild.selectType == 'group')
                        #if($htmlChild.relationField)
                            :ableRelationIds=" Array.isArray(scope.row.${htmlChild.relationField}) ? scope.row.${htmlChild.relationField} : [scope.row.${htmlChild.relationField}]"
                        #end
                    #end
                    #if($tableKey == 'relationForm') :field="'${vModel}'+scope.$index"  modelId ="${htmlChild.modelId}"  :columnOptions="${className}${vModel}columnOptions" relationField="${htmlChild.relationField}" popupType="${htmlChild.popupType}" popupWidth="${htmlChild.popupWidth}" #if($htmlChild.hasPage) hasPage :pageSize="$htmlChild.pageSize" #end  #end
                    #if($tableKey == 'popupSelect'|| $tableKey == 'popupTableSelect' ) :field="'${vModel}'+scope.$index"  interfaceId="${htmlChild.interfaceId}" :columnOptions="${className}${vModel}columnOptions" propsValue="${htmlChild.propsValue}" relationField="${htmlChild.relationField}" popupType="${htmlChild.popupType}"
                        #if(${htmlChild.popupTitle}) popupTitle="${htmlChild.popupTitle}" #end popupWidth="${htmlChild.popupWidth}"
                        #if($htmlChild.hasPage) hasPage :pageSize="$htmlChild.pageSize" #end  #end
                    #if($tableKey=='cascader' || $tableKey=='treeSelect' || $tableKey=='checkbox'||$tableKey=='radio'||$tableKey=='select') :options="${className}${vModel}Options" :props="${className}${vModel}Props"
                        #if(${html.direction}) direction="${html.direction}" #end #if(${html.optionType}) optionType="${html.optionType}" #end  #end
                    #if($tableKey == 'autoComplete')
                        relationField="${htmlChild.relationField}"
                        interfaceId="${htmlChild.interfaceId}"
                        :templateJson="interfaceRes.${className}${vModel}"
                        :total="${htmlChild.total}"
                        :formData="${context.formModel}"
                    #end>
                    #if($tableKey!='checkbox' && $tableKey!='radio' && $tableKey!='select')
                        #if($htmlChild.slot.prepend)
                            <template slot="prepend">${htmlChild.slot.prepend}</template>
                        #end
                        #if($htmlChild.slot.append)
                            <template slot="append">${htmlChild.slot.append}</template>
                        #end
                    #end
                    #if($tableKey=='divider')
                        $!{htmlChild.slot.defaultName}
                    #end
                </${config.tag}>
            </template>
        </el-table-column>
            #end
        #end
    <el-table-column label="操作" width="50" >
        <template slot-scope="scope">
            <el-button size="mini" type="text" class="XH-table-delBtn" @click="del${className}List(scope.$index)">删除</el-button>
        </template>
    </el-table-column>
    </el-table>
        #if($child.addType == 1)
            #set($isSelectDialog = true)
        <div class="table-actions" @click="openSelectDialog('${className}List')">
            <el-button type="text" icon="el-icon-plus">$!{child.actionText}</el-button>
        </div>
        #else
        <div class="table-actions" @click="add${className}List()">
            <el-button type="text" icon="el-icon-plus">$!{child.actionText}</el-button>
        </div>
        #end
    </xh-form-tip-item>
    </el-col>
    #end
#end
    </template>
</el-form>
    <SelectDialog v-if="selectDialogVisible" :config="currTableConf" :formData="dataForm"
              ref="selectDialog" @select="addForSelect" @close="selectDialogVisible=false"/>
    #if(${context.popupType}=='general')
    </el-row>
    <span slot="footer" class="dialog-footer">
#if(${context.treeTable}!=true && ${context.groupTable} !=true)
          <div class="upAndDown-button" v-if="${context.formModel}.${pKeyName}">
            <el-button @click="prev" :disabled='prevDis'>
              {{'上一条'}}
            </el-button>
            <el-button @click="next" :disabled='nextDis'>
              {{'下一条'}}
            </el-button>
          </div>
#end
          <el-button @click="visible = false">#if(${context.cancelButtonText})${context.cancelButtonText}#else 取 消#end</el-button>
          <el-button type="primary" @click="dataFormSubmit()" :loading="btnLoading">#if(${context.confirmButtonText})${context.confirmButtonText}#else 确 定#end</el-button>
          <el-button type="primary" @click="dataFormSubmit(2)" :loading="continueBtnLoading">
            {{!${context.formModel}.${pKeyName} ?'确定并新增':'确定并继续'}}</el-button>
    </span>
    </el-dialog>
    #elseif(${context.popupType}=='fullScreen')
    </el-row>
    </div>
</transition>
    #elseif(${context.popupType}=='drawer')
    </div>
        <div class="drawer-footer">
 #if(${context.treeTable}!=true && ${context.groupTable} !=true)
          <div class="upAndDown-button" v-if="${context.formModel}.${pKeyName}">
                <el-button @click="prev" :disabled='prevDis'>
                    {{'上一条'}}
                </el-button>
                <el-button @click="next" :disabled='nextDis'>
                    {{'下一条'}}
                </el-button>
            </div>
 #end
          <el-button @click="visible = false">#if(${context.cancelButtonText})${context.cancelButtonText}#else 取 消#end</el-button>
          <el-button type="primary" @click="dataFormSubmit()" :loading="btnLoading">#if(${context.confirmButtonText})${context.confirmButtonText}#else 确 定#end</el-button>
          <el-button type="primary" @click="dataFormSubmit(2)" :loading="continueBtnLoading">
                {{!${context.formModel}.${pKeyName} ?'确定并新增':'确定并继续'}}</el-button>
        </div>
    </div>
    </el-drawer>
    #end
</template>
<script>
    import request from '@/utils/request'
    import {mapGetters} from "vuex";
    import {getDataInterfaceRes} from '@/api/systemData/dataInterface'
    import {getDictionaryDataSelector} from '@/api/systemData/dictionary'
    import {getDefaultCurrentValueUserId} from '@/api/permission/user'
    import {getDefaultCurrentValueDepartmentId} from '@/api/permission/organize'
    import {
        getBeforeData,
        getBeforeTime,
        getDateDay,
        getLaterData,
        getLaterTime
    } from '@/components/Generator/utils/index.js'
    import {thousandsFormat} from "@/components/Generator/utils/index"
    import SelectDialog from '@/components/SelectDialog'
        #if($isSelectDialog == true)
        #end

    export default {
        components: { #if($isSelectDialog == true) SelectDialog #end},
        props: [],
        data() {
            return {
                dataFormSubmitType: 0,
                continueBtnLoading: false,
                index: 0,
                prevDis: false,
                nextDis: false,
                allList: [],
                visible: false,
                loading: false,
                btnLoading: false,
                selectDialogVisible: false,
                currTableConf:{},
                addTableConf:{
                #foreach($children in ${context.children})
                        #set($className = "${children.className.substring(0,1).toLowerCase()}${children.className.substring(1).toLowerCase()}")
                   #if(${children.addTableConf})
                        ${className}List :$!{children.addTableConf},
                   #end
                #end
                },
                //可选范围默认值
                ableAll:{
                    #foreach($fieLdsModel in ${context.ableAll})
                        #set($xhKey = "${fieLdsModel.xhKey}")
                        #if(${xhKey}=='mast')
                            #set($html = $fieLdsModel.formColumnModel.fieLdsModel)
                                #set($mastModel="${html.vModel}")
                            #if($html.selectType == 'custom')
                                #ableAll(${html}, ${mastModel},false)
                            #end
                        #elseif(${xhKey}=='mastTable')
                            #set($html = $fieLdsModel.formMastTableModel.mastTable.fieLdsModel)
                            #set($vmodelTable =${fieLdsModel.formMastTableModel.table})
                            #set($vmodelFeild =${html.vModel})
                            #if($html.selectType == 'custom')
                                #ableAll(${html}, "${vmodelFeild}",true)
                            #end
                        #elseif(${xhKey}=='table')
                            #set($child = $fieLdsModel.childList)
                            #set($className = ${child.tableName})
                            #foreach($htmlChild in ${child.childList})
                                #set($html = $htmlChild.fieLdsModel)
                                #set($childvModel = ${html.vModel})
                                #if($html.selectType == 'custom')
                                  #ableAll(${html},"${className}${childvModel}",true)
                                #end
                            #end
                        #end
                    #end
                },
                tableRows:{
                    #foreach($child in ${context.children})
                        #set($className = "")
                        #foreach($children in ${context.children})
                            #if(${children.tableModel}==${child.tableModel})
                                #set($className = "${children.className.substring(0,1).toLowerCase()}${children.className.substring(1).toLowerCase()}")
                            #end
                        #end
                            ${className}List:{
                        #foreach($childListAll in ${child.childList})
                            #set($html = $childListAll.fieLdsModel)
                            #set($model = "${html.vModel}")
                            #set($config = ${html.config})
                            #set($xhKey = "${config.xhKey}")
                            #if($model)
                                #if(${xhKey}=='cascader'||${xhKey}=='checkbox' || ${xhKey}=='address')
                                    ${model} : [],
                                #elseif(${xhKey}=='select' || ${xhKey}=='userSelect' || ${xhKey}=='depSelect' || ${xhKey}=='posSelect' || ${xhKey}=='treeSelect')
                                    #if(${html.multiple}=='true')
                                        ${model} : [],
                                    #end
                                #elseif(${xhKey} == 'comSelect')
                                    ${model} : [],
                                #elseif(${xhKey}=='uploadImg'||${xhKey}=='uploadFz' || ${xhKey}=='timeRange' || ${xhKey}=='dateRange')
                                    ${model} : [],
                                #elseif(${xhKey}=='switch'||${xhKey}=='slider')
                                    ${model} : [],
                                #elseif(${xhKey}=='numInput'||${xhKey}=='calculate')
                                    ${model} : undefined,
                                #else
                                    ${model} : '',
                                #end
                            #end
                        #end
                            enabledmark:undefined
                    },
                    #end
                },
            currVmodel:"",
            ${context.formModel}: {
                #foreach($fieLdsModel in ${context.fields})
            #set($html = $fieLdsModel.formColumnModel.fieLdsModel)
            #set($vModel = "${html.vModel}")
                #if($vModel !='')
                    #set($config = $html.config)
                    #set($mastKey = "${config.xhKey}")
                    #if($mastKey!='XHText' && $mastKey!='divider')
                            #if($!config.valueType=='String')
                                $!{vModel} : "$!{config.defaultValue}",
                            #elseif($!config.valueType=='undefined')
                                #if(${mastKey}=='numInput'||${mastKey}=='calculate')
                                    $!{vModel} : undefined,
                                #else
                                    $!{vModel} : '',
                                #end
                            #else
                                #if(${mastKey}=='treeSelect' && ${html.multiple}=='false')
                                    $!{vModel} : '',
                                #else
                                    $!{vModel} : $!{config.defaultValue},
                                #end
                            #end
                #end
                #end
                #end
                #foreach($child in ${context.children})
                    #set($className = "")
                    #foreach($children in ${context.children})
                        #if(${children.tableModel}==${child.tableModel})
                            #set($className = "${children.className.substring(0,1).toLowerCase()}${children.className.substring(1).toLowerCase()}")
                        #end
                    #end
                        ${className}List:[],
                #end
                #foreach($clum in ${context.columnChildren})
                    #set($clumLowName = "${clum.tableName.toLowerCase()}")
                    ${clumLowName}:
                    {
                        #foreach($field in  ${clum.fieLdsModelList})
                            #set($fieldName = ${field.field})
                                $fieldName:'',
                        #end
                    },
                #end
                #foreach($mast in ${context.mastTable})
                    #set($mastField = $mast.formMastTableModel.mastTable.fieLdsModel)
                    #set($config =$mastField.config)
                    #set($mastKey = ${config.xhKey})
                    #if($mastKey!='XHText' && $mastKey!='divider')
                            #if($!config.valueType=='String')
                                ${mast.formMastTableModel.vModel} : "$!{config.defaultValue}",
                            #elseif($!config.valueType=='undefined')
                                #if(${mastKey}=='numInput'||${mastKey}=='calculate')
                                    ${mast.formMastTableModel.vModel} : undefined,
                                #else
                                ${mast.formMastTableModel.vModel} : '',
                                #end
                            #else
                                #if(${mastKey}=='treeSelect' && ${mastField.multiple}=='false')
                                        ${mast.formMastTableModel.vModel} : '',
                                #else
                                        ${mast.formMastTableModel.vModel} : $!{config.defaultValue},
                                #end
                            #end
                    #end
                #end
                #if($context.version)
                    version: 0,
                #end
            },
            #foreach($fieLdsModel in ${context.form})
                #set($xhKey = "${fieLdsModel.xhKey}")
                #set($formModel = ${fieLdsModel.formModel})
                #set($outermost = ${formModel.outermost})
                #set($isEnd = "${fieLdsModel.isEnd}")
                #if(${isEnd}=='0')
                    #if($xhKey=='collapse')
                        #if(${outermost}=='0')
                            ${formModel.model}:${formModel.active},
                        #end
                    #end
                    #if($xhKey=='tab')
                        #if(${outermost}=='0')
                            ${formModel.model}:'${formModel.active}',
                        #end
                    #end
                #end
            #end
            ${context.formRules}:
            {
                #foreach($fieLdsModel in ${context.fields})
                    #set($html = $fieLdsModel.formColumnModel.fieLdsModel)
                    #set($vModel = "${html.vModel}")
                    #set($config = $html.config)
                    #set($mastKey = "${config.xhKey}")
                    #set($listSize=$!{config.regList})
                    #set($defaultValue=${config.defaultValue})
                    #set($defaultValueSize=$!{config.defaultValue})
                    #set($trigger = ${config.trigger})
                    #if(${trigger.substring(0,1)}!='[')
                        #set($trigger = "'"+ ${config.trigger}+ "'")
                    #end
                    #if($mastKey!='XHText' && $mastKey!='divider' && $mastKey!='switch')
                        #if(!$config.defaultValue && $config.defaultValue==[])
                            #set($messages='请至少选择一个')
                        #elseif(${config.defaultValue} && (${defaultValueSize} || $defaultValueSize.size()>0))
                            #set($messages='请至少选择一个')
                        #elseif($html.placeholder)
                            #set($messages=${html.placeholder})
                        #else
                            #set($messages='不能为空')
                        #end
                        #if($config.required==true|| (${listSize} && $listSize.size()>0))
                            ${vModel}: [
                            #if($config.required==true)
                                {
                                    required: true,
                                    message: '$!{messages}',
                                    trigger: ${trigger}
                                },
                            #end
                            #if($listSize.size()>0)
                                #foreach($regList in ${config.regList})
                                    {
                                        pattern: ${regList.pattern},
                                        message: '${regList.message}',
                                        trigger: ${trigger}
                                    },
                                #end
                            #end
                        ],
                        #end
                    #end
                #end


                #foreach($ChildField in ${context.columnChildren})
                    #foreach($FormMastTableModel in ${ChildField.fieLdsModelList})
                        #set($html = ${FormMastTableModel.mastTable.fieLdsModel})
                        #set($vModel = "${html.vModel}")
                        #set($config = $html.config)
                        #set($mastKey = "${config.xhKey}")
                        #set($listSize=$!{config.regList})
                        #set($defaultValue=${config.defaultValue})
                        #set($defaultValueSize=$!{config.defaultValue})
                        #set($trigger = ${config.trigger})
                        #if(${trigger.substring(0,1)}!='[')
                            #set($trigger = "'"+ ${config.trigger}+ "'")
                        #end
                        #if($mastKey!='XHText' && $mastKey!='divider' && $mastKey!='switch')
                            #if(!$config.defaultValue && $config.defaultValue==[])
                                #set($messages='请至少选择一个')
                            #elseif(${config.defaultValue} && (${defaultValueSize} || $defaultValueSize.size()>0))
                                #set($messages='请至少选择一个')
                            #elseif($html.placeholder)
                                #set($messages=${html.placeholder})
                            #else
                                #set($messages='不能为空')
                            #end
                            #if($config.required==true|| (${listSize} && $listSize.size()>0))
                                ${FormMastTableModel.vModel}: [
                                #if($config.required==true)
                                    {
                                        required: true,
                                        message: '$!{messages}',
                                        trigger: ${trigger}
                                    },
                                #end
                                #if($listSize.size()>0)
                                    #foreach($regList in ${config.regList})
                                        {
                                            pattern: ${regList.pattern},
                                            message: '${regList.message}',
                                            trigger: ${trigger}
                                        },
                                    #end
                                #end
                            ],
                            #end
                        #end
                    #end
                #end

            },
            #foreach($fieLdsModel in ${context.fields})
                #set($html = $fieLdsModel.formColumnModel.fieLdsModel)
                #set($vModel = "${html.vModel}")
                #set($config = $html.config)
                #set($xhkey = $config.xhKey)
                #if(${config.dataType}=='dictionary'||${config.dataType}=='dynamic')
                        ${vModel}Options:[],
                #elseif(${config.dataType} == "static")
                    #if($html.slot.options)
                        ${vModel}Options:${html.slot.options},
                    #elseif($html.options)
                        ${vModel}Options:${html.options},
                    #end
                #end
                #if($xhkey == "relationForm" || $xhkey == "popupSelect" || $xhkey == "popupTableSelect")
                    ${vModel}columnOptions:[#foreach($options in ${html.columnOptions}) {"label":"${options.label}","value":"${options.value}"},#end],
                #end
                #if($html.props)
                    #set($propsModel = ${html.props.props})
                       $!{vModel}Props:{"label":"${propsModel.label}","value":"${propsModel.value}" #if($!{propsModel.multiple}) ,"multiple":$propsModel.multiple #end #if($!{propsModel.children}),"children":"${propsModel.children}" #end},
                #end
            #end

            #foreach($child in ${context.children})
                #set($className = "${child.className.substring(0,1).toLowerCase()}${child.className.substring(1).toLowerCase()}")
                #foreach($fieLdsModel in ${child.childList})
                    #set($html = $fieLdsModel.fieLdsModel)
                    #set($vModel = "${html.vModel}")
                    #set($config = $html.config)
                    #set($xhkey = $config.xhKey)
                    #if(${config.dataType}=='dictionary'||${config.dataType}=='dynamic')
                        ${className}${vModel}Options:[],
                    #elseif(${config.dataType} == "static")
                        #if($html.slot.options)
                        ${className}${vModel}Options:${html.slot.options},
                        #elseif($html.options)
                        ${className}${vModel}Options:${html.options},
                        #end
                    #end
                    #if($xhkey == "relationForm" || $xhkey == "popupSelect" || $xhkey == "popupTableSelect")
                        ${className}${vModel}columnOptions:[#foreach($options in ${html.columnOptions}) {"label":"${options.label}","value":"${options.value}"},#end],
                    #end
                    #if($html.props)
                        #set($propsModel = ${html.props.props})
                                ${className}$!{vModel}Props:{"label":"${propsModel.label}","value":"${propsModel.value}" #if($!{propsModel.multiple}) ,"multiple":$propsModel.multiple #end #if($!{propsModel.children}),"children":"${propsModel.children}" #end},
                    #end
                #end
            #end
            #foreach($ChildField in ${context.columnChildren})
                #foreach($FormMastTableModel in ${ChildField.fieLdsModelList})
                    #set($html = ${FormMastTableModel.mastTable.fieLdsModel})
                    #set($xhKey = ${html.config.xhKey})
                    #set($ChildVmodel =${FormMastTableModel.vModel})
                    #set($ClDataType = ${html.config.dataType})
                    #if(${ClDataType}=='dictionary'||${ClDataType}=='dynamic')
                    ${ChildVmodel}Options:[],
                    #elseif(${ClDataType} == "static")
                        #if($html.slot.options)
                    ${ChildVmodel}Options:${html.slot.options},
                        #elseif($html.options)
                    ${ChildVmodel}Options:${html.options},
                        #end
                    #end
                    #if(${xhKey} == "relationForm" || ${xhKey} == "popupSelect" || $xhKey == "popupTableSelect")
                        ${ChildVmodel}columnOptions:[#foreach($options in ${html.columnOptions}) {"label":"${options.label}","value":"${options.value}"},#end],
                    #end
                    #if($html.props)
                        #set($propsModel = ${html.props.props})
                             $!{ChildVmodel}Props:{"label":"${propsModel.label}","value":"${propsModel.value}" #if($!{propsModel.multiple}) ,"multiple":$propsModel.multiple #end #if($!{propsModel.children}),"children":"${propsModel.children}" #end},
                    #end
                #end
            #end
            childIndex:-1,
                    isEdit:false,
                    interfaceRes: {
                #foreach($fieLdsModel in ${context.fields})
                    #set($html = $fieLdsModel.formColumnModel.fieLdsModel)
                    #set($vModel = "${html.vModel}")
                    #set($config = $html.config)
                    #set($mastKey = "${config.xhKey}")
                    #if(${vModel})
                        ${vModel}:#if($!{html.templateJson})${html.templateJson} #else [] #end,
                    #end
                #end
                #foreach($MastfieLds in ${context.mastTable})
                    #set($BeforeVmodel = $MastfieLds.formMastTableModel.vModel)
                    #set($tableName = $MastfieLds.formMastTableModel.table)
                    #set($lowTableName = "${tableName.toLowerCase()}")
                    #set($html = $MastfieLds.formMastTableModel.mastTable.fieLdsModel)
                    #set($vModel = "${html.vModel}")
                    #set($config = $html.config)
                    #if( ${vModel})
                        ${BeforeVmodel}:#if($!{html.templateJson})${html.templateJson} #else [] #end,
                    #end
                #end
                #foreach($child in ${context.children})
                    #set($className = "${child.className.substring(0,1).toLowerCase()}${child.className.substring(1).toLowerCase()}")
                    #foreach($fieLdsModel in ${child.childList})
                        #set($html = $fieLdsModel.fieLdsModel)
                        #set($vModel = "${html.vModel}")
                        #set($config = $html.config)
                        #if( ${vModel})
                            ${className}${vModel}:#if($!{config.templateJson})${config.templateJson} #else [] #end,
                        #end
                    #end
                #end
            },
        }
        },
        computed: {
          ...mapGetters(['userInfo']),
        },
        watch: {},
        created() {
            this.dataAll()
        },
        mounted() {},
        methods: {
            prev() {
                this.index--
                if (this.index === 0) {
                    this.prevDis = true
                }
                this.nextDis = false
                for (let index = 0; index < this.allList.length; index++) {
                    const element = this.allList[index];
                    if (this.index == index) {
                        this.getInfo(element.${context.pKeyName})
                    }
                }
            },
            next() {
                this.index++
                if (this.index === this.allList.length - 1) {
                    this.nextDis = true
                }
                this.prevDis = false
                for (let index = 0; index < this.allList.length; index++) {
                    const element = this.allList[index];
                    if (this.index == index) {
                        this.getInfo(element.${context.pKeyName})
                    }
                }
            },
            getInfo(id) {
                request({
                    url: '/api/${context.module}/${context.className}/'+ id,
                    method: 'get'
                }).then(res => {
                    this.dataInfo(res.data)
                });
            },
            changeData(model, index) {
                this.isEdit = false
                this.childIndex = index
                let modelAll = model.split("-");
                let faceMode = "";
                for (let i = 0; i < modelAll.length; i++) {
                    faceMode += modelAll[i];
                }
                for (let key in this.interfaceRes) {
                    if (key != faceMode) {
                        let faceReList = this.interfaceRes[key]
                        for (let i = 0; i < faceReList.length; i++) {
                            if (faceReList[i].relationField == model) {
                                let options = 'get' + key + 'Options';
                                if(this[options]){
                                    this[options]()
                                }
                                this.changeData(key, index)
                            }
                        }
                    }
                }
            },
            changeDataFormData(type, data, model,index,defaultValue) {
                if(!this.isEdit) {
                    if (type == 2) {
                        for (let i = 0; i < this.dataForm[data].length; i++) {
                            if (index == -1) {
                                this.dataForm[data][i][model] = defaultValue
                            } else if (index == i) {
                                this.dataForm[data][i][model] = defaultValue
                            }
                        }
                    } else {
                        this.dataForm[data] = defaultValue
                    }
                }
            },
            dataAll(){
                #foreach($fieLdsModel in ${context.fields})
                    #set($html = $fieLdsModel.formColumnModel.fieLdsModel)
                    #set($vModel = "${html.vModel}")
                    #set($config = $html.config)
                    #set($dataType = "${config.dataType}")
                    #if(${config.dataType}=='dictionary'||${config.dataType}=='dynamic')
                        this.get${vModel}Options();
                    #end
                #end
                #foreach($child in ${context.children})
                    #set($className = "${child.className.substring(0,1).toLowerCase()}${child.className.substring(1).toLowerCase()}")
                    #foreach($fieLdsModel in ${child.childList})
                        #set($html = $fieLdsModel.fieLdsModel)
                        #set($vModel = "${html.vModel}")
                        #set($config = $html.config)
                        #if(${config.dataType}=='dictionary'||${config.dataType}=='dynamic')
                            this.get${className}${vModel}Options();
                        #end
                    #end
                #end
                #foreach($ColumnFieldModel in ${context.mastTable})
                    #set($html =${ColumnFieldModel.formMastTableModel})
                    #set($vModel = "${html.vModel}")
                    #set($config = $html.mastTable.fieLdsModel.config)
                    #if(${config.dataType}=='dictionary'||${config.dataType}=='dynamic')
                        this.get${vModel}Options();
                    #end
                #end
            },
            #foreach($child in ${context.children})
				#set($className = "")
				#foreach($children in ${context.children})
					#if(${children.tableModel}==${child.tableModel})
						#set($className = "${children.className.substring(0,1).toLowerCase()}${children.className.substring(1).toLowerCase()}")
					#end
				#end
				${className}Exist() {
				let isOk = true;
					for(let i=0;i<this.dataForm.${className}List.length;i++){
						const e = this.dataForm.${className}List[i];
						#foreach($childListAll in ${child.childList})
							#set($html = $childListAll.fieLdsModel)
							#set($model = "${html.vModel}")
							#set($config = ${html.config})
							#set($control = "${config.xhKey}")
                            #set($takeEnd = true)
				            #set($req = $config.required)
                            #set($regular = ${config.regList})
				                    #if(${model} && ${req}==true)
				                        #if(${control}=='cascader'||${control}=='checkbox' || ${control}=='address'|| ${control} == 'comSelect' ||
				                            ${control}=='uploadImg'||${control}=='uploadFz' || ${control}=='timeRange' || ${control}=='dateRange')
				                        if (!e.${model}.length) {
                                        #elseif(${control}=='select' || ${control}=='userSelect'|| ${control}=='depSelect' || ${control}=='posSelect' || ${control}=='treeSelect'|| ${control}=='roleSelect'|| ${control}=='groupSelect'|| ${control}=='popupTableSelect')
				                            #if(${html.multiple}=='true')
				                            if (!e.${model}.length) {
				                            #else
				                            if (!e.${model}) {
				                            #end
				                        #elseif(${control}!='switch')
				                            if (!e.${model}) {
                                        #else
                                            #set($takeEnd = false)
                                        #end
										this.$message({
                                            message: '${config.label}不能为空',
                                            type: 'error',
                                            duration: 1000
                                        });
                                        isOk = false
                                        break
                                      #if($takeEnd == true)
                                      }
                                      #end
                                    #end
                            #if(${model} && ${regular})
                              if (e.${model}) {
                                var regPos = ${regular}
                                for (let i = 0; i < regPos.length; i++) {
                                  const element = regPos[i];
                                  if (element.pattern && !eval(element.pattern).test(e.${model})) {
                                    this.$message({
                                      message: element.message,
                                      type: 'error',
                                      duration: 1000
                                    });
                                    isOk = false
                                    break;
                                  }
                                }
                              }
                            #end
						#end
					}
				return isOk;
			},
			#end
            #foreach($fieLdsModel in ${context.fields})
                #set($html = $fieLdsModel.formColumnModel.fieLdsModel)
                #set($vModel = "${html.vModel}")
                #set($config = $html.config)
                #set($dataType = "${config.dataType}")
                #set($xhkey="${config.xhKey}")
                #set($defaultValue='""')
                #if($!config.valueType=='String')
                    #set($defaultValue="'$!{config.defaultValue}'")
                #elseif($!config.valueType=='undefined')
                    #set($defaultValue='""')
                #else
                    #set($defaultValue=$!{config.defaultValue})
                #end
                #if(${dataType}=='dictionary')
                    get${vModel}Options() {
                        getDictionaryDataSelector('${config.dictionaryType}').then(res => {
                            this.${vModel}Options = res.data.list
                        })
                    },
                #elseif(${dataType}=='dynamic')
                    get${vModel}Options() {
                    const index = this.childIndex
                    let templateJsonList = JSON.parse(JSON.stringify(this.interfaceRes.${vModel}))
                    for (let i = 0; i < templateJsonList.length; i++) {
                        let json = templateJsonList[i];
                        if(json.relationField){
                            let relationFieldAll = json.relationField.split("-");
                            let val = json.defaultValue;
                            if(relationFieldAll.length>1 && index>-1){
                                val = this.dataForm[relationFieldAll[0]+'List']&&this.dataForm[relationFieldAll[0]+'List'].length?this.dataForm[relationFieldAll[0]+'List'][index][relationFieldAll[1]]:''
                            }else {
                                val = this.dataForm[relationFieldAll]
                            }
                            json.defaultValue = val
                        }
                    }
                    let template ={
                        paramList:templateJsonList
                    }
                    getDataInterfaceRes('${config.propsUrl}',template).then(res => {
                        let data = res.data
                        this.${vModel}Options = data
                        this.changeDataFormData(1,'${vModel}','${vModel}',index,${defaultValue})
                    })
                    },
                #end
            #end
            #foreach($child in ${context.children})
                #set($showSummary = ${child.showSummary})
                #set($summaryField =  ${child.summaryField})
                #set($className = "${child.className.substring(0,1).toLowerCase()}${child.className.substring(1).toLowerCase()}")
                #if($showSummary)
                    get${className}Summaries(param) {
                        const summaryField = ${summaryField};
                        const { columns, data } = param;
                        const sums = [];
                        columns.forEach((column, index) => {
                            if (index === 0) {
                                sums[index] = '合计';
                                return;
                            }
                            if (!summaryField.includes(column.property)) {
                                sums[index] = '';
                                return;
                            }
                            const values = data.map(item => Number(item[column.property]));
                            if (!values.every(value => isNaN(value))) {
                                sums[index] = values.reduce((prev, curr) => {
                                    const value = Number(curr);
                                    if (!isNaN(value)) {
                                        return prev + curr;
                                    } else {
                                        return prev;
                                    }
                                }, 0);
                                const thousandsField = ${child.thousandsField};
                                if(thousandsField.includes(column.property)){
                                    sums[index] = thousandsFormat(sums[index]);
                                }
                            } else {
                                sums[index] = '';
                            }
                        });
                        return sums;
                    },
                #end
                #foreach($fieLdsModel in ${child.childList})
                    #set($html = $fieLdsModel.fieLdsModel)
                    #set($vModel = "${html.vModel}")
                    #set($config = $html.config)
                    #set($dataType = "${config.dataType}")
                    #set($xhkey="${config.xhKey}")
                    #set($defaultValue='""')
                    #if($!config.valueType=='String')
                        #set($defaultValue="'$!{config.defaultValue}'")
                    #elseif($!config.valueType=='undefined')
                        #set($defaultValue='""')
                    #else
                        #set($defaultValue=$!{config.defaultValue})
                    #end
                    #if(${dataType}=='dictionary')
                        get${className}${vModel}Options() {
                            getDictionaryDataSelector('${config.dictionaryType}').then(res => {
                                this.${className}${vModel}Options = res.data.list
                            })
                        },
                    #elseif(${dataType}=='dynamic')
                        get${className}${vModel}Options() {
                            const index = this.childIndex
                            let templateJsonList = JSON.parse(JSON.stringify(this.interfaceRes.${className}${vModel}))
                            for (let i = 0; i < templateJsonList.length; i++) {
                                let json = templateJsonList[i];
                                if(json.relationField){
                                    let relationFieldAll = json.relationField.split("-");
                                    let val = json.defaultValue;
                                    if(relationFieldAll.length>1 && index>-1){
                                        val = this.dataForm[relationFieldAll[0]+'List']&&this.dataForm[relationFieldAll[0]+'List'].length?this.dataForm[relationFieldAll[0]+'List'][index][relationFieldAll[1]]:''
                                    }else {
                                        val = this.dataForm[relationFieldAll]
                                    }
                                    json.defaultValue = val
                                }
                            }
                            let template ={
                                paramList:templateJsonList
                            }
                            getDataInterfaceRes('${config.propsUrl}',template).then(res => {
                                let data = res.data
                                this.${className}${vModel}Options = data
                                if(index==-1) return
                                this.${context.formModel}.${className}List[index].${html.vModel}Options =data
                                this.changeDataFormData(2,'${className}List','${vModel}',index,${defaultValue})
                            })
                        },
                    #end
                #end
            #end
            #foreach($ColumnFieldModel in ${context.mastTable})
                #set($html =${ColumnFieldModel.formMastTableModel})
                #set($vModel = "${html.vModel}")
                #set($config = $html.mastTable.fieLdsModel.config)
                #set($dataType = "${config.dataType}")
                #set($xhkey="${config.xhKey}")
                #set($defaultValue='""')
                #if($!config.valueType=='String')
                    #set($defaultValue="'$!{config.defaultValue}'")
                #elseif($!config.valueType=='undefined')
                    #set($defaultValue='""')
                #else
                    #set($defaultValue=$!{config.defaultValue})
                #end
                #if(${dataType}=='dictionary')
                    get${vModel}Options() {
                        getDictionaryDataSelector('${config.dictionaryType}').then(res => {
                            this.${vModel}Options = res.data.list
                        })
                    },
                #elseif(${dataType}=='dynamic')
                    get${vModel}Options() {
                        const index = this.childIndex
                        let templateJsonList = JSON.parse(JSON.stringify(this.interfaceRes.${vModel}))
                        for (let i = 0; i < templateJsonList.length; i++) {
                            let json = templateJsonList[i];
                            if(json.relationField){
                                let relationFieldAll = json.relationField.split("-");
                                let val = json.defaultValue;
                                if(relationFieldAll.length>1 && index>-1){
                                    val = this.dataForm[relationFieldAll[0]+'List']&&this.dataForm[relationFieldAll[0]+'List'].length?this.dataForm[relationFieldAll[0]+'List'][index][relationFieldAll[1]]:''
                                }else {
                                    val = this.dataForm[relationFieldAll]
                                }
                                json.defaultValue = val
                            }
                        }
                        let template ={
                            paramList:templateJsonList
                        }
                        getDataInterfaceRes('${config.propsUrl}',template).then(res => {
                            let data = res.data
                            this.${vModel}Options = data
                            this.changeDataFormData(1,'${vModel}','${vModel}',index,${defaultValue})
                        })
                    },
                #end
            #end
            #set($ref = "${context.formRef}")
            #set($l = "'")
            #set($c = "[")
            #set($p = "]")
            #if(${context.popupType}=='fullScreen')
                goBack() {
                    this.$emit('refresh')
                },
            #else
                goBack() {
                    this.visible = false
                    this.$emit('refreshDataList', true)
                },
            #end
            clearData(){
                #foreach($fieLdsModel in ${context.fields})
                    #set($html = $fieLdsModel.formColumnModel.fieLdsModel)
                    #set($vModel = "${html.vModel}")
                    #if($vModel !='')
                        #set($config = $html.config)
                        #set($mastKey = "${config.xhKey}")
                        #if($mastKey!='XHText' && $mastKey!='divider')
                            #if($!config.valueType=='String')
                               this.${context.formModel}.$!{vModel} = "$!{config.defaultValue}";
                            #elseif($!config.valueType=='undefined')
                                #if(${mastKey}=='numInput'||${mastKey}=='calculate')
                                this.${context.formModel}.$!{vModel} = undefined;
                                #else
                               this.${context.formModel}.$!{vModel} = '';
                                #end
                            #else
                                #if(${mastKey}=='treeSelect' && ${html.multiple}=='false')
                                    this.${context.formModel}.$!{vModel} = '';
                                #else
                                     this.${context.formModel}.$!{vModel} = $!{config.defaultValue};
                                #end
                            #end
                        #end
                    #end
                #end
                #foreach($child in ${context.children})
                    #set($className = "")
                    #foreach($children in ${context.children})
                        #if(${children.tableModel}==${child.tableModel})
                            #set($className = "${children.className.substring(0,1).toLowerCase()}${children.className.substring(1).toLowerCase()}")
                        #end
                    #end
                        this.${context.formModel}.${className}List=[];
                #end
                #foreach($clum in ${context.columnChildren})
                    #set($clumLowName = "${clum.tableName.toLowerCase()}")
                        #foreach($field in  ${clum.fieLdsModelList})
                            #set($fieldName = ${field.field})
                            this.${context.formModel}.${clumLowName}.$fieldName= '';
                        #end
                #end
                #foreach($mast in ${context.mastTable})
                    #set($mastField = $mast.formMastTableModel.mastTable.fieLdsModel)
                    #set($config =$mastField.config)
                    #set($mastKey = ${config.xhKey})
                    #if($mastKey!='XHText' && $mastKey!='divider')
                        #if($!config.valueType=='String')
                            this.${context.formModel}.${mast.formMastTableModel.vModel} = "$!{config.defaultValue}";
                        #elseif($!config.valueType=='undefined')
                            this.${context.formModel}.${mast.formMastTableModel.vModel} = '';
                        #else
                            #if(${mastKey}=='numInput'||${mastKey}=='calculate')
                            this.${context.formModel}.${mast.formMastTableModel.vModel} = undefined;
                            #elseif(${mastKey}=='treeSelect' && ${mastField.multiple}=='false')
                                this.${context.formModel}.${mast.formMastTableModel.vModel} = '';
                            #else
                            this.${context.formModel}.${mast.formMastTableModel.vModel} = $!{config.defaultValue};
                            #end
                        #end
                    #end
                #end
                #if($context.version)
                    this.dataForm.version= 0;
                #end
            },
            init(id,isDetail,allList) {
                this.prevDis = false
                this.nextDis = false
                this.allList = allList || []
                if (allList.length) {
                    this.index = this.allList.findIndex(item => item.${context.pKeyName} === id)
                    if (this.index == 0) {
                        this.prevDis = true
                    }
                    if (this.index == this.allList.length - 1) {
                        this.nextDis = true
                    }
                } else {
                    this.prevDis = true
                    this.nextDis = true
                }
                this.${context.formModel}.${pKeyName} = id || 0;
                this.visible = true;
                this.$nextTick(() => {
                    this.$refs${c}${l}${ref}${l}${p}.resetFields();
                    if(this.${context.formModel}.${pKeyName}){
                        this.loading = true
                        request({
                            url: '/api/${context.module}/${context.className}/'+this.${context.formModel}.${pKeyName},
                            method: 'get'
                        }).then(res => {
                            this.dataInfo(res.data)
                            this.loading = false
                        });
                    }else{
                       this.clearData()
                       this.initDefaultData()
                    }
                });
                this.$store.commit('generator/UPDATE_RELATION_DATA', {})
            },
            //初始化默认数据
            initDefaultData() {
        #foreach($eachItem in ${context.fields})
            #set($eachFiled=${eachItem.formColumnModel.fieLdsModel.vModel})
            #set($jk=${eachItem.formColumnModel.fieLdsModel.config.xhKey})
            #set($multiple=${eachItem.formColumnModel.fieLdsModel.multiple})
            #set($selectType=${eachItem.formColumnModel.fieLdsModel.selectType})
            #set($defaultCurrent=${eachItem.formColumnModel.fieLdsModel.config.defaultCurrent})
            #if($jk=='date' && $defaultCurrent == true)
                this.dataForm.${eachFiled} = new Date().getTime()
            #elseif($mastKey=='time' && ${defaultCurrent} == true)
                this.dataForm.${eachFiled} =this.xh.toDate(new Date(), "${eachItem.formColumnModel.fieLdsModel.format}")
            #elseif($jk=='depSelect' && $defaultCurrent == true)
                if(this.userInfo.departmentId != null && this.userInfo.departmentId != '') {
                #if($selectType=='all')
                #if($multiple == true)
                    this.dataForm.${eachFiled} = [this.userInfo.departmentId]
                #else
                    this.dataForm.${eachFiled} = this.userInfo.departmentId
                #end
                #else
                    let ableDepIds = ${eachItem.formColumnModel.fieLdsModel.ableDepIds}
                    if(ableDepIds instanceof Array && ableDepIds.length > 0 && ableDepIds.includes(this.userInfo.departmentId)) {
                      #if($multiple == true)
                        this.dataForm.${eachFiled} = [this.userInfo.departmentId]
                      #else
                        this.dataForm.${eachFiled} = this.userInfo.departmentId
                      #end
                    } else if(ableDepIds instanceof Array && ableDepIds.length > 0) {
                        getDefaultCurrentValueDepartmentId({departIds:ableDepIds}).then(res => {
                            if (res.data.departmentId != null && res.data.departmentId != '') {
                              #if($multiple == true)
                                this.dataForm.${eachFiled} = [this.userInfo.departmentId]
                              #else
                                this.dataForm.${eachFiled} = this.userInfo.departmentId
                              #end
                            }
                        })
                    } else {

                    }
                #end
                }
            #elseif($jk=='comSelect' && $defaultCurrent == true)
                if(this.userInfo.organizeIdList instanceof Array && this.userInfo.organizeIdList.length > 0) {
                    #if($multiple == true)
                    this.dataForm.${eachFiled} = [this.userInfo.organizeIdList]
                    #else
                    this.dataForm.${eachFiled} = this.userInfo.organizeIdList
                    #end
                }
            #elseif($jk=='userSelect' && $defaultCurrent == true && ($selectType=='all' || $selectType == 'custom'))
                #if($selectType == 'all')
                #if($multiple == true)
                this.dataForm.${eachFiled} = [this.userInfo.userId]
                #else
                this.dataForm.${eachFiled} = this.userInfo.userId
                #end
                #elseif($selectType=='custom')
                if(this.userInfo.userId != null && this.userInfo.userId != '') {
                    let ableUserIds = ${eachItem.formColumnModel.fieLdsModel.ableUserIds}
                    let ableDepIds = ${eachItem.formColumnModel.fieLdsModel.ableDepIds}
                    let ableGroupIds = ${eachItem.formColumnModel.fieLdsModel.ableGroupIds}
                    let ableRoleIds = ${eachItem.formColumnModel.fieLdsModel.ableRoleIds}
                    let ablePosIds = ${eachItem.formColumnModel.fieLdsModel.ablePosIds}
                    if (ableUserIds instanceof Array && ableUserIds.length > 0 && ableUserIds.includes(this.userInfo.userId)) {
                      #if($multiple == true)
                        this.dataForm.${eachFiled} = [this.userInfo.userId]
                      #else
                        this.dataForm.${eachFiled} = this.userInfo.userId
                      #end
                    } else if((ableUserIds instanceof Array && ableUserIds.length > 0)
                        || (ableDepIds instanceof Array && ableDepIds.length > 0)
                        || (ableGroupIds instanceof Array && ableGroupIds.length > 0)
                        || (ableRoleIds instanceof Array && ableRoleIds.length > 0)
                        || (ablePosIds instanceof Array && ablePosIds.length > 0)) {
                        getDefaultCurrentValueUserId({
                            departIds:ableDepIds,
                            groupIds:ableGroupIds,
                            roleIds:ableRoleIds,
                            userIds:ableUserIds,
                            positionIds:ablePosIds
                        }).then(res => {
                            if (res.data.userId != null && res.data.userId != '') {
                          #if($multiple == true)
                                this.dataForm.${eachFiled} = [this.userInfo.userId]
                          #else
                                this.dataForm.${eachFiled} = this.userInfo.userId
                          #end
                            }
                        })
                    } else {}
                }
                #end
            #end
        #end

        #foreach($eachChildren in ${context.columnChildren})
        #foreach($eachItem in ${eachChildren.fieLdsModelList})
            #set($eachFiled=${eachItem.vModel})
            #set($jk=${eachItem.mastTable.fieLdsModel.config.xhKey})
            #set($multiple=${eachItem.mastTable.fieLdsModel.multiple})
            #set($selectType=${eachItem.mastTable.fieLdsModel.selectType})
            #set($defaultCurrent=${eachItem.mastTable.fieLdsModel.config.defaultCurrent})
            #set($ableUserIds=${eachItem.mastTable.fieLdsModel.ableUserIds})
            #set($ableDepIds=${eachItem.mastTable.fieLdsModel.ableDepIds})
            #set($ableGroupIds=${eachItem.mastTable.fieLdsModel.ableGroupIds})
            #set($ableRoleIds=${eachItem.mastTable.fieLdsModel.ableRoleIds})
            #set($ablePosIds=${eachItem.mastTable.fieLdsModel.ablePosIds})
            #if($jk=='date' && $defaultCurrent == true)
              this.dataForm.${eachFiled} = new Date().getTime()
            #elseif($jk=='time' && $defaultCurrent == true)
                this.dataForm.${eachFiled} = this.xh.toDate(new Date(), "${eachItem.mastTable.fieLdsModel.format}")
            #elseif($jk=='depSelect' && $defaultCurrent == true)
              if(this.userInfo.departmentId != null && this.userInfo.departmentId != '') {
                  #if($selectType=='all')
                      #if($multiple == true)
                        this.dataForm.${eachFiled} = [this.userInfo.departmentId]
                      #else
                        this.dataForm.${eachFiled} = this.userInfo.departmentId
                      #end
                  #else
                    let ableDepIds = JSON.parse(${ableDepIds})
                    if(ableDepIds instanceof Array && ableDepIds.length > 0 && ableDepIds.includes(this.userInfo.departmentId)) {
                        #if($multiple == true)
                          this.dataForm.${eachFiled} = [this.userInfo.departmentId]
                        #else
                          this.dataForm.${eachFiled} = this.userInfo.departmentId
                        #end
                    } else if(ableDepIds instanceof Array && ableDepIds.length > 0) {
                      getDefaultCurrentValueDepartmentId({departIds:ableDepIds}).then(res => {
                        if (res.data.departmentId != null && res.data.departmentId != '') {
                            #if($multiple == true)
                              this.dataForm.${eachFiled} = [this.userInfo.departmentId]
                            #else
                              this.dataForm.${eachFiled} = this.userInfo.departmentId
                            #end
                        }
                      })
                    } else {

                    }
                  #end
              }
            #elseif($jk=='comSelect' && $defaultCurrent == true)
              if(this.userInfo.organizeIdList instanceof Array && this.userInfo.organizeIdList.length > 0) {
                  #if($multiple == true)
                    this.dataForm.${eachFiled} = [this.userInfo.organizeIdList]
                  #else
                    this.dataForm.${eachFiled} = this.userInfo.organizeIdList
                  #end
              }
            #elseif($jk=='userSelect' && $defaultCurrent == true && ($selectType=='all' || $selectType == 'custom'))
                #if($selectType == 'all')
                    #if($multiple == true)
                      this.dataForm.${eachFiled} = [this.userInfo.userId]
                    #else
                      this.dataForm.${eachFiled} = this.userInfo.userId
                    #end
                #elseif($selectType=='custom')
                  if(this.userInfo.userId != null && this.userInfo.userId != '') {
                    let ableUserIds = JSON.parse(${ableUserIds})
                    let ableDepIds = JSON.parse(${ableDepIds})
                    let ableGroupIds = JSON.parse(${ableGroupIds})
                    let ableRoleIds = JSON.parse(${ableRoleIds})
                    let ablePosIds = JSON.parse(${ablePosIds})
                    if (ableUserIds instanceof Array && ableUserIds.length > 0 && ableUserIds.includes(this.userInfo.userId)) {
                        #if($multiple == true)
                          this.dataForm.${eachFiled} = [this.userInfo.userId]
                        #else
                          this.dataForm.${eachFiled} = this.userInfo.userId
                        #end
                    } else if((ableUserIds instanceof Array && ableUserIds.length > 0)
                        || (ableDepIds instanceof Array && ableDepIds.length > 0)
                        || (ableGroupIds instanceof Array && ableGroupIds.length > 0)
                        || (ableRoleIds instanceof Array && ableRoleIds.length > 0)
                        || (ablePosIds instanceof Array && ablePosIds.length > 0)) {
                      getDefaultCurrentValueUserId({
                        departIds:ableDepIds,
                        groupIds:ableGroupIds,
                        roleIds:ableRoleIds,
                        userIds:ableUserIds,
                        positionIds:ablePosIds
                      }).then(res => {
                        if (res.data.userId != null && res.data.userId != '') {
                            #if($multiple == true)
                              this.dataForm.${eachFiled} = [this.userInfo.userId]
                            #else
                              this.dataForm.${eachFiled} = this.userInfo.userId
                            #end
                        }
                      })
                    } else {}
                  }
                #end
            #end
        #end
        #end
            },
            // 表单提交
            dataFormSubmit(type) {
                this.dataFormSubmitType = type ? type : 0
                this.$refs${c}${l}${ref}${l}${p}.validate((valid) => {
                    if (valid) {
                        #foreach($child in ${context.children})
                            #set($className = "")
                            #foreach($children in ${context.children})
                                #if(${children.tableModel}==${child.tableModel})
                                    #set($className = "${children.className.substring(0,1).toLowerCase()}${children.className.substring(1).toLowerCase()}")
                                #end
                            #end
                            if (!this.${className}Exist()) return
                        #end
                            this.request()
                    }
                })
            },
            #set($mag ='$message')
            request() {
                if (this.dataFormSubmitType == 2) {
                    this.continueBtnLoading = true
                } else {
                    this.btnLoading = true
                }
                let _data =this.dataList()
                if (!this.${context.formModel}.${pKeyName}) {
                    request({
                        url: '/api/${context.module}/${context.className}',
                        method: 'post',
                        data: _data
                    }).then((res) => {
                        this.$mag({
                            message: res.msg,
                            type: 'success',
                            duration: 1000,
                            onClose: () => {
                                if (this.dataFormSubmitType == 2) {
                                    this.$nextTick(() => {
                                        this.$refs['elForm'].resetFields();
                                        this.clearData()
                                        this.initDefaultData()
                                    })
                                    this.continueBtnLoading = false
                                    return
                                }
                                this.visible = false
                                this.btnLoading = false
                                this.$emit('refresh', true)
                            }
                        })
                    }).catch(()=>{
                        this.btnLoading = false
                        this.continueBtnLoading = false
                    })
                }else{
                    request({
                        url: '/api/${context.module}/${context.className}/'+this.${context.formModel}.${pKeyName},
                        method: 'PUT',
                        data: _data
                    }).then((res) => {
                        this.$mag({
                            message: res.msg,
                            type: 'success',
                            duration: 1000,
                            onClose: () => {
                                if (this.dataFormSubmitType == 2) return this.continueBtnLoading = false
                                this.visible = false
                                this.btnLoading = false
                                this.$emit('refresh', true)
                            }
                        })
                    }).catch(()=>{
                        this.btnLoading = false
                        this.continueBtnLoading = false
                    })
                }
            },
            #foreach($child in ${context.children})
                #set($className = "")
                #foreach($children in ${context.children})
                    #if(${children.tableModel}==${child.tableModel})
                        #set($className = "${children.className.substring(0,1).toLowerCase()}${children.className.substring(1).toLowerCase()}")
                    #end
                #end
                add${className}List(){
                let item = {
                #foreach($html in ${child.childList})
                    #set($vModel = "${html.fieLdsModel.vModel}")
                    #set($config = ${html.fieLdsModel.config})
                    #set($dataType = "${config.dataType}")
                    #set($defult = $html.fieLdsModel.config.defaultValue)
                    #set($defaultCurrent = $html.fieLdsModel.config.defaultCurrent)
                    #set($jk = $html.fieLdsModel.config.xhKey)
                    #set($multiple = $html.fieLdsModel.multiple)
                    #if($vModel)
                        #if($jk=='date' && $defaultCurrent == true)
                            ${vModel}:new Date().getTime(),
                        #elseif($jk=='time')
                            #if($defaultCurrent == true)
                                    ${vModel}:this.xh.toDate(new Date(), "$html.fieLdsModel.format"),
                            #else
                                    ${vModel}:'$defult',
                            #end
                        #elseif($jk=='address' && $defult =='[]')
                            ${vModel}:undefined,
                        #elseif($defult == "")
                            ${vModel}:'',
                        #elseif($defult =='[]')
                            ${vModel}:[],
                        #elseif($html.fieLdsModel.config.defaultValue)
                            ${vModel}:$defult,
                        #else
                            ${vModel}:undefined,
                        #end
                        #if(${dataType}=='dictionary' || ${dataType}=='dynamic')
                                ${vModel}Options:[],
                        #end
                    #end
                #end
                }
                this.${context.formModel}.${className}List.push(item)
                #foreach($html in ${child.childList})
                    #set($vModel = "${html.fieLdsModel.vModel}")
                    #set($config = ${html.fieLdsModel.config})
                    #set($dataType = "${config.dataType}")
                    #set($defult = $html.fieLdsModel.config.defaultValue)
                    #set($defaultCurrent = $html.fieLdsModel.config.defaultCurrent)
                    #set($jk = $html.fieLdsModel.config.xhKey)
                    #set($multiple = $html.fieLdsModel.multiple)
                    #set($selectType = $html.fieLdsModel.selectType)
                    #if($vModel)
                        #if($jk=='depSelect' && $defaultCurrent == true)
                if(this.userInfo.departmentId != null && this.userInfo.departmentId != '') {
                              #if($selectType=='all')
                    let lastItem = this.${context.formModel}.${className}List[this.${context.formModel}.${className}List.length-1]
                                  #if($multiple == true)
                    lastItem.$!{vModel} = [this.userInfo.departmentId]
                                  #else
                    lastItem.$!{vModel} = this.userInfo.departmentId
                                  #end
                    this.$set(this.${context.formModel}.${className}List, this.${context.formModel}.${className}List.length-1, lastItem)
                              #else
                    let ableDepIds = ${html.fieLdsModel.ableDepIds}
                    if(ableDepIds instanceof Array && ableDepIds.length > 0 && ableDepIds.includes(this.userInfo.departmentId)) {
                        let lastItem = this.${context.formModel}.${className}List[this.${context.formModel}.${className}List.length-1]
                                    #if($multiple == true)
                        lastItem.$!{vModel} = [this.userInfo.departmentId]
                                    #else
                        lastItem.$!{vModel} = this.userInfo.departmentId
                                    #end
                        this.$set(this.${context.formModel}.${className}List, this.${context.formModel}.${className}List.length-1, lastItem)
                    } else  if(ableDepIds instanceof Array && ableDepIds.length > 0) {
                        getDefaultCurrentValueDepartmentId({departIds:ableDepIds}).then(res => {
                            if (res.data.departmentId != null && res.data.departmentId != '') {
                                let lastItem = this.${context.formModel}.${className}List[this.${context.formModel}.${className}List.length-1]
                                        #if($multiple == true)
                                lastItem.$!{vModel} = [this.userInfo.departmentId]
                                        #else
                                lastItem.$!{vModel} = this.userInfo.departmentId
                                        #end
                                this.$set(this.${context.formModel}.${className}List, this.${context.formModel}.${className}List.length-1, lastItem)
                            }
                        })
                    } else {}
                    #end
                }
                        #elseif($jk=='comSelect' && $defaultCurrent == true)
                if(this.userInfo.organizeIdList instanceof Array && this.userInfo.organizeIdList.length > 0) {
                    let lastItem = this.${context.formModel}.${className}List[this.${context.formModel}.${className}List.length-1]
                              #if($multiple == true)
                    lastItem.$!{vModel} = [this.userInfo.organizeIdList]
                              #else
                    lastItem.$!{vModel} = this.userInfo.organizeIdList
                              #end
                    this.$set(this.${context.formModel}.${className}List, this.${context.formModel}.${className}List.length-1, lastItem)
                }
                        #elseif($jk=='userSelect' && $defaultCurrent == true && ($selectType=='all' || $selectType == 'custom'))
                if(this.userInfo.userId != null && this.userInfo.userId != '') {
                            #if($selectType=='all')
                    let lastItem = this.${context.formModel}.${className}List[this.${context.formModel}.${className}List.length-1]
                                #if($multiple == true)
                    lastItem.$!{vModel} = [this.userInfo.userId]
                                #else
                    lastItem.$!{vModel} = this.userInfo.userId
                                #end
                    this.$set(this.${context.formModel}.${className}List, this.${context.formModel}.${className}List.length-1, lastItem)
                            #elseif($selectType=='custom')
                    let ableUserIds = ${html.fieLdsModel.ableUserIds}
                    let ableDepIds = ${html.fieLdsModel.ableDepIds}
                    let ableGroupIds = ${html.fieLdsModel.ableGroupIds}
                    let ableRoleIds = ${html.fieLdsModel.ableRoleIds}
                    let ablePosIds = ${html.fieLdsModel.ablePosIds}
                    if(ableUserIds instanceof Array && ableUserIds.length > 0 && ableUserIds.includes(this.userInfo.userId)) {
                        let lastItem = this.${context.formModel}.${className}List[this.${context.formModel}.${className}List.length-1]
                                  #if($multiple == true)
                        lastItem.$!{vModel} = [this.userInfo.userId]
                                  #else
                        lastItem.$!{vModel} = this.userInfo.userId
                                  #end
                        this.$set(this.${context.formModel}.${className}List, this.${context.formModel}.${className}List.length-1, lastItem)
                    } else if((ableUserIds instanceof Array && ableUserIds.length > 0)
                        || (ableDepIds instanceof Array && ableDepIds.length > 0)
                        || (ableGroupIds instanceof Array && ableGroupIds.length > 0)
                        || (ableRoleIds instanceof Array && ableRoleIds.length > 0)
                        || (ablePosIds instanceof Array && ablePosIds.length > 0)) {
                        getDefaultCurrentValueUserId({
                          departIds:ableDepIds,
                          groupIds:ableGroupIds,
                          roleIds:ableRoleIds,
                          userIds:ableUserIds,
                          positionIds:ablePosIds
                        }).then(res => {
                            if (res.data.userId != null && res.data.userId != '') {
                                let lastItem = this.${context.formModel}.${className}List[this.${context.formModel}.${className}List.length-1]
                                  #if($multiple == true)
                                lastItem.$!{vModel} = [this.userInfo.userId]
                                  #else
                                lastItem.$!{vModel} = this.userInfo.userId
                                  #end
                                this.$set(this.${context.formModel}.${className}List, this.${context.formModel}.${className}List.length-1, lastItem)
                            }
                        })
                    } else {}
                    #end
                }
                #end
                #end
                #end
                this.childIndex=this.${context.formModel}.${className}List.length-1
                #foreach($childList in ${child.childList})
                    #set($fieLdsModel = $childList.fieLdsModel)
                    #set($vModel = "${fieLdsModel.vModel}")
                    #set($field = "${fieLdsModel.vModel}")
                    #set($config = ${fieLdsModel.config})
                    #set($dataType = "${config.dataType}")
                    #if(${dataType}=='dynamic')
                        this.get${className}${vModel}Options()
                    #end
                #end
                this.childIndex = -1
            },
                del${className}List(index) {
                    this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
                      type: 'warning'
                    }).then(() => {
                      this.${context.formModel}.${className}List.splice(index, 1);
                    }).catch(() => {
                    });
                },
            #end
            openSelectDialog(key) {
                this.currTableConf=this.addTableConf[key]
                this.currVmodel=key
                this.selectDialogVisible = true
                this.$nextTick(() => {
                    this.$refs.selectDialog.init()
                })
            },
            addForSelect(data) {
                for (let i = 0; i < data.length; i++) {
                    let item={...this.tableRows[this.currVmodel],...data[i]}
                    this.dataForm[this.currVmodel].push(item)
                }
            },
            dateTime(timeRule, timeType, timeTarget, timeValueData, dataValue) {
                let timeDataValue = null;
                let timeValue = Number(timeValueData)
                if (timeRule) {
                    if (timeType == 1) {
                        timeDataValue = timeValue
                    } else if (timeType == 2) {
                        timeDataValue = dataValue
                    } else if (timeType == 3) {
                        timeDataValue = new Date().getTime()
                    } else if (timeType == 4) {
                        let previousDate = '';
                        if (timeTarget == 1 || timeTarget == 2) {
                            previousDate = getDateDay(timeTarget, timeType, timeValue)
                            timeDataValue = new Date(previousDate).getTime()
                        } else if (timeTarget == 3) {
                            previousDate = getBeforeData(timeValue)
                            timeDataValue = new Date(previousDate).getTime()
                        } else {
                            timeDataValue = getBeforeTime(timeTarget, timeValue).getTime()
                        }
                    } else if (timeType == 5) {
                        let previousDate = '';
                        if (timeTarget == 1 || timeTarget == 2) {
                            previousDate = getDateDay(timeTarget, timeType, timeValue)
                            timeDataValue = new Date(previousDate).getTime()
                        } else if (timeTarget == 3) {
                            previousDate = getLaterData(timeValue)
                            timeDataValue = new Date(previousDate).getTime()
                        } else {
                            timeDataValue = getLaterTime(timeTarget, timeValue).getTime()
                        }
                    }
                }
                return timeDataValue;
            },
            time(timeRule, timeType, timeTarget, timeValue, formatType, dataValue) {
                let format = formatType == 'HH:mm' ? 'HH:mm:00' : formatType
                let timeDataValue = null
                if (timeRule) {
                    if (timeType == 1) {
                        timeDataValue = timeValue || '00:00:00'
                        if (timeDataValue.split(':').length == 3) {
                            timeDataValue = timeDataValue
                        } else {
                            timeDataValue = timeDataValue + ':00'
                        }
                    } else if (timeType == 2) {
                        timeDataValue = dataValue
                    } else if (timeType == 3) {
                        timeDataValue = this.xh.toDate(new Date(), format)
                    } else if (timeType == 4) {
                        let previousDate = '';
                        previousDate = getBeforeTime(timeTarget, timeValue)
                        timeDataValue = this.xh.toDate(previousDate, format)
                    } else if (timeType == 5) {
                        let previousDate = '';
                        previousDate = getLaterTime(timeTarget, timeValue)
                        timeDataValue = this.xh.toDate(previousDate, format)
                    }
                }
                return timeDataValue;
            },
            dataList(){
                var _data = JSON.parse(JSON.stringify(this.${context.formModel}));
                #foreach($fieLdsModel in ${context.fields})
                    #set($html = $fieLdsModel.formColumnModel.fieLdsModel)
                    #set($vModel = "${html.vModel}")
                    #set($config = $html.config)
                    #set($mastKey = "${config.xhKey}")
                    #if($vModel)
                        #StringifytoString($html,"_data.${vModel}","_data.${vModel}","")
                    #end
                #end
                #foreach($MastfieLds in ${context.mastTable})
                    #set($BeforeVmodel = $MastfieLds.formMastTableModel.vModel)
                    #set($tableName = $MastfieLds.formMastTableModel.table)
                    #set($lowTableName = "${tableName.toLowerCase()}")
                    #set($html = $MastfieLds.formMastTableModel.mastTable.fieLdsModel)
                    #set($vModel = "${html.vModel}")
                    #set($config = $html.config)
                    #set($mastKey = "${config.xhKey}")
                    #if(${vModel})
                        #StringifytoString($html,"_data.${lowTableName}.${vModel}","_data.${BeforeVmodel}","true")
                    #end
                #end
                #foreach($child in ${context.children})
                    #set($className = "")
                    #foreach($children in ${context.children})
                        #if(${children.tableModel}==${child.tableModel})
                            #set($className = "${children.className.substring(0,1).toLowerCase()}${children.className.substring(1).toLowerCase()}")
                        #end
                    #end
                    for(let i=0;i<_data.${className}List.length;i++){
                        var _list = _data.${className}List[i];
                        #foreach($childListAll in ${child.childList})
                            #set($html = $childListAll.fieLdsModel)
                            #set($model = "${html.vModel}")
                            #set($config = ${html.config})
                            #set($mastKey = "${config.xhKey}")
                            #if($!{model})
                                #StringifytoString($html,"_list.${model}","_list.${model}","")
                            #end
                        #end
                    }
                #end
                return _data;
            },
            dataInfo(dataAll){
                let _dataAll =dataAll
                #foreach($fieLdsModel in ${context.fields})
                    #set($html = $fieLdsModel.formColumnModel.fieLdsModel)
                    #set($vModel = "${html.vModel}")
                    #set($config = $html.config)
                    #set($mastKey = "${config.xhKey}")
                    #set($parm1 = "_dataAll.${vModel}")
                    #set($parm2 = "_dataAll.${vModel}")
                    #ParsetoArray($html,$parm1,$parm2,"")
                #end
                #foreach($MastfieLds in ${context.mastTable})
                    #set($table =  ${MastfieLds.formMastTableModel.table})
                    #set($lowTableName = "${table.toLowerCase()}")
                    #set($BeforeVmodel = $MastfieLds.formMastTableModel.vModel)
                    #set($html = $MastfieLds.formMastTableModel.mastTable.fieLdsModel)
                    #set($vModel = "${html.vModel}")
                    #set($config = $html.config)
                    #set($mastKey = "${config.xhKey}")
                    #set($parm1 = "_dataAll.${BeforeVmodel}")
                    #set($parm2 = "_dataAll.${lowTableName}.${vModel}")
                    #ParsetoArray($html,$parm1,$parm2,"true")
                #end
                #foreach($child in ${context.children})
                    #set($className = "")
                    #foreach($children in ${context.children})
                        #if(${children.tableModel}==${child.tableModel})
                            #set($className = "${children.className.substring(0,1).toLowerCase()}${children.className.substring(1).toLowerCase()}")
                        #end
                    #end
                    for(let i=0;i<_dataAll.${className}List.length;i++){
                        var _list = _dataAll.${className}List[i];
                        #foreach($childListAll in ${child.childList})
                            #set($html = $childListAll.fieLdsModel)
                            #set($model = "${html.vModel}")
                            #set($config = ${html.config})
                            #set($dataType = "${config.dataType}")
                            #set($mastKey = "${config.xhKey}")
                            #set($parm1 = "_list.${model}")
                            #set($parm2 = "_list.${model}")
                            #ParsetoArray($html,$parm1,$parm2,"")
                            #if(${dataType}=='dictionary' || ${dataType}=='dynamic')
                                ${parm1}Options=[]
                            #end
                        #end
                    }
                #end
                this.${context.formModel} = _dataAll
                this.isEdit = true
                this.dataAll()
                #foreach($child in ${context.children})
                    #set($className = "")
                    #foreach($children in ${context.children})
                        #if(${children.tableModel}==${child.tableModel})
                            #set($className = "${children.className.substring(0,1).toLowerCase()}${children.className.substring(1).toLowerCase()}")
                        #end
                    #end
                    for(let i=0;i<_dataAll.${className}List.length;i++){
                        this.childIndex = i
                        #foreach($childList in ${child.childList})
                            #set($fieLdsModel = $childList.fieLdsModel)
                            #set($vModel = "${fieLdsModel.vModel}")
                            #set($field = "${fieLdsModel.vModel}")
                            #set($config = ${fieLdsModel.config})
                            #set($dataType = "${config.dataType}")
                            #if(${dataType}=='dynamic')
                                this.get${className}${vModel}Options()
                            #end
                        #end
                    }
                #end
                this.childIndex=-1
            },
        },
    }

</script>
#macro(ParsetoArray $html,$parm1,$parm2,$isFailure)
    #set($config = $html.config)
    #set($mastKey = "${config.xhKey}")
    #if(${mastKey}=='checkbox' || ${mastKey}=='timeRange' || ${mastKey}=='dateRange' || ${mastKey}=='address' || ${mastKey}=='cascader')
        $parm1 = ${parm2} ?JSON.parse($parm2):[]
    #elseif(${mastKey}=="uploadFz" || ${mastKey}=="uploadImg" || ${mastKey}=='comSelect')
        $parm1 = ${parm2} ?JSON.parse($parm2):[]
    #elseif(${mastKey}=='select' || ${mastKey}=='userSelect'|| ${mastKey}=='depSelect' || ${mastKey}=='posSelect' || ${mastKey}=='treeSelect'|| ${mastKey}=='roleSelect'|| ${mastKey}=='groupSelect'|| ${mastKey}=='popupTableSelect' || ${mastKey}=='usersSelect')
##        #if($!{config.defaultValue} == '[]')
##            $parm1 = ${parm2} ?JSON.parse($parm2):[]
##        #else
            #if(${html.multiple}=='true')
                $parm1 = ${parm2} ?JSON.parse($parm2):[]
            #else
                #if($isFailure)
                    $parm1 = $parm2
                #end
            #end
##        #end
    #elseif(${mastKey}=="switch" || ${mastKey}=="slider")
        $parm1 = parseInt($parm2)
    #elseif($isFailure)
        $parm1 = $parm2
    #end
#end
#macro(StringifytoString $html,$parm1,$parm2,$isFailure)
    #set($config = $html.config)
    #set($mastKey = "${config.xhKey}")
    #if(${mastKey}=='checkbox' || ${mastKey}=='timeRange' || ${mastKey}=='dateRange' || ${mastKey}=='address' || ${mastKey}=='cascader')
        $parm1 = Array.isArray(${parm2})? JSON.stringify(${parm2}):'[]'
    #elseif(${mastKey}=="uploadFz" || ${mastKey}=="uploadImg"  || ${mastKey}=='comSelect')
        $parm1 = Array.isArray(${parm2})? JSON.stringify(${parm2}):'[]'
    #elseif(${mastKey}=='select' || ${mastKey}=='userSelect'|| ${mastKey}=='depSelect' || ${mastKey}=='posSelect' || ${mastKey}=='treeSelect'|| ${mastKey}=='roleSelect'|| ${mastKey}=='groupSelect'|| ${mastKey}=='popupTableSelect' || ${mastKey}=='usersSelect')
##        #if($!{config.defaultValue} == '[]')
##            $parm1 = Array.isArray(${parm2})? JSON.stringify(${parm2}):'[]'
##        #else
            #if(${html.multiple}=='true')
                $parm1 = Array.isArray(${parm2})? JSON.stringify(${parm2}):'[]'
            #else
                #if($isFailure)
                    $parm1 = $parm2
                #end
            #end
##        #end
    #elseif(${mastKey}=="switch" || ${mastKey}=="slider")
        $parm1 = parseInt($parm2)
    #elseif($isFailure)
        $parm1 = $parm2
    #end
#end
#macro(ableAll $html,$feildFullName,$flag)
    #if($html.ableDepIds)   ${feildFullName}ableDepIds: ${html.ableDepIds}, #end
    #if($html.ablePosIds)   ${feildFullName}ablePosIds: ${html.ablePosIds}, #end
    #if($html.ableUserIds)  ${feildFullName}ableUserIds: ${html.ableUserIds}, #end
    #if($html.ableRoleIds)  ${feildFullName}ableRoleIds: ${html.ableRoleIds}, #end
    #if($html.ableGroupIds) ${feildFullName}ableGroupIds: ${html.ableGroupIds}, #end
    #if($html.ableIds)      ${feildFullName}ableIds:  ${html.ableIds}, #end
#end
