package com.xinghuo.card.flow.model.dataflowrecharge;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 充值记录
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2022-11-27
 */
@Data
public class DataFlowRechargeVO {

    @Schema(description = "主键")
    private String id;

    @Schema(description = "充值类型")
    @JsonProperty("chargeType")
    private String chargeType;

    @Schema(description = "充值日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @JsonProperty("chargeDate")
    private Date chargeDate;

    @Schema(description = "资金账户")
    @JsonProperty("outAccId")
    private String outAccId;

    @Schema(description = "充值账户")
    @JsonProperty("inAccId")
    private String inAccId;

    @Schema(description = "充值金额")
    @JsonProperty("amount")
    private String amount;

    @Schema(description = "赠送金额")
    @JsonProperty("addAmount")
    private String addAmount;

    @Schema(description = "备注")
    @JsonProperty("note")
    private String note;

    @Schema(description = "创建人")
    @JsonProperty("createBy")
    private String createBy;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonProperty("createTime")
    private Date createTime;

    @Schema(description = "最后修改人")
    @JsonProperty("updateBy")
    private String updateBy;

    @Schema(description = "最后修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonProperty("updateTime")
    private Date updateTime;

}
