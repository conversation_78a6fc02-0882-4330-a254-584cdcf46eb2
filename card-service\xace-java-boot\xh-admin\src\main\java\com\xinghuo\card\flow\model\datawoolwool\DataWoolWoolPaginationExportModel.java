package com.xinghuo.card.flow.model.datawoolwool;

import com.xinghuo.common.base.model.Pagination;
import lombok.Data;

/**
 * 羊毛记录表
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2022-12-12
 */
@Data
public class DataWoolWoolPaginationExportModel extends Pagination {

    /**
     * 选择的key
     */
    private String selectKey;

    /**
     * json
     */
    private String json;

    /**
     * 数据类型
     */
    private String dataType;


    /**
     * 活动账户
     */
    private String srcAccId;

    /**
     * 活动名称
     */
    private String acitivity;

    /**
     * 物品名称
     */
    private String goods;
}
