import { defHttp } from '/@/utils/http/axios';

enum Api {
  Prefix = '/api/card/sys/paytype',
}

// 获取收支类型数据列表（分页）
export function getPayTypeList(data) {
  return defHttp.get({ url: Api.Prefix, data });
}

let cache = {
  // 缓存对象，存储数据和过期时间
  data: {},
  expires: 0,
};

// 检查缓存是否有效
function isCacheValid() {
  if (!cache.data) return false; // 如果缓存中没有数据，则缓存无效
  if (!cache.expires) return false; // 如果缓存没有设置过期时间，则缓存无效
  return new Date().getTime() < cache.expires; // 如果当前时间小于过期时间，则缓存有效
}
// 从缓存中获取数据
function getFromCache() {
  if (isCacheValid()) {
    console.log('Using cached data');
    return Promise.resolve(cache.data); // 返回缓存的数据
  }
  return null; // 缓存无效，返回null
}
export function getPayTypeSelector(data, id) {
  return defHttp.get({ url: Api.Prefix + `/Selector/${!!id ? id : '0'}`, data });
}

export function getPayTypeCacheSelector(data, id) {
  // 尝试从缓存中获取数据
  const cachedData = getFromCache();
  if (cachedData) {
    return cachedData; // 如果缓存有效，则直接返回缓存的数据
  }

  // 缓存无效或不存在，发起HTTP请求
  return defHttp.get({ url: Api.Prefix + `/Selector/${!!id ? id : '0'}`, data }).then(res => {
    // 设置缓存数据和过期时间（当前时间 + 60秒）
    cache.data = res;
    cache.expires = new Date().getTime() + 60000;
    return res; // 返回请求得到的数据
  });
}

// 创建收支类型
export function createPayType(data) {
  return defHttp.post({ url: Api.Prefix, data });
}
// 修改收支类型
export function updatePayType(data) {
  return defHttp.put({ url: Api.Prefix + '/' + data.id, data });
}
// 获取收支类型
export function getPayTypeInfo(id) {
  return defHttp.get({ url: Api.Prefix + '/' + id });
}
// 删除收支类型
export function delPayType(id) {
  return defHttp.delete({ url: Api.Prefix + '/' + id });
}
