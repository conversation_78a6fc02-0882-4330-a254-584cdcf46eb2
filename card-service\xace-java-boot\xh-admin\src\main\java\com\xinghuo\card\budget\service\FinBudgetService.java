package com.xinghuo.card.budget.service;

import com.xinghuo.card.budget.entity.FinBudgetEntity;
import com.xinghuo.card.budget.model.budget.FinBudgetForm;
import com.xinghuo.card.budget.model.budget.FinBudgetPagination;
import com.xinghuo.common.base.vo.PageListVO;

import java.util.List;

/**
 * 预算管理Service接口
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2024-06-28
 */
public interface FinBudgetService {

    /**
     * 分页查询预算列表
     *
     * @param finBudgetPagination 查询参数
     * @return 分页结果
     */
    PageListVO<FinBudgetEntity> getList(FinBudgetPagination finBudgetPagination);

    /**
     * 获取预算详情
     *
     * @param id 预算ID
     * @return 预算详情
     */
    FinBudgetEntity getInfo(String id);

    /**
     * 创建预算
     *
     * @param finBudgetForm 预算表单
     */
    void create(FinBudgetForm finBudgetForm);

    /**
     * 更新预算
     *
     * @param id 预算ID
     * @param finBudgetForm 预算表单
     */
    void update(String id, FinBudgetForm finBudgetForm);

    /**
     * 删除预算
     *
     * @param id 预算ID
     */
    void delete(String id);

    /**
     * 归档预算
     *
     * @param id 预算ID
     */
    void archive(String id);

    /**
     * 激活预算
     *
     * @param id 预算ID
     */
    void activate(String id);

    /**
     * 获取用户当前激活的预算列表
     *
     * @param userId 用户ID
     * @return 激活的预算列表
     */
    List<FinBudgetEntity> getActiveBudgetsByUserId(String userId);

    /**
     * 重新计算预算的支出金额
     *
     * @param id 预算ID
     */
    void recalculateBudgetSpent(String id);

    /**
     * 复制预算
     *
     * @param id 源预算ID
     * @param newName 新预算名称
     * @return 新预算ID
     */
    String copyBudget(String id, String newName);
}