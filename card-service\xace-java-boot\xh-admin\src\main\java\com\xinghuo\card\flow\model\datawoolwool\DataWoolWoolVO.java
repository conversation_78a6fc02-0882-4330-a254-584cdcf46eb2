package com.xinghuo.card.flow.model.datawoolwool;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 羊毛记录表
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2022-12-12
 */
@Data
public class DataWoolWoolVO {

    @Schema(description = "主键")
    private String id;

    @Schema(description = "类型")
    @JsonProperty("type")
    private String type;

    @Schema(description = "活动账户")
    @JsonProperty("srcAccId")
    private String srcAccId;

    @Schema(description = "积分")
    @JsonProperty("srcPoint")
    private String srcPoint;

    @Schema(description = "折算金额")
    @JsonProperty("amount")
    private String amount;

    @Schema(description = "活动名称")
    @JsonProperty("acitivity")
    private String acitivity;

    @Schema(description = "物品名称")
    @JsonProperty("goods")
    private String goods;

    @Schema(description = "入账日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @JsonProperty("inDate")
    private Date inDate;

    @Schema(description = "入账账户")
    @JsonProperty("inAccId")
    private String inAccId;

    @Schema(description = "入账金额")
    @JsonProperty("flowAmount")
    private String flowAmount;

    @Schema(description = "备注")
    @JsonProperty("note")
    private String note;

    @Schema(description = "创建人")
    @JsonProperty("createBy")
    private String createBy;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonProperty("createTime")
    private Date createTime;

    @Schema(description = "最后修改人")
    @JsonProperty("updateBy")
    private String updateBy;

    @Schema(description = "最后修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonProperty("updateTime")
    private Date updateTime;

}
