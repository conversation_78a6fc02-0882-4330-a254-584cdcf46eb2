package com.xinghuo.card.bill.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xinghuo.card.bill.dao.CreditCardBillMapper;
import com.xinghuo.card.bill.entity.CreditCardBillEntity;
import com.xinghuo.card.bill.entity.CreditCardConfigEntity;
import com.xinghuo.card.bill.service.CreditCardBillService;
import com.xinghuo.card.bill.service.CreditCardConfigService;
import com.xinghuo.card.flow.entity.DataFlowAccEntity;
import com.xinghuo.card.flow.service.DataFlowAccService;
import com.xinghuo.common.util.core.RandomUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 信用卡账单服务实现类
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2024-01-01
 */
@Slf4j
@Service
public class CreditCardBillServiceImpl extends ServiceImpl<CreditCardBillMapper, CreditCardBillEntity> 
        implements CreditCardBillService {

    @Autowired
    private CreditCardConfigService creditCardConfigService;

    @Autowired
    private DataFlowAccService dataFlowAccService;

    @Override
    @Transactional
    public CreditCardBillEntity generateBill(CreditCardConfigEntity cardConfig, Date billDate) {
        log.info("开始生成信用卡账单，卡片：{}，账单日期：{}", cardConfig.getCardName(), billDate);

        try {
            // 1. 检查是否已存在该期账单
            if (isBillExists(cardConfig.getAccId(), billDate)) {
                log.warn("账单已存在，跳过生成，卡片：{}", cardConfig.getCardName());
                return null;
            }

            // 2. 计算账单周期
            Date[] billPeriod = calculateBillPeriod(cardConfig.getBillDay(), billDate);
            Date startDate = billPeriod[0];
            Date endDate = billPeriod[1];

            // 3. 计算还款到期日
            Date dueDate = calculateDueDate(billDate, cardConfig.getRepaymentDay());

            // 4. 从DataFlowAcc中统计消费和还款金额
            BigDecimal consumptionAmount = calculateConsumptionAmount(cardConfig.getAccId(), startDate, endDate);
            BigDecimal paymentAmount = calculatePaymentAmount(cardConfig.getAccId(), startDate, endDate);

            // 5. 获取上期账单余额
            BigDecimal previousBalance = getPreviousBalance(cardConfig.getAccId(), startDate);

            // 6. 计算本期应还金额
            BigDecimal currentAmount = previousBalance.add(consumptionAmount).subtract(paymentAmount);

            // 7. 计算最低还款金额（通常为应还金额的10%，最低10元）
            BigDecimal minimumAmount = currentAmount.multiply(new BigDecimal("0.1"));
            if (minimumAmount.compareTo(new BigDecimal("10")) < 0) {
                minimumAmount = new BigDecimal("10");
            }

            // 8. 计算可用额度
            BigDecimal availableCredit = cardConfig.getCreditLimit().subtract(currentAmount);

            // 9. 创建账单实体
            CreditCardBillEntity bill = new CreditCardBillEntity();
            bill.setId(RandomUtil.snowId());
            bill.setUserId(cardConfig.getUserId());
            bill.setCardAccId(cardConfig.getAccId());
            bill.setBankCode(cardConfig.getBankCode());
            bill.setBankName(cardConfig.getBankName());
            bill.setCardNumber(cardConfig.getCardNumber());
            bill.setBillStartDate(startDate);
            bill.setBillEndDate(endDate);
            bill.setDueDate(dueDate);
            bill.setCurrentAmount(currentAmount);
            bill.setMinimumAmount(minimumAmount);
            bill.setConsumptionAmount(consumptionAmount);
            bill.setPaymentAmount(paymentAmount);
            bill.setAvailableCredit(availableCredit);
            bill.setCreditLimit(cardConfig.getCreditLimit());
            bill.setBillStatus(1); // 已出账单
            bill.setPaymentStatus(0); // 未还款
            bill.setBillDay(cardConfig.getBillDay());
            bill.setRepaymentDay(cardConfig.getRepaymentDay());
            bill.setInterestFreeDays(cardConfig.getInterestFreeDays());
            bill.setAutoGenerate(true);
            bill.setPreviousBalance(previousBalance);
            bill.setCreateTime(new Date());
            bill.setDeleteFlag(0);

            // 10. 保存账单
            this.save(bill);

            // 11. 更新DataFlowAcc中的billId
            updateFlowAccBillId(cardConfig.getAccId(), startDate, endDate, bill.getId());

            log.info("信用卡账单生成成功，账单ID：{}，应还金额：{}", bill.getId(), currentAmount);
            return bill;

        } catch (Exception e) {
            log.error("生成信用卡账单失败，卡片：{}", cardConfig.getCardName(), e);
            throw new RuntimeException("生成账单失败：" + e.getMessage());
        }
    }

    @Override
    @Scheduled(cron = "0 0 2 * * ?") // 每天凌晨2点执行
    public void checkAndGenerateBills() {
        log.info("开始检查并生成信用卡账单");

        try {
            // 获取所有启用自动生成账单的信用卡配置
            List<CreditCardConfigEntity> configs = creditCardConfigService.getAutoGenerateConfigs();

            Date today = new Date();
            Date yesterday = getYesterday(today);
            int yesterdayDay = getCurrentDay(yesterday);

            int generatedCount = 0;
            int failedCount = 0;

            for (CreditCardConfigEntity config : configs) {
                // 检查昨天是否为账单日，今天生成账单
                if (config.getBillDay().equals(yesterdayDay)) {
                    try {
                        // 使用昨天作为账单日期生成账单
                        CreditCardBillEntity bill = generateBill(config, yesterday);
                        if (bill != null) {
                            generatedCount++;
                            log.info("成功生成账单，卡片：{}，账单ID：{}", config.getCardName(), bill.getId());
                        }
                    } catch (Exception e) {
                        failedCount++;
                        log.error("生成账单失败，卡片：{}", config.getCardName(), e);
                    }
                }
            }

            log.info("账单生成检查完成，成功生成：{}个，失败：{}个", generatedCount, failedCount);
        } catch (Exception e) {
            log.error("账单生成检查失败", e);
        }
    }

    /**
     * 手动触发账单生成（用于补生成或测试）
     */
    public void manualGenerateBills() {
        log.info("手动触发账单生成");
        checkAndGenerateBills();
    }

    /**
     * 为指定日期生成账单（用于补生成历史账单）
     */
    public void generateBillsForDate(Date targetDate) {
        log.info("为指定日期生成账单：{}", targetDate);

        try {
            List<CreditCardConfigEntity> configs = creditCardConfigService.getAutoGenerateConfigs();
            int targetDay = getCurrentDay(targetDate);

            for (CreditCardConfigEntity config : configs) {
                if (config.getBillDay().equals(targetDay)) {
                    try {
                        generateBill(config, targetDate);
                        log.info("成功为日期{}生成账单，卡片：{}", targetDate, config.getCardName());
                    } catch (Exception e) {
                        log.error("为日期{}生成账单失败，卡片：{}", targetDate, config.getCardName(), e);
                    }
                }
            }
        } catch (Exception e) {
            log.error("为指定日期生成账单失败", e);
        }
    }

    @Override
    public BigDecimal calculateConsumptionAmount(String accId, Date startDate, Date endDate) {
        // 查询DataFlowAcc中的支出金额（pay字段）
        QueryWrapper<DataFlowAccEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(DataFlowAccEntity::getAccId, accId)
                .ge(DataFlowAccEntity::getFlowDate, startDate)
                .le(DataFlowAccEntity::getFlowDate, endDate)
                .isNotNull(DataFlowAccEntity::getPay)
                .gt(DataFlowAccEntity::getPay, BigDecimal.ZERO);

        List<DataFlowAccEntity> flowList = dataFlowAccService.list(queryWrapper);
        return flowList.stream()
                .map(DataFlowAccEntity::getPay)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    @Override
    public BigDecimal calculatePaymentAmount(String accId, Date startDate, Date endDate) {
        // 查询DataFlowAcc中的收入金额（income字段）
        QueryWrapper<DataFlowAccEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(DataFlowAccEntity::getAccId, accId)
                .ge(DataFlowAccEntity::getFlowDate, startDate)
                .le(DataFlowAccEntity::getFlowDate, endDate)
                .isNotNull(DataFlowAccEntity::getIncome)
                .gt(DataFlowAccEntity::getIncome, BigDecimal.ZERO);

        List<DataFlowAccEntity> flowList = dataFlowAccService.list(queryWrapper);
        return flowList.stream()
                .map(DataFlowAccEntity::getIncome)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    @Override
    public CreditCardBillEntity getLatestBill(String cardAccId) {
        QueryWrapper<CreditCardBillEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(CreditCardBillEntity::getCardAccId, cardAccId)
                .eq(CreditCardBillEntity::getDeleteFlag, 0)
                .orderByDesc(CreditCardBillEntity::getBillEndDate)
                .last("LIMIT 1");

        return this.getOne(queryWrapper);
    }

    @Override
    public List<CreditCardBillEntity> getHistoryBills(String cardAccId, int limit) {
        QueryWrapper<CreditCardBillEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(CreditCardBillEntity::getCardAccId, cardAccId)
                .eq(CreditCardBillEntity::getDeleteFlag, 0)
                .orderByDesc(CreditCardBillEntity::getBillEndDate)
                .last("LIMIT " + limit);

        return this.list(queryWrapper);
    }

    @Override
    @Transactional
    public boolean updatePaymentStatus(String billId, BigDecimal paymentAmount) {
        CreditCardBillEntity bill = this.getById(billId);
        if (bill == null) {
            return false;
        }

        BigDecimal totalPayment = bill.getPaymentAmount().add(paymentAmount);
        bill.setPaymentAmount(totalPayment);

        // 更新还款状态
        if (totalPayment.compareTo(bill.getCurrentAmount()) >= 0) {
            bill.setPaymentStatus(2); // 已还清
        } else if (totalPayment.compareTo(BigDecimal.ZERO) > 0) {
            bill.setPaymentStatus(1); // 部分还款
        }

        return this.updateById(bill);
    }

    @Override
    public boolean isOverdue(CreditCardBillEntity bill) {
        if (bill.getDueDate() == null) {
            return false;
        }
        
        Date today = new Date();
        return today.after(bill.getDueDate()) && bill.getPaymentStatus() != 2;
    }

    @Override
    public List<CreditCardBillEntity> getBillsNeedReminder() {
        // 获取需要提醒的账单（距离还款日3天内且未还清）
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DAY_OF_MONTH, 3);
        Date reminderDate = cal.getTime();

        QueryWrapper<CreditCardBillEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(CreditCardBillEntity::getDeleteFlag, 0)
                .ne(CreditCardBillEntity::getPaymentStatus, 2)
                .le(CreditCardBillEntity::getDueDate, reminderDate)
                .ge(CreditCardBillEntity::getDueDate, new Date());

        return this.list(queryWrapper);
    }

    @Override
    public Date[] calculateBillPeriod(int billDay, Date currentDate) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(currentDate);
        
        // 账单周期结束日期就是账单日
        cal.set(Calendar.DAY_OF_MONTH, billDay);
        Date endDate = cal.getTime();
        
        // 账单周期开始日期是上个月的账单日+1
        cal.add(Calendar.MONTH, -1);
        cal.add(Calendar.DAY_OF_MONTH, 1);
        Date startDate = cal.getTime();
        
        return new Date[]{startDate, endDate};
    }

    @Override
    public Date calculateDueDate(Date billDate, int repaymentDay) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(billDate);
        
        // 还款日在下个月
        cal.add(Calendar.MONTH, 1);
        cal.set(Calendar.DAY_OF_MONTH, repaymentDay);
        
        return cal.getTime();
    }

    /**
     * 检查账单是否已存在
     */
    private boolean isBillExists(String accId, Date billDate) {
        Date[] period = calculateBillPeriod(getCurrentDay(billDate), billDate);
        
        QueryWrapper<CreditCardBillEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(CreditCardBillEntity::getCardAccId, accId)
                .eq(CreditCardBillEntity::getBillStartDate, period[0])
                .eq(CreditCardBillEntity::getBillEndDate, period[1])
                .eq(CreditCardBillEntity::getDeleteFlag, 0);

        return this.count(queryWrapper) > 0;
    }

    /**
     * 获取上期账单余额
     */
    private BigDecimal getPreviousBalance(String accId, Date currentStartDate) {
        QueryWrapper<CreditCardBillEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(CreditCardBillEntity::getCardAccId, accId)
                .lt(CreditCardBillEntity::getBillEndDate, currentStartDate)
                .eq(CreditCardBillEntity::getDeleteFlag, 0)
                .orderByDesc(CreditCardBillEntity::getBillEndDate)
                .last("LIMIT 1");

        CreditCardBillEntity previousBill = this.getOne(queryWrapper);
        if (previousBill != null) {
            return previousBill.getCurrentAmount().subtract(previousBill.getPaymentAmount());
        }
        
        return BigDecimal.ZERO;
    }

    /**
     * 更新流水记录的账单ID
     */
    private void updateFlowAccBillId(String accId, Date startDate, Date endDate, String billId) {
        QueryWrapper<DataFlowAccEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(DataFlowAccEntity::getAccId, accId)
                .ge(DataFlowAccEntity::getFlowDate, startDate)
                .le(DataFlowAccEntity::getFlowDate, endDate);

        List<DataFlowAccEntity> flowList = dataFlowAccService.list(queryWrapper);
        for (DataFlowAccEntity flow : flowList) {
            flow.setBillId(billId);
            dataFlowAccService.updateById(flow);
        }
    }

    /**
     * 获取日期的天数
     */
    private int getCurrentDay(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        return cal.get(Calendar.DAY_OF_MONTH);
    }

    /**
     * 获取昨天的日期
     */
    private Date getYesterday(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.DAY_OF_MONTH, -1);
        return cal.getTime();
    }
}
