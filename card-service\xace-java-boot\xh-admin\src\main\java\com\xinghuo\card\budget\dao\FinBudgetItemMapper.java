package com.xinghuo.card.budget.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xinghuo.card.budget.entity.FinBudgetItemEntity;
import com.xinghuo.card.budget.model.budgetitem.FinBudgetItemPagination;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 预算子项表
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2024-06-28
 */
@Mapper
public interface FinBudgetItemMapper extends BaseMapper<FinBudgetItemEntity> {

    /**
     * 列表查询
     *
     * @param page 分页参数
     * @param finBudgetItemPagination 查询参数
     * @return 预算子项列表
     */
    List<FinBudgetItemEntity> getList(Page<FinBudgetItemEntity> page, @Param("finBudgetItemPagination") FinBudgetItemPagination finBudgetItemPagination);

    /**
     * 根据预算ID获取子项列表
     *
     * @param budgetId 预算ID
     * @return 预算子项列表
     */
    List<FinBudgetItemEntity> getItemsByBudgetId(@Param("budgetId") String budgetId);

    /**
     * 获取需要预警的预算项
     *
     * @param userId 用户ID
     * @return 需要预警的预算项列表
     */
    List<FinBudgetItemEntity> getItemsNeedAlert(@Param("userId") String userId);

    /**
     * 批量更新预算项的当前支出金额
     *
     * @param budgetId 预算ID
     */
    void updateCurrentSpentByBudgetId(@Param("budgetId") String budgetId);
}