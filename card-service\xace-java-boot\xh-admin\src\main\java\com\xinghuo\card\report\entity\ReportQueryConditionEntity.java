package com.xinghuo.card.report.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xinghuo.common.base.entity.BaseEntityV2;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 报表查询条件实体类
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("report_query_condition")
public class ReportQueryConditionEntity extends BaseEntityV2.CUBaseEntityV2<String> {

    /**
     * 条件名称
     */
    @TableField("condition_name")
    private String conditionName;

    /**
     * 条件描述
     */
    @TableField("condition_desc")
    private String conditionDesc;

    /**
     * 报表类型：1-收支统计，2-分类统计，3-趋势分析，4-卡片统计
     */
    @TableField("report_type")
    private Integer reportType;

    /**
     * 查询条件JSON（存储完整的查询参数）
     */
    @TableField("query_conditions")
    private String queryConditions;

    /**
     * 开始日期
     */
    @TableField("start_date")
    private Date startDate;

    /**
     * 结束日期
     */
    @TableField("end_date")
    private Date endDate;

    /**
     * 账户ID列表（JSON格式）
     */
    @TableField("acc_ids")
    private String accIds;

    /**
     * 收支类型列表（JSON格式）
     */
    @TableField("flow_types")
    private String flowTypes;

    /**
     * 交易类型：1-收入，2-支出，3-转账，0-全部
     */
    @TableField("trans_type")
    private Integer transType;

    /**
     * 关键词（用于备注搜索）
     */
    @TableField("keywords")
    private String keywords;

    /**
     * 金额范围-最小值
     */
    @TableField("min_amount")
    private java.math.BigDecimal minAmount;

    /**
     * 金额范围-最大值
     */
    @TableField("max_amount")
    private java.math.BigDecimal maxAmount;

    /**
     * 是否公共条件：0-私有，1-公共
     */
    @TableField("is_public")
    private Boolean isPublic;

    /**
     * 使用次数
     */
    @TableField("use_count")
    private Integer useCount;

    /**
     * 最后使用时间
     */
    @TableField("last_use_time")
    private Date lastUseTime;

    /**
     * 是否收藏：0-否，1-是
     */
    @TableField("is_favorite")
    private Boolean isFavorite;

    /**
     * 排序字段
     */
    @TableField("list_order")
    private Integer listOrder;

    /**
     * 备注
     */
    @TableField("note")
    private String note;
}
