#set($serviceName = "${table.serviceName.substring(0,1).toLowerCase()}${table.serviceName.substring(1)}")
#set($Name = "${genInfo.className.substring(0,1).toUpperCase()}${genInfo.className.substring(1)}")
#set($name = "${genInfo.className.substring(0,1).toLowerCase()}${genInfo.className.substring(1)}")

#set($searchListSize =$!{searchList})
package ${package.Service};
#foreach($subfield in ${child})
#set($ChildName="${subfield.className.substring(0,1).toUpperCase()}${subfield.className.substring(1)}")
#set($childName="${subfield.className.substring(0,1).toLowerCase()}${subfield.className.substring(1)}")
import ${package.Entity}.${ChildName}Entity;
import ${package.Service}.${ChildName}Service;
#end

import ${package.Entity}.${table.entityName};
import ${superServiceClassPackage};

#if(${main})
#set($packName = "${name.toLowerCase()}")
#foreach($child in ${columnChildren})
import ${package.Entity}.${child.modelUpName}Entity;
#end
#else
#set($packName = "${mainModelName.toLowerCase()}")
#end
import ${modulePackageName}.model.${packName}.*;
import java.util.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
/**
 *
 * ${genInfo.description}
 * 版本： ${genInfo.version}
 * 版权： ${genInfo.copyright}
 * 作者： ${genInfo.createUser}
 * 日期： ${genInfo.createDate}
 */
public interface ${table.serviceName} extends ${superServiceClass}<${table.entityName}> {

#if(${main})
    #if(${pKeyName.substring(0,2).toLowerCase()}=='f_')
        #set($peimaryKeyname = "${pKeyName.substring(2,3).toLowerCase()}${pKeyName.substring(3)}")
    #else
        #set($peimaryKeyname = "${pKeyName.substring(0,1).toLowerCase()}${pKeyName.substring(1).toLowerCase()}")
    #end

    List<${table.entityName}> getList(${Name}Pagination ${name}Pagination);

    List<${table.entityName}> getTypeList(${Name}Pagination ${name}Pagination,String dataType);

    #foreach($grid in ${genInfo.indexGridEntry})
      List<${table.entityName}> get${grid.className}List(String ${pKeyName.toLowerCase()});
    #end

    ${table.entityName} getInfo(String ${peimaryKeyname});

    void delete(${table.entityName} entity);

    void create(${table.entityName} entity);

    boolean update(String ${peimaryKeyname}, ${table.entityName} entity);

//  子表方法
    #foreach($grid in ${child})
     List<${grid.className}Entity> get${grid.className}List(String id,${Name}Pagination ${name}Pagination);
        List<${grid.className}Entity> get${grid.className}List(String id);
    #end

    //列表子表数据方法
    #foreach($child in ${columnChildren})
        ${child.modelUpName}Entity get${child.modelUpName}(String id);

    #end

    //验证表单
	String checkForm(${Name}Form form,int i);
#else
    #set($childWrapperName = "${modelName.substring(0,1).toLowerCase()}${modelName.substring(1)}")
	QueryWrapper<${table.entityName}> getChild(${mainModelName}Pagination pagination,QueryWrapper<${table.entityName}> ${childWrapperName}QueryWrapper);
#end
}
