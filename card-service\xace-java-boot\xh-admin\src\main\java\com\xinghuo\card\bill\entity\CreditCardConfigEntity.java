package com.xinghuo.card.bill.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 信用卡配置实体类
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2024-01-01
 */
@Data
@TableName("credit_card_config")
public class CreditCardConfigEntity implements Serializable {

    /**
     * 主键ID
     */
    @TableId("ID")
    private String id;

    /**
     * 用户ID
     */
    @TableField("USER_ID")
    private String userId;

    /**
     * 关联账户ID（对应DataAccEntity的ID）
     */
    @TableField("ACC_ID")
    private String accId;

    /**
     * 信用卡名称
     */
    @TableField("CARD_NAME")
    private String cardName;

    /**
     * 银行代码
     */
    @TableField("BANK_CODE")
    private String bankCode;

    /**
     * 银行名称
     */
    @TableField("BANK_NAME")
    private String bankName;

    /**
     * 信用卡号（脱敏显示）
     */
    @TableField("CARD_NUMBER")
    private String cardNumber;

    /**
     * 信用卡号后四位
     */
    @TableField("CARD_LAST_FOUR")
    private String cardLastFour;

    /**
     * 信用额度
     */
    @TableField(value = "CREDIT_LIMIT", updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal creditLimit;

    /**
     * 账单日（每月几号出账单，1-31）
     */
    @TableField("BILL_DAY")
    private Integer billDay;

    /**
     * 还款日（每月几号还款，1-31）
     */
    @TableField("REPAYMENT_DAY")
    private Integer repaymentDay;

    /**
     * 免息期天数
     */
    @TableField("INTEREST_FREE_DAYS")
    private Integer interestFreeDays;

    /**
     * 是否启用自动生成账单：0-否，1-是
     */
    @TableField("AUTO_GENERATE_BILL")
    private Boolean autoGenerateBill;

    /**
     * 是否启用还款提醒：0-否，1-是
     */
    @TableField("PAYMENT_REMINDER")
    private Boolean paymentReminder;

    /**
     * 提醒提前天数
     */
    @TableField("REMINDER_DAYS")
    private Integer reminderDays;

    /**
     * 卡片状态：0-停用，1-正常，2-冻结，3-注销
     */
    @TableField("CARD_STATUS")
    private Integer cardStatus;

    /**
     * 卡片类型：1-普通卡，2-金卡，3-白金卡，4-钻石卡
     */
    @TableField("CARD_TYPE")
    private Integer cardType;

    /**
     * 开卡日期
     */
    @TableField("OPEN_DATE")
    private Date openDate;

    /**
     * 到期日期
     */
    @TableField("EXPIRE_DATE")
    private Date expireDate;

    /**
     * 年费
     */
    @TableField(value = "ANNUAL_FEE", updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal annualFee;

    /**
     * 是否免年费：0-否，1-是
     */
    @TableField("FREE_ANNUAL_FEE")
    private Boolean freeAnnualFee;

    /**
     * 备注
     */
    @TableField("NOTE")
    private String note;

    /**
     * 创建人
     */
    @TableField("CREATE_BY")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField("CREATE_TIME")
    private Date createTime;

    /**
     * 最后修改人
     */
    @TableField("UPDATE_BY")
    private String updateBy;

    /**
     * 最后修改时间
     */
    @TableField("UPDATE_TIME")
    private Date updateTime;

    /**
     * 删除标记：0-正常，1-已删除
     */
    @TableField("DELETE_FLAG")
    private Integer deleteFlag;

    /**
     * 排序字段
     */
    @TableField("LIST_ORDER")
    private Integer listOrder;
}
