#set($modelPath = "model."+${context.modelPathName})
#set($moduleName = "${context.genInfo.className}")
package ${context.package}.${modelPath};

import lombok.Data;
import java.util.List;
import java.util.Date;
import java.math.BigDecimal;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.alibaba.fastjson.annotation.JSONField;

/**
 *
 * ${context.genInfo.description}
 * 版本： ${context.genInfo.version}
 * 版权: ${context.genInfo.copyright}
 * 作者： ${context.genInfo.createUser}
 * 日期： ${context.genInfo.createDate}
 */
@Data
public class ${context.className}Model  {

    #foreach($html in ${context.children.childList})
        #set($fieLdsModel = ${html.fieLdsModel})
        #set($config = ${fieLdsModel.config})
        #set($xhkey = ${config.xhKey})
        #set($vModel = "${fieLdsModel.vModel}")
        #set($fieldName=${config.label})
        #if($vModel)
        #if(${xhkey}=="date")
    /** ${fieldName} **/
    @JsonProperty("${vModel}")
    private Long  ${vModel};

        #elseif(${xhkey}=='numInput')
    /** ${fieldName} **/
    @JsonProperty("${vModel}")
    private Double ${vModel};

        #elseif(${xhkey}=='switch')
    /** ${fieldName} **/
    @JsonProperty("${vModel}")
    private Integer ${vModel};

        #elseif(${xhkey}=='rate')
    /** ${fieldName} **/
    @JsonProperty("${vModel}")
    private Integer ${vModel};

        #elseif(${xhkey}=='modifyTime')
    /** ${fieldName} **/
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonProperty("${vModel}")
    private Date  ${vModel};

        #elseif(${xhkey}=='createTime')
    /** ${fieldName} **/
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonProperty("${vModel}")
    private Date  ${vModel};

        #else
    /** ${fieldName} **/
    @JsonProperty("${vModel}")
    private String ${vModel};

        #end
        #end
    #end

}
