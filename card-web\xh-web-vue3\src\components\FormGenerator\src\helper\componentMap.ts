import type { GenItem } from '../types/genItem';
import { defaultOnLoadFunc, defaultBeforeSubmitFunc, defaultWdigetFunc } from './funcsStr';
// 表单属性【右面板】
export const formConf = {
  formRef: 'formRef',
  formModel: 'dataForm',
  size: 'default', //large,default,small
  labelPosition: 'right',
  labelWidth: 100,
  formRules: 'rules',
  popupType: 'general',
  generalWidth: '600px',
  fullScreenWidth: '100%',
  drawerWidth: '600px',
  gutter: 15,
  disabled: false,
  span: 24,
  colon: false,
  hasCancelBtn: true,
  cancelButtonText: '取消',
  hasConfirmBtn: true,
  confirmButtonText: '确定',
  hasPrintBtn: false,
  printButtonText: '打印',
  primaryKeyPolicy: 1,
  concurrencyLock: false,
  logicalDelete: false,
  printId: '',
  formStyle: '',
  classNames: [],
  className: [],
  classJson: '',
  funcs: {
    onLoad: defaultOnLoadFunc,
    beforeSubmit: defaultBeforeSubmitFunc,
    afterSubmit: defaultOnLoadFunc,
  },
  idGlobal: 100,
  fields: [],
  // TODO 表单-数据接口
  // detailInterface: {
  //   interfaceId: '',
  //   interfaceName: '',
  // },
  // detailType: '0',
};

// 基础控件 【左面板】
export const inputComponents: GenItem[] = [
  {
    __config__: {
      xhKey: 'input',
      label: '单行输入',
      tipLabel: '',
      labelWidth: undefined,
      showLabel: true,
      tag: 'XhInput',
      tagIcon: 'icon-ym icon-ym-generator-input',
      className: [],
      defaultValue: undefined,
      required: false,
      layout: 'colFormItem',
      span: 12,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      tableName: '',
      noShow: false,
      regList: [],
      trigger: 'blur',
    },
    on: {
      change: defaultWdigetFunc,
      blur: defaultWdigetFunc,
    },
    style: { width: '100%' },
    placeholder: '请输入',
    clearable: true,
    addonBefore: '',
    addonAfter: '',
    prefixIcon: '',
    suffixIcon: '',
    maxlength: null,
    showPassword: false,
    readonly: false,
    disabled: false,
  },
  {
    __config__: {
      xhKey: 'textarea',
      label: '多行输入',
      tipLabel: '',
      labelWidth: undefined,
      showLabel: true,
      tag: 'XhTextarea',
      tagIcon: 'icon-ym icon-ym-generator-textarea',
      className: [],
      defaultValue: undefined,
      required: false,
      layout: 'colFormItem',
      span: 24,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      tableName: '',
      noShow: false,
      regList: [],
      trigger: 'blur',
    },
    on: {
      change: defaultWdigetFunc,
      blur: defaultWdigetFunc,
    },
    style: { width: '100%' },
    placeholder: '请输入',
    autoSize: {
      minRows: 4,
      maxRows: 4,
    },
    clearable: true,
    maxlength: null,
    readonly: false,
    disabled: false,
  },
  {
    __config__: {
      xhKey: 'inputNumber',
      label: '数字输入',
      tipLabel: '',
      showLabel: true,
      labelWidth: undefined,
      tag: 'XhInputNumber',
      tagIcon: 'icon-ym icon-ym-generator-number',
      className: [],
      defaultValue: undefined,
      required: false,
      layout: 'colFormItem',
      span: 12,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      tableName: '',
      noShow: false,
      regList: [],
      trigger: ['blur', 'change'],
    },
    on: {
      change: defaultWdigetFunc,
      blur: defaultWdigetFunc,
    },
    style: { width: '100%' },
    placeholder: '请输入',
    min: undefined,
    max: undefined,
    controls: false,
    addonBefore: '',
    addonAfter: '',
    thousands: false,
    isAmountChinese: false,
    step: 1,
    precision: undefined,
    disabled: false,
  },
  {
    __config__: {
      xhKey: 'switch',
      label: '开关',
      tipLabel: '',
      labelWidth: undefined,
      showLabel: true,
      tag: 'XhSwitch',
      tagIcon: 'icon-ym icon-ym-generator-switch',
      className: [],
      defaultValue: 0,
      required: false,
      layout: 'colFormItem',
      span: 12,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      tableName: '',
      noShow: false,
      regList: [],
      trigger: 'change',
    },
    on: {
      change: defaultWdigetFunc,
    },
    disabled: false,
    activeTxt: '开',
    inactiveTxt: '关',
    activeValue: 1,
    inactiveValue: 0,
  },
  {
    __config__: {
      xhKey: 'radio',
      label: '单选框组',
      tipLabel: '',
      labelWidth: undefined,
      showLabel: true,
      tag: 'XhRadio',
      tagIcon: 'icon-ym icon-ym-generator-radio',
      className: [],
      defaultValue: undefined,
      required: false,
      layout: 'colFormItem',
      span: 12,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      tableName: '',
      noShow: false,
      regList: [],
      trigger: 'change',
      dataType: 'static',
      dictionaryType: '',
      propsUrl: '',
      propsName: '',
      templateJson: [],
    },
    on: {
      change: defaultWdigetFunc,
    },
    style: { width: '100%' },
    options: [
      {
        fullName: '选项一',
        id: '1',
      },
      {
        fullName: '选项二',
        id: '2',
      },
    ],
    props: {
      label: 'fullName',
      value: 'id',
    },
    direction: 'horizontal',
    optionType: 'default',
    buttonStyle: 'solid',
    size: 'default',
    disabled: false,
  },
  {
    __config__: {
      xhKey: 'checkbox',
      label: '多选框组',
      tipLabel: '',
      labelWidth: undefined,
      showLabel: true,
      tag: 'XhCheckbox',
      tagIcon: 'icon-ym icon-ym-generator-checkbox',
      className: [],
      defaultValue: [],
      required: false,
      layout: 'colFormItem',
      span: 12,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      tableName: '',
      noShow: false,
      regList: [],
      trigger: 'change',
      dataType: 'static',
      dictionaryType: '',
      propsUrl: '',
      propsName: '',
      templateJson: [],
    },
    on: {
      change: defaultWdigetFunc,
    },
    style: { width: '100%' },
    options: [
      {
        fullName: '选项一',
        id: '1',
      },
      {
        fullName: '选项二',
        id: '2',
      },
    ],
    props: {
      label: 'fullName',
      value: 'id',
    },
    direction: 'horizontal',
    disabled: false,
  },
  {
    __config__: {
      xhKey: 'select',
      label: '下拉选择',
      tipLabel: '',
      labelWidth: undefined,
      showLabel: true,
      tag: 'XhSelect',
      tagIcon: 'icon-ym icon-ym-generator-select',
      className: [],
      defaultValue: '',
      required: false,
      layout: 'colFormItem',
      span: 12,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      tableName: '',
      noShow: false,
      regList: [],
      trigger: 'change',
      dataType: 'static',
      dictionaryType: '',
      propsUrl: '',
      propsName: '',
      templateJson: [],
    },
    on: {
      change: defaultWdigetFunc,
    },
    style: { width: '100%' },
    options: [
      {
        fullName: '选项一',
        id: '1',
      },
      {
        fullName: '选项二',
        id: '2',
      },
    ],
    props: {
      label: 'fullName',
      value: 'id',
    },
    placeholder: '请选择',
    clearable: true,
    disabled: false,
    filterable: false,
    multiple: false,
  },
  {
    __config__: {
      xhKey: 'cascader',
      label: '级联选择',
      tipLabel: '',
      labelWidth: undefined,
      showLabel: true,
      tag: 'XhCascader',
      tagIcon: 'icon-ym icon-ym-generator-cascader',
      className: [],
      defaultValue: [],
      required: false,
      layout: 'colFormItem',
      span: 12,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      tableName: '',
      noShow: false,
      regList: [],
      trigger: 'change',
      dataType: 'static',
      propsUrl: '',
      propsName: '',
      templateJson: [],
      dictionaryType: '',
    },
    on: {
      change: defaultWdigetFunc,
      blur: defaultWdigetFunc,
    },
    style: { width: '100%' },
    options: [
      {
        id: '1',
        fullName: '选项1',
        children: [
          {
            id: '2',
            fullName: '选项1-1',
          },
        ],
      },
    ],
    props: {
      value: 'id',
      label: 'fullName',
      children: 'children',
    },
    placeholder: '请选择',
    disabled: false,
    clearable: true,
    filterable: false,
  },
  {
    __config__: {
      xhKey: 'datePicker',
      label: '日期选择',
      tipLabel: '',
      labelWidth: undefined,
      showLabel: true,
      tag: 'XhDatePicker',
      tagIcon: 'icon-ym icon-ym-generator-date',
      className: [],
      defaultValue: null,
      defaultCurrent: false,
      required: false,
      layout: 'colFormItem',
      span: 12,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      tableName: '',
      noShow: false,
      regList: [],
      trigger: 'change',
      startTimeRule: false,
      startTimeType: 1,
      startTimeTarget: 1,
      startTimeValue: null,
      startRelationField: '',
      endTimeRule: false,
      endTimeType: 1,
      endTimeTarget: 1,
      endTimeValue: null,
      endRelationField: '',
    },
    on: {
      change: defaultWdigetFunc,
      blur: defaultWdigetFunc,
    },
    style: { width: '100%' },
    placeholder: '请选择',
    format: 'YYYY-MM-DD',
    startTime: null,
    endTime: null,
    disabled: false,
    clearable: true,
  },
  {
    __config__: {
      xhKey: 'timePicker',
      label: '时间选择',
      tipLabel: '',
      labelWidth: undefined,
      showLabel: true,
      tag: 'XhTimePicker',
      tagIcon: 'icon-ym icon-ym-generator-time',
      className: [],
      defaultValue: null,
      required: false,
      layout: 'colFormItem',
      span: 12,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      tableName: '',
      noShow: false,
      regList: [],
      trigger: 'change',
      startTimeRule: false,
      startTimeType: 1,
      startTimeTarget: 1,
      startTimeValue: null,
      startRelationField: '',
      endTimeRule: false,
      endTimeType: 1,
      endTimeTarget: 1,
      endTimeValue: null,
      endRelationField: '',
    },
    on: {
      change: defaultWdigetFunc,
      blur: defaultWdigetFunc,
    },
    style: { width: '100%' },
    placeholder: '请选择',
    format: 'HH:mm:ss',
    startTime: null,
    endTime: null,
    disabled: false,
    clearable: true,
  },
  {
    __config__: {
      xhKey: 'uploadFile',
      label: '文件上传',
      tipLabel: '',
      labelWidth: undefined,
      showLabel: true,
      tag: 'XhUploadFile',
      tagIcon: 'icon-ym icon-ym-generator-upload',
      className: [],
      defaultValue: [],
      required: false,
      layout: 'colFormItem',
      span: 24,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      tableName: '',
      noShow: false,
      regList: [],
      trigger: 'change',
    },
    on: {
      change: defaultWdigetFunc,
    },
    disabled: false,
    accept: '',
    fileSize: 10,
    sizeUnit: 'MB',
    buttonText: '点击上传',
    limit: 9,
    pathType: 'defaultPath',
    isAccount: 0,
    folder: '',
    tipText: '',
  },
  {
    __config__: {
      xhKey: 'uploadImg',
      label: '图片上传',
      tipLabel: '',
      labelWidth: undefined,
      showLabel: true,
      tag: 'XhUploadImg',
      tagIcon: 'icon-ym icon-ym-generator-upload',
      className: [],
      defaultValue: [],
      required: false,
      layout: 'colFormItem',
      span: 24,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      tableName: '',
      noShow: false,
      regList: [],
      trigger: 'change',
    },
    on: {
      change: defaultWdigetFunc,
    },
    disabled: false,
    fileSize: 10,
    sizeUnit: 'MB',
    limit: 9,
    pathType: 'defaultPath',
    isAccount: 0,
    folder: '',
    tipText: '',
  },
  {
    __config__: {
      xhKey: 'colorPicker',
      label: '颜色选择',
      tipLabel: '',
      labelWidth: undefined,
      showLabel: true,
      tag: 'XhColorPicker',
      tagIcon: 'icon-ym icon-ym-generator-color',
      className: [],
      defaultValue: null,
      required: false,
      layout: 'colFormItem',
      span: 12,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      tableName: '',
      noShow: false,
      regList: [],
      trigger: 'change',
    },
    showAlpha: false,
    colorFormat: '',
    disabled: false,
  },
  {
    __config__: {
      xhKey: 'rate',
      label: '评分',
      tipLabel: '',
      labelWidth: undefined,
      showLabel: true,
      tag: 'XhRate',
      tagIcon: 'icon-ym icon-ym-generator-rate',
      className: [],
      defaultValue: 0,
      required: false,
      layout: 'colFormItem',
      span: 12,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      tableName: '',
      noShow: false,
      regList: [],
      trigger: 'change',
    },
    on: {
      change: defaultWdigetFunc,
    },
    count: 5,
    allowHalf: false,
    disabled: false,
  },
  {
    __config__: {
      xhKey: 'slider',
      label: '滑块',
      tipLabel: '',
      labelWidth: undefined,
      showLabel: true,
      tag: 'XhSlider',
      tagIcon: 'icon-ym icon-ym-generator-slider',
      className: [],
      defaultValue: 0,
      required: false,
      layout: 'colFormItem',
      span: 12,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      tableName: '',
      noShow: false,
      regList: [],
      trigger: 'change',
    },
    on: {
      change: defaultWdigetFunc,
    },
    style: { width: '100%' },
    disabled: false,
    min: 0,
    max: 100,
    step: 1,
  },
  {
    __config__: {
      xhKey: 'editor',
      label: '富文本',
      tipLabel: '',
      labelWidth: undefined,
      showLabel: true,
      tag: 'XhEditor',
      tagIcon: 'icon-ym icon-ym-generator-rich-text',
      className: [],
      defaultValue: null,
      required: false,
      layout: 'colFormItem',
      span: 24,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      tableName: '',
      noShow: false,
      regList: [],
      trigger: 'blur',
    },
    style: { width: '100%' },
    placeholder: '请输入',
    disabled: false,
  },
  {
    __config__: {
      xhKey: 'link',
      label: '链接',
      labelWidth: undefined,
      showLabel: false,
      tag: 'XhLink',
      tagIcon: 'icon-ym icon-ym-generator-link',
      className: [],
      required: false,
      layout: 'colFormItem',
      span: 12,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      noShow: false,
    },
    on: {
      click: defaultWdigetFunc,
    },
    content: '文本链接',
    href: '',
    target: '_self',
    textStyle: {
      'text-align': 'left',
    },
  },
  {
    __config__: {
      xhKey: 'button',
      label: '按钮',
      labelWidth: undefined,
      showLabel: false,
      tag: 'XhButton',
      tagIcon: 'icon-ym icon-ym-generator-button',
      className: [],
      defaultValue: null,
      required: false,
      layout: 'colFormItem',
      span: 12,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      noShow: false,
      regList: [],
      trigger: 'click',
    },
    on: {
      click: defaultWdigetFunc,
    },
    align: 'left',
    buttonText: '按钮',
    type: '',
    disabled: false,
  },
  {
    __config__: {
      xhKey: 'text',
      label: '文本',
      labelWidth: undefined,
      showLabel: false,
      tag: 'XhText',
      tagIcon: 'icon-ym icon-ym-generator-textarea',
      className: [],
      defaultValue: undefined,
      required: false,
      layout: 'colFormItem',
      span: 12,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      noShow: false,
    },
    textStyle: {
      color: '#000000',
      'text-align': 'left',
      'font-weight': 'normal',
      'font-style': 'normal',
      'text-decoration': 'none',
      'line-height': 32,
      'font-size': 12,
    },
    content: '这是一段文字',
  },
  {
    __config__: {
      xhKey: 'alert',
      label: '提示',
      labelWidth: undefined,
      showLabel: false,
      tag: 'XhAlert',
      tagIcon: 'icon-ym icon-ym-generator-alert',
      className: [],
      required: false,
      layout: 'colFormItem',
      span: 24,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      noShow: false,
    },
    title: '这是一个提示',
    type: 'success',
    showIcon: false,
    closable: true,
    description: '',
    closeText: '',
  },
];

// 高级控件 【左面板】
export const selectComponents: GenItem[] = [
  {
    __config__: {
      xhKey: 'organizeSelect',
      label: '组织选择',
      tipLabel: '',
      labelWidth: undefined,
      showLabel: true,
      tag: 'XhOrganizeSelect',
      tagIcon: 'icon-ym icon-ym-generator-company',
      className: [],
      defaultValue: [],
      defaultCurrent: false,
      required: false,
      layout: 'colFormItem',
      span: 12,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      tableName: '',
      noShow: false,
      regList: [],
      trigger: 'change',
    },
    on: {
      change: defaultWdigetFunc,
    },
    style: { width: '100%' },
    placeholder: '请选择',
    selectType: 'all',
    multiple: false,
    clearable: true,
    filterable: false,
    disabled: false,
  },
  {
    __config__: {
      xhKey: 'depSelect',
      label: '部门选择',
      tipLabel: '',
      labelWidth: undefined,
      showLabel: true,
      tag: 'XhDepSelect',
      tagIcon: 'icon-ym icon-ym-generator-department',
      className: [],
      defaultValue: null,
      defaultCurrent: false,
      required: false,
      layout: 'colFormItem',
      span: 12,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      tableName: '',
      noShow: false,
      regList: [],
      trigger: 'change',
    },
    on: {
      change: defaultWdigetFunc,
    },
    style: { width: '100%' },
    placeholder: '请选择',
    selectType: 'all',
    ableDepIds: [],
    multiple: false,
    clearable: true,
    filterable: false,
    disabled: false,
  },
  {
    __config__: {
      xhKey: 'posSelect',
      label: '岗位选择',
      tipLabel: '',
      labelWidth: undefined,
      showLabel: true,
      tag: 'XhPosSelect',
      tagIcon: 'icon-ym icon-ym-generator-jobs',
      className: [],
      defaultValue: null,
      required: false,
      layout: 'colFormItem',
      span: 12,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      tableName: '',
      noShow: false,
      regList: [],
      trigger: 'change',
    },
    on: {
      change: defaultWdigetFunc,
    },
    style: { width: '100%' },
    placeholder: '请选择',
    selectType: 'all',
    ableDepIds: [],
    ablePosIds: [],
    multiple: false,
    clearable: true,
    filterable: false,
    disabled: false,
  },
  {
    __config__: {
      xhKey: 'userSelect',
      label: '用户选择',
      tipLabel: '',
      labelWidth: undefined,
      showLabel: true,
      tag: 'XhUserSelect',
      tagIcon: 'icon-ym icon-ym-generator-user',
      className: [],
      defaultValue: null,
      defaultCurrent: false,
      required: false,
      layout: 'colFormItem',
      span: 12,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      tableName: '',
      noShow: false,
      regList: [],
      trigger: 'change',
    },
    on: {
      change: defaultWdigetFunc,
    },
    style: { width: '100%' },
    placeholder: '请选择',
    selectType: 'all',
    ableRelationIds: [],
    ableDepIds: [],
    ablePosIds: [],
    ableUserIds: [],
    ableRoleIds: [],
    ableGroupIds: [],
    relationField: '',
    multiple: false,
    clearable: true,
    disabled: false,
  },
  {
    __config__: {
      xhKey: 'userSelectDropdown',
      label: '用户搜索',
      tipLabel: '',
      labelWidth: undefined,
      showLabel: true,
      tag: 'XhUserSelect',
      tagIcon: 'ym-custom ym-custom-account-search',
      className: [],
      defaultValue: null,
      defaultCurrent: false,
      required: false,
      layout: 'colFormItem',
      span: 12,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      tableName: '',
      noShow: false,
      regList: [],
      trigger: 'change',
    },
    on: {
      change: defaultWdigetFunc,
    },
    style: { width: '100%' },
    placeholder: '请输入',
    multiple: false,
    clearable: true,
    disabled: false,
  },
  {
    __config__: {
      xhKey: 'roleSelect',
      label: '角色选择',
      tipLabel: '',
      labelWidth: undefined,
      showLabel: true,
      tag: 'XhRoleSelect',
      tagIcon: 'icon-ym icon-ym-generator-role',
      className: [],
      defaultValue: null,
      required: false,
      layout: 'colFormItem',
      span: 12,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      tableName: '',
      noShow: false,
      regList: [],
      trigger: 'change',
    },
    on: {
      change: defaultWdigetFunc,
    },
    style: { width: '100%' },
    placeholder: '请选择',
    multiple: false,
    clearable: true,
    disabled: false,
  },
  {
    __config__: {
      xhKey: 'groupSelect',
      label: '分组选择',
      tipLabel: '',
      labelWidth: undefined,
      showLabel: true,
      tag: 'XhGroupSelect',
      tagIcon: 'icon-ym icon-ym-generator-group1',
      className: [],
      defaultValue: '',
      required: false,
      layout: 'colFormItem',
      span: 12,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      tableName: '',
      noShow: false,
      regList: [],
      trigger: 'change',
    },
    on: {
      change: defaultWdigetFunc,
    },
    style: { width: '100%' },
    placeholder: '请选择',
    multiple: false,
    clearable: true,
    filterable: false,
    disabled: false,
  },
  {
    __config__: {
      xhKey: 'usersSelect',
      label: '多功能选择',
      tipLabel: '',
      labelWidth: undefined,
      showLabel: true,
      tag: 'XhUsersSelect',
      tagIcon: 'icon-ym icon-ym-generator-founder',
      className: [],
      defaultValue: null,
      required: false,
      layout: 'colFormItem',
      span: 12,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      tableName: '',
      noShow: false,
      regList: [],
      trigger: 'change',
    },
    on: {
      change: defaultWdigetFunc,
    },
    placeholder: '请选择',
    selectType: 'all',
    ableIds: [],
    multiple: false,
    clearable: true,
    disabled: false,
  },
  {
    __config__: {
      xhKey: 'table',
      label: '设计子表',
      tipLabel: '',
      labelWidth: undefined,
      showLabel: false,
      tagIcon: 'icon-ym icon-ym-generator-table',
      className: [],
      tag: 'XhInputTable',
      defaultValue: [],
      layout: 'rowFormItem',
      span: 24,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      noShow: false,
      showTitle: true,
      type: 'table',
      children: [],
      tableName: '',
    },
    disabled: false,
    actionText: '添加',
    showSummary: false,
    addType: 0,
    addTableConf: {
      popupTitle: '选择数据',
      popupType: 'dialog',
      popupWidth: '800px',
      interfaceId: '',
      interfaceName: '',
      templateJson: [],
      hasPage: true,
      pageSize: 20,
      columnOptions: [],
      relationOptions: [],
    },
    summaryField: [],
    tableConf: {},
    defaultValue: [],
  },
  {
    __config__: {
      xhKey: 'treeSelect',
      label: '下拉树形',
      tipLabel: '',
      labelWidth: undefined,
      showLabel: true,
      tag: 'XhTreeSelect',
      tagIcon: 'icon-ym icon-ym-generator-tree',
      className: [],
      defaultValue: null,
      required: false,
      layout: 'colFormItem',
      span: 12,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      tableName: '',
      noShow: false,
      regList: [],
      trigger: 'change',
      dataType: 'static',
      dictionaryType: '',
      propsUrl: '',
      propsName: '',
      templateJson: [],
    },
    on: {
      change: defaultWdigetFunc,
    },
    style: { width: '100%' },
    options: [
      {
        id: '1',
        fullName: '选项1',
        children: [
          {
            id: '2',
            fullName: '选项1-1',
          },
        ],
      },
    ],
    props: {
      value: 'id',
      label: 'fullName',
      children: 'children',
    },
    placeholder: '请选择',
    multiple: false,
    clearable: true,
    filterable: false,
    disabled: false,
  },
  {
    __config__: {
      xhKey: 'popupTableSelect',
      label: '下拉表格',
      tipLabel: '',
      labelWidth: undefined,
      showLabel: true,
      required: false,
      tag: 'XhPopupSelect',
      tagIcon: 'icon-ym icon-ym-generator-popupTableSelect',
      className: [],
      defaultValue: '',
      layout: 'colFormItem',
      span: 12,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      tableName: '',
      noShow: false,
      regList: [],
      trigger: 'change',
    },
    on: {
      change: defaultWdigetFunc,
    },
    style: { width: '100%' },
    placeholder: '请选择',
    interfaceId: '',
    interfaceName: '',
    templateJson: [],
    hasPage: false,
    pageSize: 20,
    columnOptions: [],
    propsValue: 'id',
    relationField: 'fullName',
    popupType: 'popover',
    popupTitle: '选择数据',
    popupWidth: '800px',
    disabled: false,
    clearable: true,
    multiple: false,
    filterable: true,
  },
  {
    __config__: {
      xhKey: 'autoComplete',
      label: '下拉补全',
      tipLabel: '',
      labelWidth: undefined,
      showLabel: true,
      required: false,
      tag: 'XhAutoComplete',
      tagIcon: 'icon-ym icon-ym-generator-autoComplete',
      className: [],
      defaultValue: '',
      layout: 'colFormItem',
      span: 12,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      tableName: '',
      noShow: false,
      regList: [],
      trigger: 'change',
    },
    on: {
      change: defaultWdigetFunc,
      blur: defaultWdigetFunc,
    },
    style: { width: '100%' },
    placeholder: '请输入',
    interfaceId: '',
    interfaceName: '',
    templateJson: [],
    total: 10,
    relationField: 'fullName',
    disabled: false,
    clearable: true,
  },
  {
    __config__: {
      xhKey: 'areaSelect',
      label: '省市区域',
      tipLabel: '',
      labelWidth: undefined,
      showLabel: true,
      tag: 'XhAreaSelect',
      tagIcon: 'icon-ym icon-ym-generator-Provinces',
      className: [],
      defaultValue: [],
      required: false,
      layout: 'colFormItem',
      span: 12,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      tableName: '',
      noShow: false,
      regList: [],
      trigger: 'change',
    },
    on: {
      change: defaultWdigetFunc,
    },
    style: { width: '100%' },
    placeholder: '请选择',
    disabled: false,
    clearable: true,
    filterable: false,
    multiple: false,
    level: 2,
  },
  {
    __config__: {
      xhKey: 'billRule',
      label: '单据组件',
      tipLabel: '',
      labelWidth: undefined,
      showLabel: true,
      tag: 'XhInput',
      tagIcon: 'icon-ym icon-ym-generator-documents',
      className: [],
      defaultValue: null,
      layout: 'colFormItem',
      required: false,
      span: 12,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      tableName: '',
      noShow: false,
      trigger: 'change',
      rule: '',
      ruleName: '',
    },
    style: { width: '100%' },
    readonly: true,
    placeholder: '系统自动生成',
  },
  {
    __config__: {
      xhKey: 'relationForm',
      label: '关联表单',
      tipLabel: '',
      labelWidth: undefined,
      showLabel: true,
      tag: 'XhRelationForm',
      tagIcon: 'icon-ym icon-ym-generator-menu',
      className: [],
      defaultValue: '',
      required: false,
      layout: 'colFormItem',
      span: 12,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      tableName: '',
      noShow: false,
      regList: [],
      trigger: 'change',
    },
    on: {
      change: defaultWdigetFunc,
    },
    style: { width: '100%' },
    placeholder: '请选择',
    modelId: '',
    relationField: '',
    hasPage: false,
    pageSize: 20,
    columnOptions: [],
    clearable: true,
    popupType: 'dialog',
    popupTitle: '选择数据',
    popupWidth: '800px',
    filterable: false,
    disabled: false,
  },
  {
    __config__: {
      xhKey: 'popupSelect',
      label: '弹窗选择',
      tipLabel: '',
      labelWidth: undefined,
      showLabel: true,
      required: false,
      tag: 'XhPopupSelect',
      tagIcon: 'icon-ym icon-ym-generator-popup',
      className: [],
      defaultValue: '',
      layout: 'colFormItem',
      span: 12,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      tableName: '',
      noShow: false,
      regList: [],
      trigger: 'change',
    },
    on: {
      change: defaultWdigetFunc,
    },
    style: { width: '100%' },
    placeholder: '请选择',
    interfaceId: '',
    interfaceName: '',
    templateJson: [],
    hasPage: false,
    pageSize: 20,
    columnOptions: [],
    propsValue: 'id',
    relationField: 'fullName',
    popupType: 'dialog',
    popupTitle: '选择数据',
    popupWidth: '800px',
    disabled: false,
    clearable: true,
  },
  {
    __config__: {
      xhKey: 'relationFormAttr',
      label: '关联表单属性',
      tipLabel: '',
      labelWidth: undefined,
      showLabel: true,
      tag: 'XhRelationFormAttr',
      tagIcon: 'icon-ym icon-ym-generator-nature',
      className: [],
      defaultValue: '',
      required: false,
      layout: 'colFormItem',
      span: 12,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      noShow: false,
    },
    style: { width: '100%' },
    showField: '',
    relationField: '',
    isStorage: 0,
  },
  {
    __config__: {
      xhKey: 'popupAttr',
      label: '弹窗选择属性',
      tipLabel: '',
      labelWidth: undefined,
      showLabel: true,
      tag: 'XhPopupAttr',
      tagIcon: 'icon-ym icon-ym-generator-popup-attr',
      className: [],
      defaultValue: '',
      required: false,
      layout: 'colFormItem',
      span: 12,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      noShow: false,
    },
    style: { width: '100%' },
    showField: '',
    relationField: '',
    isStorage: 0,
  },
];

// 系统控件 【左面板】
export const systemComponents: GenItem[] = [
  {
    __config__: {
      xhKey: 'createUser',
      label: '创建人员',
      labelWidth: undefined,
      showLabel: true,
      tag: 'XhOpenData',
      tagIcon: 'icon-ym icon-ym-generator-founder',
      className: [],
      defaultValue: '',
      required: false,
      layout: 'colFormItem',
      span: 12,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      tableName: '',
      noShow: false,
    },
    style: { width: '100%' },
    type: 'currUser',
    readonly: true,
    placeholder: '',
  },
  {
    __config__: {
      xhKey: 'createTime',
      label: '创建时间',
      labelWidth: undefined,
      showLabel: true,
      tag: 'XhOpenData',
      tagIcon: 'icon-ym icon-ym-generator-createtime',
      className: [],
      defaultValue: '',
      layout: 'colFormItem',
      required: false,
      span: 12,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      tableName: '',
      noShow: false,
    },
    style: { width: '100%' },
    type: 'currTime',
    readonly: true,
    placeholder: '',
  },
  {
    __config__: {
      xhKey: 'modifyUser',
      label: '修改人员',
      labelWidth: undefined,
      showLabel: true,
      tag: 'XhInput',
      tagIcon: 'icon-ym icon-ym-generator-modifier',
      className: [],
      defaultValue: '',
      required: false,
      layout: 'colFormItem',
      span: 12,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      tableName: '',
      noShow: false,
    },
    style: { width: '100%' },
    readonly: true,
    placeholder: '系统自动生成',
  },
  {
    __config__: {
      xhKey: 'modifyTime',
      label: '修改时间',
      labelWidth: undefined,
      showLabel: true,
      tag: 'XhInput',
      tagIcon: 'icon-ym icon-ym-generator-modifytime',
      className: [],
      defaultValue: '',
      required: false,
      layout: 'colFormItem',
      span: 12,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      tableName: '',
      noShow: false,
    },
    style: { width: '100%' },
    readonly: true,
    placeholder: '系统自动生成',
  },
  {
    __config__: {
      xhKey: 'currOrganize',
      label: '所属组织',
      labelWidth: undefined,
      showLabel: true,
      tag: 'XhOpenData',
      tagIcon: 'icon-ym icon-ym-generator-company',
      className: [],
      defaultValue: '',
      required: false,
      layout: 'colFormItem',
      span: 12,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      tableName: '',
      noShow: false,
    },
    style: { width: '100%' },
    type: 'currOrganize',
    readonly: true,
    showLevel: 'last',
    placeholder: '',
  },
  {
    __config__: {
      xhKey: 'currPosition',
      label: '所属岗位',
      labelWidth: undefined,
      showLabel: true,
      tag: 'XhOpenData',
      tagIcon: 'icon-ym icon-ym-generator-station',
      className: [],
      defaultValue: '',
      required: false,
      layout: 'colFormItem',
      span: 12,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      tableName: '',
      noShow: false,
    },
    style: { width: '100%' },
    type: 'currPosition',
    readonly: true,
    placeholder: '',
  },
];

// 布局控件 【左面板】
export const layoutComponents: GenItem[] = [
  {
    __config__: {
      xhKey: 'groupTitle',
      label: '分组标题',
      labelWidth: undefined,
      showLabel: false,
      tag: 'XhGroupTitle',
      tagIcon: 'icon-ym icon-ym-generator-group',
      className: [],
      defaultValue: null,
      required: false,
      layout: 'colFormItem',
      span: 24,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      noShow: false,
    },
    helpMessage: '',
    content: '分组标题',
    contentPosition: 'left',
  },
  {
    __config__: {
      xhKey: 'divider',
      label: '分割线',
      labelWidth: undefined,
      showLabel: false,
      tag: 'XhDivider',
      tagIcon: 'icon-ym icon-ym-generator-divider',
      className: [],
      defaultValue: null,
      required: false,
      layout: 'colFormItem',
      span: 24,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      noShow: false,
    },
    content: '我是分割线',
    contentPosition: 'center',
  },
  {
    __config__: {
      xhKey: 'collapse',
      label: '折叠面板',
      labelWidth: undefined,
      showLabel: false,
      tag: 'ACollapse',
      tagIcon: 'icon-ym icon-ym-generator-fold',
      className: [],
      layout: 'rowFormItem',
      span: 24,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      noShow: false,
      children: [
        {
          title: '面板1',
          name: '1',
          __config__: {
            xhKey: 'collapseItem',
            children: [],
          },
        },
        {
          title: '面板2',
          name: '2',
          __config__: {
            xhKey: 'collapseItem',
            children: [],
          },
        },
      ],
      active: ['1'],
    },
    on: {
      change: defaultWdigetFunc,
    },
    accordion: false,
    ghost: true,
    expandIconPosition: 'right',
  },
  {
    __config__: {
      xhKey: 'tab',
      label: '标签面板',
      labelWidth: undefined,
      showLabel: false,
      tag: 'ATab',
      tagIcon: 'icon-ym icon-ym-generator-label',
      className: [],
      layout: 'rowFormItem',
      span: 24,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      noShow: false,
      children: [
        {
          title: 'Tab 1',
          name: '1',
          __config__: {
            xhKey: 'tabItem',
            children: [],
          },
        },
        {
          title: 'Tab 2',
          name: '2',
          __config__: {
            xhKey: 'tabItem',
            children: [],
          },
        },
      ],
      active: '1',
    },
    on: {
      tabClick: defaultWdigetFunc,
    },
    type: '',
    tabPosition: 'top',
  },
  {
    __config__: {
      xhKey: 'row',
      label: '栅格容器',
      labelWidth: undefined,
      showLabel: false,
      tag: 'ARow',
      tagIcon: 'icon-ym icon-ym-generator-layout',
      className: [],
      layout: 'rowFormItem',
      span: 24,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      noShow: false,
      layoutTree: true,
    },
    type: 'default',
    justify: 'start',
    align: 'top',
  },
  {
    __config__: {
      xhKey: 'card',
      label: '卡片容器',
      tipLabel: '',
      labelWidth: undefined,
      showLabel: false,
      tag: 'ACard',
      tagIcon: 'icon-ym icon-ym-generator-card',
      className: [],
      defaultValue: [],
      layout: 'rowFormItem',
      span: 24,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      noShow: false,
      children: [],
    },
    header: '卡片容器',
    shadow: 'never',
  },
  {
    __config__: {
      xhKey: 'tableGrid',
      label: '表格容器',
      labelWidth: undefined,
      showLabel: false,
      tag: 'Table',
      tagIcon: 'icon-ym icon-ym-generator-tableGrid',
      className: [],
      defaultValue: [],
      layout: 'rowFormItem',
      span: 24,
      dragDisabled: false,
      visibility: ['pc', 'app'],
      noShow: false,
      borderType: 'solid',
      borderColor: '#E2E0E0',
      borderWidth: 1,
      children: [
        {
          __config__: {
            xhKey: 'tableGridTr',
            children: [
              {
                __config__: {
                  xhKey: 'tableGridTd',
                  merged: false,
                  colspan: 1,
                  rowspan: 1,
                  children: [],
                },
              },
            ],
          },
        },
      ],
    },
  },
];
