package com.xinghuo.card.report.controller;

import com.xinghuo.card.report.entity.ReportQueryConditionEntity;
import com.xinghuo.card.report.service.ReportConditionService;
import com.xinghuo.common.base.ActionResult;
import com.xinghuo.common.base.UserInfo;
import com.xinghuo.common.base.vo.ListVO;
import com.xinghuo.common.base.vo.PageListVO;
import com.xinghuo.common.base.vo.PaginationVO;
import com.xinghuo.common.util.UserProvider;
import com.xinghuo.common.util.core.BeanCopierUtils;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 报表查询条件管理控制器
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2024-01-01
 */
@Slf4j
@RestController
@Tag(name = "报表查询条件管理", description = "ReportCondition")
@RequestMapping("/api/card/report/condition")
public class ReportConditionController {

    @Resource
    private ReportConditionService reportConditionService;

    @Resource
    private UserProvider userProvider;

    /**
     * 获取用户查询条件列表
     */
    @Operation(summary = "获取用户查询条件列表")
    @GetMapping("/list")
    public ActionResult<ListVO<ReportQueryConditionEntity>> getConditionList(
            @RequestParam(value = "reportType", required = false) Integer reportType) {
        
        UserInfo userInfo = userProvider.get();
        List<ReportQueryConditionEntity> list = reportConditionService.getByUserId(userInfo.getUserId(), reportType);
        
        ListVO<ReportQueryConditionEntity> listVO = new ListVO<>();
        listVO.setList(list);
        return ActionResult.success(listVO);
    }

    /**
     * 获取公共查询条件
     */
    @Operation(summary = "获取公共查询条件")
    @GetMapping("/public")
    public ActionResult<ListVO<ReportQueryConditionEntity>> getPublicConditions(
            @RequestParam(value = "reportType", required = false) Integer reportType) {
        
        List<ReportQueryConditionEntity> list = reportConditionService.getPublicConditions(reportType);
        
        ListVO<ReportQueryConditionEntity> listVO = new ListVO<>();
        listVO.setList(list);
        return ActionResult.success(listVO);
    }

    /**
     * 获取收藏的查询条件
     */
    @Operation(summary = "获取收藏的查询条件")
    @GetMapping("/favorites")
    public ActionResult<ListVO<ReportQueryConditionEntity>> getFavoriteConditions() {
        UserInfo userInfo = userProvider.get();
        List<ReportQueryConditionEntity> list = reportConditionService.getFavoriteConditions(userInfo.getUserId());
        
        ListVO<ReportQueryConditionEntity> listVO = new ListVO<>();
        listVO.setList(list);
        return ActionResult.success(listVO);
    }

    /**
     * 获取查询条件详情
     */
    @Operation(summary = "获取查询条件详情")
    @GetMapping("/{conditionId}")
    public ActionResult<ReportQueryConditionEntity> getConditionDetail(@PathVariable("conditionId") String conditionId) {
        ReportQueryConditionEntity condition = reportConditionService.getById(conditionId);
        if (condition == null) {
            return ActionResult.fail("查询条件不存在");
        }
        return ActionResult.success(condition);
    }

    /**
     * 保存查询条件
     */
    @Operation(summary = "保存查询条件")
    @PostMapping("/save")
    public ActionResult saveCondition(@RequestBody @Valid ReportQueryConditionEntity condition) {
        UserInfo userInfo = userProvider.get();
        condition.setCreatedBy(userInfo.getUserId());

        boolean success = reportConditionService.saveCondition(condition);
        if (success) {
            return ActionResult.success("保存成功");
        } else {
            return ActionResult.fail("保存失败");
        }
    }

    /**
     * 从查询参数创建查询条件
     */
    @Operation(summary = "从查询参数创建查询条件")
    @PostMapping("/create-from-params")
    public ActionResult<ReportQueryConditionEntity> createFromParams(
            @RequestBody CreateConditionRequest request) {
        
        UserInfo userInfo = userProvider.get();
        ReportQueryConditionEntity condition = reportConditionService.createFromParams(
                request.getParams(), userInfo.getUserId(), request.getConditionName());
        
        boolean success = reportConditionService.saveCondition(condition);
        if (success) {
            return ActionResult.success(condition);
        } else {
            return ActionResult.fail("创建失败");
        }
    }

    /**
     * 更新查询条件
     */
    @Operation(summary = "更新查询条件")
    @PutMapping("/{conditionId}")
    public ActionResult updateCondition(
            @PathVariable("conditionId") String conditionId,
            @RequestBody @Valid ReportQueryConditionEntity condition) {
        
        condition.setId(conditionId);
        boolean success = reportConditionService.saveCondition(condition);
        if (success) {
            return ActionResult.success("更新成功");
        } else {
            return ActionResult.fail("更新失败");
        }
    }

    /**
     * 删除查询条件
     */
    @Operation(summary = "删除查询条件")
    @DeleteMapping("/{conditionId}")
    public ActionResult deleteCondition(@PathVariable("conditionId") String conditionId) {
        boolean success = reportConditionService.removeById(conditionId);
        if (success) {
            return ActionResult.success("删除成功");
        } else {
            return ActionResult.fail("删除失败");
        }
    }

    /**
     * 收藏/取消收藏查询条件
     */
    @Operation(summary = "收藏/取消收藏查询条件")
    @PostMapping("/{conditionId}/toggle-favorite")
    public ActionResult toggleFavorite(
            @PathVariable("conditionId") String conditionId,
            @RequestParam("favorite") boolean favorite) {
        
        boolean success = reportConditionService.toggleFavorite(conditionId, favorite);
        if (success) {
            String message = favorite ? "已收藏" : "已取消收藏";
            return ActionResult.success(message);
        } else {
            return ActionResult.fail("操作失败");
        }
    }

    /**
     * 使用查询条件（增加使用次数）
     */
    @Operation(summary = "使用查询条件")
    @PostMapping("/{conditionId}/use")
    public ActionResult<Map<String, Object>> useCondition(@PathVariable("conditionId") String conditionId) {
        ReportQueryConditionEntity condition = reportConditionService.getById(conditionId);
        if (condition == null) {
            return ActionResult.fail("查询条件不存在");
        }

        // 增加使用次数
        reportConditionService.incrementUseCount(conditionId);
        
        // 转换为查询参数
        Map<String, Object> params = reportConditionService.convertToQueryParams(condition);
        return ActionResult.success(params);
    }

    /**
     * 管理端：分页获取查询条件列表
     */
    @Operation(summary = "管理端：分页获取查询条件列表")
    @GetMapping("/admin/list")
    public ActionResult<PageListVO<ReportQueryConditionEntity>> getConditionListForAdmin(
            @RequestParam(value = "currentPage", defaultValue = "1") Integer currentPage,
            @RequestParam(value = "pageSize", defaultValue = "20") Integer pageSize,
            @RequestParam(value = "keyword", required = false) String keyword,
            @RequestParam(value = "reportType", required = false) Integer reportType,
            @RequestParam(value = "isPublic", required = false) Boolean isPublic) {

        Page<ReportQueryConditionEntity> page = new Page<>(currentPage, pageSize);
        IPage<ReportQueryConditionEntity> result = reportConditionService.getConditionListForAdmin(
                page, keyword, reportType, isPublic);

        PageListVO<ReportQueryConditionEntity> pageListVO = new PageListVO<>();
        pageListVO.setList(result.getRecords());

        // 创建分页对象，遵循项目规范
        PaginationVO paginationVO = new PaginationVO();
        paginationVO.setCurrentPage(currentPage);
        paginationVO.setPageSize(pageSize);
        paginationVO.setTotal(result.getTotal());
        pageListVO.setPagination(paginationVO);

        return ActionResult.success(pageListVO);
    }

    /**
     * 管理端：批量设置公共状态
     */
    @Operation(summary = "管理端：批量设置公共状态")
    @PostMapping("/admin/batch-public")
    public ActionResult batchSetPublic(@RequestBody BatchPublicRequest request) {
        boolean success = reportConditionService.batchSetPublic(request.getConditionIds(), request.getIsPublic());
        if (success) {
            return ActionResult.success("操作成功");
        } else {
            return ActionResult.fail("操作失败");
        }
    }

    /**
     * 管理端：批量删除查询条件
     */
    @Operation(summary = "管理端：批量删除查询条件")
    @DeleteMapping("/admin/batch-delete")
    public ActionResult batchDelete(@RequestBody BatchDeleteRequest request) {
        boolean success = reportConditionService.removeByIds(request.getConditionIds());
        if (success) {
            return ActionResult.success("删除成功");
        } else {
            return ActionResult.fail("删除失败");
        }
    }

    /**
     * 创建查询条件请求参数
     */
    @lombok.Data
    public static class CreateConditionRequest {
        /**
         * 条件名称
         */
        private String conditionName;

        /**
         * 查询参数
         */
        private Map<String, Object> params;
    }

    /**
     * 批量设置公共状态请求参数
     */
    @lombok.Data
    public static class BatchPublicRequest {
        /**
         * 条件ID列表
         */
        private List<String> conditionIds;

        /**
         * 是否公共
         */
        private Boolean isPublic;
    }

    /**
     * 批量删除请求参数
     */
    @lombok.Data
    public static class BatchDeleteRequest {
        /**
         * 条件ID列表
         */
        private List<String> conditionIds;
    }
}
