package com.xinghuo.card.flow.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xinghuo.card.CardConstant;
import com.xinghuo.card.flow.dao.DataFlowMapper;
import com.xinghuo.card.flow.entity.DataFlowAccEntity;
import com.xinghuo.card.flow.entity.DataFlowEntity;
import com.xinghuo.card.flow.model.dataflow.DataFlowPagination;
import com.xinghuo.card.flow.service.DataFlowAccService;
import com.xinghuo.card.flow.service.DataFlowService;
import com.xinghuo.common.util.UserProvider;
import com.xinghuo.common.util.core.DateXhUtil;
import com.xinghuo.common.util.core.StrXhUtil;
import com.xinghuo.common.util.extra.ServletUtil;
import com.xinghuo.permission.model.authorize.AuthorizeConditionModel;
import com.xinghuo.permission.service.AuthorizeService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.*;

/**
 * 流水表
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2022-11-26
 */
@Service
public class DataFlowServiceImpl extends ServiceImpl<DataFlowMapper, DataFlowEntity> implements DataFlowService {

    Map<String, Date> map = new HashMap<>();
    @Autowired
    private UserProvider userProvider;
    @Autowired
    private AuthorizeService authorizeService;
    @Autowired
    private DataFlowAccService dataFlowAccService;

    @Override
    public List<DataFlowEntity> getList(DataFlowPagination dataFlowPagination) {
        return getListByType(dataFlowPagination, 0);
    }

    @Override
    public List<DataFlowEntity> getTypeList(DataFlowPagination dataFlowPagination, int dataType) {
        return getListByType(dataFlowPagination, dataType);
    }

    private List<DataFlowEntity> getListByType(DataFlowPagination dataFlowPagination, int dataType) {
        List<String> allIdList = new ArrayList();
        int total = 0;
        int dataFlowNum = 0;
        QueryWrapper<DataFlowEntity> dataFlowQueryWrapper = new QueryWrapper<>();
        int dataFlowAccNum = 0;
        QueryWrapper<DataFlowAccEntity> dataFlowAccQueryWrapper = new QueryWrapper<>();
        boolean pcPermission = false;
        boolean appPermission = false;
        boolean isPc = "pc".equals(ServletUtil.getHeader("xh-origin"));
        if (isPc && pcPermission) {
            if (!userProvider.get().getIsAdministrator()) {
                Object dataFlowObj = authorizeService.getCondition(new AuthorizeConditionModel(dataFlowQueryWrapper, dataFlowPagination.getMenuId(), "data_flow"));
                if (ObjectUtil.isEmpty(dataFlowObj)) {
                    return new ArrayList<>();
                } else {
                    dataFlowQueryWrapper = (QueryWrapper<DataFlowEntity>) dataFlowObj;
                    dataFlowNum++;
                }
                Object dataFlowAccObj = authorizeService.getCondition(new AuthorizeConditionModel(dataFlowAccQueryWrapper, dataFlowPagination.getMenuId(), "data_flow_acc"));
                if (ObjectUtil.isEmpty(dataFlowAccObj)) {
                    return new ArrayList<>();
                } else {
                    dataFlowAccQueryWrapper = (QueryWrapper<DataFlowAccEntity>) dataFlowAccObj;
                    dataFlowAccNum++;
                }
            }
        }
        if (!isPc && appPermission) {
            if (!userProvider.get().getIsAdministrator()) {
                Object dataFlowObj = authorizeService.getCondition(new AuthorizeConditionModel(dataFlowQueryWrapper, dataFlowPagination.getMenuId(), "data_flow"));
                if (ObjectUtil.isEmpty(dataFlowObj)) {
                    return new ArrayList<>();
                } else {
                    dataFlowQueryWrapper = (QueryWrapper<DataFlowEntity>) dataFlowObj;
                    dataFlowNum++;
                }
                Object dataFlowAccObj = authorizeService.getCondition(new AuthorizeConditionModel(dataFlowAccQueryWrapper, dataFlowPagination.getMenuId(), "data_flow_acc"));
                if (ObjectUtil.isEmpty(dataFlowAccObj)) {
                    return new ArrayList<>();
                } else {
                    dataFlowAccQueryWrapper = (QueryWrapper<DataFlowAccEntity>) dataFlowAccObj;
                    dataFlowAccNum++;
                }
            }
        }
        if (StrXhUtil.isNotEmpty(dataFlowPagination.getTransType())) {
            dataFlowNum++;
            dataFlowQueryWrapper.lambda().eq(DataFlowEntity::getTransType, dataFlowPagination.getTransType());
        }
        if (StrXhUtil.isNotEmpty(dataFlowPagination.getType())) {
            dataFlowNum++;
            dataFlowQueryWrapper.lambda().eq(DataFlowEntity::getType, dataFlowPagination.getType());
        }
        if (CollUtil.isNotEmpty(dataFlowPagination.getFlowDate())) {
            dataFlowNum++;
            List<String> FlowDateList = dataFlowPagination.getFlowDate();
            Long fir = Long.valueOf(FlowDateList.get(0));
            Long sec = Long.valueOf(FlowDateList.get(1));
            dataFlowQueryWrapper.lambda().ge(DataFlowEntity::getFlowDate, new Date(fir))
                    .le(DataFlowEntity::getFlowDate, DateXhUtil.endOfDay(sec));
        }
        if (allIdList.size() > 0) {
            dataFlowQueryWrapper.lambda().in(DataFlowEntity::getId, allIdList);
        }
        //排序
        if (StrXhUtil.isEmpty(dataFlowPagination.getSidx())) {
            dataFlowQueryWrapper.lambda().orderByDesc(DataFlowEntity::getId);
        } else {
            try {
                DataFlowEntity dataFlowEntity = new DataFlowEntity();
                Field declaredField = dataFlowEntity.getClass().getDeclaredField(dataFlowPagination.getSidx());
                declaredField.setAccessible(true);
                String value = declaredField.getAnnotation(TableField.class).value();
                dataFlowQueryWrapper = "asc".equals(dataFlowPagination.getSort().toLowerCase()) ? dataFlowQueryWrapper.orderByAsc(value) : dataFlowQueryWrapper.orderByDesc(value);
            } catch (NoSuchFieldException e) {
                e.printStackTrace();
            }
        }

            return this.list(dataFlowQueryWrapper);

    }

    @Override
    public DataFlowEntity getInfo(String id) {
        QueryWrapper<DataFlowEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(DataFlowEntity::getId, id);
        return this.getOne(queryWrapper);
    }

    @Override
    public void create(DataFlowEntity entity) {

        this.save(entity);
        //更新数据
        initDataFlowAcc(entity, null, CardConstant.SYNC_ADD);

    }

    @Override
    public boolean update(String id, DataFlowEntity entity) {
        entity.setId(id);
        DataFlowEntity oldDataFlow = this.getInfo(entity.getId());
        entity.setUpdateBy(userProvider.get().getUserId());
        entity.setUpdateTime(new Date());
        boolean result = this.updateById(entity);
        initDataFlowAcc(entity, oldDataFlow, CardConstant.SYNC_UPDATE);
        return result;
    }

    @Override
    public void delete(DataFlowEntity entity) {
        DataFlowEntity oldDataFlow = this.getInfo(entity.getId());
        if (entity != null) {
            this.removeById(entity.getId());
        }
        dataFlowAccService.deleteDataFlowAccByFlowId(entity.getId());
        initDataFlowAcc(oldDataFlow, null, CardConstant.SYNC_DELETED);
    }

    @Override
    public void deleteDataFlowByRelatedIds(String relatedId) {
        deleteDataFlowByRelatedIds(relatedId, null);
    }

    @Override
    public void deleteDataFlowByRelatedIds(String relatedId, String note) {


        QueryWrapper<DataFlowEntity> dataFlowQueryWrapper = new QueryWrapper<>();


        if (StringUtils.isNotBlank(relatedId)) {
            dataFlowQueryWrapper.lambda().eq(DataFlowEntity::getRelatedId, relatedId.trim());
        }
        if (StringUtils.isNotBlank(note)) {
            dataFlowQueryWrapper.lambda().like(DataFlowEntity::getNote, note.trim());
        }
        List<DataFlowEntity> list = this.getBaseMapper().selectList(dataFlowQueryWrapper);

        this.getBaseMapper().delete(dataFlowQueryWrapper);

        for (DataFlowEntity dataFlow : list) {
            QueryWrapper<DataFlowAccEntity> dataFlowAccQueryWrapper = new QueryWrapper<>();
            dataFlowAccQueryWrapper.lambda().eq(DataFlowAccEntity::getFlowId, dataFlow.getId());
            dataFlowAccService.getBaseMapper().delete(dataFlowAccQueryWrapper);

            initDataFlowAcc(dataFlow, null, CardConstant.SYNC_DELETED);
        }

    }

    private void initDataFlowAcc(DataFlowEntity dataFlow, DataFlowEntity oldDataFlow, int SYNC_TYPE) {
        map = new HashMap<String, Date>();
        if (SYNC_TYPE == CardConstant.SYNC_ADD) {
            //新增
            if (dataFlow.getTransType().equalsIgnoreCase("1") || dataFlow.getTransType().equalsIgnoreCase("2")) {
                //收支信息
                initDataFlowAcc(dataFlow, dataFlow.getAccId(), null);
                putMap(dataFlow.getAccId(), dataFlow.getFlowDate());

            } else if (dataFlow.getTransType().equalsIgnoreCase("3") || dataFlow.getTransType().equalsIgnoreCase("4")) {
                initDataFlowAcc(dataFlow, dataFlow.getInAccId(), null);
                initDataFlowAcc(dataFlow, dataFlow.getOutAccId(), null);
                putMap(dataFlow.getInAccId(), dataFlow.getFlowDate());
                putMap(dataFlow.getOutAccId(), dataFlow.getFlowDate());
            }
        } else if (SYNC_TYPE == CardConstant.SYNC_UPDATE) {

            if (dataFlow.getTransType().equalsIgnoreCase("1") || dataFlow.getTransType().equalsIgnoreCase("2")) {
                //收支信息
                DataFlowAccEntity oldDataFlowAcc = dataFlowAccService.getDataFlowAccByFlowId(oldDataFlow.getAccId(), dataFlow.getId());

                initDataFlowAcc(dataFlow, dataFlow.getAccId(), oldDataFlowAcc);
                putMap(dataFlow.getAccId(), dataFlow.getFlowDate());
                putMap(oldDataFlow.getAccId(), oldDataFlow.getFlowDate());
            } else if (dataFlow.getTransType().equalsIgnoreCase("3") || dataFlow.getTransType().equalsIgnoreCase("4")) {

                //修改
                DataFlowAccEntity oldInDataFlowAcc = dataFlowAccService.getDataFlowAccByFlowId(oldDataFlow.getInAccId(), dataFlow.getId());
                DataFlowAccEntity oldOutDataFlowAcc = dataFlowAccService.getDataFlowAccByFlowId(oldDataFlow.getOutAccId(), dataFlow.getId());


                initDataFlowAcc(dataFlow, dataFlow.getInAccId(), oldInDataFlowAcc);
                initDataFlowAcc(dataFlow, dataFlow.getOutAccId(), oldOutDataFlowAcc);
                putMap(dataFlow.getInAccId(), dataFlow.getFlowDate());
                putMap(oldDataFlow.getInAccId(), oldDataFlow.getFlowDate());
                putMap(dataFlow.getOutAccId(), dataFlow.getFlowDate());
                putMap(oldDataFlow.getOutAccId(), oldDataFlow.getFlowDate());
            }
        } else if (SYNC_TYPE == CardConstant.SYNC_DELETED) {
            if (dataFlow.getTransType().equalsIgnoreCase("1") || dataFlow.getTransType().equalsIgnoreCase("2")) {
                putMap(dataFlow.getAccId(), dataFlow.getFlowDate());
            } else if (dataFlow.getTransType().equalsIgnoreCase("3") || dataFlow.getTransType().equalsIgnoreCase("4")) {
                putMap(dataFlow.getInAccId(), dataFlow.getFlowDate());
                putMap(dataFlow.getOutAccId(), dataFlow.getFlowDate());
            }
        }

        //更新数据
        Iterator it = map.keySet().iterator();
        while (it.hasNext()) {
            String key = (String) it.next();
            Date value = map.get(key);
            DataFlowAccEntity d1 = new DataFlowAccEntity(key, value);
           dataFlowAccService.updateDataFlowAccBalance(d1);
        }


    }

    /**
     * 判断需要修改的时间
     *
     * @param accId
     * @param flowDate
     */
    public void putMap(String accId, Date flowDate) {
        if (StringUtils.isNotBlank(accId)) {
            if (map.containsKey(accId)) {
                if (DateXhUtil.compare(map.get(accId),flowDate) < 0) {
                    map.put(accId, flowDate);
                }
            } else {
                map.put(accId, flowDate);
            }
        }
    }

    /**
     * 初始化数据
     *
     * @param dataFlow
     * @param accId
     * @param oldDataFlowAcc
     * @return
     */
    private DataFlowAccEntity initDataFlowAcc(DataFlowEntity dataFlow, String accId, DataFlowAccEntity oldDataFlowAcc) {
        boolean isNew = (oldDataFlowAcc == null);
        DataFlowAccEntity dfc1 = new DataFlowAccEntity();
        if (isNew) {
//            dfc1.set(ShiroUtils.getLoginName());
//            dfc1.setCreateTime(new Date());
            dfc1.setListOrder(dataFlowAccService.selectMaxListOrder(new DataFlowAccEntity(accId, dataFlow.getFlowDate())));
        } else {
            dfc1 = oldDataFlowAcc;
        }
        if (dataFlow.getTransType().equals("1")) {
            //收入
            dfc1.setIncome(dataFlow.getAmout());
            dfc1.setPay(BigDecimal.ZERO);
        } else if (dataFlow.getTransType().equals("2")) {
            //支出
            dfc1.setIncome(BigDecimal.ZERO);
            dfc1.setPay(dataFlow.getAmout());
        } else if (dataFlow.getTransType().equals("3") || dataFlow.getTransType().equals("4")) {
            //判断是转入还是转出
            if (accId.equals(dataFlow.getInAccId())) {
                dfc1.setOutAccId(dataFlow.getOutAccId());
                dfc1.setIncome(dataFlow.getAmout());
                dfc1.setPay(BigDecimal.ZERO);
            } else {
                dfc1.setOutAccId(dataFlow.getInAccId());
                dfc1.setIncome(BigDecimal.ZERO);
                dfc1.setPay(dataFlow.getAmout());
            }

        }
        dfc1.setAccId(accId);
        dfc1.setFlowId(dataFlow.getId());
        dfc1.setManId(dataFlow.getManId());
        dfc1.setType(dataFlow.getType());
        dfc1.setFlowDate(dataFlow.getFlowDate());
        dfc1.setNote(dataFlow.getNote());
        if (isNew) {
            dataFlowAccService.create(dfc1);
        } else {
            dataFlowAccService.update(dfc1.getId(),dfc1);
        }
        return dfc1;
    }


}
