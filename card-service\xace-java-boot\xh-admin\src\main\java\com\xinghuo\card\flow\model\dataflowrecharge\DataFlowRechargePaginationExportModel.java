package com.xinghuo.card.flow.model.dataflowrecharge;

import com.xinghuo.common.base.model.Pagination;
import lombok.Data;

import java.util.List;

/**
 * 充值记录
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2022-11-27
 */
@Data
public class DataFlowRechargePaginationExportModel extends Pagination {

    /**
     * 选择的key
     */
    private String selectKey;

    /**
     * json
     */
    private String json;

    /**
     * 数据类型
     */
    private String dataType;


    /**
     * 充值类型
     */
    private String chargeType;

    /**
     * 充值日期
     */
    private List<String> chargeDate;

    /**
     * 备注
     */
    private String note;
}
