kind: ConfigMap
apiVersion: v1
metadata:
  name: xh-web-vue3-cm
  namespace: xh-staging
  annotations:
    kubesphere.io/description: vue3测试环境配置
data:
  default.conf: |
    #MaxKey Frontend Server
    server {
      listen       80;
      server_name  localhost;

      location / {
        root /home/<USER>/xh-web;
        index index.html;
        try_files $uri $uri/ /index.html;
      }

      location /api/ {
        proxy_pass http://************:7005;
      }
    }
