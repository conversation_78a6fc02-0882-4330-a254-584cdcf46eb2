package com.xinghuo.card.flow.controller;

import com.xinghuo.admin.util.GeneraterSwapUtil;
import com.xinghuo.card.flow.entity.DataFlowOrderEntity;
import com.xinghuo.card.flow.model.datafloworder.DataFlowOrderForm;
import com.xinghuo.card.flow.model.datafloworder.DataFlowOrderPagination;
import com.xinghuo.card.flow.model.datafloworder.DataFlowOrderVO;
import com.xinghuo.card.flow.service.DataFlowOrderService;
import com.xinghuo.card.sys.entity.DataAccEntity;
import com.xinghuo.card.sys.service.DataAccService;
import com.xinghuo.common.base.ActionResult;
import com.xinghuo.common.base.UserInfo;
import com.xinghuo.common.base.vo.PageListVO;
import com.xinghuo.common.base.vo.PaginationVO;
import com.xinghuo.common.exception.DataException;
import com.xinghuo.common.util.UserProvider;
import com.xinghuo.common.util.core.BeanCopierUtils;
import com.xinghuo.common.util.core.DateXhUtil;
import com.xinghuo.common.util.core.RandomUtil;
import com.xinghuo.common.util.core.StrXhUtil;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.*;
import java.util.Date;

/**
 * 订单管理
 *
 * <AUTHOR>
 * @version V1.0.0
 * date 2022-11-27
 */
@Slf4j
@RestController
@RequestMapping("/api/card/flow/order")
public class DataFlowOrderController {

    @Autowired
    private GeneraterSwapUtil generaterSwapUtil;

    @Autowired
    private UserProvider userProvider;

    @Autowired
    private DataFlowOrderService dataFlowOrderService;

    @Autowired
    private DataAccService dataAccService;
    /**
     * 获取订单列表
     *
     * @param dataFlowOrderPagination 分页查询参数
     * @return 订单列表
     */
    @Operation(summary = "获取订单列表")
    @PostMapping("/getList")
    public ActionResult list(@RequestBody DataFlowOrderPagination dataFlowOrderPagination) throws IOException {
        List<DataFlowOrderEntity> list = dataFlowOrderService.getList(dataFlowOrderPagination);
        List<DataFlowOrderVO> listVO = BeanCopierUtils.copyList(list, DataFlowOrderVO.class);

        // 批量获取账户信息，避免重复查询
        Set<String> accIds = new HashSet<>();
        for (DataFlowOrderVO vo : listVO) {
            if (StrXhUtil.isNotEmpty(vo.getAccId())) {
                accIds.add(vo.getAccId());
            }
        }

        // 批量查询账户信息
        Map<String, String> accNameMap = new HashMap<>();
        for (String accId : accIds) {
            DataAccEntity acc = dataAccService.getInfo(accId);
            if (acc != null) {
                accNameMap.put(accId, acc.getName());
            }
        }

        // 处理数据转换和补充
        for (DataFlowOrderVO vo : listVO) {
            // 店铺名称转换
            if (StrXhUtil.isNotEmpty(vo.getAccId())) {
                vo.setAccId(accNameMap.getOrDefault(vo.getAccId(), vo.getAccId()));
            }

            // 字典值转换
            vo.setOrderItem(generaterSwapUtil.getDicName(vo.getOrderItem(), "1063018160630784"));
            vo.setDest(generaterSwapUtil.getDicName(vo.getDest(), "1063014333852672"));
            vo.setFormStatus(generaterSwapUtil.getDicName(vo.getFormStatus(), "1063014614871040"));
            vo.setPayType(generaterSwapUtil.getDicName(vo.getPayType(), "1063014467021824"));

            // 用户名称转换
            vo.setCreateBy(generaterSwapUtil.userSelectValue(vo.getCreateBy()));
            vo.setUpdateBy(generaterSwapUtil.userSelectValue(vo.getUpdateBy()));
        }

        PaginationVO page = BeanCopierUtils.copy(dataFlowOrderPagination, PaginationVO.class);
        return ActionResult.page(listVO, page);
    }

    /**
     * 创建订单
     *
     * @param dataFlowOrderForm 订单表单数据
     * @return 操作结果
     */
    @Operation(summary = "创建订单")
    @PostMapping
    @Transactional
    public ActionResult create(@RequestBody @Valid DataFlowOrderForm dataFlowOrderForm) throws DataException {
        String mainId = RandomUtil.snowId();
        UserInfo userInfo = userProvider.get();

        // 转换Form为Entity
        DataFlowOrderEntity entity = BeanCopierUtils.copy(dataFlowOrderForm, DataFlowOrderEntity.class);
        entity.setOrderId(mainId);
        entity.setCreateBy(userInfo.getUserId());
        entity.setCreateTime(DateXhUtil.date());

        dataFlowOrderService.save(entity);
        return ActionResult.success("创建成功");
    }

    /**
     * 信息
     *
     * @param id
     * @return
     */
    @GetMapping("/{id}")
    public ActionResult<DataFlowOrderVO> info(@PathVariable("id") String id) {
        DataFlowOrderEntity entity = dataFlowOrderService.getInfo(id);
        DataFlowOrderVO vo = BeanCopierUtils.copy(entity, DataFlowOrderVO.class);
        vo.setUpdateBy(generaterSwapUtil.userSelectValue(vo.getUpdateBy()));
        vo.setCreateBy(generaterSwapUtil.userSelectValue(vo.getCreateBy()));
        return ActionResult.success(vo);
    }

    /**
     * 表单信息(详情页)
     *
     * @param id
     * @return
     */
    @Operation(summary = "表单信息(详情页)")
    @GetMapping("/detail/{id}")
    public ActionResult<DataFlowOrderVO> detailInfo(@PathVariable("id") String id) {
        DataFlowOrderEntity entity = dataFlowOrderService.getInfo(id);
        DataFlowOrderVO vo = BeanCopierUtils.copy(entity, DataFlowOrderVO.class);
        //添加到详情表单对象中
        if(StrXhUtil.isNotEmpty(vo.getAccId())){
            DataAccEntity dataAcc = dataAccService.getInfo(vo.getAccId());
            if (dataAcc != null) {
                vo.setAccId(dataAcc.getName());
            }
        }
        vo.setOrderItem(generaterSwapUtil.getDicName(vo.getOrderItem(), "1063018160630784"));
        vo.setDest(generaterSwapUtil.getDicName(vo.getDest(), "1063014333852672"));
        vo.setFormStatus(generaterSwapUtil.getDicName(vo.getFormStatus(), "1063014614871040"));
        if(StrXhUtil.isNotEmpty(vo.getPrePayAccId())){
            DataAccEntity dataAcc = dataAccService.getInfo(vo.getPrePayAccId());
            if (dataAcc != null) {
                vo.setPrePayAccId(dataAcc.getName());
            }
        }
        vo.setPayType(generaterSwapUtil.getDicName(vo.getPayType(), "1063014467021824"));
        if(StrXhUtil.isNotEmpty(vo.getPayAccId())){
            DataAccEntity dataAcc = dataAccService.getInfo(vo.getPayAccId());
            if (dataAcc != null) {
                vo.setPayAccId(dataAcc.getName());
            }
        }
        if(StrXhUtil.isNotEmpty(vo.getBackAccId())){
            DataAccEntity dataAcc = dataAccService.getInfo(vo.getBackAccId());
            if (dataAcc != null) {
                vo.setBackAccId(dataAcc.getName());
            }
        }
        vo.setUpdateBy(generaterSwapUtil.userSelectValue(vo.getUpdateBy()));
        vo.setCreateBy(generaterSwapUtil.userSelectValue(vo.getCreateBy()));
        return ActionResult.success(vo);
    }

    /**
     * 更新订单
     *
     * @param id 订单ID
     * @param dataFlowOrderForm 订单表单数据
     * @return 操作结果
     */
    @Operation(summary = "更新订单")
    @PutMapping("/{id}")
    @Transactional
    public ActionResult update(@PathVariable("id") String id, @RequestBody @Valid DataFlowOrderForm dataFlowOrderForm) throws DataException {
        UserInfo userInfo = userProvider.get();
        DataFlowOrderEntity entity = dataFlowOrderService.getInfo(id);
        if (entity == null) {
            return ActionResult.fail("更新失败，数据不存在");
        }

        // 转换Form为Entity
        DataFlowOrderEntity updateEntity = BeanCopierUtils.copy(dataFlowOrderForm, DataFlowOrderEntity.class);
        updateEntity.setOrderId(id);
        updateEntity.setUpdateBy(userInfo.getUserId());
        updateEntity.setUpdateTime(DateXhUtil.date());

        // 保留原有的创建信息
        updateEntity.setCreateBy(entity.getCreateBy());
        updateEntity.setCreateTime(entity.getCreateTime());

        dataFlowOrderService.update(id, updateEntity);
        return ActionResult.success("更新成功");
    }

    /**
     * 删除订单
     *
     * @param id 订单ID
     * @return 操作结果
     */
    @Operation(summary = "删除订单")
    @DeleteMapping("/{id}")
    @Transactional
    public ActionResult delete(@PathVariable("id") String id) {
        DataFlowOrderEntity entity = dataFlowOrderService.getInfo(id);
        if (entity != null) {
            dataFlowOrderService.delete(entity);
            return ActionResult.success("删除成功");
        } else {
            return ActionResult.fail("删除失败，数据不存在");
        }
    }
}
