<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xinghuo.card.flow.dao.DataFlowAccMapper">
    <resultMap type="DataFlowAccEntity" id="DataFlowAccResult">
        <result property="id" column="ID"/>
        <result property="flowId" column="FLOW_ID"/>
        <result property="accId" column="ACC_ID"/>
        <result property="ccId" column="CC_ID"/>
        <result property="type" column="TYPE"/>
        <result property="income" column="INCOME"/>
        <result property="outAccId" column="OUT_ACC_ID"/>
        <result property="pay" column="PAY"/>
        <result property="balance" column="BALANCE"/>
        <result property="flowDate" column="FLOW_DATE"/>
        <result property="listOrder" column="LIST_ORDER"/>
        <result property="isback" column="ISBACK"/>
        <result property="billId" column="BILL_ID"/>
        <result property="note" column="NOTE"/>
    </resultMap>


    <select id="selectMaxListOrder" parameterType="DataFlowAccEntity" resultType="int">
        select ifnull(max(LIST_ORDER),0)+1 from data_flow_acc
        <where>
            <if test="accId != null  and accId != '' ">
                and ACC_ID = #{accId}
            </if>
            <if test="flowDate != null ">
                and FLOW_DATE = #{flowDate}
            </if>
        </where>
    </select>

    <!-- 更新balance -->
    <update id="updateDataFlowAccBalance" parameterType="DataFlowAccEntity">
<!--        UPDATE data_flow_acc A-->
<!--            INNER JOIN-->
<!--            (select id,-->
<!--                    (select sum(ifnull(INCOME, 0)) - sum(ifnull(PAY, 0))-->
<!--                     from data_flow_acc_view t2-->
<!--                     where t1.row >= t2.row-->
<!--                       and t2.ACC_ID = #{accId}) as BALANCE-->
<!--             from data_flow_acc_view t1-->
<!--             WHERE t1.ACC_ID = #{accId}-->
<!--               and t1.FLOW_DATE >= #{flowDate}) B-->
<!--            ON A.ID = B.id-->
<!--        SET A.BALANCE=B.BALANCE-->

        <!--  优化统计代码 -->

        UPDATE data_flow_acc A
            INNER JOIN
            (SELECT id,
                    SUM(INCOME - PAY) OVER (PARTITION BY ACC_ID ORDER BY LIST_ORDER2 ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW) AS BALANCE
             FROM data_flow_acc t
             WHERE ACC_ID = #{accId}
            ) B
            ON A.ID = B.id
        SET A.BALANCE=B.BALANCE
        where 	ACC_ID = #{accId}
          and A.FLOW_DATE >= #{flowDate}
    </update>
</mapper>
