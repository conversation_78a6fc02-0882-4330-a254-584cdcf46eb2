##通用参数
#parse("PublicMacro/ConstantMarco.vm")
#ConstantParams()
<template>
    <div class="xh-content-wrapper">
##  <!-- 左侧树 -->
    #if(${context.leftTreeTable} == true)
        <div class="xh-content-wrapper-left">
            <BasicLeftTree v-bind="getLeftTreeBindValue" ref="leftTreeRef" @reload="getTreeView()"
                           @select="handleLeftTreeSelect" />
        </div>
    #end
##  <!-- 左侧树 -->
        <div class="xh-content-wrapper-center">
##      <!-- 有搜索 -->
        #if(${context.columnData.searchList.size()}>0)
            <div class="xh-content-wrapper-search-box">
                <BasicForm @register="registerSearchForm" :schemas="searchSchemas"
                           @advanced-change="redoHeight" @submit="handleSearchSubmit" @reset="handleSearchReset"
                           class="search-form">
                </BasicForm>
            </div>
        #end
##      <!-- 有搜索 -->
            <div class="xh-content-wrapper-content">
##              按键
                <BasicTable @register="registerTable" v-bind="getTableBindValue" ref="tableRef"
                            @columns-change="handleColumnChange">
                #if(${context.btnPcList.size()}>0)
                    <template #tableTitle>
                    #foreach($btn in  ${context.btnPcList})
                        #if(${btn.value}=='add')
                        <a-button type="primary" preIcon="${btn.icon}" #if(${context.columnData.useBtnPermission}) v-auth="'btn_${btn.value}'" #end
                                  @click="addHandle()">${btn.label}</a-button>
                        #end
                        #if(${btn.value}=='download')
                        <a-button type="link" preIcon="${btn.icon}" #if(${context.columnData.useBtnPermission}) v-auth="'btn_${btn.value}'" #end
                                  @click="openExportModal(true, { columnList: state.exportList })">${btn.label}</a-button>
                        #end
                        #if(${btn.value}=='upload')
                        <a-button type="link" preIcon="${btn.icon}" #if(${context.columnData.useBtnPermission}) v-auth="'btn_${btn.value}'" #end
                                  @click="openImportModal(true, { url: '${context.module}/${context.className}' })">${btn.label}</a-button>
                        #end
                        #if(${btn.value}=='batchRemove')
                        <a-button type="link" preIcon="${btn.icon}" #if(${context.columnData.useBtnPermission}) v-auth="'btn_${btn.value}'" #end
                                  @click="handelBatchRemove()">${btn.label}</a-button>
                        #end
                        #if(${btn.value}=='batchPrint')
                        <a-button type="link" preIcon="${btn.icon}" #if(${context.columnData.useBtnPermission}) v-auth="'btn_${btn.value}'" #end
                                  @click="handelBatchPrint()">${btn.label}</a-button>
                        #end
                    #end
                    </template>
                #end
##              <!-- 有高级查询：开始 -->
                #if(${context.superQuery})
                    <template #toolbar>
                        <a-tooltip placement="top">
                            <template #title>
                                <span>{{ t('common.superQuery') }}</span>
                            </template>
                            <filter-outlined @click="openSuperQuery(true, { columnOptions: superQueryJson })" />
                        </a-tooltip>
                    </template>
                #end
##              <!-- 有高级查询：结束 -->
##              <!-- 有子表且是折叠展示：开始 -->
                #if(${context.childTableStyle}==2)
                    <template #expandedRowRender="{ record }" v-if="childColumnList.length">
                        <a-tabs size="small">
                            <a-tab-pane :key="cIndex" :tab="child.label" :label="child.label"
                                        v-for="(child, cIndex) in childColumnList">
                                <a-table size="small" :data-source="record[child.prop]" :columns="child.children"
                                         :pagination="false" :scroll="{ x: 'max-content' }">
                                    <template #bodyCell="{ column, record: childRecord }">
                                        <template v-if="column.xhKey === 'relationForm'">
                                            <p class="link-text"
                                               @click="toDetail(column.modelId, childRecord[column.dataIndex+`_id`])">
                                                {{ childRecord[column.dataIndex] }}</p>
                                        </template>
                                        <template v-if="column.xhKey === 'inputNumber'">
                                            <xh-input-number v-model:value="record[column.prop]" :precision="column.precision" :thousands="column.thousands" disabled detailed />
                                        </template>
                                    </template>
                                </a-table>
                            </a-tab-pane>
                        </a-tabs>
                    </template>
                #end
##              <!-- 有子表且是折叠展示：结束 -->
                    <template #bodyCell="{ column, record, index }">
##                  <!--  有子表且是分组展示：开始 -->
                    #if(${context.childTableStyle}==1)
                        <template v-for="(item, index) in childColumnList" v-if="childColumnList.length">
                            <template
                                    v-if="column?.id?.includes('-') && item.children && item.children[0] && column.key === item.children[0]?.dataIndex">
                                <ChildTableColumn :data="record[item.prop]" :head="item.children"
                                                  @toggleExpand="toggleExpand(record, item.prop+`Expand`)" @toDetail="toDetail"
                                                  :expand="record[item.prop+`Expand`]" :key="index" />
                            </template>
                        </template>
                    #end
##                  <!-- 有子表且是折叠展示：结束 -->
                        <template v-if="column.xhKey === 'relationForm'">
                            <p class="link-text"
                               @click="toDetail(column.modelId, record[column.dataIndex+`_id`])">
                                {{ record[column.dataIndex] }}</p>
                        </template>
                        <template v-if="column.xhKey === 'inputNumber'">
                            <xh-input-number v-model:value="record[column.prop]" :precision="column.precision" :thousands="column.thousands" disabled detailed />
                        </template>
##                  <!-- 有工作流：开始 -->
                    #if($!{context.isFlow})
                        <template v-if="column.key === 'flowState' && !record.top">
                            <a-tag color="processing" v-if="record.flowState == 1">等待审核</a-tag>
                            <a-tag color="success" v-else-if="record.flowState == 2">审核通过</a-tag>
                            <a-tag color="error" v-else-if="record.flowState == 3">审核退回</a-tag>
                            <a-tag v-else-if="record.flowState == 4">流程撤回</a-tag>
                            <a-tag v-else-if="record.flowState == 5">审核终止</a-tag>
                            <a-tag color="error" v-else-if="record.flowState == 6">已被挂起</a-tag>
                            <a-tag color="warning" v-else>等待提交</a-tag>
                        </template>
                    #end
##                  <!-- 有工作流：结束 -->
                        <template v-if="column.key === 'action' && !record.top">
                            <TableAction :actions="getTableActions(record)" />
                        </template>
                    </template>
##              <!-- 有合计：开始 -->
                #if(${context.configurationTotal} == true)
                    <template #summary v-if="state.cacheList.length">
                        <a-table-summary fixed>
                            <a-table-summary-row>
                                <a-table-summary-cell :index="0">合计</a-table-summary-cell>
                                <a-table-summary-cell v-for="(item, index) in getColumnSum" :key="index"
                                                      :index="index + 1">{{ item }}</a-table-summary-cell>
                                <a-table-summary-cell :index="getColumnSum.length + 1"></a-table-summary-cell>
                            </a-table-summary-row>
                        </a-table-summary>
                    </template>
                #end
##              <!-- 有合计：结束 -->
                </BasicTable>
            </div>
        </div>
        #if(!$context.isFlow)
        <Form ref="formRef" @reload="reload" />
        #end
#foreach($itemBtn in ${context.columnBtnPcList})
    #if(!${context.isFlow} && ${itemBtn.value}=="detail")
        <Detail ref="detailRef" />
    #end
    #if(${itemBtn.value}=="edit")
    #end
    #if(${itemBtn.value}=="remove")
    #end
#end
#foreach($itemBtn in ${context.btnPcList})
    #if(${itemBtn.value}=="add")
    #end
    #if(${itemBtn.value}=="upload")
        <ImportModal @register="registerImportModal" @reload="reload" />
    #end
    #if(${itemBtn.value}=="download")
        <ExportModal @register="registerExportModal" @download="handleDownload" />
    #end
    #if(${itemBtn.value}=="batchRemove")
    #end
    #if(${itemBtn.value}=="batchPrint")
        <PrintSelect @register="registerPrintSelect" @change="handleShowBrowse" />
        <PrintBrowse @register="registerPrintBrowse" />
    #end
#end
##        <!-- 有关联表单详情：开始 -->
        <RelationDetail ref="relationDetailRef" />
##        <!-- 有关联表单详情：结束 -->
#if(${context.superQuery})
##      <!-- 有高级查询：开始 -->
        <SuperQueryModal @register="registerSuperQueryModal" @superQuery="handleSuperQuery" />
##      <!-- 有高级查询：结束 -->
#end
#if(${context.isFlow})
##      <!-- 带流程：开始 -->
        <FlowParser @register="registerFlowParser" @reload="reload" />
        <BasicModal v-bind="$attrs" @register="registerFlowListModal" title="请选择流程" :footer="null"
                    :width="400" destroyOnClose class="xh-flow-list-modal">
            <div class="template-list">
                <ScrollContainer>
                    <div class="template-item" v-for="item in flowList" :key="item.id"
                         @click="selectFlow(item)">
                        {{ item.fullName }}
                    </div>
                </ScrollContainer>
            </div>
        </BasicModal>
##      <!-- 带流程：结束 -->
#end
    </div>
</template>

<script lang="ts" setup>

    import {batchDelete, del, exportData, getList} from './helper/api';
    import {getConfigData} from '/@/api/onlineDev/visualDev';
    // 工作流
    import {getFlowByFormId} from '/@/api/workFlow/formDesign';
    import {getFlowList} from '/@/api/workFlow/flowEngine';
    import {getDictionaryDataSelector} from '/@/api/systemData/dictionary';
    import {getDataInterfaceRes} from '/@/api/systemData/dataInterface';
    import {computed, nextTick, onMounted, reactive, ref, toRefs, unref} from 'vue';
    import {useMessage} from '/@/hooks/web/useMessage';
    import {useI18n} from '/@/hooks/web/useI18n';
    import {useOrganizeStore} from '/@/store/modules/organize';
    import {useUserStore} from '/@/store/modules/user';
    import {useModal} from '/@/components/Modal';
    import {usePopup} from '/@/components/Popup';
    import {TreeActionType} from '/@/components/Tree';
    import {useForm} from '/@/components/Form';
    import {ActionItem, TableActionType, useTable} from '/@/components/Table';
    // 有关联表单详情：开始
    import {downloadByUrl} from '/@/utils/file/download';
    // 打印模板多条生成PrintSelect
    import {useRoute} from 'vue-router';
    import {getSearchFormSchemas} from '/@/components/FormGenerator/src/helper/transform';
    import {cloneDeep} from 'lodash-es';
    import columnList from './helper/columnList';
    import searchList from './helper/searchList';
    import {dyOptionsList} from '/@/components/FormGenerator/src/helper/config';
    import {thousandsFormat} from '/@/utils/xh';
        #if(${context.isFlow})
        // 工作流
#end
        #if(${context.superQuery})
        #end
    #if(!$context.isFlow)
    #end
#foreach($itemBtn in ${context.columnBtnPcList})
#if(!${context.isFlow} &&${itemBtn.value}=="detail")
#end
#if(${itemBtn.value}=="edit")
#end
#if(${itemBtn.value}=="remove")
#end
#end
    // 有关联表单详情：结束
#if(${context.childTableStyle}==1)
#end
#foreach($itemBtn in ${context.btnPcList})
#if(${itemBtn.value}=="add")
#end
#if(${itemBtn.value}=="upload")
#end
#if(${itemBtn.value}=="download")
#end
#if(${itemBtn.value}=="batchRemove")
#end
#if(${itemBtn.value}=="batchPrint")
#end
#end

    interface State {
        formFlowId: string;
        flowList: any[];
        config: any;
        columnList: any[];
        printListOptions: any[];
        columnBtnsList: any[];
        customBtnsList: any[];
        treeFieldNames: any;
        leftTreeData: any[];
        leftTreeLoading: boolean;
        treeActiveId: string;
        treeActiveNodePath: any;
        columns: any[];
        complexColumns: any[];
        childColumnList: any[];
        exportList: any[];
        cacheList: any[];
        currFlow: any;
        isCustomCopy: boolean;
        candidateType: number;
        currRow: any;
        workFlowFormData: any;
        expandObj: any;
        columnSettingList: any[];
        searchSchemas: any[];
        treeRelationObj: any;
        resetFromTree: boolean;
    }

    const route = useRoute();
    const { createMessage, createConfirm } = useMessage();
    const { t } = useI18n();
    const organizeStore = useOrganizeStore();
    const userStore = useUserStore();
    const userInfo = userStore.getUserInfo;

    const [registerExportModal, { openModal: openExportModal, closeModal: closeExportModal, setModalProps: setExportModalProps }] = useModal();
    const [registerImportModal, { openModal: openImportModal }] = useModal();
    const [registerSuperQueryModal, { openModal: openSuperQuery }] = useModal();
    #if(${context.hasPrintBtn})
    const [registerPrintSelect, { openModal: openPrintSelect }] = useModal();
    const [registerPrintBrowse, { openModal: openPrintBrowse }] = useModal();
    #end
    #if(${context.isFlow})
    // 工作流
    const [registerFlowParser, { openPopup: openFlowParser }] = usePopup();
    const [registerFlowListModal, { openModal: openFlowListModal, closeModal: closeFlowListModal }] = useModal();
    #end
    #if(${context.leftTreeTable})
    const leftTreeRef = ref<Nullable<TreeActionType>>(null);
    #end
    const formRef = ref<any>(null);
    const tableRef = ref<Nullable<TableActionType>>(null);
    const detailRef = ref<any>(null);
    const relationDetailRef = ref<any>(null);
    const defaultSearchInfo = {
        menuId: route.meta.modelId as string,
        moduleId:'${context.moduleId}',
        superQueryJson: '',
    };
    const searchInfo = reactive({
        ...cloneDeep(defaultSearchInfo),
    });
    const state = reactive<State>({
        formFlowId: '',
        flowList: [],
        config: {},
        columnList: [],
        printListOptions: [],
        columnBtnsList: [],
        customBtnsList: [],
        treeFieldNames: {
            children: #if(${context.columnData.treePropsChildren}) '${context.columnData.treePropsChildren}' #else 'children' #end,
            title: #if(${context.columnData.treePropsLabel}) '${context.columnData.treePropsLabel}' #else 'fullName' #end,
            key: #if(${context.columnData.treePropsValue}) '${context.columnData.treePropsValue}' #else 'id' #end,
            isLeaf: 'isLeaf',
        },
        leftTreeData: [],
        leftTreeLoading: false,
        treeActiveId: '',
        treeActiveNodePath: [],
        columns: [],
        complexColumns: [], // 复杂表头
        childColumnList: [],
        exportList: [],
        cacheList: [],
        currFlow: {},
        isCustomCopy: false,
        candidateType: 1,
        currRow: {},
        workFlowFormData: {},
        expandObj: {},
        columnSettingList: [],
        searchSchemas: [],
        treeRelationObj: null,
        resetFromTree: false,
    });
    const { flowList, childColumnList, searchSchemas } = toRefs(state);
    const [registerSearchForm, { updateSchema, resetFields, setFieldsValue, submit: searchFormSubmit }] = useForm({
        baseColProps: { span: 6 },
        showActionButtonGroup: true,
        showAdvancedButton: true,
        compact: true,
    });
    const [registerTable, { reload, setLoading, getFetchParams, getSelectRowKeys, redoHeight,clearSelectedRowKeys }] = useTable({
        api: getList,
        immediate: false,
        clickToRowSelect: false,
        afterFetch: (data) => {
            const list = data.map((o) => ({
                ...o,
                ...state.expandObj,
            }));
            state.cacheList = cloneDeep(list);
            #if(${context.groupTable})
            list.map(o => {
                if (o.children && o.children.length) {
                    o.children = o.children.map(e => ({
                        ...e,
                        ...state.expandObj,
                    }));
                }
            });
            #end
            return list;
        },
    });
##左侧属性
#if(${context.leftTreeTable})
    const getLeftTreeBindValue = computed(() => {
        const key = +new Date();
        const data: any = {
            title: '${context.columnData.treeTitle}',
            showSearch: #if(${context.columnData.treeDataSource} == 'api' && ${context.columnData.treeSynType} == 1) false, #else true, //异步的时候为false  #end
            fieldNames: state.treeFieldNames,
            defaultExpandAll: #if(${context.columnData.treeDataSource} == 'api' && ${context.columnData.treeSynType} == 1) false, #else true, //异步的时候为false  #end
            treeData: state.leftTreeData,
            loading: state.leftTreeLoading,
            key,
            #if(${context.columnData.treeDataSource} == 'api' && ${context.columnData.treeSynType} == 1)
                //异步
                loadData: onLoadData,
                //异步
            #end
        };
        return data;
    });
#end

    const getTableBindValue = computed(() => {
#if(${context.childTableStyle}==1)
        let columns = state.complexColumns;
#else
        let columns = state.columns;
#end
#if(${context.isFlow})
        columns.push({ title: '状态', dataIndex: 'flowState', width: 100 });
#end
        const data: any = {
#if(!${context.hasPage} || ${context.groupTable} || ${context.treeTable})
            pagination: false, //没有分页，树形，分组
#else
            pagination: { pageSize: ${context.columnData.pageSize} }, //有分页
#end
            searchInfo: unref(searchInfo),
            defSort: {
                sort: "${context.columnData.sort}", //sort
                sidx: "${context.columnData.defaultSidx}", //取defaultSidx
            },
            columns,
            rowSelection: {
                type: 'checkbox',
                getCheckboxProps: (record) => ({ disabled: !!record.top }),
            },
#if(${context.groupTable} || ${context.treeTable})
            isTreeTable: true,
#end
#if(${context.childTableStyle}==1)
            bordered: true,
#end
            actionColumn: {
                width: 150,
                title: '操作',
                dataIndex: 'action',
            },
        };
        return data;
    });
##合计变量
#if(${context.configurationTotal})
    const getSummaryColumn = computed(() => {
    let defaultColumns = state.columns;
    // 处理列固定
    if (state.columnSettingList?.length) {
        for (let i = 0; i < defaultColumns.length; i++) {
            inner: for (let j = 0; j < state.columnSettingList.length; j++) {
                if (defaultColumns[i].dataIndex === state.columnSettingList[j].dataIndex) {
                    defaultColumns[i].fixed = state.columnSettingList[j].fixed;
                    defaultColumns[i].visible = state.columnSettingList[j].visible;
                    break inner;
                }
            }
        }
        defaultColumns = defaultColumns.filter((o) => o.visible);
    }
    let columns: any[] = [];
    for (let i = 0; i < defaultColumns.length; i++) {
        if (defaultColumns[i].xhKey === 'table') {
            columns.push(...defaultColumns[i].children);
        } else {
            columns.push(defaultColumns[i]);
        }
    }
    const leftFixedList = columns.filter((o) => o.fixed === 'left');
    const rightFixedList = columns.filter((o) => o.fixed === 'right');
    const noFixedList = columns.filter((o) => o.fixed !== 'left' && o.fixed !== 'right');
    return [...leftFixedList, ...noFixedList, ...rightFixedList];
});
    const getColumnSum = computed(() => {
    const sums: any[] = [];
    const summaryField: any =#if(${context.fieldsTotal})${context.fieldsTotal}#else [] #end; //取summaryField
    const isSummary = (key) => summaryField.includes(key);
    const useThousands = key => unref(getSummaryColumn).some(o => o.__vModel__ === key && o.thousands);
    unref(getSummaryColumn).forEach((column, index) => {
        let sumVal = state.cacheList.reduce((sum, d) => sum + getCmpValOfRow(d, column.prop), 0);
        if (!isSummary(column.prop)) sumVal = '';
        sumVal = Number.isNaN(sumVal) ? '' : sumVal;
        const realVal = sumVal && !Number.isInteger(sumVal) ? Number(sumVal).toFixed(2) : sumVal;
        sums[index] = useThousands(column.prop) ? thousandsFormat(realVal) : realVal;
    });
    // 有多选
    sums.unshift('');
    // 有多选
    return sums;
});
#end

    function init() {
        state.config = {};
    #if(${context.isFlow})
        // 带流程
        getFlowId();
    #end
        searchInfo.menuId = route.meta.modelId as string;
        state.columnList = columnList;
    #if(${context.groupTable})
        // 分组
        state.columnList = state.columnList.filter((o) => o.prop != '${context.groupField}');
    #end
        setLoading(true);
        getSearchSchemas();
        getColumnList();
    #if(${context.leftTreeTable})
        // 有左侧树
        getTreeView(true);
    #else
        nextTick(() => {
    #if(${context.columnData.searchList.size()}>0)
        // 有搜索列表
        searchFormSubmit();
    #else
        //  无搜索列表
        reload({ page: 1 });
    #end
        });
    #end
    }
    #if(${context.leftTreeTable})
    // 有左侧树
    async function getTreeView(isInit = false) {
        state.leftTreeLoading = true;
        state.leftTreeData = [];
    #if(${context.columnData.treeDataSource}=='dictionary')
        // 左侧数据字典
        getDictionaryDataSelector('${context.columnData.treeDictionary}').then(res => {
          state.leftTreeData = res.data.list;
          state.leftTreeLoading = false;
          nextTick(() => {
              #if(${context.columnData.searchList.size()}>0)
                  // 有搜索列表
                  if (isInit)    searchFormSubmit();
              #else
                  //  无搜索列表
                  if (isInit)   reload({ page: 1 });
              #end
          });
        });
    #elseif(${context.columnData.treeDataSource}=='api')
        // 数据接口
        getDataInterfaceRes('${context.columnData.treePropsUrl}').then((res) => {
            state.leftTreeData = Array.isArray(res.data) ? res.data : [];
            state.leftTreeLoading = false;
            nextTick(() => {
                #if(${context.columnData.searchList.size()}>0)
                    // 有搜索列表
                    if (isInit)    searchFormSubmit();
                #else
                    //  无搜索列表
                    if (isInit)   reload({ page: 1 });
                #end
            });
        });
    #elseif(${context.columnData.treeDataSource}=='organize' ||${context.columnData.treeDataSource}=='department' )
        //组织或者部门
        state.leftTreeData = await organizeStore.getOrganizeTree();
        state.leftTreeLoading = false;
        nextTick(() => {
            #if(${context.columnData.searchList.size()}>0)
                // 有搜索列表
                if (isInit)    searchFormSubmit();
            #else
                //  无搜索列表
                if (isInit)   reload({ page: 1 });
            #end
        });
    #end
    }
    #end
    function getSearchSchemas() {
        #if(${context.leftTreeTable})
            // 有左侧树，有关联字段
            for (let i = 0; i < searchList.length; i++) {
                const e = searchList[i];
                if (e.id === '${context.columnData.treeRelation}') {
                    state.treeRelationObj = e;
                    break;
                }
            }
            // 搜索字段里无左侧树关联字段时，去全部字段里获取关联字段属性
            if (!state.treeRelationObj) {
                for (let i = 0; i < state.columnList.length; i++) {
                    const e = state.columnList[i];
                    if (e.id === '${context.columnData.treeRelation}') {
                        state.treeRelationObj = { ...e, searchMultiple: false, xhKey: e.__config__.xhKey };
                        break;
                    }
                }
            }
        #end

        const schemas = getSearchFormSchemas(searchList);
        state.searchSchemas = schemas;
        schemas.forEach((cur) => {
            const config = cur.__config__;
            if (dyOptionsList.includes(config.xhKey)) {
                if (config.dataType === 'dictionary') {
                    if (!config.dictionaryType) return;
                    getDictionaryDataSelector(config.dictionaryType).then((res) => {
                        updateSchema([{ field: cur.field, componentProps: { options: res.data.list } }]);
                    });
                }
                if (config.dataType === 'dynamic') {
                    if (!config.propsUrl) return;
                    const query = { paramList: config.templateJson || [] };
                    getDataInterfaceRes(config.propsUrl, query).then((res) => {
                        const data = Array.isArray(res.data) ? res.data : [];
                        updateSchema([{ field: cur.field, componentProps: { options: data } }]);
                    });
                }
            }
            if (config.defaultCurrent) {
                if (config.xhKey === 'organizeSelect' && userInfo.organizeIdList?.length) {
                    cur.defaultValue = cur.componentProps.multiple ? [userInfo.organizeIdList] : userInfo.organizeIdList;
                }
                if (config.xhKey === 'depSelect' && config.defaultValue) cur.defaultValue = config.defaultValue;
                if (config.xhKey === 'userSelect' && config.defaultValue) cur.defaultValue = config.defaultValue;
            }
        });
    }
    function getColumnList() {
        #if(${context.columnData.useColumnPermission})
            // 开启列表过滤权限
            let columnList: any[] = [];
            const permissionList = userStore.getPermissionList;
            const list = permissionList.filter(o => o.modelId === searchInfo.menuId);
            const perColumnList = list[0] && list[0].column ? list[0].column : [];
            for (let i = 0; i < state.columnList.length; i++) {
                inner: for (let j = 0; j < perColumnList.length; j++) {
                    if (state.columnList[i].prop === perColumnList[j].enCode) {
                        columnList.push(state.columnList[i]);
                        break inner;
                    }
                }
            }
        #else
            // 没有开启列表权限
            let  columnList = state.columnList;
        #end
        state.exportList = columnList;
        const columns = columnList.map((o) => ({
            ...o,
            title: o.label,
            dataIndex: o.prop,
            align: o.align,
            fixed: o.fixed == 'none' ? false : o.fixed,
            sorter: o.sortable,
            width: o.width || 100,
        }));
        state.columns = columns.filter((o) => o.prop.indexOf('-') < 0);
        getComplexColumns(columns);
    }
    function getComplexColumns(columnList) {
        let list: any[] = [];
        for (let i = 0; i < columnList.length; i++) {
            const e = columnList[i];
            if (!e.prop.includes('-')) {
                list.push(e);
            } else {
                let prop = e.prop.split('-')[0];
                let vModel = e.prop.split('-')[1];
                let label = e.label.split('-')[0];
                let childLabel = e.label.replace(label + '-', '');
                let newItem = {
                    align: 'center',
                    xhKey: 'table',
                    prop,
                    label,
                    title: label,
                    dataIndex: prop,
                    children: [],
                };
                e.dataIndex = vModel;
                e.title = childLabel;
                if (!state.expandObj.hasOwnProperty(prop+`Expand`)) state.expandObj[prop+`Expand`] = false;
                if (!list.some((o) => o.prop === prop)) list.push(newItem);
                for (let i = 0; i < list.length; i++) {
                    if (list[i].prop === prop) {
                        list[i].children.push(e);
                        break;
                    }
                }
            }
        }
        #if(${context.childTableStyle}==1)
            // 行内分组展示
            getMergeList(list);
        #end

        state.complexColumns = list;
        state.childColumnList = list.filter((o) => o.xhKey === 'table');
    }
    function getMergeList(list) {
        list.forEach((item) => {
            if (item.children && item.children.length) {
                item.children.forEach((child, index) => {
                    if (index == 0) {
                        child.customCell = () => ({
                            rowspan: 1,
                            colspan: item.children.length,
                            class: 'child-table-box',
                        });
                    } else {
                        child.customCell = () => ({
                            rowspan: 0,
                            colspan: 0,
                        });
                    }
                });
            }
        });
    }
    function toggleExpand(row, field) {
        row[field] = !row[field];
    }
    // 关联表单查看详情
    function toDetail(modelId, id) {
        if (!id) return;
        getConfigData(modelId).then((res) => {
            if (!res.data || !res.data.formData) return;
            const formConf = JSON.parse(res.data.formData);
            formConf.popupType = 'general';
            const data = { id, formConf, modelId };
            relationDetailRef.value?.init(data);
        });
    }
    function handleColumnChange(data) {
        state.columnSettingList = data;
    }
##行内按键
    function getTableActions(record): ActionItem[] {
        return [
            #foreach($itemBtn in ${context.columnBtnPcList})
                #if(${itemBtn.value}=="edit")
                    {
                        label: '${itemBtn.label}',
                    #if(${context.isFlow})
                        disabled: [1, 2, 4, 5].includes(record.flowState), //有流程加上
                    #end
                        onClick: updateHandle.bind(null, record),
                    #if(${context.columnData.useBtnPermission})
                        auth: 'btn_edit', //有按钮权限
                    #end
                    },
                #end
                #if(${itemBtn.value}=="detail")
                    {
                        label: '${itemBtn.label}',
                    #if(${context.isFlow})
                        disabled: !record.flowState, //有流程加上
                    #end
                        onClick: goDetail.bind(null, record),
                    #if(${context.columnData.useBtnPermission})
                        auth: 'btn_detail', //有按钮权限
                    #end
                    },
                #end
                #if(${itemBtn.value}=="remove")
                    {
                        label: '${itemBtn.label}',
                        color: 'error',
                    #if(${context.isFlow})
                        disabled: [1, 2, 3, 5].includes(record.flowState), //有流程加上
                    #end
                        modelConfirm: {
                            onOk: handleDelete.bind(null, record.id),
                        },
                    #if(${context.columnData.useBtnPermission})
                        auth: 'btn_remove', //有按钮权限
                    #end
                    },
                #end
            #end
        ];
    }
##行内按键方法
#foreach($itemBtn in ${context.columnBtnPcList})
    #if(${itemBtn.value}=="edit")
    // 编辑
    function updateHandle(record) {
    #if(${context.isFlow})
        // 带工作流
        let data = {
          id: record.id,
          flowId: record.flowId || state.flowList[0].id,
          opType: '-1',
        };
        openFlowParser(true, data);
    #else
        // 不带工作流
        const data = {
            id: record.id,
            menuId: searchInfo.menuId,
            allList: state.cacheList,
        };
        formRef.value?.init(data);
    #end
    }
    #end
    #if(${itemBtn.value}=="detail")
    // 查看详情
    function goDetail(record) {
    #if(${context.isFlow})
        // 带流程
        const data = {
            id: record.id,
            flowId: record.flowId || state.flowList[0].id,
            opType: 0,
            status: record.flowState,
        };
        openFlowParser(true, data);
    #else
        // 不带流程
        const data = {
            id: record.id,
        };
        detailRef.value?.init(data);
    #end
    }
    #end
    #if(${itemBtn.value}=="remove")
    // 删除
    function handleDelete(id) {
        del(id).then((res) => {
            createMessage.success(res.msg);
            clearSelectedRowKeys();
            reload();
        });
    }
    #end
#end
##表头按键方法
#foreach($itemBtn in ${context.btnPcList})
    #if(${itemBtn.value}=="add")
    // 新增
    function addHandle() {
        #if(${context.isFlow})
            // 带流程新增
            if (!state.flowList.length) return createMessage.error('流程不存在');
            if (state.flowList.length === 1) return selectFlow(state.flowList[0]);
            openFlowListModal(true);
        #else
            // 不带流程新增
            const data = {
                id: '',
                menuId: searchInfo.menuId,
                allList: state.cacheList,
            };
            formRef.value?.init(data);
        #end
    }
    #end
    #if(${itemBtn.value}=="upload")
    #end
    #if(${itemBtn.value}=="download")
    // 导出
    function handleDownload(data) {
        let query = { ...getFetchParams(), ...data };
        exportData(query)
                .then((res) => {
                    setExportModalProps({ confirmLoading: false });
                    if (!res.data.url) return;
                    downloadByUrl({ url: res.data.url });
                    closeExportModal();
                })
                .catch(() => {
                    setExportModalProps({ confirmLoading: false });
                });
    }
    #end
    #if(${itemBtn.value}=="batchRemove")
    // 批量删除
    function handelBatchRemove() {
        const ids = getSelectRowKeys();
        if (!ids.length) return createMessage.error('请选择一条数据');
        createConfirm({
            iconType: 'warning',
            title: t('common.tipTitle'),
            content: '您确定要删除这些数据吗, 是否继续?',
            onOk: () => {
                batchDelete(ids).then((res) => {
                    createMessage.success(res.msg);
                    clearSelectedRowKeys();
                    reload();
                });
            },
        });
    }
    #end
    #if(${itemBtn.value}=="batchPrint")
    //打印方法
    function handelBatchPrint() {
    let printIds=#if(${context.columnData.printIds}) ${context.columnData.printIds} #else [] #end
        if (!printIds?.length) return createMessage.error('未配置打印模板');
        const ids = getSelectRowKeys();
        if (!ids.length) return createMessage.error('请选择一条数据');
        if (printIds?.length === 1) return handleShowBrowse(printIds[0]);
        openPrintSelect(true, printIds);
    }
    function handleShowBrowse(id) {
        openPrintBrowse(true, { id, batchIds: getSelectRowKeys().join() });
    }
    #end
#end
##合计方法
#if(${context.configurationTotal})
    //合计方法
    function getCmpValOfRow(row, key) {
        const summaryField: any = #if(${context.fieldsTotal})${context.fieldsTotal}#else [] #end; //取summaryField
        const isSummary = (key) => summaryField.includes(key);
        if (!summaryField.length || !isSummary(key)) return 0;
        const target = row[key];
        if (!target) return 0;
        const data = isNaN(target) ? 0 : Number(target);
        return data;
    }
#end
##开启流程
#if(${context.isFlow})
    function getFlowId() {
        getFlowByFormId("${context.moduleId}").then((res) => {
            const flowId = res.data && res.data.id;
            state.formFlowId = flowId;
            getFlowOptions();
        });
    }
    // 获取子流程list
    function getFlowOptions() {
        getFlowList(state.formFlowId, '1').then((res) => {
            state.flowList = res.data;
        });
    }
    function selectFlow(item) {
        state.currFlow = item;
        closeFlowListModal();
        const data = {
            id: '',
            flowId: item.id,
            opType: '-1',
        };
        openFlowParser(true, data);
    }
#end
##左侧树
#if(${context.leftTreeTable})
    function handleLeftTreeSelect(id, _node, nodePath) {
        if (state.treeActiveId == id) return;
        state.treeActiveId = id;
        state.treeActiveNodePath = nodePath;
        #if(${context.columnData.hasTreeQuery})
            // 有搜索
            state.resetFromTree = true;
            resetFields();
        #end
        updateSearchFormValue();
    }
    #if(${context.columnData.treeDataSource} == 'api' && ${context.columnData.treeSynType} == 1)
    // 左侧树异步加载
    function onLoadData(node) {
        return new Promise((resolve: (value?: unknown) => void) => {
            let treeTemplateJson: any[] = #if($!{context.columnData.treeTemplateJson}) ${context.columnData.treeTemplateJson} #else [] #end; // 获取treeTemplateJson字段
            if (treeTemplateJson.length) {
                for (let i = 0; i < treeTemplateJson.length; i++) {
                    const e = treeTemplateJson[i];
                    e.defaultValue = node[e.relationField] || '';
                }
            }
            const query = { paramList: treeTemplateJson || [] };
            getDataInterfaceRes('${context.columnData.treeInterfaceId}', query).then((res) => {
                const data = Array.isArray(res.data) ? res.data : [];
                leftTreeRef.value?.updateNodeByKey(node.eventKey, { children: data, isLeaf: !data.length });
                resolve();
            });
        });
    }
    #end
#end
##高级查询
#if(${context.superQuery})
    // 高级查询
    function handleSuperQuery(superQueryJson) {
        searchInfo.superQueryJson = superQueryJson;
        reload({ page: 1 });
    }
#end
##有普通查询
#if(${context.columnData.searchList.size()}>0)
    function handleSearchReset() {
        clearSelectedRowKeys();
        if (!state.resetFromTree) updateSearchFormValue();
        if (state.resetFromTree) state.resetFromTree = false;
    }
    function handleSearchSubmit(data) {
        clearSelectedRowKeys();
        let obj = {
            ...defaultSearchInfo,
            superQueryJson: searchInfo.superQueryJson,
            ...data,
        };
        Object.keys(searchInfo).map(key => {
            delete searchInfo[key];
        });
        for (let [key, value] of Object.entries(obj)) {
            searchInfo[key.replaceAll('-', '_')] = value;
        }
        console.log(searchInfo);
        reload({ page: 1 });
    }
    function updateSearchFormValue() {
        if (!state.treeActiveId) return searchFormSubmit();
        let queryJson: any = {};
        const isMultiple = !state.treeRelationObj ? false : state.treeRelationObj.searchMultiple;
        //多级左侧树，需要拼父级->转为查询参数
        if (state.treeRelationObj && state.treeRelationObj.xhKey && ${multipleTwoUnitStr}.includes(state.treeRelationObj.xhKey)) {
            const currValue = state.treeActiveNodePath.map(o => o[state.treeFieldNames.key]);
            queryJson = { '${context.columnData.treeRelation}': isMultiple ? [currValue] : currValue };
        } else {
            queryJson = { '${context.columnData.treeRelation}': isMultiple ? [state.treeActiveId] : state.treeActiveId };
        }
        #if(${context.columnData.searchList.size()}>0)
            // 有搜索列表
            setFieldsValue(queryJson);
            searchFormSubmit();
        #else
            // 无搜索列表
            handleSearchSubmit(queryJson);
        #end
    }
#end

    onMounted(() => {
        init();
    });
</script>
