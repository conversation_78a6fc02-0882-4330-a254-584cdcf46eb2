package com.xinghuo.card.flow.dao;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xinghuo.card.flow.entity.DataFlowAccEntity;

/**
 * 账号明细表
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2022-11-26
 */
public interface DataFlowAccMapper extends BaseMapper<DataFlowAccEntity> {

    public int selectMaxListOrder(DataFlowAccEntity dataFlowAcc);

    public int updateDataFlowAccBalance(DataFlowAccEntity dataFlowAcc);
}
