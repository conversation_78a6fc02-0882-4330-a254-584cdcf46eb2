package com.xinghuo.card.budget.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 预算子项表
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2024-06-28
 */
@Data
@TableName("fin_budget_item")
public class FinBudgetItemEntity implements Serializable {

    /**
     * 主键ID
     */
    @TableId("ID")
    private String id;

    /**
     * 关联 fin_budget.ID
     */
    @TableField("BUDGET_ID")
    private String budgetId;

    /**
     * 预算项类型: CATEGORY-分类, ACCOUNT-账户
     */
    @TableField("ITEM_TYPE")
    private String itemType;

    /**
     * 目标ID (关联 data_sys_paytype.ID 或 data_acc.ID)
     */
    @TableField("TARGET_ID")
    private String targetId;

    /**
     * 目标名称（冗余字段，便于查询）
     */
    @TableField("TARGET_NAME")
    private String targetName;

    /**
     * 预算金额
     */
    @TableField("BUDGETED_AMOUNT")
    private BigDecimal budgetedAmount;

    /**
     * 当前已支出金额（可通过计算得出，也可存储以提高性能）
     */
    @TableField("CURRENT_SPENT")
    private BigDecimal currentSpent;

    /**
     * 预警阈值百分比（默认80%）
     */
    @TableField("ALERT_THRESHOLD")
    private BigDecimal alertThreshold;

    /**
     * 是否已预警（避免重复提醒）
     */
    @TableField("IS_ALERTED")
    private Boolean isAlerted;

    /**
     * 创建人
     */
    @TableField("CREATE_BY")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField("CREATE_TIME")
    private Date createTime;

    /**
     * 更新人
     */
    @TableField("UPDATE_BY")
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField("UPDATE_TIME")
    private Date updateTime;
}