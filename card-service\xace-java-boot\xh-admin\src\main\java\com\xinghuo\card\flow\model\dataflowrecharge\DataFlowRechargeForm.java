package com.xinghuo.card.flow.model.dataflowrecharge;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.DecimalMin;
import java.math.BigDecimal;

/**
 * 充值记录表单数据
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2022-11-27
 */
@Data
public class DataFlowRechargeForm {

    @Schema(description = "主键ID")
    private String id;

    @Schema(description = "充值类型", required = true)
    @JsonProperty("chargeType")
    @NotBlank(message = "充值类型不能为空")
    private String chargeType;

    @Schema(description = "充值日期", required = true)
    @JsonProperty("chargeDate")
    @NotNull(message = "充值日期不能为空")
    private Long chargeDate;

    @Schema(description = "资金账户ID", required = true)
    @JsonProperty("outAccId")
    @NotBlank(message = "资金账户不能为空")
    private String outAccId;

    @Schema(description = "充值账户ID", required = true)
    @JsonProperty("inAccId")
    @NotBlank(message = "充值账户不能为空")
    private String inAccId;

    @Schema(description = "充值金额", required = true)
    @JsonProperty("amount")
    @NotNull(message = "充值金额不能为空")
    @DecimalMin(value = "0.01", message = "充值金额必须大于0")
    private BigDecimal amount;

    @Schema(description = "赠送金额")
    @JsonProperty("addAmount")
    @DecimalMin(value = "0", message = "赠送金额不能小于0")
    private BigDecimal addAmount;


    @Schema(description = "备注")
    @JsonProperty("note")
    private String note;


    @Schema(description = "创建人")
    @JsonProperty("createBy")
    private String createBy;


    @Schema(description = "创建时间")
    @JsonProperty("createTime")
    private String createTime;


    @Schema(description = "最后修改人")
    @JsonProperty("updateBy")
    private String updateBy;


    @Schema(description = "最后修改时间")
    @JsonProperty("updateTime")
    private String updateTime;


}
