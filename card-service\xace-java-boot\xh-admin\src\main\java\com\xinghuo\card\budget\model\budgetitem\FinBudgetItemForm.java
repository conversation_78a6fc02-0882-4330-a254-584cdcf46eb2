package com.xinghuo.card.budget.model.budgetitem;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 预算子项表单模型
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2024-06-28
 */
@Data
public class FinBudgetItemForm {

    /**
     * 主键ID
     */
    private String id;

    /**
     * 关联预算ID
     */
    @NotBlank(message = "预算ID不能为空")
    private String budgetId;

    /**
     * 预算项类型: CATEGORY-分类, ACCOUNT-账户
     */
    @NotBlank(message = "预算项类型不能为空")
    private String itemType;

    /**
     * 目标ID (关联 data_sys_paytype.ID 或 data_acc.ID)
     */
    @NotBlank(message = "目标ID不能为空")
    private String targetId;

    /**
     * 目标名称
     */
    private String targetName;

    /**
     * 预算金额
     */
    @NotNull(message = "预算金额不能为空")
    private BigDecimal budgetedAmount;

    /**
     * 预警阈值百分比（默认80%）
     */
    private BigDecimal alertThreshold;
}