<template>
  <component :prop-model-id="modelId" :params="params" :is="componentName" />
</template>

<script setup lang="ts">
  import { ref, markRaw } from 'vue';
  import { createAsyncComponent } from '/@/utils/factory/createAsyncComponent';
  import { importViewsFile } from '/@/utils/index';

  const componentName = ref<any>(null);

  const props = defineProps(['modelId', 'params', 'formUrl']);

  if (props.formUrl) {
    const viewer = markRaw(createAsyncComponent(() => importViewsFile(props.formUrl)));
    componentName.value = viewer;
  } else {
    const viewer = markRaw(createAsyncComponent(() => import('/@/views/common/dynamicModel/index.vue')));
    componentName.value = viewer;
  }
</script>
