#set($mapObject ="Map<String, Object>")
##
#macro(DataChange $entity $systemList,$isMast,$isEdit)
    #set($currtCountList=1)
    #set($userCountList=1)
    #set($dateRangeCount=1)
    #set($comSelectCount=1)
    #set($xh='xh')
    #set($editNeedName = "")
    #if($isEdit)
        #set($editNeedName = "_name")
    #end
    #foreach($field in ${systemList})
        #set($fieldName=${field.vModel})
        #if($fieldName != '')
            #set($isMutiple = ${field.multiple})
            #set($key = ${field.config.xhKey})
            #set($FieldName="${field.vModel.substring(0,1).toUpperCase()}${field.vModel.substring(1)}")
            #set($model = "${field.vModel.substring(0,1).toUpperCase()}${field.vModel.substring(1)}$editNeedName")
            #if(${field.config.dataType}=='dictionary' && ${field.config.dictionaryType})
                ${entity}.set${model}(generaterSwapUtil.getDicName(${entity}.get${FieldName}(),"${field.config.dictionaryType}"));
            #elseif(${field.config.dataType}=='dynamic' && ${field.config.propsUrl})
                #set($label=${field.props.propsModel.label})
                #set($value=${field.props.propsModel.value})
                #set($children = ${field.props.propsModel.children})
                #if($!{field.config.props})
                    #set($label= ${field.config.props.label})
                    #set($value= ${field.config.props.value})
                #end
                #if(${key}=='cascader')
                    ${entity}.set${model}(generaterSwapUtil.getDynName("${field.config.propsUrl}" ,"${label}" ,"${value}","$!{children}" ,${entity}.get${FieldName}(),${isMutiple},${field.showAllLevels},${field.templateJson},-1,dataMap));
                #else
                    ${entity}.set${model}(generaterSwapUtil.getDynName("${field.config.propsUrl}" ,"${label}" ,"${value}","$!{children}" ,${entity}.get${FieldName}(),${isMutiple},true,${field.templateJson},-1,dataMap));
                #end
            #else
                #if(${key}=='createUser'||${key}=='modifyUser')
                    ${entity}.set${model}(generaterSwapUtil.userSelectValue(${entity}.get${FieldName}()));
                #elseif(${key}=='currOrganize')
                    ${entity}.set${model}(generaterSwapUtil.comSelectValue(${entity}.get${FieldName}(), "${field.showLevel}"));
                #elseif(${key}=='currPosition')
                    ${entity}.set${model}(generaterSwapUtil.posSelectValue(${entity}.get${FieldName}()));
                #elseif(${key}=='areaSelect')
                    ${entity}.set${model}(generaterSwapUtil.provinceData(${entity}.get${FieldName}()));
                #elseif(${key}=='organizeSelect'||${key}=='depSelect')
                    ${entity}.set${model}(generaterSwapUtil.comSelectValues(${entity}.get${FieldName}(),$isMutiple));
                    #set($comSelectCount=${comSelectCount}+1)
                #elseif(${key}=='posSelect')
                    ${entity}.set${model}(generaterSwapUtil.posSelectValues(${entity}.get${FieldName}()));
                #elseif(${key}=='userSelect')
                    ${entity}.set${model}(generaterSwapUtil.userSelectValues(${entity}.get${FieldName}()));
                #elseif(${key}=='usersSelect')
                    ${entity}.set${model}(generaterSwapUtil.usersSelectValues(${entity}.get${FieldName}()));
                #elseif(${key}=='groupSelect')
                    ${entity}.set${model}(generaterSwapUtil.getGroupSelect(${entity}.get${FieldName}()));
                #elseif(${key}=='roleSelect')
                    ${entity}.set${model}(generaterSwapUtil.getRoleSelect(${entity}.get${FieldName}()));
                #elseif(${key}=='inputNumber')
                    ${entity}.set${FieldName}_name(generaterSwapUtil.getDecimalStr(${entity}.get${FieldName}()));
                #elseif(${key}=='switch')
                    #set($activeTxt = ${field.activeTxt})
                    #set($inactiveTxt = ${field.inactiveTxt})
                    ${entity}.set${model}(generaterSwapUtil.switchSelectValue(${entity}.get${FieldName}() ,"$activeTxt" ,"$inactiveTxt"));
                #elseif(${key}=='relationForm')
                    #set($MapName =${fieldName})
                    #if($isMast == 'mast')
                        #set($MapName = "xh_${field.config.tableName}_xh_${fieldName}")
                    #end

                    ${entity}.set${field.vModel.substring(0,1).toUpperCase()}${field.vModel.substring(1)}_id(${entity}.get${model}());

                    $mapObject ${MapName}Map = new HashMap<>();
                    ${entity}.set${model}(generaterSwapUtil.swapRelationFormValue("${field.relationField}",${entity}.get${FieldName}(),"${field.modelId}",${MapName}Map));
                #elseif(${key}=='popupSelect' || ${key}=='popupTableSelect')
                    #set($MapName =${fieldName})
                    #if($isMast == 'mast')
                        #set($MapName = "xh_${field.config.tableName}_xh_${fieldName}")
                    #end
                    $mapObject ${MapName}Map = new HashMap<>();
                    ${entity}.set${model}(generaterSwapUtil.getPopupSelectValue("${field.interfaceId}","${field.propsValue}","${field.relationField}",${entity}.get${FieldName}(),${MapName}Map,${field.templateJson},-1,dataMap));
                #elseif(${key}=='uploadFile' || ${key}=='uploadImg')
                    ${entity}.set${model}(generaterSwapUtil.getFileNameInJson(${entity}.get${FieldName}()));
                #elseif(${key}=='datePicker' && $editNeedName == "_name")
                    ${entity}.set${model}(${entity}.get${FieldName}()!=null?
                    new Date(${entity}.get${FieldName}()):null);
                #else
                    ${entity}.set${model}(${entity}.get${FieldName}());
                #end
            #end

        #end
    #end
    #foreach($field in ${systemList})
        #set($key = ${field.config.xhKey})
        #if($key == "relationFormAttr" || $key == "popupAttr")
            #set($attributeName = "${field.relationField}_${field.showField}")
            #set($model = "${attributeName.substring(0,1).toUpperCase()}${attributeName.substring(1)}")
            #set($relationName = "${field.relationField.substring(0,1).toUpperCase()}${field.relationField.substring(1)}")
            #if(${field.config.isStorage}==1)
                ${entity}.set${model}(${field.relationField}Map.get("${field.showField}") !=null ? ${field.relationField}Map.get("${field.showField}").toString() : "");
            #end

        #end
    #end
#end
##
#macro(CreateField $List $MapKey $groupField)
    #foreach($fieLdsModel in $List)
        #set($html = $fieLdsModel.fieLdsModel)
        #if($isMast)
            #set($html = $fieLdsModel.formColumnModel.fieLdsModel)
        #end
        #set($vModel = "${html.vModel}")
        #set($config = $html.config)
        #set($xhkey = "${config.xhKey}")
        #if($vModel != '')
            #if($vModel == $groupField)
                #if(${xhkey}!='text' && ${xhkey}!='divider')
                    #if(${xhkey}=='inputNumber' || ${xhkey}=='calculate' )
                        #if($!{fieLdsModel.formColumnModel.fieLdsModel.precision})
                            #set($MapKey = "BigDecimal")
                        #else
                            #set($MapKey = "Integer")
                        #end
                    #elseif(${xhkey}=='slider'|| ${xhkey}=='rate')
                        #set($MapKey = "Integer")
                    #elseif(${xhkey}=='modifyTime' || ${xhkey}=='createTime')
                        #set($MapKey = 'datePicker')
                    #elseif(${xhkey}=='datePicker')
                        #set($MapKey = 'datePicker')
                    #else
                        #set($MapKey = "String")
                    #end
                #end
            #end
        #end
    #end
#end
##
#macro(StaticDataSwap $tableType,$mapName,$lowName,$parentVModel)
    #foreach($cf in ${listOptions})
        #set($flag = true)
        #set($prexName = "_name")
        #if($!{parentVModel})
            #set($flag = "${parentVModel}"=="${cf.config.parentVModel}")
            #set($prexName = "")
        #end
        #if(${cf.tableType} == ${tableType} && $flag && ${cf.config.dataType} == 'static')
            if (${mapName}.get("$!{lowName}${cf.vModel}") != null && StringUtil.isNotEmpty(${mapName}.get("$!{lowName}${cf.vModel}").toString())){
            ${mapName}.put("$!{lowName}${cf.vModel}$!{prexName}" ,GeneraterSwapUtil.selectStaitcSwap(${mapName}.get("$!{lowName}${cf.vModel}" ).toString(),
            ${cf.options},
            "${cf.props.value}" ,"${cf.props.label}" ,${cf.multiple}));
            }
        #end
        #if(${cf.tableType} == ${tableType} && $flag && ${cf.xhKey} == 'datePicker')
            if( ${mapName}.get("$!{lowName}${cf.vModel}") != null && StringUtil.isNotEmpty(${mapName}.get("$!{lowName}${cf.vModel}").toString())){
            ${mapName}.put("$!{lowName}${cf.vModel}$!{prexName}",DateUtil.dateToString(
            new Date(Long.parseLong(${mapName}.get("$!{lowName}${cf.vModel}").toString())),"${cf.format}"));
            }
        #end
    #end
#end
##  导入实例信息添加
#macro(TemlateDownloadDemo $MapName $Field)
    #set($key = $Field.config.xhKey)
    #set($mul = $Field.multiple)
    #set($level = $Field.level)
    #set($vModelThis = $Field.vModel)
    #if($Field.beforeVmodel)
        #set($vModelThis = $Field.beforeVmodel)
    #end
    #if($key == "createUser" || $key == "modifyUser" || $key == "createTime" || $key == "modifyTime" || $key == "currOrganize" || $key == "currPosition" || $key == "currDept" || $key == "billRule")
        ${MapName}.put("${vModelThis}", "系统自动生成");
    #elseif($key == 'organizeSelect')
        #if($mul)
        ${MapName}.put("${vModelThis}", "例:XX信息/销售部,XX信息/财务部");
        #else
        ${MapName}.put("${vModelThis}", "例:XX信息/财务部");
        #end
    #elseif($key == "depSelect")
        #if($mul)
        ${MapName}.put("${vModelThis}", "例:销售部/部门编码,财务部/部门编码");
        #else
        ${MapName}.put("${vModelThis}", "例:财务部/部门编码");
        #end
    #elseif($key == "posSelect")
        #if($mul)
        ${MapName}.put("${vModelThis}", "例:技术经理/岗位编码,技术员/岗位编码");
        #else
        ${MapName}.put("${vModelThis}", "例:技术员/岗位编码");
        #end
    #elseif($key == "userSelect")
        #if($mul)
        ${MapName}.put("${vModelThis}", "例:张三/账号,李四/账号");
        #else
        ${MapName}.put("${vModelThis}", "例:张三/账号");
        #end
    #elseif($key == "usersSelect")
        #if($mul)
        ${MapName}.put("${vModelThis}", "例:方方/账号,财务部/部门编码");
        #else
        ${MapName}.put("${vModelThis}", "例:方方/账号");
        #end
    #elseif($key == "roleSelect")
        #if($mul)
        ${MapName}.put("${vModelThis}", "例:研发人员/角色编码,测试人员/角色编码");
        #else
        ${MapName}.put("${vModelThis}", "例:研发人员/角色编码");
        #end
    #elseif($key == "groupSelect")
        #if($mul)
        ${MapName}.put("${vModelThis}", "例:A分组/分组编码,B分组/分组编码");
        #else
        ${MapName}.put("${vModelThis}", "例:A分组/分组编码");
        #end
    #elseif($key == 'datePicker')
        ${MapName}.put("${vModelThis}", "例: ${Field.format}");
    #elseif($key == 'timePicker')
        ${MapName}.put("${vModelThis}", "例: ${Field.format}");
    #elseif($key == 'areaSelect')
    #if($level==0)
        #if($mul)
        ${MapName}.put("${vModelThis}", "例:福建省,广东省");
        #else
        ${MapName}.put("${vModelThis}", "例:福建省");
        #end
    #elseif($level==1)
        #if($mul)
        ${MapName}.put("${vModelThis}", "例:福建省/莆田市,广东省/广州市");
        #else
        ${MapName}.put("${vModelThis}", "例:福建省/莆田市");
        #end
    #elseif($level==2)
        #if($mul)
        ${MapName}.put("${vModelThis}", "例:福建省/莆田市/城厢区,广东省/广州市/荔湾区");
        #else
        ${MapName}.put("${vModelThis}", "例:福建省/莆田市/城厢区");
        #end
    #elseif($level==3)
        #if($mul)
        ${MapName}.put("${vModelThis}", "例:福建省/莆田市/城厢区/霞林街道,广东省/广州市/荔湾区/沙面街道");
        #else
        ${MapName}.put("${vModelThis}", "例:福建省/莆田市/城厢区/霞林街道");
        #end
    #end
    #end
#end
##
#macro(SystemDataChange $system $vo)
    #foreach($field in ${system})
        #set($xhKey="${field.config.xhKey}")
        #set($EntityName =${field.vModel})
        #set($fieldName="${field.vModel.substring(0,1).toUpperCase()}${field.vModel.substring(1)}")
        #if(${xhKey}=='createTime'||${xhKey}=='modifyTime')
            if(${vo}.get${fieldName}()!=null){
            ${vo}.set${fieldName}(${vo}.get${fieldName}());
            }
        #elseif(${xhKey}=='createUser'||${xhKey}=='modifyUser')
            ${vo}.set${fieldName}(generaterSwapUtil.userSelectValue(${vo}.get${fieldName}()));
        #elseif(${xhKey}=='currOrganize')
            #set($showLevel = ${field.showLevel})
            ${vo}.set${fieldName}(generaterSwapUtil.comSelectValue(${vo}.get${fieldName}(), "${showLevel}"));
        #elseif(${xhKey}=='currPosition')
            ${vo}.set${fieldName}(generaterSwapUtil.posSelectValue(${vo}.get${fieldName}()));
        #end
    #end
#end
##获取系统参数
#macro(GetSystemData)
    #if(${key}=='createUser')
            ${nameEntity}.set${model}(userInfo.getUserId());
    #elseif(${key}=='createTime')
            ${nameEntity}.set${model}(DateXhUtil.date());
    #elseif(${key}=='currOrganize')
            ${nameEntity}.set${model}(StringUtil.isEmpty(userInfo.getDepartmentId()) ? userInfo.getOrganizeId() : userInfo.getDepartmentId());
    #elseif(${key}=='currPosition')
            ${nameEntity}.set${model}(userEntity.getPositionId());
    #elseif(${key}=='billRule')
            ${nameEntity}.set${model}(generaterSwapUtil.getBillNumber("${rule}", false));
    #end
#end
##  主子副，entity转成map   isGetInfo是否不转换数据详情 isDetail 是否转换数据的详情
#macro(EntityToMap $isGetInfo $isDetail)
        ${mapObject} ${name}Map=JsonXhUtil.entityToMap(entity);
        //副表数据
    #foreach($child in ${columnTableHandle})
    #if($snowflake)
        ${child.modelUpName}Entity  ${child.modelLowName}Entity = ${name}Service.get${child.modelUpName}(entity.get${peimaryKeyName}());
    #else
        ${child.modelUpName}Entity  ${child.modelLowName}Entity = ${name}Service.get${child.modelUpName}(entity.getFlowtaskid());
    #end
        if(ObjectUtil.isNotEmpty(${child.modelLowName}Entity)){
            ${mapObject} ${child.modelLowName}Map=JsonXhUtil.entityToMap(${child.modelLowName}Entity);
            for(String key:${child.modelLowName}Map.keySet()){
                ${name}Map.put("xh_${child.modelLowName}_xh_"+key,${child.modelLowName}Map.get(key));
            }
        }
    #end
        //子表数据
    #foreach($grid in ${childTableHandle})
    #set($aliasUpName = "${grid.aliasUpName}")
    #set($aliasLowName = "${grid.aliasLowName}")
#if($isGetInfo)
##详情-info调用的service接口以及put的key不同
    #if($snowflake)
        List<${aliasUpName}Entity> ${aliasLowName}List = ${name}Service.get${aliasUpName}List(entity.get${peimaryKeyName}());
    #else
        List<${aliasUpName}Entity> ${aliasLowName}List = ${name}Service.get${aliasUpName}List(entity.getFlowtaskid());
    #end
        ${name}Map.put("${grid.aliasLowName}List",JsonXhUtil.getJsonToList(JsonUtil.getListToJsonArray(${aliasLowName}List)));
#else
##详情-detailinfo
    #if($snowflake)
        List<${aliasUpName}Entity> ${aliasLowName}List = ${name}Service.get${aliasUpName}List(entity.get${peimaryKeyName}()#if(!$isDetail),${name}Pagination#end);
    #else
        List<${aliasUpName}Entity> ${aliasLowName}List = ${name}Service.get${aliasUpName}List(entity.getFlowtaskid()#if(!$isDetail),${name}Pagination#end);
    #end
        ${name}Map.put("${grid.tableModel}",JsonXhUtil.getJsonToList(JsonUtil.getListToJsonArray(${aliasLowName}List)));
    #end
#end
#end
###########################以上为通用宏，以下是controller方法#################################
##  获取列表信息
#macro(GetList)
   /**
    * 列表
    *
    * @param ${name}Pagination
    * @return
    */
    @Operation(summary = "获取列表")
    @PostMapping("/getList")
    public ActionResult list(@RequestBody ${Name}Pagination ${name}Pagination)throws IOException{
        List<${table.entityName}> list= ${serviceName}.getList(${name}Pagination);
        List<${mapObject}> realList=new ArrayList<>();
        for (${Name}Entity entity : list) {
            #EntityToMap(false,false)
            realList.add(${name}Map);
        }
        //数据转换
        realList = generaterSwapUtil.swapDataList(realList, ${Name}Constant.getFormData(), ${Name}Constant.getColumnData(), ${name}Pagination.getModuleId(),${lineEdit});

        #if($isFlow)
        //流程状态添加
        for($mapObject vo:realList){
        #if($snowflake)
            FlowTaskEntity flowTaskEntity = generaterSwapUtil.getInfoSubmit(String.valueOf(vo.get("${peimaryKeyname}")), FlowTaskEntity::getStatus);
        #else
            FlowTaskEntity flowTaskEntity = generaterSwapUtil.getInfoSubmit(String.valueOf(vo.get("flowtaskid")), FlowTaskEntity::getStatus);
        #end
            if (flowTaskEntity!=null){
                vo.put("flowState",flowTaskEntity.getStatus());
            }else{
                vo.put("flowState",null);
            }
            //添加流程id
            String flowId="";
            if(vo.get("flowid")!=null){
                flowId = String.valueOf(vo.get("flowid"));
            }
            if(vo.get("flowid".toUpperCase())!=null){
                flowId = String.valueOf(vo.get("flowid".toUpperCase()));
            }
            vo.put("flowId" ,flowId);
        }
        #end
        #if($treeTable || $groupTable)
        boolean isPc = "pc".equals(ServletUtil.getHeader("xh-origin" ));
        if(isPc){
            //分组和树形的树形数据转换
            realList = generaterSwapUtil.swapDataList(realList, ${Name}Constant.getColumnData(), "${peimaryKeyname}");
        }
        #end
        //返回对象
        PageListVO vo = new PageListVO();
        vo.setList(realList);
        PaginationVO page = JsonXhUtil.toBean(${name}Pagination, PaginationVO.class);
        vo.setPagination(page);
        return ActionResult.success(vo);
    }
#end
##  新增
#macro(CreateMethod)

   /**
    * 创建
    *
    * @param ${name}Form
    * @return
    */
    @PostMapping(#if($isFlow)"/{id}"#end)
    #if(${DS})
    @DSTransactional
    #else
    @Transactional
    #end
    @Operation(summary = "创建")
    public ActionResult create(#if($isFlow)@PathVariable("id") String id, #end@RequestBody @Valid ${Name}Form ${name}Form) throws DataException {
        String b = ${serviceName}.checkForm(${name}Form,0);
        if (StringUtil.isNotEmpty(b)){
            return ActionResult.fail(b);
        }
        String mainId =#if($isFlow) id #else RandomUtil.snowId()#end;
        UserInfo userInfo=userProvider.get();
        UserEntity userEntity = generaterSwapUtil.getUser(userInfo.getUserId());
        GeneraterSwapUtil.swapDatetime(${name}Form);
        ${Name}Entity entity = JsonXhUtil.toBean(${name}Form, ${Name}Entity.class);
        #set($peimaryKeyName="${pKeyName.substring(0,1).toUpperCase()}${pKeyName.substring(1)}")
        #foreach($field in ${mastTableHandle})
            #set($model = "${field.vModel.substring(0,1).toUpperCase()}${field.vModel.substring(1)}")
            #set($key = ${field.config.xhKey})
            #set($nameEntity = "entity")
            #set($rule ="${field.config.rule}")
            #GetSystemData()
        #end
        #if($snowflake)
        entity.set${peimaryKeyName}(mainId);
        #else
        entity.set${peimaryKeyName}(0);
        entity.setFlowtaskid(mainId);
        #end
        #if($isFlow)
        entity.setFlowid(${name}Form.getFlowId());
        #end
        #if($version)
        entity.setVersion(0);
        #end
        ${name}Service.save(entity);
        #if(${childTableHandle.size()}>0)
            //子表数据添加
            #foreach($grid in ${childTableHandle})
                if (${name}Form.get${grid.aliasUpName}List()!=null){
                List<${grid.aliasUpName}Entity> ${grid.tableModel} = JsonXhUtil.getJsonToList(${name}Form.get${grid.aliasUpName}List(),${grid.aliasUpName}Entity.class);
                    for(${grid.aliasUpName}Entity entitys : ${grid.tableModel}){
                    #set($tableField = "${grid.tablefield.substring(0,1).toUpperCase()}${grid.tablefield.substring(1)}")
                    #set($relationField = "${grid.relationField.substring(0,1).toUpperCase()}${grid.relationField.substring(1)}")
                    #set($chidKeyName = "${grid.chidKeyName.substring(0,1).toUpperCase()}${grid.chidKeyName.substring(1)}")
                    #if($snowflake)
                        entitys.set${chidKeyName}(RandomUtil.snowId());
                        entitys.set${tableField}(entity.get${relationField}());
                    #else
                        entitys.set${chidKeyName}(0);
                        entitys.set${tableField}(entity.getFlowtaskid());
                    #end
                    #foreach($xhkey in ${grid.childList})
                        #if(${xhkey.fieLdsModel.vModel} != '')
                            #set($key = ${xhkey.fieLdsModel.config.xhKey})
                            #set($rule = ${xhkey.fieLdsModel.config.rule})
                            #set($model = "${xhkey.fieLdsModel.vModel.substring(0,1).toUpperCase()}${xhkey.fieLdsModel.vModel.substring(1)}")
                            #set($nameEntity = "entitys")
                            #GetSystemData()
                        #end
                    #end
                    ${grid.aliasLowName}Service.save(entitys);
                    }
                }
            #end
        #end
        #if(${columnTableHandle.size()}>0)
            //副表数据添加
            #foreach($cl in  ${columnTableHandle})
                #set($mainField = $cl.mainField)
                #set($mainUpId = "${mainField.substring(0,1).toUpperCase()}${mainField.substring(1)}")
                $mapObject ${cl.modelName}Map = generaterSwapUtil.getMastTabelData(${name}Form,"${cl.tableName}");
                ${cl.modelName}Entity  ${cl.tableName}entity = JsonXhUtil.toBean(${cl.modelName}Map,${cl.modelName}Entity.class);
                //自动生成的字段
                #foreach($clModel in ${cl.fieLdsModelList})
                    #set($model = "${clModel.field.substring(0,1).toUpperCase()}${clModel.field.substring(1)}")
                    #set($key =  ${clModel.mastTable.fieLdsModel.config.xhKey})
                    #set($nameEntity = "${cl.tableName}entity")
                    #set($rule ="${clModel.mastTable.fieLdsModel.config.rule}")
                        #GetSystemData()
                #end
                #if($snowflake)
                ${cl.tableName}entity.set${cl.relationUpField}(entity.get${cl.mainUpKey}());
                ${cl.tableName}entity.set${mainUpId}(RandomUtil.snowId());
                #else
                ${cl.tableName}entity.set${cl.relationUpField}(entity.getFlowtaskid());
                ${cl.tableName}entity.set${mainUpId}(0);
                #end
                ${cl.modelLowName}Service.save(${cl.tableName}entity);
            #end
        #end
        return ActionResult.success("创建成功");
    }
#end
##  编辑
#macro(UpdateMethod)

   /**
    * 编辑
    * @param id
    * @param ${name}Form
    * @return
    */
    @PutMapping("/{id}")
    #if(${DS})
    @DSTransactional
    #else
    @Transactional
    #end
    @Operation(summary = "更新")
    public ActionResult update(@PathVariable("id") String id,@RequestBody @Valid ${Name}Form ${name}Form) throws DataException {
        String b =  ${name}Service.checkForm(${name}Form,1);
        if (StringUtil.isNotEmpty(b)){
            return ActionResult.fail(b);
        }
        UserInfo userInfo=userProvider.get();
        ${Name}Entity entity= ${name}Service.getInfo(id);
        if(entity!=null){
            GeneraterSwapUtil.swapDatetime(${name}Form);
            #foreach($xhkey in ${mastTableHandle})
                #set($model = "${xhkey.vModel.substring(0,1).toUpperCase()}${xhkey.vModel.substring(1)}")
                #set($key = ${xhkey.config.xhKey})
                #set($rule = ${xhkey.config.rule})
                #if(${key}=='createUser')
            ${name}Form.set${model}(null);
                #elseif(${key}=='createTime')
            ${name}Form.set${model}(null);
                #elseif(${key}=='modifyUser')
            ${name}Form.set${model}(userInfo.getUserId());
                #elseif(${key}=='modifyTime')
            ${name}Form.set${model}(DateUtil.getNow());
                #elseif(${key}=='currOrganize')
            ${name}Form.set${model}(entity.get${model}());
                #elseif(${key}=='currPosition')
            ${name}Form.set${model}(entity.get${model}());
                #end
            #end
            ${Name}Entity subentity=JsonXhUtil.toBean(${name}Form, ${Name}Entity.class);
            #if($isFlow)
            subentity.setFlowid(${name}Form.getFlowId());
            #end
            boolean b1 = ${name}Service.updateById(subentity);
            if (!b1){
            return ActionResult.fail("当前表单原数据已被调整，请重新进入该页面编辑并提交数据");
            }
            #if(${lineEdit})
                boolean	isApp = "app".equals(ServletUtil.getHeader("xh-origin"));
                if (isApp){
            #end
##子表数据修改
            #if(${childTableHandle.size()}>0)
                //子表数据修改
                #foreach($grid in ${childTableHandle})
                    #set($tableField = "${grid.tablefield.substring(0,1).toUpperCase()}${grid.tablefield.substring(1)}")
                    #set($relationField = "${grid.relationField.substring(0,1).toUpperCase()}${grid.relationField.substring(1)}")
                    #set($chidKeyName = "${grid.chidKeyName.substring(0,1).toUpperCase()}${grid.chidKeyName.substring(1)}")
                    #set($serviceName = "${grid.aliasUpName.substring(0,1).toLowerCase()}${grid.aliasUpName.substring(1)}")
                    QueryWrapper<${grid.aliasUpName}Entity> ${grid.aliasUpName}queryWrapper = new QueryWrapper<>();
                    #if($snowflake)
                        ${grid.aliasUpName}queryWrapper.lambda().eq(${grid.aliasUpName}Entity::get${tableField}, entity.get${relationField}());
                    #else
                        ${grid.aliasUpName}queryWrapper.lambda().eq(${grid.aliasUpName}Entity::get${tableField}, entity.getFlowtaskid());
                    #end
                    ${serviceName}Service.remove(${grid.aliasUpName}queryWrapper);
                    #set($list = "${grid.aliasUpName.substring(0,1).toUpperCase()}${grid.aliasUpName.substring(1)}")
                    if (${name}Form.get${list}List()!=null){
                    List<${grid.aliasUpName}Entity> ${grid.aliasUpName}List = JsonXhUtil.getJsonToList(${name}Form.get${list}List(),${grid.aliasUpName}Entity.class);
                    for(${grid.aliasUpName}Entity entitys : ${grid.aliasUpName}List){
                    #foreach($xhkey in ${grid.childList})
                        #if(${xhkey.fieLdsModel.vModel} !='')
                            #set($key = ${xhkey.fieLdsModel.config.xhKey})
                            #set($rule = ${xhkey.fieLdsModel.config.rule})
                            #set($model = "${xhkey.fieLdsModel.vModel.substring(0,1).toUpperCase()}${xhkey.fieLdsModel.vModel.substring(1)}")
                            #if(${key}=='createUser')
                                entitys.set${model}(null);
                            #elseif(${key}=='createTime')
                                entitys.set${model}(null);
                            #elseif(${key}=='modifyUser')
                                entitys.set${model}(userInfo.getUserId());
                            #elseif(${key}=='modifyTime')
                                entitys.set${model}(DateXhUtil.date());
                            #elseif(${xhkey}=='currOrganize' || ${xhkey}=='currPosition')
                                entity.set${model}(null);
                            #elseif(${key}=='billRule')
                                if(StringUtil.isEmpty(entitys.get${model}())){
                                    entitys.set${model}(StringUtil.isEmpty(entitys.get${model}())?generaterSwapUtil.getBillNumber("${rule}",false):entitys.get${model}());
                                }
                            #end
                        #end
                    #end
                    #if($snowflake)
                        entitys.set${chidKeyName}(RandomUtil.snowId());
                        entitys.set${tableField}(entity.get${relationField}());
                    #else
                        entitys.set${tableField}(entity.getFlowtaskid());
                    #end
                    ${serviceName}Service.save(entitys);
                    }
                    }
                #end
            #end
            #if(${lineEdit})
                }
            #end
##副表数据修改
            #if(${columnTableHandle.size()}>0)
                //副表数据修改
                #foreach($cl in  ${columnTableHandle})
                    #set($oracleName ="${cl.TableName.substring(0,1).toUpperCase()}${cl.TableName.substring(1).toLowerCase()}")
                    $mapObject ${cl.modelName}Map = generaterSwapUtil.getMastTabelData(${name}Form,"${cl.tableName}");
                    if(ObjectUtil.isNotEmpty(${cl.modelName}Map)){
                    ${cl.modelName}Entity  ${cl.tableName}entity = JsonXhUtil.toBean(${cl.modelName}Map,${cl.modelName}Entity.class);
                    QueryWrapper<${cl.modelUpName}Entity> queryWrapper${cl.modelUpName} =new QueryWrapper<>();
                    #if($snowflake)
                        queryWrapper${cl.modelUpName}.lambda().eq(${cl.modelUpName}Entity::get${cl.relationUpField},entity.get${cl.mainUpKey}());
                    #else
                        queryWrapper${cl.modelUpName}.lambda().eq(${cl.modelUpName}Entity::get${cl.relationUpField},entity.getFlowtaskid());
                    #end
                    ${cl.modelUpName}Entity ${cl.tableName}OneEntity= ${cl.modelLowName}Service.getOne(queryWrapper${cl.modelUpName});
                        if(${cl.tableName}OneEntity!=null){
                            ${cl.tableName}entity.set${cl.mainField}(${cl.tableName}OneEntity.get${cl.mainField}());
                            #if($snowflake)
                            ${cl.tableName}entity.set${cl.relationUpField}(entity.get${cl.mainUpKey}());
                            #else
                            ${cl.tableName}entity.set${cl.relationUpField}(entity.getFlowtaskid());
                            #end
                            #foreach($clModel in ${cl.fieLdsModelList})
                                #set($model ="${clModel.field.substring(0,1).toUpperCase()}${clModel.field.substring(1)}")
                                #set($xhkey =  ${clModel.mastTable.fieLdsModel.config.xhKey})
                                #if(${xhkey}=='modifyUser')
                            ${cl.tableName}entity.set${model}(userInfo.getUserId());
                                #elseif(${xhkey}=='modifyTime')
                            ${cl.tableName}entity.set${model}(DateXhUtil.date());
                                #elseif(${xhkey}=='currOrganize' || ${xhkey}=='currPosition' || ${xhkey}=='createUser' || ${xhkey}=='createTime')
                            ${cl.tableName}entity.set${model}(null);
                                #elseif(${xhkey}=='billRule')
                                    #set($rule = ${clModel.mastTable.fieLdsModel.config.rule})
                            if(StringUtil.isEmpty(${cl.tableName}entity.get${model}())){
                                ${cl.tableName}entity.set${model}(generaterSwapUtil.getBillNumber("$!{rule}",false));
                            }
                                #end
                            #end
                            ${cl.modelLowName}Service.updateById(${cl.tableName}entity);
                        }
                    }
                #end
            #end
            return ActionResult.success("更新成功");
        }else{
            return ActionResult.fail("更新失败，数据不存在");
        }
    }
#end
##  获取详情(不转数据)
#macro(GetInfoMethod)

   /**
    * 获取详情(编辑页)
    * 编辑页面使用-不转换数据
    * @param id
    * @return
    */
    @Operation(summary = "信息")
    @GetMapping("/{id}")
    public ActionResult info(@PathVariable("id") String id){
        ${Name}Entity entity= ${name}Service.getInfo(id);
        if(entity==null){
            return ActionResult.fail("表单数据不存在！");
        }
        #EntityToMap(true,false)
        ${name}Map = generaterSwapUtil.swapDataForm(${name}Map,${Name}Constant.getFormData(),${Name}Constant.TABLEFIELDKEY,${Name}Constant.TABLERENAMES);
        return ActionResult.success(${name}Map);
    }
#end
##  详情(转换数据)
#macro(GetDetailMethod)

   /**
    * 表单信息(详情页)
    * 详情页面使用-转换数据
    * @param id
    * @return
    */
    @Operation(summary = "表单信息(详情页)")
    @GetMapping("/detail/{id}")
    public ActionResult detailInfo(@PathVariable("id") String id){
        ${Name}Entity entity= ${name}Service.getInfo(id);
        if(entity==null){
            return ActionResult.fail("表单数据不存在！");
        }
        #EntityToMap(false,true)
        ${name}Map = generaterSwapUtil.swapDataDetail(${name}Map,${Name}Constant.getFormData(),"${VisualDevId}",false);
        return ActionResult.success(${name}Map);
    }
#end
##  删除
#macro(DeleteMethod)

   /**
    * 删除
    * @param id
    * @return
    */
    @Operation(summary = "删除")
    @DeleteMapping("/{id}")
#if(${DS})
    @DSTransactional
#else
    @Transactional
#end
    public ActionResult delete(@PathVariable("id") String id){
        ${Name}Entity entity= ${name}Service.getInfo(id);
        if(entity!=null){
        #if($isFlow)
        #if($snowflake)
            FlowTaskEntity taskEntity = generaterSwapUtil.getInfoSubmit(id, FlowTaskEntity::getId, FlowTaskEntity::getStatus);
        #else
            FlowTaskEntity taskEntity = generaterSwapUtil.getInfoSubmit(entity.getFlowtaskid(), FlowTaskEntity::getId, FlowTaskEntity::getStatus);
        #end
            if (taskEntity != null) {
                try {
                    generaterSwapUtil.deleteFlowTask(taskEntity);
                } catch (WorkFlowException e) {
                    e.printStackTrace();
                }
            }
        #end
    #if($logicalDelete)
            //假删除
            entity.setDeletemark(1);
            ${name}Service.update(id,entity);
    #else
            //主表数据删除
            ${name}Service.delete(entity);
##      副表
        #if(${columnTableHandle.size()}>0)
        #foreach($cl in ${columnTableHandle})
            QueryWrapper<${cl.modelUpName}Entity> queryWrapper${cl.modelUpName}=new QueryWrapper<>();
            #if($snowflake)
            queryWrapper${cl.modelUpName}.lambda().eq(${cl.modelUpName}Entity::get${cl.relationUpField},entity.get${cl.mainUpKey}());
            #else
            queryWrapper${cl.modelUpName}.lambda().eq(${cl.modelUpName}Entity::get${cl.relationUpField}, entity.getFlowtaskid());
            #end
            //副表数据删除
            ${cl.modelLowName}Service.remove(queryWrapper${cl.modelUpName});
        #end
        #end
##      子表
        #foreach($tableModel in ${childTableHandle})
            #set($mainFeild="${tableModel.relationField}")
            #set($MainFeild="${tableModel.relationField.substring(0,1).toUpperCase()}${tableModel.relationField.substring(1)}")
            #set($childFeild="${tableModel.tablefield}")
            #set($ChildFeild="${tableModel.tablefield.substring(0,1).toUpperCase()}${tableModel.tablefield.substring(1)}")
            QueryWrapper<${tableModel.aliasUpName}Entity> queryWrapper${tableModel.aliasUpName}=new QueryWrapper<>();
            #if($snowflake)
            queryWrapper${tableModel.aliasUpName}.lambda().eq(${tableModel.aliasUpName}Entity::get${ChildFeild},entity.get${MainFeild}());
            #else
            queryWrapper${tableModel.aliasUpName}.lambda().eq(${tableModel.aliasUpName}Entity::get${ChildFeild},entity.getFlowtaskid());
            #end
            //子表数据删除
            ${tableModel.aliasLowName}Service.remove(queryWrapper${tableModel.aliasUpName});
        #end
    #end
        }
        return ActionResult.success("删除成功");
    }
#end
##  批量删除
#macro(BatchRemoveMethod)
   /**
    * 批量删除
    * @param ids
    * @return
    */
    @DeleteMapping("/batchRemove")
    #if(${DS})
    @DSTransactional
    #else
    @Transactional
    #end
    @Operation(summary = "批量删除")
    public ActionResult batchRemove(@RequestBody String ids){
    #if(!$isFlow)
        List<String> idList = JsonXhUtil.getJsonToList(ids, String.class);
        int i =0;
        for (String allId : idList){
            this.delete(allId);
            i++;
        }
        if (i == 0 ){
        return ActionResult.fail("删除失败");
        }
        return ActionResult.success("删除成功");
    #else
        #FlowBatchDel()
    #end
    }
#end
##流程批量删除
#macro(FlowBatchDel)
    List<String> idList = JsonXhUtil.getJsonToList(ids, String.class);
    List<String> columnIdList = new ArrayList<>(20);
    int i =0;
    for (String allId : idList){
        #if($snowflake)
        FlowTaskEntity taskEntity = generaterSwapUtil.getInfoSubmit(allId, FlowTaskEntity::getId, FlowTaskEntity::getStatus);
        #else
        ${Name}Entity entity= ${name}Service.getInfo(allId);
        FlowTaskEntity taskEntity = generaterSwapUtil.getInfoSubmit(entity.getFlowtaskid(), FlowTaskEntity::getId, FlowTaskEntity::getStatus);
        #end
        if (taskEntity==null){
            columnIdList.add(allId);
        }else if (taskEntity.getStatus().equals(0) || taskEntity.getStatus().equals(4)){
            try {
                generaterSwapUtil.deleteFlowTask(taskEntity);
                columnIdList.add(allId);
                i++;
            } catch (WorkFlowException e) {
                e.printStackTrace();
            }
        }
    }
    ${name}Service.removeByIds(columnIdList);
    if (i == 0 && columnIdList.size()==0){
        return ActionResult.fail("流程已发起，无法删除");
    }
    return ActionResult.success("删除成功");
#end
##  批量打印
#macro(BatchPrintMethod)
#end
##  导入
#macro(UploaderMethod)

   /**
    * 上传文件
    * @return
    */
    @Operation(summary = "上传文件")
    @PostMapping("/Uploader")
    public ActionResult<Object> Uploader() {
        List<MultipartFile> list = UpUtil.getFileAll();
        MultipartFile file = list.get(0);
        if (file.getOriginalFilename().endsWith(".xlsx") || file.getOriginalFilename().endsWith(".xls")) {
            String filePath = XSSEscape.escape(configValueUtil.getTemporaryFilePath());
            String fileName = XSSEscape.escape(RandomUtil.snowId() + "." + UpUtil.getFileType(file));
            //上传文件
            #if(${isCloud}=="cloud")
            FileInfo fileInfo = fileUploadApi.uploadFile(file, filePath, fileName);
            #else
            FileInfo fileInfo = FileUploadUtils.uploadFile(file, filePath, fileName);
            #end
            DownloadVO vo = DownloadVO.builder().build();
            vo.setName(fileInfo.getFilename());
            return ActionResult.success(vo);
        } else {
            return ActionResult.fail("选择文件不符合导入");
        }
    }

   /**
    * 模板下载
    *
    * @return
    */
    @Operation(summary = "模板下载")
    @GetMapping("/TemplateDownload")
    public ActionResult<DownloadVO>  TemplateDownload(){
        DownloadVO vo = DownloadVO.builder().build();
        UserInfo userInfo = userProvider.get();
        ${mapObject} dataMap = new HashMap<>();
        //主表对象
        List<ExcelExportEntity> entitys = new ArrayList<>();
        //以下添加字段
##        导入字段添加到entity
        #foreach($fieldModel in $importFields)
            #set($config = $fieldModel.config)
            #set($vModel = ${fieldModel.vModel})
            #if($vModel.contains("tableField"))
        //${vModel}子表对象
        ExcelExportEntity ${vModel}ExcelEntity = new ExcelExportEntity("${fieldModel.label}","${vModel}");
        List<ExcelExportEntity> ${vModel}ExcelEntityList = new ArrayList<>();
        ${mapObject} ${vModel}ChildData = new HashMap<>();
        List<${mapObject}> ${vModel}ChildDataList = new ArrayList<>();
                #foreach($child in ${fieldModel.childList})
                    #set($itemFields =$child.fieLdsModel)
        ${vModel}ExcelEntityList.add(new ExcelExportEntity("${itemFields.config.label}" ,"${itemFields.vModel}"));
        #TemlateDownloadDemo("${vModel}ChildData",$itemFields)
                #end
        ${vModel}ChildDataList.add(${vModel}ChildData);
        dataMap.put("${vModel}",${vModel}ChildDataList);
        ${vModel}ExcelEntity.setList(${vModel}ExcelEntityList);
        entitys.add(${vModel}ExcelEntity);
            #else
        entitys.add(new ExcelExportEntity("${config.label}" ,"$vModel"));
        #TemlateDownloadDemo("dataMap",$fieldModel)
            #end
        #end
        List<${mapObject}> list = new ArrayList<>();
        list.add(dataMap);

        ExportParams exportParams = new ExportParams(null, "${formModelName}模板");
        exportParams.setType(ExcelType.XSSF);
        try{
            @Cleanup Workbook workbook = new HSSFWorkbook();
            if (entitys.size()>0){
                if (list.size()==0){
                  list.add(new HashMap<>());
                }
                workbook = ExcelExportUtil.exportExcel(exportParams, entitys, list);
            }
            String fileName = "${formModelName}模板" + DateUtil.dateNow("yyyyMMddHHmmss") + ".xlsx";
            MultipartFile multipartFile = ExcelUtil.workbookToCommonsMultipartFile(workbook, fileName);
            #if(${isCloud}=="cloud")
            String temporaryFilePath = fileApi.getPath(FileTypeConstant.TEMPORARY);
            FileInfo fileInfo = fileUploadApi.uploadFile(multipartFile, temporaryFilePath, fileName);
            #else
            String temporaryFilePath = configValueUtil.getTemporaryFilePath();
            FileInfo fileInfo = FileUploadUtils.uploadFile(multipartFile, temporaryFilePath, fileName);
            #end
            vo.setName(fileInfo.getFilename());
            vo.setUrl(UploaderUtil.uploaderFile(fileInfo.getFilename() + "#" + "Temporary") + "&name=" + fileName);
        } catch (Exception e) {
            log.error("模板信息导出Excel错误:{}", e.getMessage());
            e.printStackTrace();
        }
        return ActionResult.success(vo);
    }

   /**
    * 导入预览
    *
    * @return
    */
    @Operation(summary = "导入预览" )
    @GetMapping("/ImportPreview")
    public ActionResult<${mapObject}> ImportPreview(String fileName) throws Exception {
        ${mapObject} headAndDataMap = new HashMap<>(2);
        #if(${isCloud}=="cloud")
        String filePath = fileApi.getLocalBasePath() + configValueUtil.getTemporaryFilePath();
        UploadFileModel uploadFileModel =new UploadFileModel();
        uploadFileModel.setFolderName(filePath);
        uploadFileModel.setObjectName(fileName);
        fileUploadApi.downToLocal(uploadFileModel);
        #else
        String filePath = FileUploadUtils.getLocalBasePath() + configValueUtil.getTemporaryFilePath();
        FileUploadUtils.downLocal(configValueUtil.getTemporaryFilePath(), filePath, fileName);
        #end
        File temporary = new File(XSSEscape.escapePath(filePath + fileName));
        #if($importHasChildren)
        int headerRowIndex = 2;
        #else
        int headerRowIndex = 1;
        #end
        ImportParams params = new ImportParams();
        params.setTitleRows(0);
        params.setHeadRows(headerRowIndex);
        params.setNeedVerify(true);
        try {
            List<${Name}ExcelVO> excelDataList = ExcelImportUtil.importExcel(temporary, ${Name}ExcelVO.class, params);
            // 导入字段
            List<ExcelImFieldModel> columns = new ArrayList<>();
## 添加导入所有字段信息
#foreach($fieldModel in $importFields)
    #set($config = $fieldModel.config)
    #set($vModel = ${fieldModel.vModel})
    #if($vModel.contains("tableField"))
            //${vModel}子表对象
            List<ExcelImFieldModel> ${vModel}columns = new ArrayList<>();
        #foreach($child in ${fieldModel.childList})
            #set($itemFields =$child.fieLdsModel)
            ${vModel}columns.add(new ExcelImFieldModel("${itemFields.vModel}" ,"${itemFields.config.label}"));
        #end
            columns.add(new ExcelImFieldModel("${vModel}","${fieldModel.label}",${vModel}columns));
    #else
            columns.add(new ExcelImFieldModel("${vModel}","${config.label}"));
    #end
#end
            headAndDataMap.put("dataRow" , JsonXhUtil.getJsonToList(JsonUtil.getListToJsonArray(excelDataList)));
            headAndDataMap.put("headerRow" , JsonXhUtil.getJsonToList(JsonUtil.getListToJsonArray(columns)));
        } catch (Exception e){
            e.printStackTrace();
            return ActionResult.fail("表头名称不可更改,表头行不能删除");
        }
        return ActionResult.success(headAndDataMap);
    }

   /**
    * 导入数据
    *
    * @return
    */
    @Operation(summary = "导入数据" )
    @PostMapping("/ImportData")
    public ActionResult<ExcelImportModel> ImportData(@RequestBody VisualImportModel visualImportModel) throws Exception {
        List<${Name}ExcelVO> ${name}VOList = JsonXhUtil.getJsonToList(visualImportModel.getList(), ${Name}ExcelVO.class);
        ExcelImportModel excelImportModel = new ExcelImportModel();
        List<${mapObject}> errorList = new ArrayList<>();
        UserInfo userInfo=userProvider.get();
        UserEntity userEntity = generaterSwapUtil.getUser(userInfo.getUserId());
        //Collections.reverse(${name}VOList);//倒序插入
        for (${Name}ExcelVO vo : ${name}VOList) {
            ${mapObject} map = JsonXhUtil.entityToMap(vo);
            ${mapObject} erroMap = JsonXhUtil.entityToMap(vo);
            StringJoiner error = new StringJoiner(",");
            ${mapObject} insMap = new HashMap<>(map);
            //缓存
            ${mapObject} localcheMap = new HashMap<>();
            String mainID = RandomUtil.snowId();
            localcheMap = generaterSwapUtil.putCache(localcheMap);
            //表单验证
            Map<String, String> checkResMap = generaterSwapUtil.checkExcelData(${Name}Constant.getFormData(), map, localcheMap,
                    insMap, error, erroMap, "${importType}" , ${Name}Constant.DBLINKID, ${Name}Constant.getTableList());
            boolean hasError = "true".equals(checkResMap.get("hasError" )) ? true : false;
            String unionId = checkResMap.get("unionId" ) != null ? checkResMap.get("unionId" ) : "";
            if (hasError) {
                erroMap.put("errorsInfo",error.toString());
                errorList.add(erroMap);
            }else {
                if(StringUtil.isNotEmpty(unionId)){
                    this.delete(unionId);
                }
                ${Name}Entity ${name}Entity = JsonXhUtil.toBean(insMap, ${Name}Entity.class);
                #foreach($field in ${mastTableHandle})
                    #set($model = "${field.vModel.substring(0,1).toUpperCase()}${field.vModel.substring(1)}")
                    #set($key = ${field.config.xhKey})
                    #set($nameEntity = "${name}Entity")
                    #set($rule ="${field.config.rule}")
                        #GetSystemData()
                #end
                #if($snowflake)
                ${name}Entity.set${peimaryKeyName}(mainID);
                #else
                ${name}Entity.set${peimaryKeyName}(0);
                ${name}Entity.setFlowtaskid(mainID);
                #end
                #if($version)
                ${name}Entity.setVersion(0);
                #end
                ${name}Service.save(${name}Entity);
                #if(${columnTableHandle.size()}>0)
                    //副表数据添加
                    #foreach($cl in  ${columnTableHandle})
                        #set($mainField = $cl.mainField)
                        #set($mainUpId = "${mainField.substring(0,1).toUpperCase()}${mainField.substring(1)}")
                        $mapObject ${cl.modelName}Map = generaterSwapUtil.getMastTabelData(insMap,"${cl.tableName}");
                        ${cl.modelName}Entity  ${cl.tableName}entity = JsonXhUtil.toBean(${cl.modelName}Map,${cl.modelName}Entity.class);
                        //自动生成的字段
                        #foreach($clModel in ${cl.fieLdsModelList})
                            #set($model = "${clModel.field.substring(0,1).toUpperCase()}${clModel.field.substring(1)}")
                            #set($key =  ${clModel.mastTable.fieLdsModel.config.xhKey})
                            #set($nameEntity = "${cl.tableName}entity")
                            #set($rule ="${clModel.mastTable.fieLdsModel.config.rule}")
                            #GetSystemData()
                        #end
                        #if($snowflake)
                        ${cl.tableName}entity.set${cl.relationUpField}(${name}Entity.get${cl.mainUpKey}());
                        ${cl.tableName}entity.set${mainUpId}(RandomUtil.snowId());
                        #else
                        ${cl.tableName}entity.set${cl.relationUpField}(${name}Entity.getFlowtaskid());
                        ${cl.tableName}entity.set${mainUpId}(0);
                        #end
                        ${cl.modelLowName}Service.save(${cl.tableName}entity);
                    #end
                #end
                #if(${childTableHandle.size()}>0)
                    //子表数据添加
                    #foreach($grid in ${childTableHandle})
                        if (insMap.get("${grid.tableModel}")!=null){
                            List<${grid.aliasUpName}Entity> ${grid.tableModel} = JsonXhUtil.getJsonToList(insMap.get("${grid.tableModel}"),${grid.aliasUpName}Entity.class);
                            for(${grid.aliasUpName}Entity entitys : ${grid.tableModel}){
                            #set($tableField = "${grid.tablefield.substring(0,1).toUpperCase()}${grid.tablefield.substring(1)}")
                            #set($relationField = "${grid.relationField.substring(0,1).toUpperCase()}${grid.relationField.substring(1)}")
                            #set($chidKeyName = "${grid.chidKeyName.substring(0,1).toUpperCase()}${grid.chidKeyName.substring(1)}")
                            #if($snowflake)
                                entitys.set${chidKeyName}(RandomUtil.snowId());
                                entitys.set${tableField}(${name}Entity.get${relationField}());
                            #else
                                entitys.set${chidKeyName}(0);
                                entitys.set${tableField}(${name}Entity.getFlowtaskid());
                            #end
                            #foreach($xhkey in ${grid.childList})
                                #if(${xhkey.fieLdsModel.vModel} != '')
                                    #set($key = ${xhkey.fieLdsModel.config.xhKey})
                                    #set($rule = ${xhkey.fieLdsModel.config.rule})
                                    #set($model = "${xhkey.fieLdsModel.vModel.substring(0,1).toUpperCase()}${xhkey.fieLdsModel.vModel.substring(1)}")
                                    #set($nameEntity = "entitys")
                                    #GetSystemData()
                                #end
                            #end
                                ${grid.aliasLowName}Service.save(entitys);
                            }
                        }
                    #end
                #end
            }
        }
        excelImportModel.setSnum(${name}VOList.size()-errorList.size());
        excelImportModel.setFnum(errorList.size());
        excelImportModel.setResultType(errorList.size() > 0 ? 1 : 0);
        excelImportModel.setFailResult(errorList);
        return ActionResult.success(excelImportModel);
    }

   /**
    * 导出异常报告
    *
    * @return
    */
    @Operation(summary = "导出异常报告")
    @PostMapping("/ImportExceptionData")
    public ActionResult<DownloadVO> ImportExceptionData(@RequestBody VisualImportModel visualImportModel) {
        DownloadVO vo=DownloadVO.builder().build();
        List<${Name}ExcelErrorVO> ${name}VOList = JsonXhUtil.getJsonToList(visualImportModel.getList(), ${Name}ExcelErrorVO.class);
        UserInfo userInfo = userProvider.get();

        try{
            @Cleanup Workbook workbook = new HSSFWorkbook();
            ExportParams exportParams = new ExportParams(null, "错误报告");
            exportParams.setType(ExcelType.XSSF);
            workbook = ExcelExportUtil.exportExcel(exportParams,
            ${Name}ExcelErrorVO.class, ${name}VOList);

            String fileName = "错误报告" + DateUtil.dateNow("yyyyMMdd") + "_" + RandomUtil.snowId() + ".xlsx";
            MultipartFile multipartFile = ExcelUtil.workbookToCommonsMultipartFile(workbook, fileName);
            #if(${isCloud}=="cloud")
            String temporaryFilePath = fileApi.getPath(FileTypeConstant.TEMPORARY);
            FileInfo fileInfo = fileUploadApi.uploadFile(multipartFile, temporaryFilePath, fileName);
            #else
            String temporaryFilePath = configValueUtil.getTemporaryFilePath();
            FileInfo fileInfo = FileUploadUtils.uploadFile(multipartFile, temporaryFilePath, fileName);
            #end
            vo.setName(fileInfo.getFilename());
            vo.setUrl(UploaderUtil.uploaderFile(fileInfo.getFilename() + "#" + "Temporary") + "&name=" + fileName);
        } catch (Exception e) {
             e.printStackTrace();
        }
        return ActionResult.success(vo);
    }
#end
##  导出
#macro(ExportMethod)

   /**
    * 导出Excel
    *
    * @return
    */
    @Operation(summary = "导出Excel")
    @PostMapping("/Actions/Export")
    public ActionResult Export(@RequestBody ${Name}Pagination ${name}Pagination) throws IOException {
        if (StringUtil.isEmpty(${name}Pagination.getSelectKey())){
            return ActionResult.fail("请选择导出字段");
        }
        List<${table.entityName}> list= ${serviceName}.getList(${name}Pagination);
        List<${mapObject}> realList=new ArrayList<>();
        for (${Name}Entity entity : list) {
            #EntityToMap(false,false)
            realList.add(${name}Map);
        }
        //数据转换
        realList = generaterSwapUtil.swapDataList(realList, ${Name}Constant.getFormData(), ${Name}Constant.getColumnData(), ${name}Pagination.getModuleId(),${lineEdit});
        String[]keys=!StringUtil.isEmpty(${name}Pagination.getSelectKey())?${name}Pagination.getSelectKey():new String[0];
        UserInfo userInfo=userProvider.get();
        #if(${isCloud}=="cloud")
        DownloadVO vo=this.creatModelExcel(fileApi.getPath(FileTypeConstant.TEMPORARY),realList,keys,userInfo);
        #else
        DownloadVO vo=this.creatModelExcel(configValueUtil.getTemporaryFilePath(),realList,keys,userInfo);
        #end
        return ActionResult.success(vo);
    }

   /**
    * 导出表格方法
    */
    public DownloadVO creatModelExcel(String path,List<${mapObject}>list,String[]keys,UserInfo userInfo){
        DownloadVO vo=DownloadVO.builder().build();
        List<ExcelExportEntity> entitys=new ArrayList<>();
        if(keys.length>0){
##    子表对象
        #foreach($cl in $childTableHandle)
            ExcelExportEntity ${cl.tableModel}ExcelEntity = new ExcelExportEntity("${cl.label}","${cl.tableModel}");
            List<ExcelExportEntity> ${cl.tableModel}List = new ArrayList<>();
        #end
        #set($lineEditName = "#if(${lineEdit})_name#end")
            for(String key:keys){
                switch(key){
        #if($columnListSize.size()>0)
##        主表
            #foreach($fieldModel in ${mastTableHandle})
                #set($config = $fieldModel.config)
                #set($vModel = ${fieldModel.vModel})
                #if($vModel)
                    case "${vModel}" :
                    entitys.add(new ExcelExportEntity("${config.label}" ,"${vModel}${lineEditName}"));
                    break;
                #end
            #end
##        副表
            #foreach($clid in $columnTableHandle)
                #set($fieLdsModelList = $clid.fieLdsModelList)
                #foreach($cf in $fieLdsModelList)
                    #set($field = ${cf.field})
                    #if($field)
                    #set($label = $cf.mastTable.fieLdsModel.config.label)
                    #set($lowName= ${cf.table.toLowerCase()})
                    case "${cf.vModel}" :
                    entitys.add(new ExcelExportEntity("${label}" ,"${cf.vModel}${lineEditName}"));
                    break;
                    #end
                #end
            #end
##        子表
            #foreach($cl in $childTableHandle)
                #set($clForm = $cl.childList)
                #foreach($clField in $clForm)
                    #set($clForm =  $clField.fieLdsModel)
                    #if($!{clField.fieLdsModel.vModel})
                    case "${cl.tableModel}-${clField.fieLdsModel.vModel}":
                    ${cl.tableModel}List.add(new ExcelExportEntity("${clField.fieLdsModel.config.label}" ,"${clField.fieLdsModel.vModel}${lineEditName}"));
                    break;
                    #end
                #end
            #end
                    default:
                    break;
        #end
                }
            }
            List<String> keylist =  Arrays.asList(keys);
        #foreach($cl in $childTableHandle)
            if(${cl.tableModel}List.size() > 0){
            String ${cl.tableModel} = keylist.stream().filter(k -> k.startsWith("${cl.tableModel}")).findFirst().orElse("");
            ${cl.tableModel}ExcelEntity.setList(${cl.tableModel}List);
            entitys.add(keylist.indexOf(${cl.tableModel}), ${cl.tableModel}ExcelEntity);
            }
        #end
        }

        ExportParams exportParams = new ExportParams(null, "表单信息");
        exportParams.setType(ExcelType.XSSF);
        try{
            @Cleanup Workbook workbook = new HSSFWorkbook();
            if (entitys.size()>0){
                if (list.size()==0){
                    list.add(new HashMap<>());
                }
                //去除空数据
                List<$mapObject> dataList = new ArrayList<>();
                for ($mapObject map : list) {
                    int i = 0;
                    for (String key : keys) {
                        //子表
                        if (key.toLowerCase().startsWith("tablefield")) {
                            String tableField = key.substring(0, key.indexOf("-" ));
                            String field = key.substring(key.indexOf("-" ) + 1);
                            Object o = map.get(tableField);
                            if (o != null) {
                                List<$mapObject> childList = (List<$mapObject>) o;
                                for ($mapObject childMap : childList) {
                                    if (childMap.get(field) != null) {
                                        i++;
                                    }
                                }
                            }
                        } else {
                            Object o = map.get(key);
                            if (o != null) {
                                i++;
                            }
                        }
                    }
                    if (i > 0) {
                        dataList.add(map);
                    }
                }
                workbook = ExcelExportUtil.exportExcel(exportParams, entitys, dataList);
            }
            String fileName = "表单信息" + DateUtil.dateNow("yyyyMMdd") + "_" + RandomUtil.snowId() + ".xlsx";
            MultipartFile multipartFile = ExcelUtil.workbookToCommonsMultipartFile(workbook, fileName);
            #if(${isCloud}=="cloud")
            String temporaryFilePath = fileApi.getPath(FileTypeConstant.TEMPORARY);
            FileInfo fileInfo = fileUploadApi.uploadFile(multipartFile, temporaryFilePath, fileName);
            #else
            String temporaryFilePath = configValueUtil.getTemporaryFilePath();
            FileInfo fileInfo = FileUploadUtils.uploadFile(multipartFile, temporaryFilePath, fileName);
            #end
            vo.setName(fileInfo.getFilename());
            vo.setUrl(UploaderUtil.uploaderFile(fileInfo.getFilename() + "#" + "Temporary") + "&name=" + fileName);
        } catch (Exception e) {
            log.error("信息导出Excel错误:{}", e.getMessage());
            e.printStackTrace();
        }
        return vo;
    }
#end
