#set($pKeyName = "${context.pKeyName}")
<template>
#if(${context.popupType}=='general')
<el-dialog title="详情"
           :close-on-click-modal="false" append-to-body
           :visible.sync="visible" class="XH-dialog XH-dialog_center" lock-scroll
           width="${context.generalWidth}">
<el-row :gutter="${context.gutter}" class="${context.formStyle}">
#elseif(${context.popupType}=='fullScreen')
<transition name="el-zoom-in-center">
<div class="XH-preview-main">
    <Detail v-if="detailVisible" ref="Detail" @close="detailVisible = false" />
    <div class="XH-common-page-header">
        <el-page-header @back="goBack"
                        content="详情"/>
        <div class="options">
            #if($context.HasPrintBtn)
            <el-button type="primary" @click="printDialog">#if(${context.PrintButton})${context.PrintButton}#else 打 印#end </el-button>
            #end
            <el-button @click="goBack">#if(${context.CancelButton})${context.CancelButton}#else 取 消#end</el-button>
        </div>
    </div>
<el-row :gutter="${context.gutter}" class="${context.formStyle} main" :style="{margin: '0 auto',width: '${context.fullScreenWidth}'}">
#elseif(${context.popupType}=='drawer')
<el-drawer title="详情" :visible.sync="visible" :wrapperClosable="false"
           size="${context.drawerWidth}" append-to-body class="XH-common-drawer">
<div class="XH-flex-main">
<div class="dynamicForm dynamicDetail" >
#end
<el-form ref="${context.formRef}" :model="${context.formModel}" size="${context.size}" label-width="${context.labelWidth}px" label-position="${context.labelPosition}" #if($context.disabled == true ) :disabled="true"  #end>
    <template v-if="!loading">
#foreach($fieLdsModel in ${context.form})
    #set($xhKey = "${fieLdsModel.xhKey}")
    #set($isEnd = "${fieLdsModel.isEnd}")
    #set($formModel = ${fieLdsModel.formModel})
    #set($config=$formModel.config)
    #set($span=$config.span)
    #set($outermost = ${formModel.outermost})
    #set($borderType = ${formModel.borderType})
    #set($borderColor = ${formModel.borderColor})
    #set($borderWidth = ${formModel.borderWidth})
    #set($pcshow = $config.pc)
    #if(${xhKey}=='row' && $pcshow == true)
        #if(${isEnd}=='0')
        <el-col :span="${formModel.span}">
        <el-row :gutter="${context.gutter}">
        #else
        </el-row>
        </el-col>
        #end
    #elseif(${xhKey}=='card' && $pcshow == true)
        #if(${isEnd}=='0')
        <el-col  #if(${span}) :span="${span}" #else :span="24" #end>
        <el-card class="mb-20" shadow ="${formModel.shadow}"  header ="${formModel.header}">
            #if(${config.tipLabel} && ${formModel.header})
                <div slot="header">
				<span slot="label">${formModel.header}
					<el-tooltip placement="top" content='${config.tipLabel}'>
                  		<a class='el-icon-question tooltip-question' ></a>
                	</el-tooltip>
				</span>
                </div>
            #end
        #else
        </el-card>
        </el-col>
        #end
    #elseif(${xhKey}=='tab' && $pcshow == true)
        #set($tabs = "el-tabs")
        #if(${outermost}=='1')
            #set($tabs = "el-tab-pane")
        #end
        #if(${isEnd}=='0')
            #if(${outermost}=='0')
            <el-col :span="${formModel.span}">
                <${tabs}  v-model="${formModel.model}" #if($formModel.type)type="${formModel.type}"#end tab-position="${formModel.tabPosition}" class="mb-20">
            #else
                <${tabs}  label="${formModel.title}">
            #end
        #else
            #if(${outermost}=='0')
            </${tabs}>
                </el-col>
            #else
            </${tabs} >
            #end
        #end
    #elseif(${xhKey}=='tableGrid' || ${xhKey}=='tableGridTd' || ${xhKey}=='tableGridTr')
        #set($tabs = "tbody")
        #set($tableGrid = "table")
        #if(${xhKey}=='tableGridTr')
            #set($tabs = "tr")
        #elseif(${xhKey}=='tableGridTd')
            #set($tabs = "")
            #if(${config.merged}==false)
                #set($tabs = "td")
            #end
        #end
        #if(${config.pc}==true)
            #if(${isEnd}=='0')
                #if(${xhKey}=='tableGrid')
                    <${tableGrid} class="table-grid-box" :style='{"--borderType":"${borderType}","--borderColor":"${borderColor}","--borderWidth":"${borderWidth}px"}'>
                #end
                #if($tabs)
                    <${tabs}#if(${config.colspan}) colspan="${config.colspan}"#end#if(${config.rowspan}) rowspan="${config.rowspan}"#end>
                #end
            #else
                #if($tabs)
                </${tabs}>
                #end
                #if(${xhKey}=='tableGrid')
                </${tableGrid}>
                #end
            #end
        #end
    #elseif(${xhKey}=='groupTitle' || ${xhKey}=='XHText'|| ${xhKey} == 'button' || ${xhKey} == 'link' || ${xhKey} == 'alert')
        #if($pcshow== true)
            #set($content=$formModel.content)
            #set($defaultName=${formModel.slot.defaultName})
            <el-col :span="${span}" >
                <xh-form-tip-item  label-width="0">
                    <${config.tag} #if($formModel.style) :style='${formModel.style}'#end
                    #if($formModel.href) href = "$formModel.href"#end
                    #if($formModel.target) target = "$formModel.target"#end
                    #if($formModel.showIcon) :show-icon= "$formModel.showIcon"#end
                    #if($formModel.align) align="${formModel.align}" #end
                    #if($formModel.disabled) :disabled="${formModel.disabled}" #end
                    #if($formModel.buttonText)  buttonText="${formModel.buttonText}"#end
                    #if($formModel.type) type="${formModel.type}" #end
                    #if($formModel.textStyle) :textStyle='${formModel.textStyle}'#end
                    #if($config.defaultValue) value="${config.defaultValue}"#end
                    #if($formModel.contentposition) content-position="${formModel.contentposition}" #end
                    #if($formModel.closable==false) :closable= "$formModel.closable" #end
                    #if($formModel.title) title ="${formModel.title}" #end
                    #if($formModel.closeText) closeText ="${formModel.closeText}" #end
                    #if($formModel.description) description ="${formModel.description}" #end
                    #if($formModel.tipLabel) tipLabel ="${formModel.tipLabel}" #end
                    #if($content) content ="$!{content}" #elseif(${defaultName}) content ="$!{defaultName}"#end>
                </${config.tag}>
                </xh-form-tip-item>
            </el-col>
        #end
    #elseif(${xhKey}=='divider')
        #set($content=$formModel.content)
        #set($defaultName=${formModel.slot.defaultName})
        #if(${config.pc}==true)
            <el-col :span="24"  >
                <${config.tag} #if($formModel.contentposition)  content-position="${formModel.contentposition}" #end>
                #if($content) $!{content} #elseif(${defaultName})$!{defaultName} #end
            </${config.tag}>
            </el-col>
        #end
    #elseif(${xhKey}=='collapse' && $pcshow == true)
        #set($collapse = "el-collapse")
        #if(${outermost}=='1')
            #set($collapse = "el-collapse-item")
        #end
        #if(${isEnd}=='0')
            #if(${outermost}=='0')
            <el-col :span="${formModel.span}">
                <${collapse} :accordion="${formModel.accordion}" v-model="${formModel.model}" class="mb-20">
            #else
                <${collapse} title="${formModel.title}" name="${formModel.name}">
            #end
        #else
            #if(${outermost}=='0')
            </${collapse}>
                </el-col>
            #else
            </${collapse}>
            #end
        #end
    #elseif(${xhKey}=='mast')
        #set($html = $fieLdsModel.formColumnModel.fieLdsModel)
        #set($vModel = "${html.vModel}")
        #set($config = $html.config)
        #set($mastKey = "${config.xhKey}")
        #set($show = $config.noShow)
	 #set($pcshow = $config.pc)
        #set($dataType = ${config.dataType})
        #set($func ="dynamicText")
        #if(${mastKey} == 'treeSelect' || ${mastKey} == 'cascader')
            #set($func ="dynamicTreeText")
        #end
        #if($show == false  && $pcshow == true)
            <el-col :span="${config.span}" #if(${context.columnData.useFormPermission}) #if(${vModel}) v-if="xh.hasFormP('${vModel}')"
            #elseif($mastKey == 'relationFormAttr' || $mastKey == 'popupAttr')
                 v-if="xh.hasFormP('${html.relationField}')"
            #end
           #end >
                <xh-form-tip-item #if($config.showLabel && $config.showLabel == true)
                    #if($config.label) label="${config.label}" #end
                    #if($config.labelWidth) label-width="${config.labelWidth}px"#end #else label-width="0"#end
                    #if($vModel) prop="${vModel}" #end #if($config.label && $config.tipLabel) tip-label="${config.tipLabel}" #end>
                    #set($mastModel="${context.formModel}.${vModel}")
                    #if($mastKey == 'XHText' || $mastKey =='uploadFz' || $mastKey =='uploadImg' || $mastKey =='colorPicker' || $mastKey =='rate'
                    || $mastKey =='slider' || $mastKey =='numInput')
                        #if($mastKey =='numInput')
                            #set(${config.tag}="XhNumber")
                            #set($mastModel="${mastModel}_name")
                        #end
                    <${config.tag}  #if($vModel)  v-model="${mastModel}"#end
                    #if($mastKey!='XHText')
                        #if($html.placeholder) placeholder="${html.placeholder}" #end
                    #else
                        #if($config.defaultValue) value="${config.defaultValue}"#end
                    #end
                    #if($html.maxlength) :maxlength="${html.maxlength}" #end disabled
                    #if($mastKey =='uploadFz' || $mastKey =='uploadImg') detailed #end
                    #if($html.readonly == true ) readonly #end
                    #if($html.clearable == true ) clearable #end
                    #if($html.prefixicon) prefix-icon='${html.prefixicon}' #end
                    #if($html.suffixicon) suffix-icon='${html.suffixicon}' #end
                    #if($html.style) :style='${html.style}'#end
                    #if($html.showWordLimit == true ) ${html.showWordLimit} #end
                    #if($html.size) size="${html.size}" #end
                    #if($html.min) :min="${html.min}" #end
                    #if($html.max) :max="${html.max}" #end
                    #if($html.type) type="${html.type}" #end
                    #if($html.autosize) :autosize='${html.autosize}' #end
                    #if($html.step) :step="${html.step}" #end
                    #if($html.precision) :precision="${html.precision}" #end
                    #if($html.stepstrictly==true) stepstrictly #end
                    #if($html.textStyle) :textStyle='${html.textStyle}' #end
                    #if($html.lineHeight) :lineHeight="${html.lineHeight}" #end
                    #if($html.fontSize) :fontSize="${html.fontSize}" #end
                    #if($html.controlsposition) controls-position='${html.controlsposition}' #end
                    #if($html.showChinese) :showChinese="${html.showChinese}" #end
                    #if($html.showPassword) show-password #end
                    #if($html.filterable==true) filterable #end
                    #if($html.multiple) :multiple="${html.multiple}" #end
                    #if($html.separator) separator="${html.separator}" #end
                    #if($html.isrange==true) is-range #end
                    #if($html.rangeseparator) range-separator="${html.rangeseparator}" #end
                    #if($html.startplaceholder) start-placeholder="${html.startplaceholder}" #end
                    #if($html.endplaceholder) end-placeholder="${html.endplaceholder}" #end
                    #if($html.format) format="${html.format}" #end
                    #if($html.colorformat) color-format="${html.colorformat}" #end
                    #if($html.valueformat) value-format="${html.valueformat}" #end
                    #if($html.activetext) active-text="${html.activetext}" #end
                    #if($html.inactivetext) inactive-text="${html.inactivetext}" #end
                    #if($html.activecolor) active-color="${html.activecolor}" #end
                    #if($html.inactivecolor) inactive-color="${html.inactivecolor}" #end
                    #if($html.activevalue) :active-value="${html.activevalue}" #end
                    #if($html.inactivevalue) :inactive-value="${html.inactivevalue}" #end
                    #if($html.pickeroptions) :picker-options='${html.pickeroptions}'#end
                    #if($html.showScore == true ) show-score #end
                    #if($html.showText == true ) show-text #end
                    #if($html.allowhalf == true ) allow-half #end
                    #if($html.showAlpha == true ) show-alpha #end
                    #if($html.showStops == true ) show-stops #end
                    #if($html.range == true ) range #end
                    #if($html.showTip == true ) :showTip="${html.showTip}" #end
                    #if($html.accept) accept="${html.accept}" #end
                    #if($html.fileSize) :fileSize="${html.fileSize}" #end
                    #if($html.sizeUnit) sizeUnit="${html.sizeUnit}" #end
                    #if($html.limit) :limit="${html.limit}" #end
                    #if($html.pathType) pathType="${html.pathType}" #end
                    #if($html.isAccount) :isAccount="${html.isAccount}" #end
                    #if($html.folder) folder="${html.folder}" #end
                    #if($html.buttonText) buttonText="${html.buttonText}" #end
                    #if($html.contentposition) content-position="${html.contentposition}" #end
                    #if($html.isAmountChinese) isAmountChinese #end
                    #if($html.thousands) thousands #end
                    #if($html.addonAfter) addonAfter="${html.addonAfter}" #end
                    #if($html.addonBefore) addonBefore="${html.addonBefore}" #end
                    #if($html.controlsPosition) controlsPosition="${html.controlsPosition}" #end
                    #if($html.level) :level=${html.level} #end >
                </${config.tag}>
                    #else
                        #if(${mastKey} == 'relationFormAttr' || ${mastKey} == 'popupAttr')
                            #if(${config.isStorage} == 1)
                                #set($mastModel = "${context.formModel}.${html.relationField}_${html.showField}")
                            #end
                        #end
                        #if($dataType=='static')
                         <p>{{ ${mastModel} | ${func}($!{vModel}Options) }} </p>
                        #else
                                #if(${mastKey} == 'relationForm')
                        <a @click="toDetail(${mastModel}_id,'${html.modelId}')" style="color:#1890ff">
                             {{${mastModel}}}</a>
                                #elseif(${mastKey} == 'comInput')
                        <p>#if($html.slot.prepend)${html.slot.prepend}#end{{${mastModel}}}#if($html.slot.append)${html.slot.append}#end</p>
                                #else
                        <p>{{${mastModel}}}</p>
                                #end
                        #end
                    #end
                </xh-form-tip-item>
            </el-col>
        #end
    #elseif(${xhKey}=='mastTable')
        #set($beforeVmodel =${fieLdsModel.formMastTableModel.vModel})
        #set($html = $fieLdsModel.formMastTableModel.mastTable.fieLdsModel)
        #set($tableName =  $fieLdsModel.formMastTableModel.table)
        #set($vModel = "${html.vModel}")
        #set($config = $html.config)
        #set($mastKey = "${config.xhKey}")
        #set($show = $config.noShow)
	#set($pcshow = $config.pc)
        #set($dataType = ${config.dataType})
        #set($func ="dynamicText")
        #if(${mastKey} == 'treeSelect' || ${mastKey} == 'cascader')
            #set($func ="dynamicTreeText")
        #end
        #if($show == false && $pcshow ==true)
            <el-col :span="${config.span}" #if(${context.columnData.useFormPermission}) #if(${beforeVmodel}) v-if="xh.hasFormP('${beforeVmodel}')"
        #elseif($mastKey == 'relationFormAttr' || $mastKey == 'popupAttr')
                    v-if="xh.hasFormP('${html.relationField}')"
            #end
        #end>
                <xh-form-tip-item #if($config.showLabel && $config.showLabel == true)
                    #if($config.label) label="${config.label}" #end
                    #if($config.labelWidth) label-width="${config.labelWidth}px"#end #else label-width="0"#end
                    #if($vModel) prop="${beforeVmodel}" #end #if($config.label && $config.tipLabel) tip-label="${config.tipLabel}" #end>
                     #set($mastModel="${context.formModel}.${beforeVmodel}")
                     #if($mastKey == 'XHText' || $mastKey =='uploadFz' || $mastKey =='uploadImg' || $mastKey =='colorPicker' || $mastKey =='rate'
                     || $mastKey =='slider' || $mastKey =='numInput')
                         #if($mastKey =='numInput')
                             #set(${config.tag}="XhNumber")
                         #end
                    <${config.tag}  #if($vModel)  v-model=" ${mastModel}"#end
                    #if($mastKey!='XHText')
                        #if($html.placeholder) placeholder="${html.placeholder}" #end
                    #else
                        #if($config.defaultValue) value="${config.defaultValue}"#end
                    #end
                    #if($html.maxlength) :maxlength="${html.maxlength}" #end disabled
                     #if($mastKey =='uploadFz' || $mastKey =='uploadImg') detailed #end
                    #if($html.readonly == true ) readonly #end
                    #if($html.clearable == true ) clearable #end
                    #if($html.prefixicon) prefix-icon='${html.prefixicon}' #end
                    #if($html.suffixicon) suffix-icon='${html.suffixicon}' #end
                    #if($html.style) :style='${html.style}'#end
                    #if($html.showWordLimit == true ) ${html.showWordLimit} #end
                    #if($html.size) size="${html.size}" #end
                    #if($html.min) :min="${html.min}" #end
                    #if($html.max) :max="${html.max}" #end
                    #if($html.type) type="${html.type}" #end
                    #if($html.autosize) :autosize='${html.autosize}' #end
                    #if($html.step) :step="${html.step}" #end
                    #if($html.precision) :precision="${html.precision}" #end
                    #if($html.stepstrictly==true) stepstrictly #end
                    #if($html.textStyle) :textStyle='${html.textStyle}' #end
                    #if($html.lineHeight) :lineHeight="${html.lineHeight}" #end
                    #if($html.fontSize) :fontSize="${html.fontSize}" #end
                    #if($html.controlsposition) controls-position='${html.controlsposition}' #end
                    #if($html.showChinese) :showChinese="${html.showChinese}" #end
                    #if($html.showPassword) show-password #end
                    #if($html.filterable==true) filterable #end
                    #if($html.multiple) :multiple="${html.multiple}" #end
                    #if($html.separator) separator="${html.separator}" #end
                    #if($html.isrange==true) is-range #end
                    #if($html.rangeseparator) range-separator="${html.rangeseparator}" #end
                    #if($html.startplaceholder) start-placeholder="${html.startplaceholder}" #end
                    #if($html.endplaceholder) end-placeholder="${html.endplaceholder}" #end
                    #if($html.format) format="${html.format}" #end
                    #if($html.colorformat) color-format="${html.colorformat}" #end
                    #if($html.valueformat) value-format="${html.valueformat}" #end
                    #if($html.activetext) active-text="${html.activetext}" #end
                    #if($html.inactivetext) inactive-text="${html.inactivetext}" #end
                    #if($html.activecolor) active-color="${html.activecolor}" #end
                    #if($html.inactivecolor) inactive-color="${html.inactivecolor}" #end
                    #if($html.activevalue) :active-value="${html.activevalue}" #end
                    #if($html.inactivevalue) :inactive-value="${html.inactivevalue}" #end
                    #if($html.pickeroptions) :picker-options='${html.pickeroptions}'#end
                    #if($html.showScore == true ) show-score #end
                    #if($html.showText == true ) show-text #end
                    #if($html.allowhalf == true ) allow-half #end
                    #if($html.showAlpha == true ) show-alpha #end
                    #if($html.showStops == true ) show-stops #end
                    #if($html.range == true ) range #end
                    #if($html.showTip == true ) :showTip="${html.showTip}" #end
                    #if($html.accept) accept="${html.accept}" #end
                    #if($html.fileSize) :fileSize="${html.fileSize}" #end
                    #if($html.sizeUnit) sizeUnit="${html.sizeUnit}" #end
                    #if($html.limit) :limit="${html.limit}" #end
                    #if($html.pathType) pathType="${html.pathType}" #end
                    #if($html.isAccount) :isAccount="${html.isAccount}" #end
                    #if($html.folder) folder="${html.folder}" #end
                    #if($html.buttonText) buttonText="${html.buttonText}" #end
                    #if($html.contentposition) content-position="${html.contentposition}" #end
                    #if($html.isAmountChinese) isAmountChinese #end
                    #if($html.thousands) thousands #end
                    #if($html.addonAfter) addonAfter="${html.addonAfter}" #end
                    #if($html.addonBefore) addonBefore="${html.addonBefore}" #end
                    #if($html.controlsPosition) controlsPosition="${html.controlsPosition}" #end
                    #if($html.level) level=${html.level} #end>
                </${config.tag}>
                #else
                    #set($field =  $fieLdsModel.formMastTableModel.field)
                    #set($mastModel="${context.formModel}.${beforeVmodel}")
                    #if(${mastKey} == 'relationFormAttr' || ${mastKey} == 'popupAttr')
                        #if(${config.isStorage} == 1)
                            #set($mastModel = "${context.formModel}.${html.relationField}_${html.showField}")
                        #end
                    #end
                    #if($dataType=='static')
                        <p>{{ ${mastModel} | ${func}($!{beforeVmodel}Options) }} </p>
                    #else
                        #if(${mastKey} == 'relationForm')
                            <a  @click="toDetail(${context.formModel}.${html.config.tableName}.${html.vModel}_id,'${html.modelId}')" style="color:#1890ff">
                                {{${mastModel}}}</a>
                        #elseif(${mastKey} == 'comInput')
                            <p>#if($html.slot.prepend)${html.slot.prepend}#end{{${mastModel}}}#if($html.slot.append)${html.slot.append}#end</p>
                        #else
                            <p>{{${mastModel}}}</p>
                        #end
                    #end
                #end
                </xh-form-tip-item>
            </el-col>
        #end
    #elseif($xhKey == 'table')
        #set($child = $fieLdsModel.childList)
        #set($className = "")
        #set($TabField = "")
        #foreach($children in ${context.children})
            #if(${children.tableModel}==${child.tableModel})
                #set($TabField = "${children.tableModel}")
                #set($className = "${children.className.substring(0,1).toLowerCase()}${children.className.substring(1).toLowerCase()}")
            #end
        #end
    <el-col :span="${child.span}"  #if(${context.columnData.useFormPermission}) v-if="xh.hasFormP('${TabField}')" #end>
    <xh-form-tip-item label-width="0">
        #if($child.showTitle== true)
            <div class="XH-common-title">
              <h2>${child.label}#if($child.label && $child.tipLabel)<el-tooltip placement="top" content='${child.tipLabel}'><a class='el-icon-question tooltip-question'></a></el-tooltip>#end</h2>
            </div>
        #end
    <el-table :data="${context.formModel}.${className}List" size='mini'  #if(${child.showSummary}) show-summary :summary-method="get${className}Summaries" #end >
        <el-table-column type="index" width="50" label="序号" align="center" />
        #foreach($html in ${child.childList})
            #set($htmlChild = $html.fieLdsModel)
            #set($vModel = "${htmlChild.vModel}")
            #set($config = $htmlChild.config)
            #set($tableKey = "${config.xhKey}")
            #set($dataType = ${config.dataType})
            #set($pcShow = $config.pc)
            #set($func ="dynamicText")
            #if(${tableKey} == 'treeSelect' || ${tableKey} == 'cascader')
                #set($func ="dynamicTreeText")
            #end
            #if($pcShow ==true)
            <el-table-column prop="$!{vModel}" label="${config.label}" #if(${context.columnData.useFormPermission}) #if(${vModel})  v-if="xh.hasFormP('${TabField}-$!{vModel}')"
            #elseif($tableKey == 'relationFormAttr' || $tableKey == 'popupAttr')
                             v-if="xh.hasFormP('${TabField}-${htmlChild.relationField}')"
            #end
            #end>
                #if($config.tipLabel)
                  <template slot="header">
                      ${config.label}
                      #if($config.label && ${config.tipLabel})
                        <span slot="label">
                      		<el-tooltip placement="top" content='${config.tipLabel}'>
                        		<a class='el-icon-question tooltip-question'></a>
                      		</el-tooltip>
                    	</span>
                      #end
                  </template>
                #end
            <template slot-scope="scope">
                    #if($tableKey == 'XHText' || $tableKey =='uploadFz' || $tableKey =='uploadImg' || $tableKey =='colorPicker' || $tableKey =='rate'
                    || $tableKey =='slider' || $tableKey =='numInput')
                        #if($tableKey =='numInput')
                            #set(${config.tag}="XhNumber")
                            #set($vModel="${vModel}_name")
                        #end
                    <${config.tag} #if($vModel)  v-model="scope.row.${vModel}"#end
                    #if($tableKey!='XHText')
                        #if($htmlChild.placeholder) placeholder="${htmlChild.placeholder}" #end
                    #else
                        #if($config.defaultValue) value="${config.defaultValue}"#end
                    #end
                    #if($htmlChild.maxlength) :maxlength="${htmlChild.maxlength}" #end
                    #if($htmlChild.readonly == true ) readonly #end  disabled
                    #if($tableKey =='uploadFz' || $tableKey =='uploadImg') detailed #end
                    #if($htmlChild.clearable == true ) clearable #end
                    #if($htmlChild.prefixicon) prefix-icon='${htmlChild.prefixicon}' #end
                    #if($htmlChild.suffixicon) suffix-icon='${htmlChild.suffixicon}' #end
                    #if($htmlChild.style) :style='${htmlChild.style}'#end
                    #if($htmlChild.showWordLimit == true ) ${htmlChild.showWordLimit} #end
                    #if($htmlChild.size) size="${htmlChild.size}" #end
                    #if($htmlChild.min) :min="${htmlChild.min}" #end
                    #if($htmlChild.max) :max="${htmlChild.max}" #end
                    #if($htmlChild.type) type="${htmlChild.type}" #end
                    #if($htmlChild.autosize) :autosize='${htmlChild.autosize}' #end
                    #if($htmlChild.step) :step="${htmlChild.step}" #end
                    #if($htmlChild.stepstrictly==true) stepstrictly #end
                    #if($htmlChild.textStyle) :textStyle='${htmlChild.textStyle}' #end
                    #if($htmlChild.lineHeight) :lineHeight="${htmlChild.lineHeight}" #end
                    #if($htmlChild.fontSize) :fontSize="${htmlChild.fontSize}" #end
                    #if($htmlChild.controlsposition) controls-position='${htmlChild.controlsposition}' #end
                    #if($htmlChild.showChinese) :showChinese="${htmlChild.showChinese}" #end
                    #if($htmlChild.showPassword) show-password #end
                    #if($htmlChild.filterable==true) filterable #end
                    #if($htmlChild.multiple) :multiple="${htmlChild.multiple}" #end
                    #if($htmlChild.separator) separator="${htmlChild.separator}" #end
                    #if($htmlChild.isrange==true) is-range #end
                    #if($htmlChild.rangeseparator) range-separator="${htmlChild.rangeseparator}" #end
                    #if($htmlChild.startplaceholder) start-placeholder="${htmlChild.startplaceholder}" #end
                    #if($htmlChild.endplaceholder) end-placeholder="${htmlChild.endplaceholder}" #end
                    #if($htmlChild.format) format="${htmlChild.format}" #end
                    #if($htmlChild.colorformat) color-format="${htmlChild.colorformat}" #end
                    #if($htmlChild.valueformat) value-format="${htmlChild.valueformat}" #end
                    #if($htmlChild.activetext) active-text="${htmlChild.activetext}" #end
                    #if($htmlChild.inactivetext) inactive-text="${htmlChild.inactivetext}" #end
                    #if($htmlChild.activecolor) active-color="${htmlChild.activecolor}" #end
                    #if($htmlChild.inactivecolor) inactive-color="${htmlChild.inactivecolor}" #end
                    #if($htmlChild.activevalue) active-value="${htmlChild.activevalue}" #end
                    #if($htmlChild.inactivevalue) inactive-value="${htmlChild.inactivevalue}" #end
                    #if($htmlChild.pickeroptions) :picker-options='${htmlChild.pickeroptions}'#end
                    #if($htmlChild.showScore == true ) show-score #end
                    #if($htmlChild.showText == true ) show-text #end
                    #if($htmlChild.allowhalf == true ) allow-half #end
                    #if($htmlChild.showAlpha == true ) show-alpha #end
                    #if($htmlChild.showStops == true ) show-stops #end
                    #if($htmlChild.range == true ) range #end
                    #if($htmlChild.showTip == true ) :showTip="${htmlChild.showTip}" #end
                    #if($htmlChild.accept) accept="${htmlChild.accept}" #end
                    #if($htmlChild.fileSize) :fileSize="${htmlChild.fileSize}" #end
                    #if($htmlChild.sizeUnit) sizeUnit="${htmlChild.sizeUnit}" #end
                    #if($htmlChild.limit) :limit="${htmlChild.limit}" #end
                    #if($htmlChild.pathType) pathType="${htmlChild.pathType}" #end
                    #if($htmlChild.isAccount) :isAccount="${htmlChild.isAccount}" #end
                    #if($htmlChild.folder) folder="${htmlChild.folder}" #end
                    #if($htmlChild.buttonText) buttonText="${htmlChild.buttonText}" #end
                    #if($htmlChild.contentposition) content-position="${htmlChild.contentposition}" #end
                    #if($htmlChild.isAmountChinese) isAmountChinese #end
                    #if($htmlChild.thousands) thousands #end
                    #if($htmlChild.addonAfter) addonAfter="${htmlChild.addonAfter}" #end
                    #if($htmlChild.addonBefore) addonBefore="${htmlChild.addonBefore}" #end
                    #if($htmlChild.controlsPosition) controlsPosition="${htmlChild.controlsPosition}" #end
                    #if($htmlChild.level) level=${htmlChild.level} #end>
                </${config.tag}>
                #else
                    #set($mastModel="${vModel}")
                    #set($isStorage = ${config.isStorage})
                    #if(${tableKey} == 'relationFormAttr' || ${tableKey} == 'popupAttr')
                        #if(${config.isStorage} == 1)
                            #set($mastModel = "${htmlChild.relationField}_${htmlChild.showField}")
                        #end
                    #end
                    #if($dataType=='static')
                        <p>{{ scope.row.${mastModel} | ${func}($!{vModel}Options) }}</p>
                    #else
                        #if(${tableKey} == 'relationForm')
                            <a  @click="toDetail(scope.row.${mastModel}_id,'${htmlChild.modelId}')" style="color:#1890ff">
                              {{scope.row.${mastModel}}}</a>
                        #elseif(${mastKey} == 'comInput')
                            <p>#if($htmlChild.slot.prepend)${htmlChild.slot.prepend}#end{{scope.row.${mastModel}}}#if($htmlChild.slot.append)${htmlChild.slot.append}#end</p>
                        #else
                            <p>{{scope.row.${mastModel}}}</p>
                        #end
                    #end
                #end
        </template>
        </el-table-column>
            #end
        #end
    </el-table>
    </xh-form-tip-item>
    </el-col>
    #end
#end
    </template>
</el-form>
#if($context.HasPrintBtn)
<print-browse :visible.sync="printBrowseVisible" :id="printIdNow" :formId="${context.formModel}.${pKeyName}" />
<PrintDialog v-if="printDialogVisible" ref="printDialog" @change="printBrowseHandle"/>
#end
    #if(${context.popupType}=='general')
    </el-row>
    <span slot="footer" class="dialog-footer">
          #if($context.HasPrintBtn)
            <el-button type="primary" @click="printDialog">#if(${context.PrintButton})${context.PrintButton}#else 打 印#end</el-button>
          #end
        <el-button @click="visible = false">#if(${context.cancelButtonText})${context.cancelButtonText}#else 取 消#end</el-button>
    </span>
    <Detail v-if="detailVisible" ref="Detail" @close="detailVisible = false" />
    </el-dialog>
    #elseif(${context.popupType}=='fullScreen')
    </el-row>
    </div>
</transition>
    #elseif(${context.popupType}=='drawer')
    </div>
    <div class="drawer-footer">
        #if($context.HasPrintBtn)
        <el-button type="primary" @click="printDialog">#if(${context.PrintButton})${context.PrintButton}#else 打 印#end</el-button>
        #end
        <el-button @click="visible = false">#if(${context.cancelButtonText})${context.cancelButtonText}#else 取 消#end</el-button>
    </div>
    </div>
    </el-drawer>
    #end
</template>
<script>
    import request from '@/utils/request'
    import PrintBrowse from '@/components/PrintBrowse'
    import PrintDialog from '@/components/PrintDialog'
    import {getConfigData} from '@/api/onlineDev/visualDev'
    import Detail from '@/views/basic/dynamicModel/list/detail'
    import {thousandsFormat} from "@/components/Generator/utils/index"
        #if($context.HasPrintBtn)
        #end

    export default {
        components: {#if($context.HasPrintBtn) PrintBrowse,PrintDialog, #end Detail},
        props: [],
        data() {
            return {
                visible: false,
                detailVisible: false,
                loading: false,
                #if($context.HasPrintBtn)
                printBrowseVisible: false,
                printDialogVisible:false,
                printId: '$!{context.printId}',
                printIdNow:'',
                #end

            ${context.formModel}: {
                ${pKeyName} :'',
                #foreach($fieLdsModel in ${context.fields})
                    #set($html = $fieLdsModel.formColumnModel.fieLdsModel)
                    #set($vModel = "${html.vModel}")
                        #if($vModel != '')
                    #set($config = $html.config)
                    #set($mastKey = "${config.xhKey}")
                    #if($mastKey!='XHText' && $mastKey!='divider')
                        #if($!config.valueType=='String')
                            $!{vModel} : "$!{config.defaultValue}",
                        #elseif($!config.valueType=='undefined')
                            $!{vModel} : '',
                        #else
                            $!{vModel} : $!{config.defaultValue},
                        #end
                    #end
                        #end
                #end
                #foreach($child in ${context.children})
                    #set($className = "")
                    #foreach($children in ${context.children})
                        #if(${children.tableModel}==${child.tableModel})
                            #set($className = "${children.className.substring(0,1).toLowerCase()}${children.className.substring(1).toLowerCase()}")
                        #end
                    #end
                        ${className}List:[],
                #end
                #foreach($clum in ${context.columnChildren})
                    #set($clumLowName = "${clum.tableName.toLowerCase()}")
                    ${clumLowName}:
                    {
                        #foreach($field in  ${clum.fieLdsModelList})
                            #set($fieldName = ${field.field})
                                $fieldName:'',
                        #end
                    },
                #end
                #foreach($mast in ${context.mastTable})
                    ${mast.formMastTableModel.vModel}:'',
                #end
            },
            #foreach($fieLdsModel in ${context.form})
                #set($xhKey = "${fieLdsModel.xhKey}")
                #set($formModel = ${fieLdsModel.formModel})
                #set($outermost = ${formModel.outermost})
                #set($isEnd = "${fieLdsModel.isEnd}")
                #if(${isEnd}=='0')
                    #if($xhKey=='collapse')
                        #if(${outermost}=='0')
                            ${formModel.model}:${formModel.active},
                        #end
                    #end
                    #if($xhKey=='tab')
                        #if(${outermost}=='0')
                            ${formModel.model}:'${formModel.active}',
                        #end
                    #end
                #end
            #end
            #foreach($fieLdsModel in ${context.fields})
                #set($html = $fieLdsModel.formColumnModel.fieLdsModel)
                #set($vModel = "${html.vModel}")
                #set($config = $html.config)
                #set($xhkey = $config.xhKey)
                #if(${config.dataType} == "static")
                    #if($html.slot.options)
                                ${vModel}Options:${html.slot.options},
                    #elseif($html.options)
                            ${vModel}Options:${html.options},
                    #end
                #end
                #if($html.props.props)
                    #set($propsModel = ${html.props.props})
                        $!{vModel}Props:{"label":"${propsModel.label}","value":"${propsModel.value}" #if($!{multiple}) ,"multiple":$multiple #end #if($!{propsModel.children}),"children":"${propsModel.children}" #end},
                #end
            #end

            #foreach($child in ${context.children})
                #set($className = "${child.className.substring(0,1).toLowerCase()}${child.className.substring(1).toLowerCase()}")
                #foreach($fieLdsModel in ${child.childList})
                    #set($html = $fieLdsModel.fieLdsModel)
                    #set($vModel = "${html.vModel}")
                    #set($config = $html.config)
                    #set($xhkey = $config.xhKey)
                    #if(${config.dataType} == "static")
                        #if($html.slot.options)
                                    ${vModel}Options:${html.slot.options},
                        #elseif($html.options)
                                ${vModel}Options:${html.options},
                        #end
                    #end
                    #if($html.props.props)
                        #set($propsModel = ${html.props.props})
                            $!{vModel}Props:{"label":"${propsModel.label}","value":"${propsModel.value}" #if($!{multiple}) ,"multiple":$multiple #end #if($!{propsModel.children}),"children":"${propsModel.children}" #end},
                    #end
                #end
            #end
            #foreach($ChildField in ${context.columnChildren})
                #foreach($FormMastTableModel in ${ChildField.fieLdsModelList})
                    #set($html = ${FormMastTableModel.mastTable.fieLdsModel})
                    #set($xhKey = ${html.config.xhKey})
                    #set($ChildVmodel =${FormMastTableModel.vModel})
                    #set($ClDataType = ${html.config.dataType})
                    #if(${ClDataType} == "static")
                        #if($html.slot.options)
                                    ${ChildVmodel}Options:${html.slot.options},
                        #elseif($html.options)
                                ${ChildVmodel}Options:${html.options},
                        #end
                    #end
                    #if($html.props.props)
                        #set($propsModel = ${html.props.props})
                            $!{vModel}Props:{"label":"${propsModel.label}","value":"${propsModel.value}" #if($!{multiple}) ,"multiple":$multiple #end #if($!{propsModel.children}),"children":"${propsModel.children}" #end},
                    #end
                #end
            #end
        }
        },
        computed: {},
        watch: {},
        created() {

        },
        mounted() {},
        methods: {
            #if($context.HasPrintBtn)

              printBrowseHandle(id) {
                this.printDialogVisible = false
                this.printIdNow = id;
                this.printBrowseVisible = true;
              },
              printDialog() {
                this.printDialogVisible = true
                this.$nextTick(() => {
                  if(this.printId.length === 1){
                    this.printBrowseHandle(this.printId.id)
                    return
                  }
                   #set($ref='this.'+'$'+'refs')
                   ${ref}.printDialog.init(this.printId.split(","))
                })
              },
            #end
            toDetail(defaultValue, modelId) {
                if (!defaultValue) return
                getConfigData(modelId).then(res => {
                    if (!res.data || !res.data.formData) return
                    let formData = JSON.parse(res.data.formData)
                    formData.popupType = 'general'
                    this.detailVisible = true
                    this.$nextTick(() => {
                        this.$refs.Detail.init(formData, modelId, defaultValue)
                    })
                })
            },
            #foreach($child in ${context.children})
                #set($showSummary = ${child.showSummary})
                #set($summaryField =  ${child.summaryField})
                #set($className = "${child.className.substring(0,1).toLowerCase()}${child.className.substring(1).toLowerCase()}")
                #if($showSummary)
                    get${className}Summaries(param) {
                        const summaryField = ${summaryField};
                        const { columns, data } = param;
                        const sums = [];
                        columns.forEach((column, index) => {
                            if (index === 0) {
                                sums[index] = '合计';
                                return;
                            }
                            if (!summaryField.includes(column.property)) {
                                sums[index] = '';
                                return;
                            }
                            const values = data.map(item => Number(item[column.property]));
                            if (!values.every(value => isNaN(value))) {
                                sums[index] = values.reduce((prev, curr) => {
                                    const value = Number(curr);
                                    if (!isNaN(value)) {
                                        return prev + curr;
                                    } else {
                                        return prev;
                                    }
                                }, 0);
                                const thousandsField = ${child.thousandsField};
                                if(thousandsField.includes(column.property)){
                                    sums[index] = thousandsFormat(sums[index]);
                                }
                            } else {
                                sums[index] = '';
                            }
                        });
                        return sums;
                    },
                #end
            #end
            #set($ref = "${context.formRef}")
            #set($l = "'")
            #set($c = "[")
            #set($p = "]")
            dataInfo(dataAll){
                let _dataAll =dataAll
                #set($needToJson = ["uploadFz","uploadImg"])
                #set($needToJsonStatic = ["cascader","checkbox"])
                #set($needToJsonMultiple = ["select","treeSelect"])
                #foreach($fieLdsModel in ${context.fields})
                    #set($html = $fieLdsModel.formColumnModel.fieLdsModel)
                    #set($vModel = "${html.vModel}")
                    #set($config = $html.config)
                    #set($dataType = $config.dataType)
                    #set($mastKey = "${config.xhKey}")
                    #if(${needToJson.contains($mastKey)}
                        || ${needToJsonStatic.contains($mastKey)} && $dataType=='static'
                        || ${needToJsonMultiple.contains($mastKey)} && ${dataType}=='static' && ${html.multiple}=='true')
                        _dataAll.${vModel} = _dataAll.${vModel} ? JSON.parse( _dataAll.${vModel}):[]
                    #end
                    #if(${mastKey}=='date')
                        _dataAll.${vModel} = _dataAll.${vModel} ? this.xh.toDate(Number(_dataAll.${vModel}),"${html.format}") : ''
                    #end
                #end
                #foreach($MastfieLds in ${context.mastTable})
                    #set($table =  ${MastfieLds.formMastTableModel.table})
                    #set($lowTableName = "${table.toLowerCase()}")
                    #set($BeforeVmodel = $MastfieLds.formMastTableModel.vModel)
                    #set($html = $MastfieLds.formMastTableModel.mastTable.fieLdsModel)
                    #set($vModel = "${html.vModel}")
                    #set($config = $html.config)
                    #set($dataType = $config.dataType)
                    #set($mastKey = "${config.xhKey}")
                    #if(${needToJson.contains($mastKey)}
                        || ${needToJsonStatic.contains($mastKey)} && $dataType=='static'
                        || ${needToJsonMultiple.contains($mastKey)} && ${dataType}=='static' && ${html.multiple}=='true')
                        _dataAll.${BeforeVmodel} = _dataAll.${lowTableName}.${vModel} ? JSON.parse(_dataAll.${lowTableName}.${vModel}) :[]
                    #else
                        #if(${mastKey}=='date')
                            _dataAll.${BeforeVmodel} = _dataAll.${lowTableName}.${vModel}? this.xh.toDate(Number(_dataAll.${lowTableName}.${vModel}),"${html.format}") : ''
                        #elseif(${mastKey}=="relationForm")
                            _dataAll.${BeforeVmodel}_id =  _dataAll.${lowTableName}.${vModel}_id
                        #elseif(${mastKey}=="numInput")
                            _dataAll.${BeforeVmodel} =  _dataAll.${lowTableName}.${vModel}_name
                        #else
                            _dataAll.${BeforeVmodel} =  _dataAll.${lowTableName}.${vModel}
                        #end
                    #end
                #end
                #foreach($child in ${context.children})
                    #set($className = "")
                    #foreach($children in ${context.children})
                        #if(${children.tableModel}==${child.tableModel})
                            #set($className = "${children.className.substring(0,1).toLowerCase()}${children.className.substring(1).toLowerCase()}")
                        #end
                    #end
                for(let i=0;i<_dataAll.${className}List.length;i++){
                    var _list = _dataAll.${className}List[i];
                    #foreach($childListAll in ${child.childList})
                        #set($html = $childListAll.fieLdsModel)
                        #set($model = "${html.vModel}")
                        #set($config = ${html.config})
                        #set($dataType = $config.dataType)
                        #set($control = "${config.xhKey}")
                        #if(${needToJson.contains($control)}
                            || ${needToJsonStatic.contains($control)} && $dataType=='static'
                            || ${needToJsonMultiple.contains($control)} && ${dataType}=='static' && ${html.multiple}=='true')
                            _list.${model} = _list.${model}? JSON.parse(_list.${model}):[]
                         #elseif(${control}=='date')
                             _list.${model} = _list.${model} ? this.xh.toDate(Number(_list.${model}),"${html.format}") : ''
                        #end
                    #end
                }
                #end
                this.${context.formModel} = _dataAll
            },

            #if(${context.popupType}=='fullScreen')
                goBack() {
                    this.$emit('refresh')
                },
            #end
            init(id) {
                this.${context.formModel}.${pKeyName} = id || 0;
                this.visible = true;
                this.$nextTick(() => {
                    if(this.${context.formModel}.${pKeyName}){
                        this.loading = true
                        request({
                            url: '/api/${context.module}/${context.className}/detail/'+this.${context.formModel}.${pKeyName},
                            method: 'get'
                        }).then(res => {
                            this.dataInfo(res.data)
                            this.loading = false
                        })
                    }

                })
            },
        },
    }

</script>
