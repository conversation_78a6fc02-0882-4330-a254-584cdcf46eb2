
#set($modelPath = "model."+${context.modelPathName})

package ${context.package}.${modelPath};

#foreach($html in ${context.children})
import ${context.package}.${modelPath}.${html.className}Model;
#end
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import java.math.BigDecimal;
import java.util.Date;
import com.xinghuo.obsolete.util.generater.DataSwapUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
/**
 *
 * ${context.genInfo.description}
 * @版本： ${context.genInfo.version}
 * @版权： ${context.genInfo.copyright}
 * @作者： ${context.genInfo.createUser}
 * @日期： ${context.genInfo.createDate}
 */
@Data
public class $!{context.className}InfoVO{
        /** 主键 **/
        @JsonProperty("${context.pKeyName}")
        private String ${context.pKeyName};

    #foreach($fieLdsModel in ${context.fields})
        #set($html = $fieLdsModel.formColumnModel.fieLdsModel)
        #set($vModel = "${html.vModel.toLowerCase()}")
        #set($VModel = "${html.vModel}")
        #set($config = $html.config)
        #set($xhkey = "${config.xhKey}")
        #set($fieldName=${config.label})
        #if(${xhkey}!='XHText' && ${xhkey}!='divider')
    #if(${xhkey}=='numInput' || ${xhkey}=='calculate' )
        /** ${fieldName} **/
        @JsonProperty("${VModel}")
#if(${fieLdsModel.formColumnModel.fieLdsModel.precision})
        private BigDecimal ${VModel};
#else
        private Integer ${VModel};
#end

    #elseif(${xhkey}=='slider'|| ${xhkey}=='rate')
        /** ${fieldName} **/
        @JsonProperty("${VModel}")
        private Integer ${VModel};

    #elseif(${xhkey}=='modifyTime')
        /** ${fieldName} **/
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
        @JsonProperty("${VModel}")
        private Date  ${VModel};

    #elseif(${xhkey}=='createTime')
        /** ${fieldName} **/
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
        @JsonProperty("${VModel}")
        private Date  ${VModel};

    #elseif(${xhkey}=='date')
        /** ${fieldName} **/
        @JsonProperty("${VModel}")
        private Long  ${VModel};

    #elseif(${xhkey}=='relationFormAttr'|| ${xhkey}=='popupAttr')
        #if(${config.isStorage} == 1)
            #set($VModel ="${html.relationField}_${html.showField}")
        #end
        /** ${fieldName} **/
        @JsonProperty("${VModel}")
        private String ${VModel};

    #elseif(${xhkey}=='relationForm')
        /** ${fieldName} **/
        @JsonProperty("${VModel}")
        private String ${VModel};

        @JsonProperty("${VModel}_id")
        private String ${VModel}_id;

    #else
        /** ${fieldName} **/
        @JsonProperty("${VModel}")
        private String ${VModel};


        #if(
            ${xhkey}=='select' || ${xhkey}=='depSelect' || ${xhkey} =='roleSelect' || ${xhkey} =='userSelect'
        || ${xhkey}=='usersSelect' || ${xhkey} =='comSelect' || ${xhkey} =='posSelect' || ${xhkey} =='groupSelect'
        || ${xhkey}=='address' || ${xhkey} =='cascader' || ${xhkey} =='currOrganize'
        )
            #set($upModel = "${VModel.substring(0,1).toUpperCase()}${VModel.substring(1)}")
            #set($isOrg = ${xhkey}=='comSelect')
            /** 多选组件重写get **/
            public void get${upModel}(String ${VModel}) {
                this.${VModel} = DataSwapUtil.convertValueToString(${VModel},${html.multiple},${isOrg});
            }
        #end

    #end
        #end
    #end
    #foreach($html in ${context.children})
        #set($className = "${html.className.substring(0,1).toLowerCase()}${html.className.substring(1)}")
        #set($jsonClassName = "${html.className.toLowerCase()}")
        /** 子表数据 **/
        @JsonProperty("${jsonClassName}List")
        private List<${html.className}Model> ${className}List;
    #end
    #foreach($html in ${context.columnChildren})
        #set($className = "${html.tableName.toLowerCase()}")
        /** 列表子表数据 **/
        @JsonProperty("${className}")
        private ${html.modelName}Model ${className};
    #end
    #if(${context.version})
        @JsonProperty("version")
        private int version;
    #end
}
