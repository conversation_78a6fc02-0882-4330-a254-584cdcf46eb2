#parse("PublicMacro/FormMarco.vm")
<template>
<BasicPopup v-bind="$attrs" @register="registerPopup" :show-back-icon="false" :show-cancel-btn="false" title="${context.formModelName}">
    <template #insertToolbar>
        <a-button type="primary" @click="handleSubmit" :loading="btnLoading">#if(${context.confirmButtonText})${context.confirmButtonText}#else 保 存#end</a-button>
        <a-button type="warning" class="ml-10px" @click="handleReset">重置</a-button>
    </template>
<a-row class="p-10px dynamic-form ${context.formStyle}" :style="{ margin: '0 auto', width: '100%' }">
    <!-- 表单 -->
        <a-form :colon="false" size="${context.size}" layout=#if(${context.labelPosition}=="top") "vertical" #else "horizontal" #end
                labelAlign=#if(${context.labelPosition}=="right") "right" #else "left" #end
                #if(${context.labelPosition}!="top") :labelCol="{ style: { width: '${context.labelWidth}px' } }" #end
                :model="dataForm" :rules="dataRule" ref="formRef" >
            <a-row :gutter="#if(${context.formStyle}=='word-form')0#else${context.gutter}#end">
            <!-- 具体表单 -->
                #FormRendering()
            <!-- 表单结束 -->
            </a-row>
        </a-form>
</a-row>
#if($isSelectDialog == true)
<SelectModal :config="state.currTableConf" :formData="state.dataForm" ref="selectModal" @select="addForSelect"/>
#end
</BasicPopup>
</template>

<script lang="ts" setup>
    import {create} from './helper/api';
    import {nextTick, reactive, ref, toRefs, unref} from 'vue';
    import {usePopupInner} from '/@/components/Popup';
    import {useUserStore} from '/@/store/modules/user';
    import type {FormInstance} from 'ant-design-vue';
    import {useMessage} from '/@/hooks/web/useMessage';
    import {useI18n} from '/@/hooks/web/useI18n';
    // 表单权限
    import {usePermission} from '/@/hooks/web/usePermission';
        #if($isSelectDialog == true)
        #end

    interface State {
        btnLoading: boolean;
        #createStateParam("any")
    }

    defineEmits(['register']);
    const userStore = useUserStore();
    const userInfo = userStore.getUserInfo;
    const { createMessage, createConfirm } = useMessage();
    const { t } = useI18n();
    const [registerPopup, { changeLoading }] = usePopupInner(init);
    const formRef = ref<FormInstance>();
    #if($isSelectDialog == true)
    // 子表弹窗数据
    const selectModal = ref(null);
    #end
    const state = reactive<State>({
        btnLoading: false,
        #createStateParam()
    });
    const { dataForm, dataRule, btnLoading, optionsObj,ableAll} = toRefs(state);
    // 表单权限
    const { hasFormP } = usePermission();
    #GetChildTableColumns()

    function init() {
        changeLoading(true);
        nextTick(() => {
            changeLoading(false);
        });
    }
    async function handleSubmit() {
        try {
            const values = await getForm()?.validate();
            if (!values) return;
            state.btnLoading = true;
            create(state.dataForm)
                .then(res => {
                    createMessage.success(res.msg);
                    state.btnLoading = false;
                    handleReset();
                })
                .catch(() => {
                    state.btnLoading = false;
                });
        } catch (_) {}
    }
    function handleReset() {
        getForm().resetFields();
        #foreach($child in ${context.children})
        state.dataForm.${child.aliasLowName}List = [];
        #end
        init();
    }
    function getForm() {
        const form = unref(formRef);
        if (!form) {
            throw new Error('form is null!');
        }
        return form;
    }
    ##数据联动changeData方法
    #ChangeData()
    ##子表其他方法
    #CreateChildTableMethod()
    ##子表弹窗数据方法
    #if($isSelectDialog == true)
    function openSelectDialog(key) {
        state.currTableConf=state.addTableConf[key]
        state.currVmodel=key
        nextTick(() => {
            (selectModal.value as any)?.openSelectModal();
        })
    }
    function addForSelect(data) {
        for (let i = 0; i < data.length; i++) {
            let item={...state.tableRows[state.currVmodel],...data[i]}
            state.dataForm[state.currVmodel].push(item)
        }
    }
    #end
    ##合计方法
    #if($childSummary==true)
    //子表合计方法
    function getCmpValOfRow(row, key, summaryField) {
        if (!summaryField.length) return '';
        const isSummary = key => summaryField.includes(key);
        const target = row[key];
        if (!target) return '';
        let data = isNaN(target) ? 0 : Number(target);
        if (isSummary(key)) return data || 0;
        return '';
    }
    #end
##数据选项--数据字典和远端数据初始化方法
    #GetDataOptionsMethod()
##动态时间处理
    #GetRelationDate()
</script>

