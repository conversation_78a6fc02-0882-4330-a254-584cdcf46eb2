package com.xinghuo.card.budget.controller;

import com.xinghuo.card.budget.entity.FinBudgetItemEntity;
import com.xinghuo.card.budget.model.budgetitem.FinBudgetItemForm;
import com.xinghuo.card.budget.model.budgetitem.FinBudgetItemPagination;
import com.xinghuo.card.budget.model.budgetitem.FinBudgetItemVO;
import com.xinghuo.card.budget.service.FinBudgetItemService;
import com.xinghuo.common.base.ActionResult;
import com.xinghuo.common.base.vo.PageListVO;
import com.xinghuo.common.base.vo.PaginationVO;
import com.xinghuo.common.util.core.BeanCopierUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.math.BigDecimal;
import java.util.List;

/**
 * 预算子项管理
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2024-06-28
 */
@Slf4j
@RestController
@Tag(name = "预算子项管理", description = "预算子项管理")
@RequestMapping("/api/card/budget/item")
public class FinBudgetItemController {

    @Autowired
    private FinBudgetItemService finBudgetItemService;

    /**
     * 获取预算子项列表
     *
     * @param finBudgetItemPagination 分页查询参数
     * @return 预算子项列表
     */
    @PostMapping("/getList")
    @Operation(summary = "获取预算子项列表")
    public ActionResult<PageListVO<FinBudgetItemVO>> list(@RequestBody FinBudgetItemPagination finBudgetItemPagination) {
        PageListVO<FinBudgetItemEntity> pageList = finBudgetItemService.getList(finBudgetItemPagination);
        List<FinBudgetItemVO> listVOs = BeanCopierUtils.copyList(pageList.getList(), FinBudgetItemVO.class);
        
        // 补充显示信息
        for (FinBudgetItemVO vo : listVOs) {
            enrichBudgetItemVO(vo);
        }
        
        PaginationVO page = BeanCopierUtils.copy(finBudgetItemPagination, PaginationVO.class);
        return ActionResult.page(listVOs, page);
    }

    /**
     * 根据预算ID获取子项列表
     *
     * @param budgetId 预算ID
     * @return 预算子项列表
     */
    @GetMapping("/budget/{budgetId}")
    @Operation(summary = "根据预算ID获取子项列表")
    public ActionResult<List<FinBudgetItemVO>> getItemsByBudgetId(@PathVariable("budgetId") String budgetId) {
        List<FinBudgetItemEntity> list = finBudgetItemService.getItemsByBudgetId(budgetId);
        List<FinBudgetItemVO> listVOs = BeanCopierUtils.copyList(list, FinBudgetItemVO.class);
        
        for (FinBudgetItemVO vo : listVOs) {
            enrichBudgetItemVO(vo);
        }
        
        return ActionResult.success(listVOs);
    }

    /**
     * 获取预算子项详情
     *
     * @param id 预算子项ID
     * @return 预算子项详情
     */
    @GetMapping("/{id}")
    @Operation(summary = "获取预算子项详情")
    public ActionResult<FinBudgetItemVO> info(@PathVariable("id") String id) {
        FinBudgetItemEntity entity = finBudgetItemService.getInfo(id);
        if (entity == null) {
            return ActionResult.fail("预算子项不存在");
        }
        
        FinBudgetItemVO vo = BeanCopierUtils.copy(entity, FinBudgetItemVO.class);
        enrichBudgetItemVO(vo);
        
        return ActionResult.success(vo);
    }

    /**
     * 创建预算子项
     *
     * @param finBudgetItemForm 预算子项表单
     * @return 操作结果
     */
    @PostMapping
    @Operation(summary = "创建预算子项")
    public ActionResult<String> create(@RequestBody @Valid FinBudgetItemForm finBudgetItemForm) {
        finBudgetItemService.create(finBudgetItemForm);
        return ActionResult.success("预算子项创建成功");
    }

    /**
     * 批量创建预算子项
     *
     * @param budgetItemForms 预算子项表单列表
     * @return 操作结果
     */
    @PostMapping("/batch")
    @Operation(summary = "批量创建预算子项")
    public ActionResult<String> batchCreate(@RequestBody @Valid List<FinBudgetItemForm> budgetItemForms) {
        finBudgetItemService.batchCreate(budgetItemForms);
        return ActionResult.success("预算子项批量创建成功");
    }

    /**
     * 更新预算子项
     *
     * @param id 预算子项ID
     * @param finBudgetItemForm 预算子项表单
     * @return 操作结果
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新预算子项")
    public ActionResult<String> update(@PathVariable("id") String id, @RequestBody @Valid FinBudgetItemForm finBudgetItemForm) {
        finBudgetItemService.update(id, finBudgetItemForm);
        return ActionResult.success("预算子项更新成功");
    }

    /**
     * 删除预算子项
     *
     * @param id 预算子项ID
     * @return 操作结果
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除预算子项")
    public ActionResult<String> delete(@PathVariable("id") String id) {
        finBudgetItemService.delete(id);
        return ActionResult.success("预算子项删除成功");
    }

    /**
     * 重新计算预算子项的当前支出
     *
     * @param id 预算子项ID
     * @return 操作结果
     */
    @PutMapping("/{id}/recalculate")
    @Operation(summary = "重新计算预算子项支出")
    public ActionResult<String> recalculateCurrentSpent(@PathVariable("id") String id) {
        finBudgetItemService.recalculateCurrentSpent(id);
        return ActionResult.success("预算子项支出重新计算完成");
    }

    /**
     * 批量重新计算预算的所有子项支出
     *
     * @param budgetId 预算ID
     * @return 操作结果
     */
    @PutMapping("/budget/{budgetId}/recalculate")
    @Operation(summary = "批量重新计算预算子项支出")
    public ActionResult<String> batchRecalculateCurrentSpent(@PathVariable("budgetId") String budgetId) {
        finBudgetItemService.batchUpdateCurrentSpent(budgetId);
        return ActionResult.success("预算所有子项支出重新计算完成");
    }

    /**
     * 获取需要预警的预算项
     *
     * @return 需要预警的预算项列表
     */
    @GetMapping("/alert")
    @Operation(summary = "获取需要预警的预算项")
    public ActionResult<List<FinBudgetItemVO>> getItemsNeedAlert() {
        // TODO: 获取当前用户ID
        String userId = "";
        List<FinBudgetItemEntity> list = finBudgetItemService.getItemsNeedAlert(userId);
        List<FinBudgetItemVO> listVOs = BeanCopierUtils.copyList(list, FinBudgetItemVO.class);
        
        for (FinBudgetItemVO vo : listVOs) {
            enrichBudgetItemVO(vo);
        }
        
        return ActionResult.success(listVOs);
    }

    /**
     * 补充预算子项VO的显示信息
     *
     * @param vo 预算子项VO
     */
    private void enrichBudgetItemVO(FinBudgetItemVO vo) {
        // 预算项类型名称
        if ("CATEGORY".equals(vo.getItemType())) {
            vo.setItemTypeName("分类");
        } else if ("ACCOUNT".equals(vo.getItemType())) {
            vo.setItemTypeName("账户");
        }
        
        // 计算剩余金额
        if (vo.getBudgetedAmount() != null && vo.getCurrentSpent() != null) {
            vo.setRemainingAmount(vo.getBudgetedAmount().subtract(vo.getCurrentSpent()));
        }
        
        // 计算支出百分比
        if (vo.getBudgetedAmount() != null && vo.getCurrentSpent() != null && 
            vo.getBudgetedAmount().compareTo(BigDecimal.ZERO) > 0) {
            vo.setSpentPercentage(vo.getCurrentSpent()
                    .divide(vo.getBudgetedAmount(), 4, BigDecimal.ROUND_HALF_UP)
                    .multiply(new BigDecimal("100")));
        } else {
            vo.setSpentPercentage(BigDecimal.ZERO);
        }
        
        // 判断是否超支
        vo.setIsOverBudget(vo.getCurrentSpent() != null && vo.getBudgetedAmount() != null && 
                vo.getCurrentSpent().compareTo(vo.getBudgetedAmount()) > 0);
        
        // 设置预警状态
        if (vo.getIsOverBudget()) {
            vo.setAlertStatus("EXCEEDED");
        } else if (vo.getSpentPercentage() != null && vo.getAlertThreshold() != null && 
                vo.getSpentPercentage().compareTo(vo.getAlertThreshold()) >= 0) {
            vo.setAlertStatus("WARNING");
        } else {
            vo.setAlertStatus("NORMAL");
        }
    }
}