package com.xinghuo.common.swagger;

import cn.hutool.core.util.RandomUtil;
import com.xinghuo.common.constant.Constants;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import org.springdoc.core.customizers.GlobalOpenApiCustomizer;
import org.springdoc.core.customizers.OperationCustomizer;
import org.springdoc.webmvc.core.SpringWebMvcProvider;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpHeaders;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

/**
 * Swagger配置类
 * <p>
 * 通过注解 @Configuration 指明这是一个配置类，用于配置 Swagger 相关信息。
 * 实现了 WebMvcConfigurer 接口，以便添加资源处理器，用于映射 Swagger UI 页面和静态资源。
 *
 * <AUTHOR>
 * @date 2023-10-05
 */
@Configuration
public class SwaggerConfig implements WebMvcConfigurer {

    /**
     * 通过注解 @Value 从配置文件中读取应用程序名称。
     */
    @Value("${spring.application.name}")
    private String name;

    /**
     * 创建 OpenAPI 对象的 Bean。
     */
    @SuppressWarnings("AlibabaLowerCamelCaseVariableNaming")
    @Bean
    public OpenAPI openAPI() {
        OpenAPI openAPI = new OpenAPI();
        openAPI.info(apiInfo());

        openAPI.schemaRequirement(Constants.AUTHORIZATION, security());
        return openAPI;
    }

    /**
     * 创建全局 OpenAPI 定制器的 Bean。
     * 用于定制全局的 OpenAPI 信息，如标签的排序和路径的扩展。
     */
    @Bean
    public GlobalOpenApiCustomizer orderGlobalOpenApiCustomizer() {
        return openApi -> {
            if (openApi.getTags() != null) {
                //遍历 OpenAPI 配置中的标签。对于每个标签，它添加了一个名为 "x-order" 的自定义扩展，值为介于 0 和 100 之间的随机整数
                openApi.getTags().forEach(tag -> {
                    Map<String, Object> map = new HashMap<>(16);
                    map.put("x-order", RandomUtil.randomInt(0, 100));
                    tag.setExtensions(map);
                });
            }
            if (openApi.getPaths() != null) {
                openApi.addExtension("x-test123", "333");
                openApi.getPaths().addExtension("x-abb", RandomUtil.randomInt(1, 100));
            }
        };
    }

    @Bean
    public OperationCustomizer securityOperationCustomizer() {
        return (operation, handlerMethod) -> operation.security(
                Collections.singletonList(new SecurityRequirement().addList(HttpHeaders.AUTHORIZATION))
        );
    }

    /**
     * 创建 GroupedOpenApi 的 Bean，用于分组 API。
     * 在这个例子中，将应用程序名称作为分组，匹配所有路径。
     */
//    @Bean
//    public GroupedOpenApi userApi(){
//        String[] paths = { "/**" };
//        return GroupedOpenApi.builder().group(name)
//                .pathsToMatch(paths)
//                .addOperationCustomizer((operation, handlerMethod) -> operation.security(
//                        Collections.singletonList(new SecurityRequirement().addList(HttpHeaders.AUTHORIZATION))
//                ))
//                .build();
//    }

    /**
     * 设置文档信息的辅助方法
     *
     * @return
     */
    private Info apiInfo() {
        return new Info()
                .title("接口文档")
                //描述
                .description("XACE接口文档")
                .version(Constants.SWAGGER_VERSION);
    }

    /**
     * 创建 SecurityScheme 的 Bean，用于设置 API 安全。
     */
    private SecurityScheme security() {
        SecurityScheme securityScheme = new SecurityScheme();
        securityScheme.setType(SecurityScheme.Type.APIKEY);
        securityScheme.setName(Constants.AUTHORIZATION);
        securityScheme.setIn(SecurityScheme.In.HEADER);
        return securityScheme;
    }

    /**
     * 创建 SpringWebMvcProvider 的 Bean。
     * 在这个例子中，使用自定义的 MySpringWebMvcProvider。
     */
    @Bean
    @Lazy(false)
    public SpringWebMvcProvider springWebProvider() {
        return new MySpringWebMvcProvider();
    }


    /**
     * 添加资源处理器，映射 Swagger UI 页面和静态资源。
     */
    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        registry.addResourceHandler("doc.html").addResourceLocations("classpath:/META-INF/resources/");
        registry.addResourceHandler("/webjars/**").addResourceLocations("classpath:/META-INF/resources/webjars/");
    }
}
