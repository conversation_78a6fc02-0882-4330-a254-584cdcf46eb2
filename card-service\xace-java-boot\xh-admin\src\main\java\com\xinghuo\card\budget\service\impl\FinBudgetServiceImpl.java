package com.xinghuo.card.budget.service.impl;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xinghuo.card.budget.dao.FinBudgetMapper;
import com.xinghuo.card.budget.dao.FinBudgetItemMapper;
import com.xinghuo.card.budget.entity.FinBudgetEntity;
import com.xinghuo.card.budget.entity.FinBudgetItemEntity;
import com.xinghuo.card.budget.model.budget.FinBudgetForm;
import com.xinghuo.card.budget.model.budget.FinBudgetPagination;
import com.xinghuo.card.budget.model.budget.BudgetDashboardVO;
import com.xinghuo.card.budget.model.budget.BudgetReportVO;
import com.xinghuo.card.budget.service.FinBudgetService;
import com.xinghuo.common.base.service.BaseServiceImpl;
import com.xinghuo.common.base.vo.PageListVO;
import com.xinghuo.common.base.vo.PaginationVO;
import com.xinghuo.common.util.UserProvider;
import com.xinghuo.common.util.core.BeanCopierUtils;
import com.xinghuo.common.util.core.RandomUtil;
import lombok.extern.slf4j.Slf4j;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;

/**
 * 预算管理Service实现
 *
 * <AUTHOR> Assistant
 * @version V1.0.0
 * @date 2024-12-29
 */
@Service
@Slf4j
public class FinBudgetServiceImpl extends BaseServiceImpl<FinBudgetMapper, FinBudgetEntity> implements FinBudgetService {

    @Resource
    private UserProvider userProvider;

    @Resource
    private FinBudgetItemMapper finBudgetItemMapper;

    @Override
    public PageListVO<FinBudgetEntity> getList(FinBudgetPagination finBudgetPagination) {
        String userId = userProvider.get().getUserId();
        finBudgetPagination.setUserId(userId);
        
        Page<FinBudgetEntity> page = new Page<>(finBudgetPagination.getCurrentPage(), finBudgetPagination.getPageSize());
        List<FinBudgetEntity> list = this.baseMapper.getList(page, finBudgetPagination);
        
        PaginationVO paginationVO = BeanCopierUtils.copy(finBudgetPagination, PaginationVO.class);
        return new PageListVO<>(list, paginationVO);
    }

    @Override
    public FinBudgetEntity getInfo(String id) {
        QueryWrapper<FinBudgetEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(FinBudgetEntity::getId, id);
        queryWrapper.lambda().eq(FinBudgetEntity::getUserId, userProvider.get().getUserId());
        return this.getOne(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(FinBudgetForm finBudgetForm) {
        FinBudgetEntity entity = BeanCopierUtils.copy(finBudgetForm, FinBudgetEntity.class);
        entity.setId(RandomUtil.snowId());
        entity.setUserId(userProvider.get().getUserId());
        
        // 设置默认值
        if (StrUtil.isBlank(entity.getStatus())) {
            entity.setStatus("ACTIVE");
        }
        if (ObjectUtil.isNull(entity.getTotalBudgetAmount())) {
            entity.setTotalBudgetAmount(BigDecimal.ZERO);
        }
        
        
        
        this.save(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(String id, FinBudgetForm finBudgetForm) {
        FinBudgetEntity entity = this.getInfo(id);
        if (ObjectUtil.isNull(entity)) {
            throw new RuntimeException("预算不存在");
        }

        entity =  BeanCopierUtils.copy(finBudgetForm, FinBudgetEntity.class);
        entity.setLastUpdatedBy(userProvider.get().getUserId());
        entity.setLastUpdatedAt(new Date());
        
        this.updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(String id) {
        FinBudgetEntity entity = this.getInfo(id);
        if (ObjectUtil.isNull(entity)) {
            throw new RuntimeException("预算不存在");
        }
        
        // 删除预算子项
        QueryWrapper<FinBudgetItemEntity> itemQueryWrapper = new QueryWrapper<>();
        itemQueryWrapper.lambda().eq(FinBudgetItemEntity::getBudgetId, id);
        finBudgetItemMapper.delete(itemQueryWrapper);
        
        // 删除预算
        this.removeById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void archive(String id) {
        FinBudgetEntity entity = this.getInfo(id);
        if (ObjectUtil.isNull(entity)) {
            throw new RuntimeException("预算不存在");
        }
        
        entity.setStatus("ARCHIVED");

        
        this.updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void activate(String id) {
        FinBudgetEntity entity = this.getInfo(id);
        if (ObjectUtil.isNull(entity)) {
            throw new RuntimeException("预算不存在");
        }
        
        entity.setStatus("ACTIVE");

        
        this.updateById(entity);
    }

    @Override
    public List<FinBudgetEntity> getActiveBudgetsByUserId(String userId) {
        return this.baseMapper.getBudgetsByUserIdAndStatus(userId, "ACTIVE");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void recalculateBudgetSpent(String id) {
        // 重新计算预算的支出金额
        finBudgetItemMapper.updateCurrentSpentByBudgetId(id);
        
        // 计算总预算金额和总支出金额
        QueryWrapper<FinBudgetItemEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(FinBudgetItemEntity::getBudgetId, id);
        List<FinBudgetItemEntity> items = finBudgetItemMapper.selectList(queryWrapper);
        
        BigDecimal totalBudget = items.stream()
                .map(FinBudgetItemEntity::getBudgetedAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        
        FinBudgetEntity budget = this.getById(id);
        budget.setTotalBudgetAmount(totalBudget);
        
        
        this.updateById(budget);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String copyBudget(String id, String newName) {
        FinBudgetEntity sourceBudget = this.getInfo(id);
        if (ObjectUtil.isNull(sourceBudget)) {
            throw new RuntimeException("源预算不存在");
        }
        
        // 复制预算主记录
        FinBudgetEntity newBudget = BeanCopierUtils.copy(sourceBudget, FinBudgetEntity.class);
        String newBudgetId = RandomUtil.snowId();
        newBudget.setId(newBudgetId);
        newBudget.setName(newName);
        newBudget.setStatus("ACTIVE");
        newBudget.setCreateBy(userProvider.get().getUserId());

        
        this.save(newBudget);
        
        // 复制预算子项
        QueryWrapper<FinBudgetItemEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(FinBudgetItemEntity::getBudgetId, id);
        List<FinBudgetItemEntity> sourceItems = finBudgetItemMapper.selectList(queryWrapper);
        
        for (FinBudgetItemEntity sourceItem : sourceItems) {
            FinBudgetItemEntity newItem = BeanCopierUtils.copy(sourceItem, FinBudgetItemEntity.class);
            newItem.setId(RandomUtil.snowId());
            newItem.setBudgetId(newBudgetId);
            newItem.setCurrentSpent(BigDecimal.ZERO);
            newItem.setIsAlerted(false);

            
            finBudgetItemMapper.insert(newItem);
        }
        
        return newBudgetId;
    }

    @Override
    public BudgetDashboardVO getBudgetDashboard(String userId) {
        BudgetDashboardVO dashboard = new BudgetDashboardVO();

        // 获取用户当前激活的预算
        List<FinBudgetEntity> activeBudgets = getActiveBudgetsByUserId(userId);
        dashboard.setActiveBudgetCount(activeBudgets.size());

        // 初始化统计数据
        BigDecimal totalBudget = BigDecimal.ZERO;
        BigDecimal totalSpent = BigDecimal.ZERO;
        int overBudgetCount = 0;
        int alertCount = 0;

        List<BudgetDashboardVO.BudgetExecutionItemVO> budgetItems = new ArrayList<>();

        for (FinBudgetEntity budget : activeBudgets) {
            // 获取预算子项
            QueryWrapper<FinBudgetItemEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(FinBudgetItemEntity::getBudgetId, budget.getId());
            List<FinBudgetItemEntity> items = finBudgetItemMapper.selectList(queryWrapper);

            for (FinBudgetItemEntity item : items) {
                BudgetDashboardVO.BudgetExecutionItemVO itemVO = new BudgetDashboardVO.BudgetExecutionItemVO();
                itemVO.setBudgetId(budget.getId());
                itemVO.setBudgetName(budget.getName());
                itemVO.setItemId(item.getId());
                itemVO.setItemType(item.getItemType());
                itemVO.setItemTypeName("CATEGORY".equals(item.getItemType()) ? "分类" : "账户");
                itemVO.setTargetId(item.getTargetId());
                itemVO.setTargetName(item.getTargetName());
                itemVO.setBudgetedAmount(item.getBudgetedAmount());
                itemVO.setCurrentSpent(item.getCurrentSpent());

                // 计算剩余金额和百分比
                BigDecimal remaining = item.getBudgetedAmount().subtract(item.getCurrentSpent());
                itemVO.setRemainingAmount(remaining);

                BigDecimal percentage = BigDecimal.ZERO;
                if (item.getBudgetedAmount().compareTo(BigDecimal.ZERO) > 0) {
                    percentage = item.getCurrentSpent().divide(item.getBudgetedAmount(), 4, BigDecimal.ROUND_HALF_UP)
                            .multiply(new BigDecimal("100"));
                }
                itemVO.setSpentPercentage(percentage);

                itemVO.setAlertThreshold(item.getAlertThreshold());
                itemVO.setIsAlerted(item.getIsAlerted());

                // 判断状态
                boolean isOverBudget = item.getCurrentSpent().compareTo(item.getBudgetedAmount()) > 0;
                itemVO.setIsOverBudget(isOverBudget);

                if (isOverBudget) {
                    itemVO.setStatus("超支");
                    itemVO.setStatusColor("red");
                    overBudgetCount++;
                } else if (percentage.compareTo(item.getAlertThreshold()) >= 0) {
                    itemVO.setStatus("预警");
                    itemVO.setStatusColor("yellow");
                    alertCount++;
                } else {
                    itemVO.setStatus("正常");
                    itemVO.setStatusColor("green");
                }

                budgetItems.add(itemVO);

                // 累计统计
                totalBudget = totalBudget.add(item.getBudgetedAmount());
                totalSpent = totalSpent.add(item.getCurrentSpent());
            }
        }

        dashboard.setTotalBudgetAmount(totalBudget);
        dashboard.setTotalSpentAmount(totalSpent);
        dashboard.setTotalRemainingAmount(totalBudget.subtract(totalSpent));

        if (totalBudget.compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal totalPercentage = totalSpent.divide(totalBudget, 4, BigDecimal.ROUND_HALF_UP)
                    .multiply(new BigDecimal("100"));
            dashboard.setTotalSpentPercentage(totalPercentage);
        } else {
            dashboard.setTotalSpentPercentage(BigDecimal.ZERO);
        }

        dashboard.setOverBudgetItemCount(overBudgetCount);
        dashboard.setAlertItemCount(alertCount);
        dashboard.setBudgetItems(budgetItems);

        return dashboard;
    }

    @Override
    public BudgetReportVO getBudgetReport(String budgetId) {
        FinBudgetEntity budget = this.getById(budgetId);
        if (ObjectUtil.isNull(budget)) {
            throw new RuntimeException("预算不存在");
        }

        BudgetReportVO report =   BeanCopierUtils.copy(budget, BudgetReportVO.class);

        // 设置周期类型名称
        report.setPeriodTypeName("MONTHLY".equals(budget.getPeriodType()) ? "月度" : "年度");
        report.setStatusName("ACTIVE".equals(budget.getStatus()) ? "激活" : "归档");

        // 获取预算子项
        QueryWrapper<FinBudgetItemEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(FinBudgetItemEntity::getBudgetId, budgetId);
        List<FinBudgetItemEntity> items = finBudgetItemMapper.selectList(queryWrapper);

        BigDecimal totalBudget = BigDecimal.ZERO;
        BigDecimal totalSpent = BigDecimal.ZERO;
        List<BudgetReportVO.BudgetItemReportVO> itemReports = new ArrayList<>();
        List<BudgetReportVO.BudgetItemReportVO> overBudgetItems = new ArrayList<>();

        for (FinBudgetItemEntity item : items) {
            BudgetReportVO.BudgetItemReportVO itemReport = new BudgetReportVO.BudgetItemReportVO();
            itemReport.setItemId(item.getId());
            itemReport.setItemType(item.getItemType());
            itemReport.setItemTypeName("CATEGORY".equals(item.getItemType()) ? "分类" : "账户");
            itemReport.setTargetId(item.getTargetId());
            itemReport.setTargetName(item.getTargetName());
            itemReport.setBudgetedAmount(item.getBudgetedAmount());
            itemReport.setActualSpent(item.getCurrentSpent());

            // 计算差额
            BigDecimal difference = item.getBudgetedAmount().subtract(item.getCurrentSpent());
            itemReport.setDifference(difference);

            // 计算支出百分比
            BigDecimal percentage = BigDecimal.ZERO;
            if (item.getBudgetedAmount().compareTo(BigDecimal.ZERO) > 0) {
                percentage = item.getCurrentSpent().divide(item.getBudgetedAmount(), 4, BigDecimal.ROUND_HALF_UP)
                        .multiply(new BigDecimal("100"));
            }
            itemReport.setSpentPercentage(percentage);

            // 判断是否超支
            boolean isOverBudget = item.getCurrentSpent().compareTo(item.getBudgetedAmount()) > 0;
            itemReport.setIsOverBudget(isOverBudget);

            if (isOverBudget) {
                BigDecimal overAmount = item.getCurrentSpent().subtract(item.getBudgetedAmount());
                itemReport.setOverBudgetAmount(overAmount);
                overBudgetItems.add(itemReport);
            }

            itemReport.setAlertThreshold(item.getAlertThreshold());
            itemReport.setIsAlerted(item.getIsAlerted());

            // 设置执行状态
            if (isOverBudget) {
                itemReport.setExecutionStatus("超支");
            } else if (percentage.compareTo(item.getAlertThreshold()) >= 0) {
                itemReport.setExecutionStatus("预警");
            } else if (percentage.compareTo(new BigDecimal("70")) >= 0) {
                itemReport.setExecutionStatus("良好");
            } else {
                itemReport.setExecutionStatus("优秀");
            }

            // TODO: 查询流水记录数量
            itemReport.setFlowRecordCount(0);

            itemReports.add(itemReport);

            totalBudget = totalBudget.add(item.getBudgetedAmount());
            totalSpent = totalSpent.add(item.getCurrentSpent());
        }

        report.setTotalBudgetAmount(totalBudget);
        report.setTotalSpentAmount(totalSpent);
        report.setTotalRemainingAmount(totalBudget.subtract(totalSpent));

        if (totalBudget.compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal totalPercentage = totalSpent.divide(totalBudget, 4, BigDecimal.ROUND_HALF_UP)
                    .multiply(new BigDecimal("100"));
            report.setTotalSpentPercentage(totalPercentage);
        } else {
            report.setTotalSpentPercentage(BigDecimal.ZERO);
        }

        boolean isOverBudget = totalSpent.compareTo(totalBudget) > 0;
        report.setIsOverBudget(isOverBudget);
        if (isOverBudget) {
            report.setOverBudgetAmount(totalSpent.subtract(totalBudget));
        }

        report.setItemReports(itemReports);
        report.setOverBudgetItems(overBudgetItems);

        return report;
    }

    @Override
    public List<Object> getBudgetItemFlowDetails(String budgetItemId) {
        // TODO: 实现预算项的详细流水记录查询
        // 需要根据预算项的类型（分类或账户）和时间范围查询对应的流水记录
        return new ArrayList<>();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void checkAndSendBudgetAlerts(String userId) {
        // 获取需要预警的预算项
        List<FinBudgetItemEntity> alertItems = finBudgetItemMapper.getItemsNeedAlert(userId);

        for (FinBudgetItemEntity item : alertItems) {
            // 标记为已预警
            item.setIsAlerted(true);

            finBudgetItemMapper.updateById(item);

            // TODO: 发送预警通知
            log.info("预算预警：用户{}的预算项{}已达到预警阈值", userId, item.getTargetName());
        }
    }

    @Override
    public Map<String, Object> getBudgetStatistics(String userId) {
        Assert.notBlank(userId, "用户ID不能为空");

        Map<String, Object> statistics = new HashMap<>();

        // 查询用户的预算数量
        QueryWrapper<FinBudgetEntity> budgetWrapper = new QueryWrapper<>();
        budgetWrapper.lambda().eq(FinBudgetEntity::getUserId, userId);
        long budgetCount = this.count(budgetWrapper);
        statistics.put("budgetCount", budgetCount);

        // 查询激活的预算数量
        QueryWrapper<FinBudgetEntity> activeWrapper = new QueryWrapper<>();
        activeWrapper.lambda()
                .eq(FinBudgetEntity::getUserId, userId)
                .eq(FinBudgetEntity::getStatus, "ACTIVE");
        long activeBudgetCount = this.count(activeWrapper);
        statistics.put("activeBudgetCount", activeBudgetCount);

        // 查询预算项统计
        QueryWrapper<FinBudgetItemEntity> itemWrapper = new QueryWrapper<>();
        itemWrapper.lambda().eq(FinBudgetItemEntity::getUserId, userId);
        long itemCount = finBudgetItemMapper.selectCount(itemWrapper);
        statistics.put("budgetItemCount", itemCount);

        // 计算总预算金额
        List<FinBudgetEntity> budgets = this.list(activeWrapper);
        BigDecimal totalBudgetAmount = budgets.stream()
                .map(budget -> budget.getTotalAmount() != null ? budget.getTotalAmount() : BigDecimal.ZERO)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        statistics.put("totalBudgetAmount", totalBudgetAmount);

        log.info("获取用户预算统计信息，用户ID: {}, 预算数量: {}, 激活数量: {}", userId, budgetCount, activeBudgetCount);

        return statistics;
    }

    @Override
    public boolean validateBudgetConfig(FinBudgetEntity entity) {
        Assert.notNull(entity, "预算实体不能为空");

        // 验证预算名称
        if (StrXhUtil.isEmpty(entity.getBudgetName())) {
            log.warn("预算名称不能为空");
            return false;
        }

        // 验证预算周期
        if (StrXhUtil.isEmpty(entity.getPeriodType())) {
            log.warn("预算周期类型不能为空");
            return false;
        }

        // 验证预算金额
        if (entity.getTotalAmount() == null || entity.getTotalAmount().compareTo(BigDecimal.ZERO) <= 0) {
            log.warn("预算总金额必须大于0");
            return false;
        }

        // 验证开始和结束日期
        if (entity.getStartDate() == null || entity.getEndDate() == null) {
            log.warn("预算开始和结束日期不能为空");
            return false;
        }

        if (entity.getStartDate().after(entity.getEndDate())) {
            log.warn("预算开始日期不能晚于结束日期");
            return false;
        }

        // 验证预算名称唯一性
        if (!checkBudgetNameUnique(entity.getBudgetName(), entity.getUserId(), entity.getId())) {
            log.warn("预算名称已存在：{}", entity.getBudgetName());
            return false;
        }

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchUpdateStatus(List<String> budgetIds, String status) {
        Assert.notEmpty(budgetIds, "预算ID列表不能为空");
        Assert.notBlank(status, "状态不能为空");

        // 验证状态值
        if (!Arrays.asList("ACTIVE", "INACTIVE", "ARCHIVED").contains(status)) {
            log.warn("无效的预算状态：{}", status);
            return 0;
        }

        UpdateWrapper<FinBudgetEntity> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda()
                .in(FinBudgetEntity::getId, budgetIds)
                .set(FinBudgetEntity::getStatus, status);

        int result = this.getBaseMapper().update(null, updateWrapper);

        log.info("批量更新预算状态完成，更新数量: {}, 新状态: {}", result, status);

        return result;
    }

    @Override
    public List<FinBudgetEntity> getListByPeriodType(String userId, String periodType) {
        Assert.notBlank(userId, "用户ID不能为空");
        Assert.notBlank(periodType, "周期类型不能为空");

        QueryWrapper<FinBudgetEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(FinBudgetEntity::getUserId, userId)
                .eq(FinBudgetEntity::getPeriodType, periodType)
                .orderByDesc(FinBudgetEntity::getCreateTime);

        List<FinBudgetEntity> result = this.list(queryWrapper);

        log.info("根据周期类型查询预算列表，用户ID: {}, 周期类型: {}, 结果数量: {}", userId, periodType, result.size());

        return result;
    }

    @Override
    public Map<String, Object> getBudgetProgress(String budgetId) {
        Assert.notBlank(budgetId, "预算ID不能为空");

        FinBudgetEntity budget = this.getById(budgetId);
        if (budget == null) {
            log.warn("预算不存在，ID: {}", budgetId);
            return new HashMap<>();
        }

        Map<String, Object> progress = new HashMap<>();

        // 查询预算项
        QueryWrapper<FinBudgetItemEntity> itemWrapper = new QueryWrapper<>();
        itemWrapper.lambda().eq(FinBudgetItemEntity::getBudgetId, budgetId);
        List<FinBudgetItemEntity> items = finBudgetItemMapper.selectList(itemWrapper);

        // 计算总预算和已使用金额
        BigDecimal totalBudget = BigDecimal.ZERO;
        BigDecimal totalUsed = BigDecimal.ZERO;

        for (FinBudgetItemEntity item : items) {
            if (item.getBudgetedAmount() != null) {
                totalBudget = totalBudget.add(item.getBudgetedAmount());
            }
            if (item.getActualAmount() != null) {
                totalUsed = totalUsed.add(item.getActualAmount());
            }
        }

        // 计算进度百分比
        BigDecimal progressPercentage = BigDecimal.ZERO;
        if (totalBudget.compareTo(BigDecimal.ZERO) > 0) {
            progressPercentage = totalUsed.divide(totalBudget, 4, BigDecimal.ROUND_HALF_UP)
                    .multiply(new BigDecimal("100"));
        }

        progress.put("budgetId", budgetId);
        progress.put("budgetName", budget.getBudgetName());
        progress.put("totalBudget", totalBudget);
        progress.put("totalUsed", totalUsed);
        progress.put("remaining", totalBudget.subtract(totalUsed));
        progress.put("progressPercentage", progressPercentage);
        progress.put("itemCount", items.size());

        // 判断执行状态
        String executionStatus = "正常";
        if (progressPercentage.compareTo(new BigDecimal("80")) > 0) {
            executionStatus = "接近预算";
        }
        if (progressPercentage.compareTo(new BigDecimal("100")) >= 0) {
            executionStatus = "超出预算";
        }
        progress.put("executionStatus", executionStatus);

        return progress;
    }

    @Override
    public void syncBudgetTotalAmount(String budgetId) {

    }
}