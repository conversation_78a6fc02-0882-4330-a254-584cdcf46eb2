package com.xinghuo.card.budget.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xinghuo.card.budget.dao.FinBudgetMapper;
import com.xinghuo.card.budget.dao.FinBudgetItemMapper;
import com.xinghuo.card.budget.entity.FinBudgetEntity;
import com.xinghuo.card.budget.entity.FinBudgetItemEntity;
import com.xinghuo.card.budget.model.budget.FinBudgetForm;
import com.xinghuo.card.budget.model.budget.FinBudgetPagination;
import com.xinghuo.card.budget.service.FinBudgetService;
import com.xinghuo.common.base.vo.PageListVO;
import com.xinghuo.common.base.vo.PaginationVO;
import com.xinghuo.common.util.UserProvider;
import com.xinghuo.common.util.core.BeanCopierUtils;
import com.xinghuo.common.util.core.RandomUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 预算管理Service实现
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2024-06-28
 */
@Service
@Slf4j
public class FinBudgetServiceImpl extends ServiceImpl<FinBudgetMapper, FinBudgetEntity> implements FinBudgetService {

    @Autowired
    private UserProvider userProvider;

    @Autowired
    private FinBudgetItemMapper finBudgetItemMapper;

    @Override
    public PageListVO<FinBudgetEntity> getList(FinBudgetPagination finBudgetPagination) {
        String userId = userProvider.get().getUserId();
        finBudgetPagination.setUserId(userId);
        
        Page<FinBudgetEntity> page = new Page<>(finBudgetPagination.getCurrentPage(), finBudgetPagination.getPageSize());
        List<FinBudgetEntity> list = this.baseMapper.getList(page, finBudgetPagination);
        
        PaginationVO paginationVO = BeanCopierUtils.copy(finBudgetPagination, PaginationVO.class);
        return new PageListVO<>(list, paginationVO);
    }

    @Override
    public FinBudgetEntity getInfo(String id) {
        QueryWrapper<FinBudgetEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(FinBudgetEntity::getId, id);
        queryWrapper.lambda().eq(FinBudgetEntity::getUserId, userProvider.get().getUserId());
        return this.getOne(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(FinBudgetForm finBudgetForm) {
        FinBudgetEntity entity = BeanCopierUtils.copy(finBudgetForm, FinBudgetEntity.class);
        entity.setId(RandomUtil.uuId());
        entity.setUserId(userProvider.get().getUserId());
        
        // 设置默认值
        if (StrUtil.isBlank(entity.getStatus())) {
            entity.setStatus("ACTIVE");
        }
        if (ObjectUtil.isNull(entity.getTotalBudgetAmount())) {
            entity.setTotalBudgetAmount(BigDecimal.ZERO);
        }
        
        entity.setCreateBy(userProvider.get().getUserId());
        entity.setCreateTime(new Date());
        entity.setUpdateBy(userProvider.get().getUserId());
        entity.setUpdateTime(new Date());
        
        this.save(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(String id, FinBudgetForm finBudgetForm) {
        FinBudgetEntity entity = this.getInfo(id);
        if (ObjectUtil.isNull(entity)) {
            throw new RuntimeException("预算不存在");
        }
        
        BeanCopierUtils.copy(finBudgetForm, entity);
        entity.setUpdateBy(userProvider.get().getUserId());
        entity.setUpdateTime(new Date());
        
        this.updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(String id) {
        FinBudgetEntity entity = this.getInfo(id);
        if (ObjectUtil.isNull(entity)) {
            throw new RuntimeException("预算不存在");
        }
        
        // 删除预算子项
        QueryWrapper<FinBudgetItemEntity> itemQueryWrapper = new QueryWrapper<>();
        itemQueryWrapper.lambda().eq(FinBudgetItemEntity::getBudgetId, id);
        finBudgetItemMapper.delete(itemQueryWrapper);
        
        // 删除预算
        this.removeById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void archive(String id) {
        FinBudgetEntity entity = this.getInfo(id);
        if (ObjectUtil.isNull(entity)) {
            throw new RuntimeException("预算不存在");
        }
        
        entity.setStatus("ARCHIVED");
        entity.setUpdateBy(userProvider.get().getUserId());
        entity.setUpdateTime(new Date());
        
        this.updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void activate(String id) {
        FinBudgetEntity entity = this.getInfo(id);
        if (ObjectUtil.isNull(entity)) {
            throw new RuntimeException("预算不存在");
        }
        
        entity.setStatus("ACTIVE");
        entity.setUpdateBy(userProvider.get().getUserId());
        entity.setUpdateTime(new Date());
        
        this.updateById(entity);
    }

    @Override
    public List<FinBudgetEntity> getActiveBudgetsByUserId(String userId) {
        return this.baseMapper.getBudgetsByUserIdAndStatus(userId, "ACTIVE");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void recalculateBudgetSpent(String id) {
        // 重新计算预算的支出金额
        finBudgetItemMapper.updateCurrentSpentByBudgetId(id);
        
        // 计算总预算金额和总支出金额
        QueryWrapper<FinBudgetItemEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(FinBudgetItemEntity::getBudgetId, id);
        List<FinBudgetItemEntity> items = finBudgetItemMapper.selectList(queryWrapper);
        
        BigDecimal totalBudget = items.stream()
                .map(FinBudgetItemEntity::getBudgetedAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        
        FinBudgetEntity budget = this.getById(id);
        budget.setTotalBudgetAmount(totalBudget);
        budget.setUpdateBy(userProvider.get().getUserId());
        budget.setUpdateTime(new Date());
        
        this.updateById(budget);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String copyBudget(String id, String newName) {
        FinBudgetEntity sourceBudget = this.getInfo(id);
        if (ObjectUtil.isNull(sourceBudget)) {
            throw new RuntimeException("源预算不存在");
        }
        
        // 复制预算主记录
        FinBudgetEntity newBudget = BeanCopierUtils.copy(sourceBudget, FinBudgetEntity.class);
        String newBudgetId = RandomUtil.uuId();
        newBudget.setId(newBudgetId);
        newBudget.setName(newName);
        newBudget.setStatus("ACTIVE");
        newBudget.setCreateBy(userProvider.get().getUserId());
        newBudget.setCreateTime(new Date());
        newBudget.setUpdateBy(userProvider.get().getUserId());
        newBudget.setUpdateTime(new Date());
        
        this.save(newBudget);
        
        // 复制预算子项
        QueryWrapper<FinBudgetItemEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(FinBudgetItemEntity::getBudgetId, id);
        List<FinBudgetItemEntity> sourceItems = finBudgetItemMapper.selectList(queryWrapper);
        
        for (FinBudgetItemEntity sourceItem : sourceItems) {
            FinBudgetItemEntity newItem = BeanCopierUtils.copy(sourceItem, FinBudgetItemEntity.class);
            newItem.setId(RandomUtil.uuId());
            newItem.setBudgetId(newBudgetId);
            newItem.setCurrentSpent(BigDecimal.ZERO);
            newItem.setIsAlerted(false);
            newItem.setCreateBy(userProvider.get().getUserId());
            newItem.setCreateTime(new Date());
            newItem.setUpdateBy(userProvider.get().getUserId());
            newItem.setUpdateTime(new Date());
            
            finBudgetItemMapper.insert(newItem);
        }
        
        return newBudgetId;
    }
}