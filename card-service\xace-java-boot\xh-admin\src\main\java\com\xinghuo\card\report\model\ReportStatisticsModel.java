package com.xinghuo.card.report.model;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 报表统计结果模型
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2024-01-01
 */
@Data
public class ReportStatisticsModel {

    /**
     * 统计概要
     */
    private StatisticsSummary summary;

    /**
     * 收支统计列表
     */
    private List<IncomeExpenseItem> incomeExpenseList;

    /**
     * 分类统计列表
     */
    private List<CategoryStatItem> categoryStatList;

    /**
     * 趋势分析数据
     */
    private List<TrendAnalysisItem> trendAnalysisList;

    /**
     * 卡片统计列表
     */
    private List<CardStatItem> cardStatList;

    /**
     * 明细数据列表
     */
    private List<FlowDetailItem> detailList;

    /**
     * 统计概要
     */
    @Data
    public static class StatisticsSummary {
        /**
         * 总收入
         */
        private BigDecimal totalIncome;

        /**
         * 总支出
         */
        private BigDecimal totalExpense;

        /**
         * 净收入（收入-支出）
         */
        private BigDecimal netIncome;

        /**
         * 交易笔数
         */
        private Integer transactionCount;

        /**
         * 平均单笔金额
         */
        private BigDecimal averageAmount;

        /**
         * 最大单笔金额
         */
        private BigDecimal maxAmount;

        /**
         * 最小单笔金额
         */
        private BigDecimal minAmount;

        /**
         * 统计时间范围
         */
        private String dateRange;

        /**
         * 涉及账户数量
         */
        private Integer accountCount;

        /**
         * 涉及分类数量
         */
        private Integer categoryCount;
    }

    /**
     * 收支统计项
     */
    @Data
    public static class IncomeExpenseItem {
        /**
         * 日期（按日/月/年统计）
         */
        private String date;

        /**
         * 收入金额
         */
        private BigDecimal incomeAmount;

        /**
         * 支出金额
         */
        private BigDecimal expenseAmount;

        /**
         * 净收入
         */
        private BigDecimal netAmount;

        /**
         * 收入笔数
         */
        private Integer incomeCount;

        /**
         * 支出笔数
         */
        private Integer expenseCount;
    }

    /**
     * 分类统计项
     */
    @Data
    public static class CategoryStatItem {
        /**
         * 分类ID
         */
        private String categoryId;

        /**
         * 分类名称
         */
        private String categoryName;

        /**
         * 父分类名称
         */
        private String parentCategoryName;

        /**
         * 交易类型：1-收入，2-支出
         */
        private Integer transType;

        /**
         * 总金额
         */
        private BigDecimal totalAmount;

        /**
         * 交易笔数
         */
        private Integer transactionCount;

        /**
         * 平均金额
         */
        private BigDecimal averageAmount;

        /**
         * 占比（百分比）
         */
        private BigDecimal percentage;

        /**
         * 最大单笔
         */
        private BigDecimal maxAmount;

        /**
         * 最小单笔
         */
        private BigDecimal minAmount;
    }

    /**
     * 趋势分析项
     */
    @Data
    public static class TrendAnalysisItem {
        /**
         * 时间点
         */
        private String timePoint;

        /**
         * 累计收入
         */
        private BigDecimal cumulativeIncome;

        /**
         * 累计支出
         */
        private BigDecimal cumulativeExpense;

        /**
         * 累计净收入
         */
        private BigDecimal cumulativeNet;

        /**
         * 当期收入
         */
        private BigDecimal currentIncome;

        /**
         * 当期支出
         */
        private BigDecimal currentExpense;

        /**
         * 当期净收入
         */
        private BigDecimal currentNet;

        /**
         * 收入环比增长率
         */
        private BigDecimal incomeGrowthRate;

        /**
         * 支出环比增长率
         */
        private BigDecimal expenseGrowthRate;
    }

    /**
     * 卡片统计项
     */
    @Data
    public static class CardStatItem {
        /**
         * 账户ID
         */
        private String accId;

        /**
         * 账户名称
         */
        private String accName;

        /**
         * 账户类型
         */
        private Integer accType;

        /**
         * 收入金额
         */
        private BigDecimal incomeAmount;

        /**
         * 支出金额
         */
        private BigDecimal expenseAmount;

        /**
         * 净收入
         */
        private BigDecimal netAmount;

        /**
         * 交易笔数
         */
        private Integer transactionCount;

        /**
         * 当前余额
         */
        private BigDecimal currentBalance;

        /**
         * 最后交易时间
         */
        private Date lastTransactionTime;
    }

    /**
     * 流水明细项
     */
    @Data
    public static class FlowDetailItem {
        /**
         * 流水ID
         */
        private String flowId;

        /**
         * 交易日期
         */
        private Date flowDate;

        /**
         * 账户名称
         */
        private String accName;

        /**
         * 分类名称
         */
        private String categoryName;

        /**
         * 交易类型：1-收入，2-支出，3-转账
         */
        private Integer transType;

        /**
         * 金额
         */
        private BigDecimal amount;

        /**
         * 余额
         */
        private BigDecimal balance;

        /**
         * 备注
         */
        private String note;

        /**
         * 转入账户（转账时）
         */
        private String inAccName;

        /**
         * 转出账户（转账时）
         */
        private String outAccName;
    }
}
