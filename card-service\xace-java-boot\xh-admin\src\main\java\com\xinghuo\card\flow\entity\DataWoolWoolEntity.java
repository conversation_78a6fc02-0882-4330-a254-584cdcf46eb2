package com.xinghuo.card.flow.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;


/**
 * 羊毛记录表
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2022-12-12
 */
@Data
@TableName("data_wool_wool")
public class DataWoolWoolEntity {

    /**
     *
     */
    @TableId("ID")
    private String id;


    /**
     * 类型
     */
    @TableField("TYPE")
    private Integer type;


    /**
     * 来源ID
     */
    @TableField("SRC_ACC_ID")
    private String srcAccId;


    /**
     *
     */
    @TableField("SRC_POINT")
    private BigDecimal srcPoint;


    /**
     * 折算金额
     */
    @TableField("AMOUNT")
    private BigDecimal amount;


    /**
     * 活动名称
     */
    @TableField("ACITIVITY")
    private String acitivity;


    /**
     * 物品名称
     */
    @TableField("GOODS")
    private String goods;


    /**
     * 入账日期
     */
    @TableField("IN_DATE")
    private Date inDate;


    /**
     * 入账账户
     */
    @TableField("IN_ACC_ID")
    private String inAccId;


    /**
     * 入账金额
     */
    @TableField("FLOW_AMOUNT")
    private BigDecimal flowAmount;


    /**
     * 备注
     */
    @TableField("NOTE")
    private String note;


    /**
     * 创建人
     */
    @TableField("CREATE_BY")
    private String createBy;


    /**
     * 创建时间
     */
    @TableField("CREATE_TIME")
    private Date createTime;


    /**
     * 最后修改人
     */
    @TableField("UPDATE_BY")
    private String updateBy;


    /**
     * 最后修改时间
     */
    @TableField("UPDATE_TIME")
    private Date updateTime;

}
