package com.xinghuo.card.flow.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;


/**
 * 充值记录表
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2022-11-27
 */
@Data
@TableName("data_flow_recharge")
public class DataFlowRechargeEntity {

    /**
     * 主键ID
     */
    @TableId("ID")
    private String id;

    /**
     * 充值账户ID
     */
    @TableField("IN_ACC_ID")
    private String inAccId;

    /**
     * 充值类型
     */
    @TableField("CHARGE_TYPE")
    private String chargeType;

    /**
     * 资金账户ID（转出账户）
     */
    @TableField("OUT_ACC_ID")
    private String outAccId;


    /**
     * 充值日期
     */
    @TableField("CHARGE_DATE")
    private Date chargeDate;


    /**
     * 充值金额
     */
    @TableField("AMOUNT")
    private BigDecimal amount;


    /**
     * 赠送金额
     */
    @TableField("ADD_AMOUNT")
    private BigDecimal addAmount;


    /**
     * 备注
     */
    @TableField("NOTE")
    private String note;


    /**
     * 创建人
     */
    @TableField("CREATE_BY")
    private String createBy;


    /**
     * 创建时间
     */
    @TableField("CREATE_TIME")
    private Date createTime;


    /**
     * 最后修改人
     */
    @TableField("UPDATE_BY")
    private String updateBy;


    /**
     * 最后修改时间
     */
    @TableField("UPDATE_TIME")
    private Date updateTime;

}
