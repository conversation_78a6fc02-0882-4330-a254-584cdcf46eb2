package com.xinghuo.card.flow.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xinghuo.card.flow.entity.DataWoolWoolEntity;
import com.xinghuo.card.flow.model.datawoolwool.DataWoolWoolPagination;

import java.util.List;

/**
 * 羊毛记录表
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2022-12-12
 */
public interface DataWoolWoolService extends IService<DataWoolWoolEntity> {

    /**
     * 查询分页数据
     *
     * @param dataWoolWoolPagination 查询对象
     * @return 查询结果
     */
    List<DataWoolWoolEntity> getList(DataWoolWoolPagination dataWoolWoolPagination);

    /**
     * 查询分页或者不分页列表
     *
     * @param dataWoolWoolPagination 查询对象
     * @param dataType               0:分页 1:不分页
     * @return 查询结果
     */
    List<DataWoolWoolEntity> getTypeList(DataWoolWoolPagination dataWoolWoolPagination, int dataType);

    /**
     * 获取DataWoolWoolEntity详细信息
     *
     * @param id   主键
     */
    DataWoolWoolEntity getInfo(String id);

    /**
     * 删除
     *
     * @param entity 删除的对象
     */
    void delete(DataWoolWoolEntity entity);

    /**
     * 新增保存
     *
     * @param entity 新增的对象
     */
    void create(DataWoolWoolEntity entity);

    /**
     * 修改保存
     *
     * @param id     主键
     * @param entity 修改的对象
     */
    boolean update(String id, DataWoolWoolEntity entity);


}
