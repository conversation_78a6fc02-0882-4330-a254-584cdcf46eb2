import { defHttp } from '/@/utils/http/axios';

/**
 * 流水管理相关API接口
 */

enum Api {
  Prefix = '/api/card/flow/flow',
}

/**
 * 流水记录分页查询参数
 */
export interface FlowListParams {
  transType?: string;
  type?: string;
  flowDate?: string[];
  keyword?: string;
  current?: number;
  size?: number;
}

/**
 * 流水记录表单数据
 */
export interface FlowFormData {
  id?: string;
  transType: string;
  type?: string;
  flowDate: string;
  accId?: string;
  outAccId?: string;
  inAccId?: string;
  amout: number;
  note?: string;
}

/**
 * 获取流水记录列表（分页）
 */
export function getFlowList(data: FlowListParams) {
  return defHttp.post({ url: Api.Prefix + '/getList', data });
}

/**
 * 创建流水记录
 */
export function createFlow(data: FlowFormData) {
  return defHttp.post({ url: Api.Prefix, data });
}

/**
 * 修改流水记录
 */
export function updateFlow(data: FlowFormData) {
  return defHttp.put({ url: Api.Prefix + '/' + data.id, data });
}

/**
 * 获取流水记录详情
 */
export function getFlowInfo(id: string) {
  return defHttp.get({ url: Api.Prefix + '/' + id });
}

/**
 * 删除流水记录
 */
export function delFlow(id: string) {
  return defHttp.delete({ url: Api.Prefix + '/' + id });
}
