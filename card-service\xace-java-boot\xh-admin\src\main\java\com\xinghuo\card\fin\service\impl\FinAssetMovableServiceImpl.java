package com.xinghuo.card.fin.service.impl;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xinghuo.card.fin.dao.FinAssetMovableMapper;
import com.xinghuo.card.fin.entity.FinAssetMovableEntity;
import com.xinghuo.card.fin.model.finAssetMovable.FinAssetMovablePagination;
import com.xinghuo.card.fin.service.FinAssetMovableService;
import com.xinghuo.common.base.service.impl.ExtendedBaseServiceImpl;
import com.xinghuo.common.util.UserProvider;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

/**
 * 动产资产服务实现类
 *
 * <AUTHOR> Assistant
 * @version V1.0.0
 * @date 2024-12-29
 */
@Slf4j
@Service
public class FinAssetMovableServiceImpl extends ExtendedBaseServiceImpl<FinAssetMovableMapper, FinAssetMovableEntity> implements FinAssetMovableService {

    @Resource
    private UserProvider userProvider;

    @Override
    public List<FinAssetMovableEntity> getList(FinAssetMovablePagination pagination) {
        QueryWrapper<FinAssetMovableEntity> queryWrapper = new QueryWrapper<>();
        
        // 构建查询条件
        if (StrUtil.isNotBlank(pagination.getUserId())) {
            queryWrapper.eq("USER_ID", pagination.getUserId());
        }
        
        if (StrUtil.isNotBlank(pagination.getManId())) {
            queryWrapper.eq("MAN_ID", pagination.getManId());
        }
        
        if (StrUtil.isNotBlank(pagination.getAssetType())) {
            queryWrapper.eq("ASSET_TYPE", pagination.getAssetType());
        }
        
        if (StrUtil.isNotBlank(pagination.getName())) {
            queryWrapper.like("NAME", pagination.getName());
        }
        
        if (StrUtil.isNotBlank(pagination.getStatus())) {
            queryWrapper.eq("STATUS", pagination.getStatus());
        }
        
        // 排序
        queryWrapper.orderByDesc("f_created_at");
        
        // 分页查询
        Page<FinAssetMovableEntity> page = new Page<>(pagination.getCurrentPage(), pagination.getPageSize());
        Page<FinAssetMovableEntity> result = this.page(page, queryWrapper);
        
        return result.getRecords();
    }

    @Override
    public FinAssetMovableEntity getInfo(String id) {
        Assert.notBlank(id, "资产ID不能为空");
        return this.getById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveInfo(FinAssetMovableEntity entity) {
        Assert.notNull(entity, "资产信息不能为空");
        
        // 设置默认值
        if (StrUtil.isBlank(entity.getStatus())) {
            entity.setStatus("OWNED");
        }
        
        // 设置当前用户ID
        if (StrUtil.isBlank(entity.getUserId())) {
            entity.setUserId(userProvider.get().getUserId());
        }
        
        boolean result = this.save(entity);
        
        if (result) {
            log.info("创建动产资产成功，ID: {}, 名称: {}", entity.getId(), entity.getName());
        }
        
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateInfo(FinAssetMovableEntity entity) {
        Assert.notNull(entity, "资产信息不能为空");
        Assert.notBlank(entity.getId(), "资产ID不能为空");
        
        boolean result = this.updateById(entity);
        
        if (result) {
            log.info("更新动产资产成功，ID: {}, 名称: {}", entity.getId(), entity.getName());
        }
        
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteById(String id) {
        Assert.notBlank(id, "资产ID不能为空");
        
        FinAssetMovableEntity entity = this.getById(id);
        if (entity == null) {
            log.warn("要删除的动产资产不存在，ID: {}", id);
            return false;
        }
        
        boolean result = this.removeById(id);
        
        if (result) {
            log.info("删除动产资产成功，ID: {}, 名称: {}", id, entity.getName());
        }
        
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteBatch(List<String> ids) {
        Assert.notEmpty(ids, "资产ID列表不能为空");
        
        boolean result = this.removeByIds(ids);
        
        if (result) {
            log.info("批量删除动产资产成功，数量: {}", ids.size());
        }
        
        return result;
    }

    @Override
    public List<FinAssetMovableEntity> getListByManId(String manId) {
        Assert.notBlank(manId, "持卡人ID不能为空");
        
        QueryWrapper<FinAssetMovableEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("MAN_ID", manId);
        queryWrapper.eq("STATUS", "OWNED");
        queryWrapper.orderByDesc("f_created_at");
        
        return this.list(queryWrapper);
    }

    @Override
    public Map<String, Object> getAssetStatistics() {
        return baseMapper.getAssetStatistics();
    }

    @Override
    public List<Map<String, Object>> getAssetTypeDistribution() {
        return baseMapper.getAssetTypeDistribution();
    }

    @Override
    public List<Map<String, Object>> getBrandDistribution() {
        QueryWrapper<FinAssetMovableEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .select(FinAssetMovableEntity::getBrand)
                .groupBy(FinAssetMovableEntity::getBrand);

        List<FinAssetMovableEntity> assets = this.list(queryWrapper);
        List<Map<String, Object>> distribution = new ArrayList<>();

        Map<String, Integer> brandCount = new HashMap<>();
        for (FinAssetMovableEntity asset : assets) {
            String brand = asset.getBrand();
            if (StrXhUtil.isNotEmpty(brand)) {
                brandCount.put(brand, brandCount.getOrDefault(brand, 0) + 1);
            }
        }

        for (Map.Entry<String, Integer> entry : brandCount.entrySet()) {
            Map<String, Object> item = new HashMap<>();
            item.put("brand", entry.getKey());
            item.put("count", entry.getValue());
            distribution.add(item);
        }

        return distribution;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateValuation(String id, BigDecimal currentValue, String valuationMethod) {
        Assert.notBlank(id, "资产ID不能为空");
        Assert.notNull(currentValue, "当前估值不能为空");
        
        FinAssetMovableEntity entity = new FinAssetMovableEntity();
        entity.setId(id);
        entity.setCurrentValue(currentValue);
        entity.setValuationDate(new Date());
        
        boolean result = this.updateById(entity);
        
        if (result) {
            log.info("更新动产估值成功，ID: {}, 估值: {}", id, currentValue);
        }
        
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchUpdateValuation(List<Map<String, Object>> valuationList) {
        Assert.notEmpty(valuationList, "估值列表不能为空");
        
        int successCount = 0;
        for (Map<String, Object> valuation : valuationList) {
            String id = (String) valuation.get("id");
            BigDecimal currentValue = (BigDecimal) valuation.get("currentValue");
            String valuationMethod = (String) valuation.get("valuationMethod");
            
            if (updateValuation(id, currentValue, valuationMethod)) {
                successCount++;
            }
        }
        
        log.info("批量更新动产估值完成，成功数量: {}", successCount);
        
        return successCount;
    }

    @Override
    public List<Map<String, Object>> getValuationHistory(String id) {
        Assert.notBlank(id, "资产ID不能为空");
        return baseMapper.getValuationHistory(id);
    }

    @Override
    public BigDecimal calculateTotalValue(String manId) {
        Assert.notBlank(manId, "持卡人ID不能为空");
        
        BigDecimal totalValue = baseMapper.calculateTotalValue(manId);
        return totalValue != null ? totalValue : BigDecimal.ZERO;
    }

    @Override
    public List<Map<String, Object>> getValueTrend(String manId, Integer months) {
        Assert.notBlank(manId, "持卡人ID不能为空");
        
        if (months == null || months <= 0) {
            months = 12; // 默认12个月
        }
        
        return baseMapper.getValueTrend(manId, months);
    }

    @Override
    public boolean checkNameDuplicate(String name, String manId, String excludeId) {
        Assert.notBlank(name, "资产名称不能为空");
        Assert.notBlank(manId, "持卡人ID不能为空");
        
        QueryWrapper<FinAssetMovableEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("NAME", name);
        queryWrapper.eq("MAN_ID", manId);
        
        if (StrUtil.isNotBlank(excludeId)) {
            queryWrapper.ne("ID", excludeId);
        }
        
        return this.count(queryWrapper) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> importAssetData(List<FinAssetMovableEntity> assetList) {
        Assert.notEmpty(assetList, "资产数据列表不能为空");
        
        Map<String, Object> result = new HashMap<>();
        int successCount = 0;
        int failCount = 0;
        List<String> errorMessages = new ArrayList<>();
        
        for (FinAssetMovableEntity asset : assetList) {
            try {
                if (saveInfo(asset)) {
                    successCount++;
                } else {
                    failCount++;
                    errorMessages.add("保存资产失败: " + asset.getName());
                }
            } catch (Exception e) {
                failCount++;
                errorMessages.add("保存资产异常: " + asset.getName() + ", 错误: " + e.getMessage());
                log.error("导入动产资产异常", e);
            }
        }
        
        result.put("successCount", successCount);
        result.put("failCount", failCount);
        result.put("errorMessages", errorMessages);
        
        log.info("导入动产资产完成，成功: {}, 失败: {}", successCount, failCount);
        
        return result;
    }

    @Override
    public List<Map<String, Object>> exportAssetData(FinAssetMovablePagination pagination) {
        return baseMapper.exportAssetData(pagination);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public FinAssetMovableEntity copyAsset(String id, String newName) {
        Assert.notBlank(id, "源资产ID不能为空");
        Assert.notBlank(newName, "新资产名称不能为空");
        
        FinAssetMovableEntity sourceAsset = this.getById(id);
        Assert.notNull(sourceAsset, "源资产不存在");
        
        FinAssetMovableEntity newAsset = new FinAssetMovableEntity();
        // 复制属性
        newAsset.setUserId(sourceAsset.getUserId());
        newAsset.setManId(sourceAsset.getManId());
        newAsset.setAssetType(sourceAsset.getAssetType());
        newAsset.setName(newName);
        newAsset.setBrand(sourceAsset.getBrand());
        newAsset.setModel(sourceAsset.getModel());
        newAsset.setPurchasePrice(sourceAsset.getPurchasePrice());
        newAsset.setCurrentValue(sourceAsset.getCurrentValue());
        newAsset.setStatus("OWNED");
        newAsset.setNote("复制自: " + sourceAsset.getName());
        
        if (saveInfo(newAsset)) {
            log.info("复制动产资产成功，源ID: {}, 新ID: {}", id, newAsset.getId());
            return newAsset;
        }
        
        return null;
    }

    @Override
    public Map<String, Object> getAssetDetailWithHistory(String id) {
        Assert.notBlank(id, "资产ID不能为空");
        return baseMapper.getAssetDetailWithHistory(id);
    }

    @Override
    public List<FinAssetMovableEntity> getListByAssetType(String assetType) {
        Assert.notBlank(assetType, "动产类型不能为空");
        
        QueryWrapper<FinAssetMovableEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("ASSET_TYPE", assetType);
        queryWrapper.eq("STATUS", "OWNED");
        queryWrapper.orderByDesc("f_created_at");
        
        return this.list(queryWrapper);
    }

    @Override
    public List<FinAssetMovableEntity> getListByBrand(String brand) {
        Assert.notBlank(brand, "品牌不能为空");

        QueryWrapper<FinAssetMovableEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(FinAssetMovableEntity::getBrand, brand)
                .orderByDesc(FinAssetMovableEntity::getCreateTime);

        return this.list(queryWrapper);
    }

    @Override
    public List<FinAssetMovableEntity> getHighValueAssets(BigDecimal minValue) {
        Assert.notNull(minValue, "最小价值不能为空");
        
        QueryWrapper<FinAssetMovableEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.ge("CURRENT_VALUE", minValue);
        queryWrapper.eq("STATUS", "OWNED");
        queryWrapper.orderByDesc("CURRENT_VALUE");
        
        return this.list(queryWrapper);
    }

    @Override
    public Map<String, Object> calculateDepreciation(String id) {
        Assert.notBlank(id, "资产ID不能为空");

        FinAssetMovableEntity asset = this.getById(id);
        if (asset == null) {
            log.warn("资产不存在，ID: {}", id);
            return new HashMap<>();
        }

        Map<String, Object> depreciation = new HashMap<>();

        // 计算使用年限
        Date purchaseDate = asset.getPurchaseDate();
        if (purchaseDate != null) {
            long daysBetween = (new Date().getTime() - purchaseDate.getTime()) / (1000 * 60 * 60 * 24);
            double usageYears = daysBetween / 365.0;

            // 简单的直线折旧计算
            BigDecimal originalValue = asset.getPurchasePrice() != null ? asset.getPurchasePrice() : BigDecimal.ZERO;
            BigDecimal currentValue = asset.getCurrentValue() != null ? asset.getCurrentValue() : originalValue;

            BigDecimal depreciationAmount = originalValue.subtract(currentValue);
            BigDecimal depreciationRate = BigDecimal.ZERO;

            if (originalValue.compareTo(BigDecimal.ZERO) > 0) {
                depreciationRate = depreciationAmount.divide(originalValue, 4, BigDecimal.ROUND_HALF_UP)
                        .multiply(new BigDecimal("100"));
            }

            depreciation.put("originalValue", originalValue);
            depreciation.put("currentValue", currentValue);
            depreciation.put("depreciationAmount", depreciationAmount);
            depreciation.put("depreciationRate", depreciationRate);
            depreciation.put("usageYears", usageYears);
        }

        return depreciation;
    }

    @Override
    public Map<String, Object> getUsageYearStats(String manId) {
        Assert.notBlank(manId, "持卡人ID不能为空");

        QueryWrapper<FinAssetMovableEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(FinAssetMovableEntity::getManId, manId);
        List<FinAssetMovableEntity> assets = this.list(queryWrapper);

        Map<String, Object> stats = new HashMap<>();
        Map<String, Integer> yearRangeCount = new HashMap<>();

        for (FinAssetMovableEntity asset : assets) {
            if (asset.getPurchaseDate() != null) {
                long daysBetween = (new Date().getTime() - asset.getPurchaseDate().getTime()) / (1000 * 60 * 60 * 24);
                double usageYears = daysBetween / 365.0;

                String range;
                if (usageYears < 1) {
                    range = "1年以内";
                } else if (usageYears < 3) {
                    range = "1-3年";
                } else if (usageYears < 5) {
                    range = "3-5年";
                } else {
                    range = "5年以上";
                }

                yearRangeCount.put(range, yearRangeCount.getOrDefault(range, 0) + 1);
            }
        }

        stats.put("totalAssets", assets.size());
        stats.put("yearRangeDistribution", yearRangeCount);

        return stats;
    }

    @Override
    public List<FinAssetMovableEntity> getExpiringAssets(String manId, Integer months) {
        Assert.notBlank(manId, "持卡人ID不能为空");

        if (months == null || months <= 0) {
            months = 6; // 默认6个月
        }

        QueryWrapper<FinAssetMovableEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(FinAssetMovableEntity::getManId, manId)
                .isNotNull(FinAssetMovableEntity::getWarrantyExpiry);

        List<FinAssetMovableEntity> allAssets = this.list(queryWrapper);
        List<FinAssetMovableEntity> expiringAssets = new ArrayList<>();

        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MONTH, months);
        Date futureDate = calendar.getTime();

        for (FinAssetMovableEntity asset : allAssets) {
            if (asset.getWarrantyExpiry() != null && asset.getWarrantyExpiry().before(futureDate)) {
                expiringAssets.add(asset);
            }
        }

        return expiringAssets;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateAssetStatus(String id, String status) {
        Assert.notBlank(id, "资产ID不能为空");
        Assert.notBlank(status, "状态不能为空");

        FinAssetMovableEntity asset = new FinAssetMovableEntity();
        asset.setId(id);
        asset.setStatus(status);

        boolean result = this.updateById(asset);
        if (result) {
            log.info("更新动产资产状态成功，资产ID: {}, 新状态: {}", id, status);
        }

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchUpdateAssetStatus(List<String> ids, String status) {
        Assert.notEmpty(ids, "资产ID列表不能为空");
        Assert.notBlank(status, "状态不能为空");

        UpdateWrapper<FinAssetMovableEntity> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda()
                .in(FinAssetMovableEntity::getId, ids)
                .set(FinAssetMovableEntity::getStatus, status);

        int result = this.getBaseMapper().update(null, updateWrapper);
        log.info("批量更新动产资产状态完成，更新数量: {}, 新状态: {}", result, status);

        return result;
    }

    @Override
    public List<FinAssetMovableEntity> getInsuranceExpiringAssets(String manId, Integer days) {
        Assert.notBlank(manId, "持卡人ID不能为空");

        if (days == null || days <= 0) {
            days = 30; // 默认30天
        }

        QueryWrapper<FinAssetMovableEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(FinAssetMovableEntity::getManId, manId)
                .isNotNull(FinAssetMovableEntity::getInsuranceExpiry);

        List<FinAssetMovableEntity> allAssets = this.list(queryWrapper);
        List<FinAssetMovableEntity> expiringAssets = new ArrayList<>();

        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, days);
        Date futureDate = calendar.getTime();

        for (FinAssetMovableEntity asset : allAssets) {
            if (asset.getInsuranceExpiry() != null && asset.getInsuranceExpiry().before(futureDate)) {
                expiringAssets.add(asset);
            }
        }

        return expiringAssets;
    }

    @Override
    public Map<String, Object> calculateROI(String id) {
        Assert.notBlank(id, "资产ID不能为空");
        return baseMapper.calculateROI(id);
    }

    @Override
    public List<Map<String, Object>> getMaintenanceRecords(String id) {
        Assert.notBlank(id, "资产ID不能为空");

        // TODO: 实现维护记录查询逻辑
        // 这里需要关联维护记录表，暂时返回空列表
        List<Map<String, Object>> records = new ArrayList<>();

        log.info("查询资产维护记录，资产ID: {}", id);

        return records;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addMaintenanceRecord(String id, Map<String, Object> maintenanceInfo) {
        Assert.notBlank(id, "资产ID不能为空");
        Assert.notEmpty(maintenanceInfo, "维护信息不能为空");

        FinAssetMovableEntity asset = this.getById(id);
        if (asset == null) {
            log.warn("资产不存在，ID: {}", id);
            return false;
        }

        // TODO: 实现维护记录添加逻辑
        // 这里需要插入到维护记录表

        log.info("添加资产维护记录，资产ID: {}, 维护信息: {}", id, maintenanceInfo);

        return true;
    }

    @Override
    public Map<String, Object> getHoldingPeriodStats(String manId) {
        Assert.notBlank(manId, "持卡人ID不能为空");
        return baseMapper.getHoldingPeriodStats(manId);
    }

    @Override
    public Map<String, Object> getDepreciationInfo(String id) {
        Assert.notBlank(id, "资产ID不能为空");
        return baseMapper.getDepreciationInfo(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchUpdateStatus(List<String> ids, String status) {
        Assert.notEmpty(ids, "资产ID列表不能为空");
        Assert.notBlank(status, "状态不能为空");
        
        return baseMapper.batchUpdateStatus(ids, status);
    }

    @Override
    public List<FinAssetMovableEntity> getExpiringAssets(int days) {
        Assert.isTrue(days > 0, "天数必须大于0");
        
        return baseMapper.getExpiringAssets(days);
    }

    @Override
    public Map<String, Object> getInsuranceInfo(String id) {
        Assert.notBlank(id, "资产ID不能为空");
        return baseMapper.getInsuranceInfo(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateInsuranceInfo(String id, Map<String, Object> insuranceInfo) {
        Assert.notBlank(id, "资产ID不能为空");
        Assert.notEmpty(insuranceInfo, "保险信息不能为空");
        
        return baseMapper.updateInsuranceInfo(id, insuranceInfo) > 0;
    }
}
