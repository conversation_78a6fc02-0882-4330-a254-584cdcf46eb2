# 代码规范指南

本目录包含了项目的完整代码规范和开发指南。

## 📋 规范文档列表

1. [技术栈规范](01_TECH_STACK.md) - 项目使用的技术栈和环境要求
2. [项目结构](02_PROJECT_STRUCTURE.md) - 代码组织和目录结构
3. [编码规范](03_CODING_CONVENTIONS.md) - 基本编码约定和格式规范
4. [控制器规范](04_CONTROLLER_GUIDELINES.md) - Controller 层开发规范
5. [数据访问规范](05_DAO_MAPPER_GUIDELINES.md) - DAO/Mapper 层开发规范
6. [实体类规范](06_ENTITY_GUIDELINES.md) - Entity 实体类开发规范
7. [模型类规范](07_MODEL_GUIDELINES.md) - DTO/VO/Form 等模型类规范
8. [服务层规范](08_SERVICE_GUIDELINES.md) - Service 层开发规范
9. [提示词模板](09_PROMPTS_TEMPLATES.md) - AI 辅助开发提示词

## 🚀 快速开始

1. 阅读 [技术栈规范](01_TECH_STACK.md) 了解项目使用的技术
2. 查看 [项目结构](02_PROJECT_STRUCTURE.md) 了解代码组织方式
3. 遵循 [编码规范](03_CODING_CONVENTIONS.md) 编写代码
4. 按照各层级规范开发功能

## ⚠️ 重要提醒：Jakarta EE 规范

项目使用 **JDK 17+ 和 Spring Boot 3.x**，必须使用 **Jakarta EE** 规范：

### 正确的导入 ✅
```java
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.NotBlank;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.persistence.Entity;
```

### 错误的导入 ❌
```java
import javax.validation.constraints.NotNull;  // 错误！
import javax.servlet.http.HttpServletRequest;  // 错误！
import javax.persistence.Entity;               // 错误！
```

详细说明请参考：[技术栈规范 - Jakarta EE 部分](01_TECH_STACK.md#重要技术迁移说明)

## 🔧 自动检查工具

项目提供了自动检查脚本来确保代码符合导入规范：

### 本地检查
```powershell
# 检查是否有错误的导入
.\.github\scripts\check-imports.ps1

# 自动修复错误的导入
.\.github\scripts\check-imports.ps1 -Fix
```

### CI/CD 检查
项目配置了 GitHub Actions 自动检查，每次提交和 PR 都会自动验证导入规范。

## 📝 开发流程

1. **开发前**：阅读相关规范文档
2. **开发中**：遵循规范编写代码
3. **提交前**：运行本地检查脚本
4. **提交后**：确保 CI 检查通过

## 🤝 贡献指南

如果发现规范文档有需要改进的地方，请：

1. 创建 Issue 描述问题
2. 提交 PR 修改相关文档
3. 确保修改符合项目整体规范

## 📚 参考资源

- [Spring Boot 官方文档](https://spring.io/projects/spring-boot)
- [Jakarta EE 规范](https://jakarta.ee/)
- [MyBatis-Plus 官方文档](https://baomidou.com/)
- [阿里巴巴Java开发手册](https://github.com/alibaba/p3c)
