package com.xinghuo.card.sys.model.datasyspaytype;

import com.xinghuo.common.base.model.Page;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;


/**
 * 收支类型管理
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2022-11-14
 */
@Data
public class DataSysPaytypePagination extends Page {

    @Schema(description = "类型")
    private String type;

    @Schema(description = "名称 ")
    private String name;

    @Schema(description = "菜单id")
    private String menuId;
}
