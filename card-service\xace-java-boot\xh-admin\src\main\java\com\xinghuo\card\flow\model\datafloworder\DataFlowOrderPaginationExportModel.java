package com.xinghuo.card.flow.model.datafloworder;

import com.xinghuo.common.base.model.Pagination;
import lombok.Data;

import java.util.List;

/**
 * 订单管理
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2022-11-27
 */
@Data
public class DataFlowOrderPaginationExportModel extends Pagination {

    /**
     * 选择的key
     */
    private String selectKey;

    /**
     * json
     */
    private String json;

    /**
     * 数据类型
     */
    private String dataType;


    /**
     * 店铺
     */
    private String accId;

    /**
     * 订单日期
     */
    private List<String> orderDate;

    /**
     * 状态
     */
    private String formStatus;

    /**
     * 订单编号
     */
    private String orderNo;
}
