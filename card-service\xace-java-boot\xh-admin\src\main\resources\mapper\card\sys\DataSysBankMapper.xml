<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xinghuo.card.sys.dao.SysBankMapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.xinghuo.card.sys.entity.SysBankEntity">
        <id column="id" property="id" jdbcType="VARCHAR"/>
        <result column="BANK_TYPE" property="bankType" jdbcType="VARCHAR"/>
        <result column="BANK_ICON" property="bankIcon" jdbcType="VARCHAR"/>
        <result column="BANK_NAME" property="bankName" jdbcType="VARCHAR"/>
        <result column="BANK_URL" property="bankUrl" jdbcType="VARCHAR"/>
        <result column="BANK_KEY" property="bankKey" jdbcType="VARCHAR"/>
        <result column="CREDIT_TYPE" property="creditType" jdbcType="VARCHAR"/>
        <result column="create_by" property="createBy" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_by" property="updateBy" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础字段列表 -->
    <sql id="Base_Column_List">
        id, BANK_TYPE, BANK_ICON, BANK_NAME, BANK_URL, BANK_KEY, CREDIT_TYPE, 
        create_by, create_time, update_by, update_time
    </sql>

    <!-- 检查银行名称是否唯一 -->
    <select id="checkBankNameUnique" resultType="int">
        SELECT COUNT(1)
        FROM data_sys_bank
        WHERE BANK_NAME = #{bankName}
        <if test="id != null and id != ''">
            AND id != #{id}
        </if>
    </select>

    <!-- 检查银行简称是否唯一 -->
    <select id="checkBankKeyUnique" resultType="int">
        SELECT COUNT(1)
        FROM data_sys_bank
        WHERE BANK_KEY = #{bankKey}
        <if test="id != null and id != ''">
            AND id != #{id}
        </if>
    </select>

    <!-- 根据银行类型获取银行列表 -->
    <select id="getListByType" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM data_sys_bank
        WHERE BANK_TYPE = #{bankType}
        ORDER BY BANK_NAME
    </select>

    <!-- 获取银行统计信息 -->
    <select id="getBankStatistics" resultType="map">
        SELECT 
            COUNT(1) as totalCount,
            COUNT(DISTINCT BANK_TYPE) as typeCount,
            MIN(create_time) as earliestCreateTime,
            MAX(create_time) as latestCreateTime
        FROM data_sys_bank
    </select>

    <!-- 根据银行类型统计数量 -->
    <select id="getBankCountByType" resultType="map">
        SELECT 
            BANK_TYPE as bankType,
            COUNT(1) as count
        FROM data_sys_bank
        GROUP BY BANK_TYPE
        ORDER BY count DESC
    </select>

    <!-- 获取热门银行列表 -->
    <select id="getPopularBanks" resultType="map">
        SELECT 
            b.id,
            b.BANK_NAME as bankName,
            b.BANK_ICON as bankIcon,
            b.BANK_TYPE as bankType,
            COUNT(ub.id) as userCount,
            COUNT(cc.id) as cardCount
        FROM data_sys_bank b
        LEFT JOIN data_sys_user_bank ub ON b.id = ub.BANK_ID
        LEFT JOIN data_credit_card cc ON b.id = cc.BANK_ID
        GROUP BY b.id, b.BANK_NAME, b.BANK_ICON, b.BANK_TYPE
        ORDER BY cardCount DESC, userCount DESC
        <if test="limit != null and limit > 0">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 搜索银行 -->
    <select id="searchBanks" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM data_sys_bank
        WHERE 1=1
        <if test="keyword != null and keyword != ''">
            AND (
                BANK_NAME LIKE CONCAT('%', #{keyword}, '%')
                OR BANK_KEY LIKE CONCAT('%', #{keyword}, '%')
            )
        </if>
        ORDER BY BANK_NAME
    </select>

    <!-- 批量更新银行状态 -->
    <update id="batchUpdateStatus">
        UPDATE data_sys_bank
        SET update_time = NOW()
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 获取银行详细信息（包含关联统计） -->
    <select id="getBankDetailWithStats" resultType="map">
        SELECT 
            b.*,
            COUNT(DISTINCT ub.id) as userBankCount,
            COUNT(DISTINCT cc.id) as creditCardCount,
            SUM(ub.LIMIT_MONEY) as totalLimitMoney,
            SUM(ub.POINT) as totalPoint
        FROM data_sys_bank b
        LEFT JOIN data_sys_user_bank ub ON b.id = ub.BANK_ID
        LEFT JOIN data_credit_card cc ON b.id = cc.BANK_ID
        WHERE b.id = #{id}
        GROUP BY b.id
    </select>

</mapper>
