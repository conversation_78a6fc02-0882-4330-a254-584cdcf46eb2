#set($serviceName = "${table.serviceName.substring(0,1).toLowerCase()}${table.serviceName.substring(1)}")
#set($Name = "${genInfo.className.substring(0,1).toUpperCase()}${genInfo.className.substring(1)}")
#set($name = "${genInfo.className.substring(0,1).toLowerCase()}${genInfo.className.substring(1)}")
#set($packName = "${name.toLowerCase()}")
package ${package.Service};
#foreach($subfield in ${child})
    #set($ChildName="${subfield.className.substring(0,1).toUpperCase()}${subfield.className.substring(1)}")
    #set($childName="${subfield.className.substring(0,1).toLowerCase()}${subfield.className.substring(1)}")
import ${package.Entity}.${ChildName}Entity;
import ${package.Service}.${ChildName}Service;
#end

import ${package.Entity}.${table.entityName};
import ${superServiceClassPackage};
#if(${main})
import java.util.*;
import ${modulePackageName}.model.${packName}.*;
#end
/**
 *
 * ${genInfo.description}
 * 版本： ${genInfo.version}
 * 版权： ${genInfo.copyright}
 * 作者： ${genInfo.createUser}
 * 日期： ${genInfo.createDate}
 */
public interface ${table.serviceName} extends ${superServiceClass}<${table.entityName}> {

#if(${main})
    void create(${table.entityName} entity);

    //验证表单
    String checkForm(${Name}CrForm form,int i);

#end
}
