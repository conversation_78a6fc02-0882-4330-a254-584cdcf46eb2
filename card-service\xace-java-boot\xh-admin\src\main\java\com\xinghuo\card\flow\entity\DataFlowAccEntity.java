package com.xinghuo.card.flow.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;


/**
 * 账号明细表
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2022-11-26
 */
@Data
@TableName("data_flow_acc")
public class DataFlowAccEntity {

    /**
     * 流水ID
     */
    @TableId("ID")
    private String id;
    /**
     *
     */
    @TableField("FLOW_ID")
    private String flowId;
    /**
     *
     */
    @TableField("ACC_ID")
    private String accId;
    /**
     * 收支类型
     */
    @TableField("TYPE")
    private String type;

    @TableField("MAN_ID")
    private String manId;

    /**
     * 收支，1-收入  -1 支出
     */
    @TableField(value = "INCOME", updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal income;
    /**
     * 金额
     */
    @TableField(value = "PAY", updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal pay;
    /**
     *
     */
    @TableField("CC_ID")
    private String ccId;
    /**
     * 关联账号ID(转账时用)
     */
    @TableField("OUT_ACC_ID")
    private String outAccId;



    /**
     *
     */
    @TableField(value = "BALANCE", updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal balance;
    /**
     * 1-表示退款
     */
    @TableField("ISBACK")
    private Boolean isback;
    /**
     * 日期
     */
    @TableField("FLOW_DATE")
    private Date flowDate;
    /**
     * 排序
     */
    @TableField("LIST_ORDER")
    private Integer listOrder;
    /**
     *
     */
    @TableField("BILL_ID")
    private String billId;
    /**
     * 备注
     */
    @TableField("NOTE")
    private String note;
    /**
     *
     */
    @TableField("LIST_ORDER2")
    private Integer listOrder2;


    public DataFlowAccEntity() {
    }


    public DataFlowAccEntity(String accId, Date flowDate) {
        this.accId = accId;
        this.flowDate = flowDate;
    }

}
