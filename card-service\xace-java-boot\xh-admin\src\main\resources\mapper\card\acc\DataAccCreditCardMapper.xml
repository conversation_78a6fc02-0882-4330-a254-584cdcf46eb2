<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xinghuo.card.acc.dao.AccCreditCardMapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.xinghuo.card.acc.entity.AccCreditCardEntity">
        <id column="ID" property="id" jdbcType="VARCHAR"/>
        <result column="UBANK_ID" property="ubankId" jdbcType="VARCHAR"/>
        <result column="BANK_ID" property="bankId" jdbcType="VARCHAR"/>
        <result column="BILL_TYPE" property="billType" jdbcType="VARCHAR"/>
        <result column="BILL_DAY" property="billDay" jdbcType="INTEGER"/>
        <result column="REPAY_TYPE" property="repayType" jdbcType="VARCHAR"/>
        <result column="REPAY_DAY" property="repayDay" jdbcType="INTEGER"/>
        <result column="LIMIT_MONEY" property="limitMoney" jdbcType="INTEGER"/>
        <result column="BILL_DAY_TYPE" property="billDayType" jdbcType="VARCHAR"/>
        <result column="DEFAULT_CREDIT_ID" property="defaultCreditId" jdbcType="VARCHAR"/>
        <result column="f_created_by" property="createBy" jdbcType="VARCHAR"/>
        <result column="f_created_at" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="f_last_updated_by" property="updateBy" jdbcType="VARCHAR"/>
        <result column="f_last_updated_at" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="f_tenantid" property="tenantId" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 基础字段列表 -->
    <sql id="Base_Column_List">
        ID, UBANK_ID, BANK_ID, BILL_TYPE, BILL_DAY, REPAY_TYPE, REPAY_DAY, LIMIT_MONEY, 
        BILL_DAY_TYPE, DEFAULT_CREDIT_ID, f_created_by, f_created_at, f_last_updated_by, f_last_updated_at, f_tenantid
    </sql>

    <!-- 检查用户银行关系是否已有信用卡账户 -->
    <select id="checkUbankExists" resultType="int">
        SELECT COUNT(1)
        FROM data_acc_credit_card
        WHERE UBANK_ID = #{ubankId}
        <if test="id != null and id != ''">
            AND ID != #{id}
        </if>
    </select>

    <!-- 根据用户银行关系ID获取信用卡账户列表（包含关联信息） -->
    <select id="getListByUbankIdWithInfo" resultType="map">
        SELECT 
            cc.*,
            b.BANK_NAME as bankName,
            b.BANK_ICON as bankIcon,
            ub.BANKNAME as ubankName,
            m.name as manName
        FROM data_acc_credit_card cc
        LEFT JOIN data_sys_bank b ON cc.BANK_ID = b.id
        LEFT JOIN data_sys_user_bank ub ON cc.UBANK_ID = ub.id
        LEFT JOIN data_sys_man m ON ub.MAN_ID = m.id
        WHERE cc.UBANK_ID = #{ubankId}
        ORDER BY cc.f_created_at DESC
    </select>

    <!-- 根据银行ID获取信用卡账户列表（包含关联信息） -->
    <select id="getListByBankIdWithInfo" resultType="map">
        SELECT 
            cc.*,
            b.BANK_NAME as bankName,
            b.BANK_ICON as bankIcon,
            ub.BANKNAME as ubankName,
            m.name as manName
        FROM data_acc_credit_card cc
        LEFT JOIN data_sys_bank b ON cc.BANK_ID = b.id
        LEFT JOIN data_sys_user_bank ub ON cc.UBANK_ID = ub.id
        LEFT JOIN data_sys_man m ON ub.MAN_ID = m.id
        WHERE cc.BANK_ID = #{bankId}
        ORDER BY cc.f_created_at DESC
    </select>

    <!-- 获取信用卡账户详细信息（包含关联信息） -->
    <select id="getCreditCardDetailWithInfo" resultType="map">
        SELECT 
            cc.*,
            b.BANK_NAME as bankName,
            b.BANK_ICON as bankIcon,
            b.BANK_TYPE as bankType,
            ub.BANKNAME as ubankName,
            ub.LIMIT_MONEY as ubankLimitMoney,
            ub.POINT as ubankPoint,
            m.name as manName,
            m.sex as manSex
        FROM data_acc_credit_card cc
        LEFT JOIN data_sys_bank b ON cc.BANK_ID = b.id
        LEFT JOIN data_sys_user_bank ub ON cc.UBANK_ID = ub.id
        LEFT JOIN data_sys_man m ON ub.MAN_ID = m.id
        WHERE cc.ID = #{id}
    </select>

    <!-- 获取信用卡账户统计信息 -->
    <select id="getCreditCardStatistics" resultType="map">
        SELECT 
            COUNT(1) as totalCount,
            COUNT(CASE WHEN BILL_TYPE = 'FIX' THEN 1 END) as fixBillCount,
            COUNT(CASE WHEN BILL_TYPE = 'LAST' THEN 1 END) as lastBillCount,
            COUNT(CASE WHEN REPAY_TYPE = 'FIX' THEN 1 END) as fixRepayCount,
            COUNT(CASE WHEN REPAY_TYPE = 'LATER' THEN 1 END) as laterRepayCount,
            AVG(LIMIT_MONEY) as avgLimitMoney,
            SUM(LIMIT_MONEY) as totalLimitMoney,
            MIN(f_created_at) as earliestCreateTime,
            MAX(f_created_at) as latestCreateTime
        FROM data_acc_credit_card
    </select>

    <!-- 根据账单类型统计数量 -->
    <select id="getCreditCardCountByBillType" resultType="map">
        SELECT 
            BILL_TYPE as billType,
            COUNT(1) as count,
            CASE 
                WHEN BILL_TYPE = 'FIX' THEN '固定账单日'
                WHEN BILL_TYPE = 'LAST' THEN '每月最后一天'
                ELSE '未设置'
            END as billTypeName
        FROM data_acc_credit_card
        WHERE BILL_TYPE IS NOT NULL
        GROUP BY BILL_TYPE
        ORDER BY count DESC
    </select>

    <!-- 根据还款类型统计数量 -->
    <select id="getCreditCardCountByRepayType" resultType="map">
        SELECT 
            REPAY_TYPE as repayType,
            COUNT(1) as count,
            CASE 
                WHEN REPAY_TYPE = 'FIX' THEN '固定还款日'
                WHEN REPAY_TYPE = 'LATER' THEN '账单日后还款'
                ELSE '未设置'
            END as repayTypeName
        FROM data_acc_credit_card
        WHERE REPAY_TYPE IS NOT NULL
        GROUP BY REPAY_TYPE
        ORDER BY count DESC
    </select>

    <!-- 设置默认信用卡 -->
    <update id="setDefaultCreditCard">
        UPDATE data_acc_credit_card
        SET DEFAULT_CREDIT_ID = CASE WHEN ID = #{id} THEN #{id} ELSE NULL END,
            f_last_updated_at = NOW()
        WHERE UBANK_ID = #{ubankId}
    </update>

    <!-- 清除默认信用卡设置 -->
    <update id="clearDefaultCreditCard">
        UPDATE data_acc_credit_card
        SET DEFAULT_CREDIT_ID = NULL,
            f_last_updated_at = NOW()
        WHERE UBANK_ID = #{ubankId}
    </update>

    <!-- 获取默认信用卡账户 -->
    <select id="getDefaultCreditCard" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM data_acc_credit_card
        WHERE UBANK_ID = #{ubankId}
        AND DEFAULT_CREDIT_ID IS NOT NULL
        AND DEFAULT_CREDIT_ID = ID
    </select>

    <!-- 同步银行信息 -->
    <update id="syncBankInfo">
        UPDATE data_acc_credit_card
        SET f_last_updated_at = NOW()
        WHERE BANK_ID = #{bankId}
    </update>

    <!-- 获取即将到期的账单提醒 -->
    <select id="getBillReminder" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM data_acc_credit_card
        WHERE BILL_TYPE IS NOT NULL
        AND BILL_DAY IS NOT NULL
        AND (
            (BILL_TYPE = 'FIX' AND DAY(#{endDate}) >= BILL_DAY AND DAY(#{startDate}) &lt;= BILL_DAY)
            OR BILL_TYPE = 'LAST'
        )
        ORDER BY BILL_DAY
    </select>

    <!-- 获取即将到期的还款提醒 -->
    <select id="getRepayReminder" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM data_acc_credit_card
        WHERE REPAY_TYPE IS NOT NULL
        AND REPAY_DAY IS NOT NULL
        ORDER BY REPAY_DAY
    </select>

    <!-- 批量更新额度 -->
    <update id="batchUpdateLimit">
        <foreach collection="list" item="item" separator=";">
            UPDATE data_acc_credit_card
            SET LIMIT_MONEY = #{item.limitMoney},
                f_last_updated_at = NOW()
            WHERE ID = #{item.id}
        </foreach>
    </update>

    <!-- 获取额度排行榜 -->
    <select id="getLimitRanking" resultType="map">
        SELECT 
            cc.ID as id,
            cc.LIMIT_MONEY as limitMoney,
            b.BANK_NAME as bankName,
            m.name as manName
        FROM data_acc_credit_card cc
        LEFT JOIN data_sys_bank b ON cc.BANK_ID = b.id
        LEFT JOIN data_sys_user_bank ub ON cc.UBANK_ID = ub.id
        LEFT JOIN data_sys_man m ON ub.MAN_ID = m.id
        WHERE cc.LIMIT_MONEY IS NOT NULL AND cc.LIMIT_MONEY > 0
        ORDER BY cc.LIMIT_MONEY DESC
        <if test="limit != null and limit > 0">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 根据用户银行关系ID获取统计信息 -->
    <select id="getCreditCardStatisticsByUbankId" resultType="map">
        SELECT 
            COUNT(1) as count,
            SUM(LIMIT_MONEY) as totalLimitMoney,
            AVG(LIMIT_MONEY) as avgLimitMoney
        FROM data_acc_credit_card
        WHERE UBANK_ID = #{ubankId}
    </select>

    <!-- 获取账单日分布统计 -->
    <select id="getBillDayDistribution" resultType="map">
        SELECT 
            BILL_DAY as billDay,
            COUNT(1) as count
        FROM data_acc_credit_card
        WHERE BILL_TYPE = 'FIX' AND BILL_DAY IS NOT NULL
        GROUP BY BILL_DAY
        ORDER BY BILL_DAY
    </select>

    <!-- 获取还款日分布统计 -->
    <select id="getRepayDayDistribution" resultType="map">
        SELECT 
            REPAY_DAY as repayDay,
            COUNT(1) as count
        FROM data_acc_credit_card
        WHERE REPAY_TYPE = 'FIX' AND REPAY_DAY IS NOT NULL
        GROUP BY REPAY_DAY
        ORDER BY REPAY_DAY
    </select>

    <!-- 根据账单日刷卡类型统计数量 -->
    <select id="getCreditCardCountByBillDayType" resultType="map">
        SELECT 
            BILL_DAY_TYPE as billDayType,
            COUNT(1) as count,
            CASE 
                WHEN BILL_DAY_TYPE = 'THIS' THEN '计入本期'
                WHEN BILL_DAY_TYPE = 'NEXT' THEN '计入下期'
                ELSE '未设置'
            END as billDayTypeName
        FROM data_acc_credit_card
        WHERE BILL_DAY_TYPE IS NOT NULL
        GROUP BY BILL_DAY_TYPE
        ORDER BY count DESC
    </select>

</mapper>
