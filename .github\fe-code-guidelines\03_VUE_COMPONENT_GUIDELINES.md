# Vue组件开发规范

## 组件设计原则

1. **单一职责**：每个组件只做一件事，避免过于复杂的组件
2. **高内聚低耦合**：相关功能应该集中在一个组件，组件之间的依赖应该减少
3. **可复用性**：设计组件时应考虑复用场景，使用合理的接口设计
4. **可测试性**：组件应该易于被单元测试
5. **可维护性**：代码结构应清晰易懂，便于后续维护
6. **可扩展性**：组件设计应考虑未来的功能扩展需求
7. **一致性**：遵循项目统一的设计风格和交互模式

## 组件分类

1. **基础组件**：不包含业务逻辑的通用UI组件，如按钮、输入框等
2. **业务组件**：包含特定业务逻辑的组件，如用户卡片、项目列表等
3. **容器组件**：负责数据获取和状态管理，较少包含UI元素
4. **展示组件**：主要负责UI展示，通过props接收数据
5. **布局组件**：负责页面布局结构，如Header、Sidebar等

## 组件导入路径规范

### 项目组件导入路径

项目中的组件按功能模块组织，使用统一的导入路径规范：

#### 1. 基础组件路径

```typescript
// 表格组件
import { BasicTable, useTable, TableAction } from '/@/components/Table';

// 表单组件
import { BasicForm, useForm } from '/@/components/Form/index';

// 弹窗组件
import { BasicModal, useModalInner } from '/@/components/Modal';
import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';

// 按钮组件
import { BasicButton } from '/@/components/Button';
```

#### 2. 业务组件路径

```typescript
// 组织架构相关选择器组件
import UserSelect from '/@/components/Xh/Organize/src/UserSelect.vue';
import DepSelect from '/@/components/Xh/Organize/src/DepSelect.vue';
import RoleSelect from '/@/components/Xh/Organize/src/RoleSelect.vue';
import OrganizeSelect from '/@/components/Xh/Organize/src/OrganizeSelect.vue';
import PosSelect from '/@/components/Xh/Organize/src/PosSelect.vue';
import GroupSelect from '/@/components/Xh/Organize/src/GroupSelect.vue';
import UsersSelect from '/@/components/Xh/Organize/src/UsersSelect.vue';
import UserSelectDropdown from '/@/components/Xh/Organize/src/UserSelectDropdown.vue';

// 项目业务组件
import ContractSelect from '/@/views/project/components/ContractSelect.vue';
import PaycontractSelect from '/@/views/project/components/PaycontractSelect.vue';
import CustomerSelect from '/@/views/project/components/CustomerSelect.vue';
import SupplierSelect from '/@/views/project/components/SupplierSelect.vue';
```

#### 3. 第三方组件路径

```typescript
// Ant Design Vue 组件
import { RangePicker } from 'ant-design-vue';
import { Button, Table, Form, Select, Input } from 'ant-design-vue';

// VueUse 工具
import { useDebounceFn, useThrottleFn } from '@vueuse/core';
```

#### 4. 工具和API导入路径

```typescript
// 工具函数
import { useMessage } from '/@/hooks/web/useMessage';
import { formatToDate } from '/@/utils/dateUtil';

// API接口
import { getContractList, createContract } from '/@/api/project/contract';
import { getPaycontractMoneyList } from '/@/api/project/paycontractMoney';

// 类型定义
import type { FormSchema } from '/@/components/Table';
import type { ContractModel } from '/@/api/project/contract';
```

### 导入路径规则

1. **使用绝对路径**：统一使用 `/@/` 开头的绝对路径，避免相对路径
2. **按类型分组**：将导入按类型分组，顺序为：
   - Vue 核心库导入
   - 类型导入
   - 第三方库组件
   - 项目基础组件
   - 项目业务组件
   - API和工具导入

3. **组件路径规范**：
   - 基础组件：`/@/components/[ComponentName]`
   - 组织架构组件：`/@/components/Xh/Organize/src/[ComponentName].vue`
   - 业务组件：`/@/views/[module]/components/[ComponentName].vue`

4. **命名规范**：
   - 组件文件使用 PascalCase 命名
   - 导入时保持原始命名
   - 避免重命名，除非有命名冲突

### 常用组件导入示例

```typescript
<script lang="ts" setup>
  // 1. Vue 核心库导入
  import { ref, computed, watch, onMounted } from 'vue';

  // 2. 类型导入
  import type { FormSchema } from '/@/components/Table';
  import type { ContractModel } from '/@/api/project/contract';

  // 3. 第三方库组件
  import { RangePicker } from 'ant-design-vue';
  import { useDebounceFn } from '@vueuse/core';

  // 4. 项目基础组件
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { BasicForm, useForm } from '/@/components/Form/index';
  import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';

  // 5. 项目业务组件
  import UserSelect from '/@/components/Xh/Organize/src/UserSelect.vue';
  import ContractSelect from '/@/views/project/components/ContractSelect.vue';

  // 6. API和工具导入
  import { getContractList, createContract } from '/@/api/project/contract';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { formatToDate } from '/@/utils/dateUtil';
</script>
```

### 注意事项

1. **路径一致性**：确保所有开发者使用相同的导入路径
2. **避免循环依赖**：组件之间避免相互导入造成循环依赖
3. **按需导入**：对于大型组件库，使用按需导入减少打包体积
4. **路径别名**：充分利用项目配置的路径别名，提高代码可读性

## 组件结构

### 组件文件结构

```vue
<template>
  <!-- 模板内容，保持简洁，复杂逻辑放在计算属性或方法中 -->
  <div class="component-container">
    <!-- 使用有语义的HTML标签 -->
    <header v-if="showHeader">{{ title }}</header>

    <!-- 条件渲染和列表渲染 -->
    <section v-if="hasContent">
      <ul>
        <li v-for="(item, index) in displayItems" :key="item.id || index">
          {{ item.name }}
        </li>
      </ul>
    </section>

    <!-- 事件处理 -->
    <footer>
      <button @click="handleSubmit">{{ submitButtonText }}</button>
    </footer>
  </div>
</template>

<script lang="ts" setup>
  // 1. 导入 - 按类型分组
  // 核心库导入
  import { ref, computed, watch, onMounted, nextTick } from 'vue';
  import type { PropType } from 'vue';

  // 类型导入
  import type { Item, FormState } from '@/types';

  // 组件导入
  import BaseButton from '@/components/Basic/BaseButton.vue';

  // API和工具导入
  import { fetchItems } from '@/api/modules/items';
  import { usePermission } from '@/hooks/usePermission';

  // 2. 组件选项
  defineOptions({
    name: 'ComponentName', // 必须与文件名匹配
    inheritAttrs: false,   // 当需要自定义属性传递时使用
  });

  // 3. Props、Emits定义 - 包含类型、默认值和验证
  const props = defineProps({
    title: {
      type: String,
      required: true,
    },
    items: {
      type: Array as PropType<Item[]>,
      default: () => [],
    },
    showHeader: {
      type: Boolean,
      default: true,
    },
    maxItems: {
      type: Number,
      default: 10,
      validator: (value: number) => value > 0,
    },
  });

  const emit = defineEmits(['update:modelValue', 'change', 'submit']);

  // 4. 组件状态 - 使用ref和reactive
  const loading = ref(false);
  const formState = reactive<FormState>({
    name: '',
    description: '',
    isActive: false,
  });

  // 5. 计算属性 - 派生状态
  const hasContent = computed(() => props.items.length > 0);
  const displayItems = computed(() => props.items.slice(0, props.maxItems));
  const submitButtonText = computed(() => loading.value ? '提交中...' : '提交');

  // 6. 方法 - 事件处理和业务逻辑
  function handleSubmit() {
    if (!hasContent.value) return;

    loading.value = true;
    // 业务逻辑处理
    emit('submit', formState);

    // 异步操作后重置状态
    nextTick(() => {
      loading.value = false;
    });
  }

  // 7. 监听器 - 响应props或状态变化
  watch(() => props.items, (newItems) => {
    console.log('Items changed:', newItems.length);
  }, { immediate: true });

  // 8. 生命周期钩子
  onMounted(() => {
    // 初始化逻辑
    console.log('Component mounted');
  });

  // 9. 暴露公共方法和属性（可选）
  defineExpose({
    reset: () => {
      Object.assign(formState, {
        name: '',
        description: '',
        isActive: false,
      });
    },
    loading,
  });
</script>

<style lang="less" scoped>
  /* 使用BEM命名规范 */
  .component-container {
    /* 布局样式 */
    display: flex;
    flex-direction: column;

    /* 使用项目定义的变量 */
    color: var(--text-color);
    background-color: var(--bg-color);

    /* 子元素样式 */
    header {
      font-weight: bold;
      margin-bottom: 16px;
    }

    section {
      /* 样式... */
    }

    footer {
      margin-top: 16px;
    }
  }
</style>
```

### 组件通信

1. **Props向下传递数据**:
   ```typescript
   // 推荐：使用完整的类型定义，包含默认值和验证
   const props = defineProps({
     // 字符串类型，必填
     title: {
       type: String,
       required: true,
     },

     // 数组类型，带默认值
     data: {
       type: Array as PropType<DataItem[]>,
       default: () => [],
       // 可选：添加自定义验证
       validator: (value: DataItem[]) => value.every(item => 'id' in item),
     },

     // 布尔类型，带默认值
     loading: {
       type: Boolean,
       default: false,
     },

     // 对象类型，带默认值
     config: {
       type: Object as PropType<Config>,
       default: () => ({
         showHeader: true,
         pageSize: 10,
       }),
     },

     // 联合类型
     status: {
       type: String as PropType<'active' | 'inactive' | 'pending'>,
       default: 'active',
       validator: (value: string) => ['active', 'inactive', 'pending'].includes(value),
     },

     // 函数类型
     formatter: {
       type: Function as PropType<(value: string) => string>,
       default: (value: string) => value,
     },
   });
   ```

2. **Emits向上传递事件**:
   ```typescript
   // 推荐：使用类型化的事件定义
   const emit = defineEmits<{
     // 简单事件，无参数
     (event: 'close'): void;
     // 带参数的事件
     (event: 'change', value: string): void;
     // 带多个参数的事件
     (event: 'select', item: Item, index: number): void;
     // 用于v-model的事件
     (event: 'update:modelValue', value: string): void;
   }>();

   // 使用
   function handleChange(value: string) {
     // 类型安全的事件触发
     emit('change', value);
   }

   function handleSelect(item: Item, index: number) {
     emit('select', item, index);
   }
   ```

3. **v-model实现双向绑定**:
   ```typescript
   // 单个v-model
   const props = defineProps({
     modelValue: {
       type: String,
       default: '',
     },
   });

   const emit = defineEmits(['update:modelValue']);

   // 计算属性方式实现
   const value = computed({
     get: () => props.modelValue,
     set: (val) => emit('update:modelValue', val),
   });

   // 多个v-model (Vue 3.2+)
   const props = defineProps({
     modelValue: {
       type: String,
       default: '',
     },
     visible: {
       type: Boolean,
       default: false,
     },
   });

   const emit = defineEmits(['update:modelValue', 'update:visible']);

   const value = computed({
     get: () => props.modelValue,
     set: (val) => emit('update:modelValue', val),
   });

   const isVisible = computed({
     get: () => props.visible,
     set: (val) => emit('update:visible', val),
   });
   ```

4. **使用provide/inject进行深层组件通信**:
   ```typescript
   // 在父组件中提供数据
   import { provide, ref } from 'vue';

   // 使用Symbol作为key，避免命名冲突
   export const ThemeKey = Symbol('theme');

   // 在父组件中
   const theme = ref('light');

   // 提供响应式数据
   provide(ThemeKey, {
     theme,
     toggleTheme: () => {
       theme.value = theme.value === 'light' ? 'dark' : 'light';
     },
   });

   // 在子组件中注入数据
   import { inject } from 'vue';
   import { ThemeKey } from './parent';

   // 使用默认值，确保类型安全
   const { theme, toggleTheme } = inject(ThemeKey, {
     theme: ref('light'),
     toggleTheme: () => {},
   });
   ```

## Composition API使用指南

### 响应式数据

1. **ref用于简单数据**:
   ```typescript
   // 基本类型
   const count = ref(0);
   const name = ref('');
   const isActive = ref(true);

   // 带类型注解的ref
   const count = ref<number>(0);
   const name = ref<string>('');

   // 复杂类型
   const user = ref<User | null>(null);

   // 访问和修改
   console.log(count.value); // 在模板中直接使用 count
   count.value++; // 修改值

   // 在模板中使用
   // <div>{{ count }}</div> - 不需要.value
   ```

2. **reactive用于对象数据**:
   ```typescript
   // 基本用法
   const state = reactive({
     user: {
       name: '',
       age: 0,
     },
     loading: false,
     items: [] as Item[],
   });

   // 使用接口定义类型
   interface UserState {
     user: {
       name: string;
       age: number;
     };
     loading: boolean;
     items: Item[];
   }

   const state = reactive<UserState>({
     user: {
       name: '',
       age: 0,
     },
     loading: false,
     items: [],
   });

   // 访问和修改
   console.log(state.user.name);
   state.loading = true;
   state.items.push(newItem);

   // 注意：避免解构reactive对象，会丢失响应性
   // 错误示例
   const { user } = state; // user不再是响应式的

   // 正确示例：使用toRefs保持响应性
   const { user } = toRefs(state);
   ```

3. **computed用于派生状态**:
   ```typescript
   // 只读计算属性
   const fullName = computed(() => {
     return `${state.user.firstName} ${state.user.lastName}`;
   });

   // 带类型注解
   const fullName = computed<string>(() => {
     return `${state.user.firstName} ${state.user.lastName}`;
   });

   // 可写计算属性
   const fullName = computed({
     get: () => `${state.user.firstName} ${state.user.lastName}`,
     set: (newValue: string) => {
       const names = newValue.split(' ');
       state.user.firstName = names[0] || '';
       state.user.lastName = names[1] || '';
     },
   });
   ```

4. **readonly和shallowRef/shallowReactive**:
   ```typescript
   // 创建只读的响应式对象
   const original = reactive({ count: 0 });
   const copy = readonly(original);

   // 浅层响应式 - 只有顶层属性是响应式的
   const state = shallowReactive({
     user: { name: 'John' }, // user的变化是响应式的，但user.name不是
   });

   // 对于大型不可变数据结构很有用
   const data = shallowRef(largeData);
   ```

### 生命周期钩子

```typescript
import {
  onMounted,
  onUpdated,
  onUnmounted,
  onBeforeMount,
  onBeforeUpdate,
  onBeforeUnmount,
  onActivated,
  onDeactivated,
  onErrorCaptured,
} from 'vue';

// 组件挂载前
onBeforeMount(() => {
  console.log('Component will be mounted');
});

// 组件挂载完成 - 最常用的钩子之一
onMounted(() => {
  // 初始化数据或DOM操作
  fetchData();
  initializeChart();

  // 可以访问DOM
  const element = document.getElementById('chart');
});

// 组件更新前
onBeforeUpdate(() => {
  // 更新前的处理
  // 例如：获取更新前的DOM状态
  const oldScrollPosition = scrollRef.value.scrollTop;
  scrollPositionRef.value = oldScrollPosition;
});

// 组件更新后
onUpdated(() => {
  // 更新后的处理
  // 例如：根据新数据调整DOM
  if (shouldScrollToBottom.value) {
    scrollToBottom();
  }
});

// 组件卸载前 - 清理资源的重要钩子
onBeforeUnmount(() => {
  // 清理操作
  clearInterval(timer);
  removeEventListener('resize', handleResize);
  disposeChart();
});

// 组件卸载后
onUnmounted(() => {
  console.log('Component has been unmounted');
});

// keep-alive组件激活时
onActivated(() => {
  // 当使用<keep-alive>包裹的组件被激活时调用
  refreshData();
});

// keep-alive组件停用时
onDeactivated(() => {
  // 当使用<keep-alive>包裹的组件被停用时调用
  pauseVideoPlayback();
});

// 错误捕获
onErrorCaptured((err, instance, info) => {
  // 捕获后代组件抛出的错误
  console.error(`Error captured: ${err}`);
  logError(err, info);
  // 返回false阻止错误继续传播
  return false;
});
```

### 监听器(watch/watchEffect)

```typescript
import { ref, watch, watchEffect } from 'vue';

const searchQuery = ref('');
const searchResults = ref([]);
const isLoading = ref(false);

// 基本watch - 监听单个数据源
watch(searchQuery, async (newValue, oldValue) => {
  if (newValue === oldValue) return;

  isLoading.value = true;
  try {
    searchResults.value = await fetchSearchResults(newValue);
  } catch (error) {
    console.error(error);
  } finally {
    isLoading.value = false;
  }
}, {
  immediate: true, // 立即执行一次
  deep: true,      // 深度监听（对象内部变化）
});

// 监听多个数据源
const firstName = ref('');
const lastName = ref('');

watch([firstName, lastName], ([newFirst, newLast], [oldFirst, oldLast]) => {
  console.log(`Name changed from ${oldFirst} ${oldLast} to ${newFirst} ${newLast}`);
});

// watchEffect - 自动跟踪依赖
watchEffect(async () => {
  // 会自动跟踪内部使用的响应式数据
  if (searchQuery.value.length > 2) {
    isLoading.value = true;
    try {
      searchResults.value = await fetchSearchResults(searchQuery.value);
    } finally {
      isLoading.value = false;
    }
  } else {
    searchResults.value = [];
  }
});

// 停止监听
const stopWatching = watch(searchQuery, () => {
  // ...
});

// 在需要时停止
stopWatching();

// 在组件卸载前自动停止
onBeforeUnmount(() => {
  stopWatching();
});
```

### 提取可复用逻辑(组合式函数)

```typescript
// 1. 基本组合式函数示例 - useCounter.ts
import { ref, Ref } from 'vue';

// 定义返回类型接口
interface UseCounterReturn {
  count: Ref<number>;
  increment: () => void;
  decrement: () => void;
  reset: (value?: number) => void;
}

export function useCounter(initialValue = 0): UseCounterReturn {
  const count = ref(initialValue);

  function increment() {
    count.value++;
  }

  function decrement() {
    count.value--;
  }

  function reset(value = initialValue) {
    count.value = value;
  }

  return {
    count,
    increment,
    decrement,
    reset,
  };
}

// 2. 处理异步请求的组合式函数 - useRequest.ts
import { ref, Ref } from 'vue';

interface UseRequestOptions<T> {
  immediate?: boolean;
  onSuccess?: (data: T) => void;
  onError?: (error: Error) => void;
}

interface UseRequestReturn<T, P extends any[]> {
  data: Ref<T | null>;
  loading: Ref<boolean>;
  error: Ref<Error | null>;
  execute: (...args: P) => Promise<T>;
  reset: () => void;
}

export function useRequest<T, P extends any[]>(
  requestFn: (...args: P) => Promise<T>,
  options: UseRequestOptions<T> = {}
): UseRequestReturn<T, P> {
  const data = ref<T | null>(null) as Ref<T | null>;
  const loading = ref(false);
  const error = ref<Error | null>(null);

  async function execute(...args: P): Promise<T> {
    loading.value = true;
    error.value = null;

    try {
      const result = await requestFn(...args);
      data.value = result;
      options.onSuccess?.(result);
      return result;
    } catch (err) {
      error.value = err as Error;
      options.onError?.(err as Error);
      throw err;
    } finally {
      loading.value = false;
    }
  }

  function reset() {
    data.value = null;
    error.value = null;
    loading.value = false;
  }

  // 如果设置了immediate，立即执行
  if (options.immediate) {
    execute([] as unknown as P);
  }

  return {
    data,
    loading,
    error,
    execute,
    reset,
  };
}

// 3. 组合式函数使用示例
import { useCounter } from '@/hooks/useCounter';
import { useRequest } from '@/hooks/useRequest';
import { fetchUserData } from '@/api/user';

// 在组件中使用
const { count, increment } = useCounter(10);

const {
  data: userData,
  loading: userLoading,
  error: userError,
  execute: fetchUser
} = useRequest(fetchUserData, {
  immediate: true,
  onSuccess: (data) => {
    console.log('User data loaded:', data);
  },
});

// 手动触发请求
function refreshUserData() {
  fetchUser(userId.value);
}
```

## 性能优化

1. **合理使用v-once和v-memo**:
   ```html
   <!-- 静态内容只渲染一次 -->
   <div v-once>这个内容只会渲染一次: {{staticContent}}</div>

   <!-- 有条件地跳过更新 -->
   <div v-memo="[item.id, item.active]">
     只有当item.id或item.active改变时才会重新渲染
   </div>

   <!-- 列表项使用v-memo优化 -->
   <div v-for="item in list" :key="item.id" v-memo="[item.completed]">
     <ItemComponent :item="item" />
   </div>
   ```

2. **大列表性能优化**:
   ```html
   <!-- 使用虚拟滚动组件 -->
   <VirtualList
     :items="largeList"
     :item-height="40"
     :buffer="10"
   >
     <template #default="{item}">
       <div>{{item.name}}</div>
     </template>
   </VirtualList>

   <!-- 或使用分页加载 -->
   <div>
     <DataTable :data="paginatedData" />
     <Pagination
       :total="totalItems"
       :current="currentPage"
       @change="handlePageChange"
     />
   </div>
   ```

3. **懒加载和代码分割**:
   ```typescript
   // 路由懒加载
   const routes = [
     {
       path: '/dashboard',
       component: () => import('./views/Dashboard.vue'),
       // 预加载
       // component: () => import(/* webpackPrefetch: true */ './views/Dashboard.vue'),
     },
     {
       path: '/settings',
       // 带注释的懒加载，便于调试
       component: () => import(/* webpackChunkName: "settings" */ './views/Settings.vue'),
     }
   ];

   // 组件懒加载
   const HeavyComponent = defineAsyncComponent({
     loader: () => import('./HeavyComponent.vue'),
     loadingComponent: LoadingComponent,
     errorComponent: ErrorComponent,
     delay: 200,
     timeout: 3000,
   });
   ```

4. **避免不必要的渲染**:
   ```typescript
   // 使用计算属性缓存结果
   const filteredItems = computed(() => {
     console.log('Computing filtered items'); // 只在依赖变化时执行
     return items.value.filter(item => item.active);
   });

   // 使用useMemo缓存复杂计算
   import { useMemo } from '@vueuse/core';

   const expensiveResult = useMemo(() => {
     return performExpensiveCalculation(propA.value, propB.value);
   }, [propA, propB]);

   // 使用shallowRef优化大型不可变数据
   const tableData = shallowRef(largeDataset);

   // 更新时替换整个引用
   function updateData(newData) {
     tableData.value = newData;
   }
   ```

5. **事件处理优化**:
   ```html
   <!-- 使用防抖/节流处理频繁事件 -->
   <input
     type="text"
     @input="debouncedSearch"
     placeholder="Search..."
   />

   <script setup>
   import { useDebounceFn } from '@vueuse/core';

   // 防抖处理搜索
   const debouncedSearch = useDebounceFn((e) => {
     searchQuery.value = e.target.value;
   }, 300);

   // 或使用节流处理滚动
   const throttledScroll = useThrottleFn(() => {
     // 处理滚动事件
   }, 100);
   </script>
   ```

6. **按需导入第三方库**:
   ```typescript
   // 按需导入lodash函数
   import debounce from 'lodash-es/debounce';
   import throttle from 'lodash-es/throttle';

   // 按需导入Ant Design Vue组件
   import { Button, Table, Form } from 'ant-design-vue';
   ```

## 组件通信最佳实践

1. **父子组件通信**:
   ```typescript
   // 父组件
   <template>
     <ChildComponent
       :data="parentData"
       @update="handleUpdate"
     />
   </template>

   <script setup>
   import { ref } from 'vue';
   import ChildComponent from './ChildComponent.vue';

   const parentData = ref({ id: 1, name: 'Parent Data' });

   function handleUpdate(newData) {
     console.log('Child component updated:', newData);
     // 更新父组件数据
   }
   </script>

   // 子组件
   <template>
     <div>
       <button @click="updateParent">更新</button>
     </div>
   </template>

   <script setup>
   const props = defineProps({
     data: {
       type: Object,
       required: true,
     },
   });

   const emit = defineEmits(['update']);

   function updateParent() {
     emit('update', { id: props.data.id, updated: true });
   }
   </script>
   ```

2. **兄弟组件通信**:
   ```typescript
   // 方法1: 通过共同的父组件
   // 父组件
   <template>
     <SiblingA :sharedData="sharedState" @update="updateSharedState" />
     <SiblingB :sharedData="sharedState" />
   </template>

   <script setup>
   import { ref } from 'vue';

   const sharedState = ref({ count: 0 });

   function updateSharedState(newState) {
     sharedState.value = { ...sharedState.value, ...newState };
   }
   </script>

   // 方法2: 通过Pinia store
   // store/counter.ts
   import { defineStore } from 'pinia';

   export const useCounterStore = defineStore('counter', {
     state: () => ({
       count: 0,
     }),
     actions: {
       increment() {
         this.count++;
       },
     },
   });

   // SiblingA.vue
   <script setup>
   import { useCounterStore } from '@/store/counter';

   const counterStore = useCounterStore();

   function incrementCounter() {
     counterStore.increment();
   }
   </script>

   // SiblingB.vue
   <script setup>
   import { useCounterStore } from '@/store/counter';
   import { storeToRefs } from 'pinia';

   const counterStore = useCounterStore();
   // 解构并保持响应性
   const { count } = storeToRefs(counterStore);
   </script>
   ```

3. **深层组件通信**:
   ```typescript
   // 方法1: 使用provide/inject
   // 根组件
   <script setup>
   import { provide, ref } from 'vue';

   const theme = ref('light');

   provide('theme', {
     theme,
     toggleTheme: () => {
       theme.value = theme.value === 'light' ? 'dark' : 'light';
     },
   });
   </script>

   // 深层子组件
   <script setup>
   import { inject } from 'vue';

   const { theme, toggleTheme } = inject('theme', {
     theme: ref('light'),
     toggleTheme: () => {},
   });
   </script>

   // 方法2: 使用Pinia全局状态
   // 任何组件都可以访问
   <script setup>
   import { useAppStore } from '@/store/app';

   const appStore = useAppStore();
   </script>
   ```

4. **事件总线替代方案**:
   ```typescript
   // 使用mitt作为轻量级事件总线
   // eventBus.ts
   import mitt from 'mitt';

   export const eventBus = mitt();

   // 组件A - 发送事件
   import { eventBus } from '@/utils/eventBus';

   function sendMessage() {
     eventBus.emit('message', { text: 'Hello from Component A' });
   }

   // 组件B - 监听事件
   import { eventBus } from '@/utils/eventBus';
   import { onBeforeUnmount, onMounted } from 'vue';

   onMounted(() => {
     eventBus.on('message', handleMessage);
   });

   onBeforeUnmount(() => {
     eventBus.off('message', handleMessage);
   });

   function handleMessage(data) {
     console.log('Received:', data.text);
   }
   ```

## 组件文档规范

每个重要组件应该有相应的文档注释:

```typescript
/**
 * @component DataTable
 * @description 数据表格组件，支持排序、筛选、分页等功能
 *
 * @example
 * <DataTable
 *   :columns="columns"
 *   :data-source="dataSource"
 *   :loading="loading"
 *   @change="handleTableChange"
 * />
 *
 * @props {TableColumn[]} columns - 表格列配置
 * @props {Record<string, any>[]} dataSource - 数据源
 * @props {boolean} [loading=false] - 加载状态，默认为false
 * @props {boolean} [showPagination=true] - 是否显示分页，默认为true
 * @props {PaginationConfig} [pagination] - 分页配置
 * @props {SorterConfig} [sorter] - 排序配置
 * @props {FilterConfig} [filters] - 筛选配置
 *
 * @emits {(event: 'change', pagination: PaginationConfig, filters: Record<string, any>, sorter: SorterConfig) => void} change - 表格数据变化事件
 * @emits {(event: 'select', selectedRows: Record<string, any>[], selectedRowKeys: string[]) => void} select - 选择行事件
 * @emits {(event: 'expand', expanded: boolean, record: Record<string, any>) => void} expand - 展开行事件
 *
 * @slots default - 自定义表格内容
 * @slots header - 自定义表头
 * @slots footer - 自定义表尾
 * @slots expandedRow - 自定义展开行
 *
 * @see 相关组件 {@link Pagination}
 * @see 相关组件 {@link TableColumn}
 */
```

## 常见错误与解决方法

1. **响应式丢失**:
   - **问题**: 修改响应式对象但UI未更新
   - **原因**:
     - 解构reactive对象导致响应性丢失
     - 直接修改ref对象而不是.value
     - 直接替换reactive对象的属性
   - **解决方案**:
     - 使用toRefs/toRef保持解构的响应性
     - 确保使用.value访问和修改ref
     - 使用Object.assign或扩展运算符更新reactive对象

   ```typescript
   // 错误示例
   const state = reactive({ count: 0 });
   const { count } = state; // 丢失响应性
   count++; // 不会触发更新

   // 正确示例
   const state = reactive({ count: 0 });
   const { count } = toRefs(state); // 保持响应性
   count.value++; // 正确更新

   // 或者直接使用
   state.count++; // 正确更新
   ```

2. **组件不更新**:
   - **问题**: 改变了数据但组件未重新渲染
   - **原因**:
     - 数组/对象的变更检测限制
     - 未正确触发响应式系统
   - **解决方案**:
     - 使用响应式API正确修改数据
     - 使用nextTick等待DOM更新
     - 检查key绑定是否正确

   ```typescript
   // 错误示例
   const arr = reactive([1, 2, 3]);
   arr[0] = 4; // 虽然是响应式的，但可能在某些情况下不触发更新

   // 更可靠的方式
   arr.splice(0, 1, 4);
   // 或
   arr.value = [4, ...arr.slice(1)];
   ```

3. **内存泄漏**:
   - **问题**: 组件销毁但事件监听器或定时器未清理
   - **原因**:
     - 未在组件卸载时清理资源
     - 全局事件监听未移除
   - **解决方案**:
     - 在onBeforeUnmount钩子中清理资源
     - 使用组合式函数自动处理清理

   ```typescript
   // 正确示例
   onMounted(() => {
     const timer = setInterval(() => {
       // 定时任务
     }, 1000);

     window.addEventListener('resize', handleResize);

     // 清理函数
     onBeforeUnmount(() => {
       clearInterval(timer);
       window.removeEventListener('resize', handleResize);
     });
   });

   // 或使用VueUse自动清理
   import { useEventListener, useIntervalFn } from '@vueuse/core';

   // 自动在组件卸载时清理
   useEventListener(window, 'resize', handleResize);

   const { pause } = useIntervalFn(() => {
     // 定时任务
   }, 1000);
   ```

4. **Props类型错误**:
   - **问题**: 组件接收到类型不符的props
   - **解决方案**:
     - 使用TypeScript类型定义
     - 添加运行时验证
     - 使用默认值和转换函数

   ```typescript
   const props = defineProps({
     value: {
       type: Number,
       required: true,
       validator: (val) => val >= 0, // 运行时验证
     },
     items: {
       type: Array,
       default: () => [], // 默认值
     },
   });
   ```

## 表格组件规范

### BasicTable 使用规范

#### ⚠️ 重要：操作列配置

**避免重复配置操作列**：
- 使用 `actionColumn` 配置操作列，**不要**在 `columns` 中再次定义操作列
- 这样会导致操作列显示两次

**✅ 正确的配置：**
```typescript
const columns = [
  {
    title: '序号',
    dataIndex: 'index',
    width: 80,
    customRender: ({ index }) => index + 1,
  },
  {
    title: '名称',
    dataIndex: 'name',
    width: 150,
  },
  // 不要在这里定义操作列
];

// 表格配置
const [registerTable] = useTable({
  columns,
  actionColumn: {
    width: 200,
    title: '操作',
    dataIndex: 'action',
  },
});
```

**❌ 错误的配置：**
```typescript
const columns = [
  // 其他列...
  {
    title: '操作',        // 错误！会导致重复显示
    dataIndex: 'action',
    key: 'action',
    width: 200,
    fixed: 'right' as const,
  },
];

// 同时又配置了 actionColumn
const [registerTable] = useTable({
  columns,
  actionColumn: {      // 这会导致操作列显示两次
    width: 200,
    title: '操作',
    dataIndex: 'action',
  },
});
```

### 下拉选择组件规范

#### ⚠️ 重要：选项数据格式

**项目中统一使用 `fullName` 和 `id` 格式**：

**✅ 正确的选项格式：**
```typescript
const options = [
  { fullName: '全部', id: '' },
  { fullName: '未收款', id: '0' },
  { fullName: '已收款', id: '1' },
];

// 在表单配置中
{
  field: 'status',
  label: '状态',
  component: 'Select',
  componentProps: {
    options: [
      { fullName: '全部', id: '' },
      { fullName: '未收款', id: '0' },
      { fullName: '已收款', id: '1' },
    ],
    placeholder: '请选择状态',
    allowClear: true,
  },
}
```

**❌ 错误的选项格式：**
```typescript
// 不要使用 label 和 value
const options = [
  { label: '全部', value: '' },        // 错误！
  { label: '未收款', value: '0' },     // 错误！
  { label: '已收款', value: '1' },     // 错误！
];
```

#### 状态值规范

**数字状态统一使用字符串格式**：
- 后端返回数字状态时，前端统一转换为字符串处理
- 选项值使用字符串格式，便于统一处理

```typescript
// 状态转换函数
function getStatusText(status: string | number) {
  const statusStr = String(status);
  const textMap = {
    '0': '未收款',
    '1': '已收款',
  };
  return textMap[statusStr] || status;
}

// 状态颜色函数
function getStatusColor(status: string | number) {
  const statusStr = String(status);
  const colorMap = {
    '0': 'red',   // 未收款
    '1': 'green', // 已收款
  };
  return colorMap[statusStr] || 'default';
}
```

## 总结

Vue 3 组合式API提供了更好的逻辑复用、类型推导和代码组织方式。通过遵循这些规范，可以编写出更加清晰、可维护和高性能的Vue组件。记住始终考虑组件的可复用性、可测试性和性能表现。

### 关键要点

1. **表格组件**：避免重复配置操作列，使用 `actionColumn` 统一配置
2. **选择组件**：统一使用 `fullName` 和 `id` 格式，不使用 `label` 和 `value`
3. **状态处理**：数字状态统一转换为字符串处理，便于维护
4. **类型安全**：充分利用 TypeScript 的类型检查能力
5. **性能优化**：合理使用响应式API，避免不必要的重新渲染