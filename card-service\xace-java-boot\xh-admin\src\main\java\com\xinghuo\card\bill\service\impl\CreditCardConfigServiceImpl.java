package com.xinghuo.card.bill.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xinghuo.card.bill.dao.CreditCardConfigMapper;
import com.xinghuo.card.bill.entity.CreditCardConfigEntity;
import com.xinghuo.card.bill.service.CreditCardConfigService;
import com.xinghuo.common.util.core.RandomUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 信用卡配置服务实现类
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2024-01-01
 */
@Slf4j
@Service
public class CreditCardConfigServiceImpl extends ServiceImpl<CreditCardConfigMapper, CreditCardConfigEntity> 
        implements CreditCardConfigService {

    @Override
    public List<CreditCardConfigEntity> getByUserId(String userId) {
        QueryWrapper<CreditCardConfigEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(CreditCardConfigEntity::getUserId, userId)
                .eq(CreditCardConfigEntity::getDeleteFlag, 0)
                .orderByAsc(CreditCardConfigEntity::getListOrder);

        return this.list(queryWrapper);
    }

    @Override
    public CreditCardConfigEntity getByAccId(String accId) {
        QueryWrapper<CreditCardConfigEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(CreditCardConfigEntity::getAccId, accId)
                .eq(CreditCardConfigEntity::getDeleteFlag, 0);

        return this.getOne(queryWrapper);
    }

    @Override
    public List<CreditCardConfigEntity> getAutoGenerateConfigs() {
        QueryWrapper<CreditCardConfigEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(CreditCardConfigEntity::getAutoGenerateBill, true)
                .eq(CreditCardConfigEntity::getCardStatus, 1) // 正常状态
                .eq(CreditCardConfigEntity::getDeleteFlag, 0);

        return this.list(queryWrapper);
    }

    @Override
    public boolean validateConfig(CreditCardConfigEntity config) {
        // 验证必填字段
        if (StringUtils.isBlank(config.getUserId()) || 
            StringUtils.isBlank(config.getAccId()) ||
            StringUtils.isBlank(config.getCardName())) {
            log.warn("信用卡配置验证失败：缺少必填字段");
            return false;
        }

        // 验证账单日和还款日
        if (config.getBillDay() == null || config.getBillDay() < 1 || config.getBillDay() > 31) {
            log.warn("信用卡配置验证失败：账单日无效");
            return false;
        }

        if (config.getRepaymentDay() == null || config.getRepaymentDay() < 1 || config.getRepaymentDay() > 31) {
            log.warn("信用卡配置验证失败：还款日无效");
            return false;
        }

        // 验证信用额度
        if (config.getCreditLimit() == null || config.getCreditLimit().compareTo(BigDecimal.ZERO) <= 0) {
            log.warn("信用卡配置验证失败：信用额度无效");
            return false;
        }

        // 检查账户ID是否已被使用
        if (StringUtils.isNotBlank(config.getId())) {
            // 更新时排除自己
            QueryWrapper<CreditCardConfigEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda()
                    .eq(CreditCardConfigEntity::getAccId, config.getAccId())
                    .ne(CreditCardConfigEntity::getId, config.getId())
                    .eq(CreditCardConfigEntity::getDeleteFlag, 0);
            
            if (this.count(queryWrapper) > 0) {
                log.warn("信用卡配置验证失败：账户ID已被使用");
                return false;
            }
        } else {
            // 新增时检查
            QueryWrapper<CreditCardConfigEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda()
                    .eq(CreditCardConfigEntity::getAccId, config.getAccId())
                    .eq(CreditCardConfigEntity::getDeleteFlag, 0);
            
            if (this.count(queryWrapper) > 0) {
                log.warn("信用卡配置验证失败：账户ID已被使用");
                return false;
            }
        }

        return true;
    }

    @Override
    public boolean createConfig(CreditCardConfigEntity config) {
        if (!validateConfig(config)) {
            return false;
        }

        // 设置默认值
        config.setId(RandomUtil.snowId());
        config.setCreateTime(new Date());
        config.setDeleteFlag(0);
        
        if (config.getCardStatus() == null) {
            config.setCardStatus(1); // 默认正常状态
        }
        
        if (config.getAutoGenerateBill() == null) {
            config.setAutoGenerateBill(true); // 默认启用自动生成账单
        }
        
        if (config.getPaymentReminder() == null) {
            config.setPaymentReminder(true); // 默认启用还款提醒
        }
        
        if (config.getReminderDays() == null) {
            config.setReminderDays(3); // 默认提前3天提醒
        }
        
        if (config.getInterestFreeDays() == null) {
            config.setInterestFreeDays(20); // 默认20天免息期
        }
        
        if (config.getFreeAnnualFee() == null) {
            config.setFreeAnnualFee(false); // 默认不免年费
        }

        return this.save(config);
    }

    @Override
    public boolean updateConfig(CreditCardConfigEntity config) {
        if (!validateConfig(config)) {
            return false;
        }

        config.setUpdateTime(new Date());
        return this.updateById(config);
    }

    @Override
    public boolean deleteConfig(String configId) {
        CreditCardConfigEntity config = this.getById(configId);
        if (config == null) {
            return false;
        }

        // 软删除
        config.setDeleteFlag(1);
        config.setUpdateTime(new Date());
        
        return this.updateById(config);
    }

    @Override
    public boolean toggleAutoGenerate(String configId, boolean enable) {
        CreditCardConfigEntity config = this.getById(configId);
        if (config == null) {
            return false;
        }

        config.setAutoGenerateBill(enable);
        config.setUpdateTime(new Date());
        
        return this.updateById(config);
    }
}
