package com.xinghuo.card.sys.service;

import com.alicp.jetcache.anno.CacheInvalidate;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.CacheUpdate;
import com.alicp.jetcache.anno.Cached;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xinghuo.card.sys.entity.DataAccEntity;
import com.xinghuo.card.sys.model.dataacc.DataAccPagination;

import java.util.List;

/**
 * 个人账号
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2022-12-02
 */
public interface DataAccService extends IService<DataAccEntity> {

    String CACHE_NAME = "dataAcc";

    List<DataAccEntity> getList(boolean showNormalAcc) ;

    @Cached(name = CACHE_NAME, key = "selectList" ,cacheType = CacheType.LOCAL, expire = 120)
    List<DataAccEntity> getSelectList();


    /**
     * 查询分页数据
     *
     * @param dataAccPagination 查询对象
     * @return 查询结果
     */
    List<DataAccEntity> getList(DataAccPagination dataAccPagination);

    /**
     * 查询分页或者不分页列表
     *
     * @param dataAccPagination 查询对象
     * @param dataType          0:分页 1:不分页
     * @return 查询结果
     */
    List<DataAccEntity> getTypeList(DataAccPagination dataAccPagination, int dataType);

    /**
     * 获取DataAccEntity详细信息
     *
     * @param id   主键
     */
    @Cached(name = CACHE_NAME, key = "#id" ,cacheType = CacheType.LOCAL, expire = 360)
    DataAccEntity getInfo(String id);

    /**
     * 删除
     *
     * @param entity 删除的对象
     */
    @CacheInvalidate(name = CACHE_NAME, key = "#id")
    void delete(DataAccEntity entity);

    /**
     * 新增保存
     *
     * @param entity 新增的对象
     */
    void create(DataAccEntity entity);

    /**
     * 修改保存
     *
     * @param id     主键
     * @param entity 修改的对象
     */
    @CacheUpdate(name = CACHE_NAME, key = "#id",value = "#entity")
    boolean update(String id, DataAccEntity entity);


}
