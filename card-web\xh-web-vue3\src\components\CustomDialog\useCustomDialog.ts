import { useCustomDialogStore } from '/@/store/modules/customDialog';
import type { CustomDialogParams } from '/@/components/CustomDialog/types';

export const useCustomDialog = () => {
  const customDialogStore = useCustomDialogStore();

  const addDialog = (option: Partial<CustomDialogParams>): string => {
    return customDialogStore.addDialog(option);
  };

  const getDialog = (id: string) => {
    return customDialogStore.getDialog(id);
  };

  const closeDialog = (id: string) => {
    return customDialogStore.closeDialog(id);
  };
  return {
    addDialog,
    getDialog,
    closeDialog,
  };
};
