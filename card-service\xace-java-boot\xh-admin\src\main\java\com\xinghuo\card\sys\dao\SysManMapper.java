package com.xinghuo.card.sys.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xinghuo.card.sys.entity.SysManEntity;
import com.xinghuo.common.base.dao.XHBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 持卡账户管理Mapper接口
 * 用于管理持卡人的基本信息和代称
 * 支持多持卡人管理，每个持卡人有唯一的代称标识
 *
 * <AUTHOR> Assistant
 * @version V1.0.0
 * @date 2024-12-29
 */
@Mapper
public interface SysManMapper extends XHBaseMapper<SysManEntity> {

}
