#parse("PublicMacro/FormMarco.vm")
<template>
##    全屏弹窗
#if(${context.popupType}=="fullScreen")
##<!-- 全屏弹窗 -->
    <BasicPopup v-bind="$attrs" @register="registerPopup" :title="title" showOkBtn
                    :showContinueBtn="showContinueBtn" :continueText="continueText" destroyOnClose
                    #if(${context.CancelButton}) cancelText="${context.CancelButton}"#end
                    #if(${context.ConfirmButton}) okText="${context.ConfirmButton}"#end
                    @ok="handleSubmit(0)" @continue="handleSubmit(1)" :closeFunc="onClose">
        #if(!${context.groupTable} || !${context.treeTable})
##      <!-- 分组和树形不展示 -->
        <template #insertToolbar>
            <a-space :size="10" v-if="dataForm.id">
                <a-button :disabled="getPrevDisabled" @click="handlePrev">上一条</a-button>
                <a-button :disabled="getNextDisabled" @click="handleNext">下一条</a-button>
            </a-space>
        </template>
        #end
        <a-row class="p-10px dynamic-form ${context.formStyle}" :style="{ margin: '0 auto', width: '${context.fullScreenWidth}' }">
            <a-form :colon="false" size="${context.size}" layout=#if(${context.labelPosition}=="top") "vertical" #else "horizontal" #end
                labelAlign=#if(${context.labelPosition}=="right") "right" #else "left" #end
                #if(${context.labelPosition}!="top") :labelCol="{ style: { width: '${context.labelWidth}px' } }" #end
                :model="dataForm" :rules="dataRule" ref="formRef" >
                <a-row :gutter="#if(${context.formStyle}=='word-form')0#else${context.gutter}#end">
                    <!-- 具体表单 -->
                    #FormRendering()
                    <!-- 表单结束 -->
                </a-row>
            </a-form>
        </a-row>
    </BasicPopup>
##<!-- 全屏弹窗 -->
#end
##    普通弹窗
#if(${context.popupType}=="general")
##<!-- 普通弹窗 -->
    <BasicModal v-bind="$attrs" @register="registerModal" :title="title" width="${context.generalWidth}"
                :minHeight="100" :showContinueBtn="showContinueBtn" :continueText="continueText"
                #if(${context.CancelButton}) cancelText="${context.CancelButton}"#end
                #if(${context.ConfirmButton}) okText="${context.ConfirmButton}"#end
                @ok="handleSubmit(0)" @continue="handleSubmit(1)" :closeFunc="onClose">
    #if(!${context.groupTable} || !${context.treeTable})
##        <!-- 分组和树形不展示 -->
        <template #insertFooter>
            <a-space :size="10" v-if="dataForm.id" class="float-left">
                <a-button :disabled="getPrevDisabled" @click="handlePrev">上一条</a-button>
                <a-button :disabled="getNextDisabled" @click="handleNext">下一条</a-button>
            </a-space>
        </template>
##        <!-- 分组和树形不展示 -->
    #end
        <a-row class="dynamic-form ${context.formStyle}">
            <a-form :colon="false" size="${context.size}" layout=#if(${context.labelPosition}=="top") "vertical" #else "horizontal" #end
            labelAlign=#if(${context.labelPosition}=="right") "right" #else "left" #end
            #if(${context.labelPosition}!="top") :labelCol="{ style: { width: '${context.labelWidth}px' } }" #end
            :model="dataForm" :rules="dataRule" ref="formRef">
            <a-row :gutter="#if(${context.formStyle}=='word-form')0#else${context.gutter}#end">
                <!-- 具体表单 -->
                #FormRendering()
                <!-- 表单结束 -->
            </a-row>
            </a-form>
        </a-row>
    </BasicModal>
##<!-- 普通弹窗 -->
#end
##    右侧弹窗
#if(${context.popupType}=="drawer")
##<!-- 右侧弹窗 -->
    <BasicDrawer v-bind="$attrs" @register="registerDrawer" :title="title" width="${context.drawerWidth}" showFooter
                :showContinueBtn="showContinueBtn" :continueText="continueText"
                #if(${context.CancelButton}) cancelText="${context.CancelButton}"#end
                #if(${context.ConfirmButton}) okText="${context.ConfirmButton}"#end
                 @ok="handleSubmit(0)" @continue="handleSubmit(1)" :closeFunc="onClose">
    #if(!${context.groupTable} || !${context.treeTable})
##        <!-- 分组和树形不展示 -->
        <template #insertFooter>
            <a-space :size="10" v-if="dataForm.id">
                <a-button :disabled="getPrevDisabled" @click="handlePrev">上一条</a-button>
                <a-button :disabled="getNextDisabled" @click="handleNext">下一条</a-button>
            </a-space>
        </template>
##        <!-- 分组和树形不展示 -->
    #end
        <a-row class="p-10px dynamic-form ${context.formStyle}">
            <!-- 表单 -->
            <a-form :colon="false" size="${context.size}" layout=#if(${context.labelPosition}=="top") "vertical" #else "horizontal" #end
                labelAlign=#if(${context.labelPosition}=="right") "right" #else "left" #end
                #if(${context.labelPosition}!="top") :labelCol="{ style: { width: '${context.labelWidth}px' } }" #end
                :model="dataForm" :rules="dataRule" ref="formRef" >
                <a-row :gutter="#if(${context.formStyle}=='word-form')0#else${context.gutter}#end">
                    <!-- 具体表单 -->
                    #FormRendering()
                    <!-- 表单结束 -->
                </a-row>
            </a-form>
        </a-row>
    </BasicDrawer>
##<!-- 右侧弹窗 -->
#end
#if($isSelectDialog == true)
    <SelectModal :config="state.currTableConf" :formData="state.dataForm" ref="selectModal" @select="addForSelect"/>
#end
</template>
<script lang="ts" setup>
    import {create, getInfo, update} from './helper/api';
    import {computed, nextTick, reactive, ref, toRefs, unref} from 'vue';
    import {usePopup} from '/@/components/Popup';
    import {useModal} from '/@/components/Modal';
    import {useDrawer} from '/@/components/Drawer';
    import {useMessage} from '/@/hooks/web/useMessage';
    import {useUserStore} from '/@/store/modules/user';
    import type {FormInstance} from 'ant-design-vue';
    // 表单权限
    import {usePermission} from '/@/hooks/web/usePermission';
        #if(${context.popupType}=="fullScreen")
        #end
    #if(${context.popupType}=="general")
    #end
    #if(${context.popupType}=="drawer")
    #end
        #if($isSelectDialog == true)
        #end

    interface State {
        #createStateParam("any")
        title: string;
        continueText: string;
        allList: any[];
        currIndex: number;
        isContinue: boolean;
        submitType: number;
        showContinueBtn: boolean;
    }

    const emit = defineEmits(['reload']);
    const userStore = useUserStore();
    const userInfo = userStore.getUserInfo;
    const { createMessage, createConfirm } = useMessage();
    #if(${context.popupType}=="fullScreen")
    const [registerPopup, { openPopup, setPopupProps }] = usePopup();
    #end
    #if(${context.popupType}=="general")
    const [registerModal, { openModal, setModalProps }] = useModal();
    #end
    #if(${context.popupType}=="drawer")
    const [registerDrawer, { openDrawer, setDrawerProps }] = useDrawer();
    #end
    const formRef = ref<FormInstance>();
    #if($isSelectDialog == true)
    // 子表弹窗数据
    const selectModal = ref(null);
    #end
    #GetChildTableColumns()
    const state = reactive<State>({
        #createStateParam()
        title: "",
        continueText: "",
        allList: [],
        currIndex: 0,
        isContinue: false,
        submitType: 0,
        showContinueBtn: true,
    });
    const { title, continueText, showContinueBtn, dataRule, dataForm, optionsObj, ableAll } = toRefs(state);

    const getPrevDisabled = computed(() => state.currIndex === 0);
    const getNextDisabled = computed(() => state.currIndex === state.allList.length - 1);
    // 表单权限
    const { hasFormP } = usePermission();

    defineExpose({ init });

    function init(data) {
        state.isContinue = false;
        state.title = !data.id ? '新增' : '编辑';
        state.continueText = !data.id ? '确定并新增' : '确定并继续';
        setFormProps({ continueLoading: false });
        state.dataForm.id = data.id;
    #if(${context.popupType}=="fullScreen")
        openPopup();
    #end
    #if(${context.popupType}=="general")
        openModal();
    #end
    #if(${context.popupType}=="drawer")
        openDrawer();
    #end
        state.allList = data.allList;
        state.currIndex = state.allList.length && data.id ? state.allList.findIndex((item) => item.id === data.id) : 0;
        nextTick(() => {
            getForm().resetFields();
        #foreach($child in ${context.children})
            state.dataForm.${child.aliasLowName}List = [];
        #end
            setTimeout(initData, 0);
        });
    }
    function initData() {
        changeLoading(true);
        if (state.dataForm.id) {
            getData(state.dataForm.id);
        } else {
            //初始化options
            #EditGetOption(false)
            // 设置默认值
            state.dataForm={
                #CreateDataform()
            };
            state.childIndex = -1;
            changeLoading(false);
        }
    }
    function getForm() {
        const form = unref(formRef);
        if (!form) {
            throw new Error('form is null!');
        }
        return form;
    }
    function getData(id) {
        getInfo(id).then((res) => {
            state.dataForm = res.data || {};
##          初始化options
            #EditGetOption(true)
            state.childIndex = -1;
            changeLoading(false);
        });
    }
    async function handleSubmit(type) {
        try {
            const values = await getForm()?.validate();
            if (!values) return;
            state.submitType = type;
            state.submitType === 1 ? setFormProps({ continueLoading: true }) : setFormProps({ confirmLoading: true });
            const formMethod = state.dataForm.id ? update : create;
            formMethod(state.dataForm)
                    .then((res) => {
                        createMessage.success(res.msg);
                        state.submitType === 1 ? setFormProps({ continueLoading: false }) : setFormProps({ confirmLoading: false });
                        if (state.submitType == 1) {
                            initData();
                            state.isContinue = true;
                        } else {
                            setFormProps({ visible: false });
                            emit('reload');
                        }
                    })
                    .catch(() => {
                        state.submitType === 1 ? setFormProps({ continueLoading: false }) : setFormProps({ confirmLoading: false });
                    });
        } catch (_) {}
    }
    function handlePrev() {
        state.currIndex--;
        handleGetNewInfo();
    }
    function handleNext() {
        state.currIndex++;
        handleGetNewInfo();
    }
    function handleGetNewInfo() {
        changeLoading(true);
        getForm().resetFields();
        const id = state.allList[state.currIndex].id;
        getData(id);
    }
    function setFormProps(data) {
    #if(${context.popupType}=="fullScreen")
        setPopupProps(data);
    #end
    #if(${context.popupType}=="general")
        setModalProps(data);
    #end
    #if(${context.popupType}=="drawer")
        setDrawerProps(data);
    #end
    }
    function changeLoading(loading) {
    #if(${context.popupType}=="fullScreen")
        setPopupProps({ loading });
    #end
    #if(${context.popupType}=="general")
        setModalProps({ loading });
    #end
    #if(${context.popupType}=="drawer")
        setDrawerProps({ loading });
    #end
    }
    async function onClose() {
        if (state.isContinue) emit('reload');
        return true;
    }

    ##合计方法
    #if($childSummary==true)
    //子表合计方法
    function getCmpValOfRow(row, key, summaryField) {
        if (!summaryField.length) return '';
        const isSummary = key => summaryField.includes(key);
        const target = row[key];
        if (!target) return '';
        let data = isNaN(target) ? 0 : Number(target);
        if (isSummary(key)) return data || 0;
        return '';
    }
    #end
    ##数据联动changeData方法
    #ChangeData()
    ##子表其他方法
    #CreateChildTableMethod()
    ##子表弹窗数据方法
    #if($isSelectDialog == true)
    #ChildDialogMethod()
    #end
##数据选项--数据字典和远端数据初始化方法
    #GetDataOptionsMethod()
##动态时间处理
    #GetRelationDate()
</script>
