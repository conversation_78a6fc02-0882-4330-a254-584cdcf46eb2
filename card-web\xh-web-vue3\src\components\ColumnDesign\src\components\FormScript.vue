<template>
  <BasicModal
    v-bind="$attrs"
    @register="registerModal"
    :title="title"
    helpMessage="小程序不支持在线JS脚本"
    :width="1000"
    @ok="handleSubmit"
    @fullscreen-change="fullscreenChange"
    destroyOnClose
    canFullscreen
    defaultFullscreen
    class="form-script-modal">
    <div class="form-script-modal-body">
      <div class="main-board">
        <div class="main-board-editor">
          <MonacoEditor v-if="isOK" ref="editorRef" :provideHover="provideHover" v-model="text" />
        </div>
        <div class="main-board-tips">
          <p>支持JavaScript的脚本</p>
          <template v-if="funcName === 'afterOnload'">
            <p>data--列表行数据，tableRef--表格DOM元素</p>
            <p><a @click="showDemo('request')">request</a>--异步请求(url,method,data)</p>
            <p><a @click="showDemo('searchInfo')">searchInfo</a>--获取menudId和modelId以及搜索框内容</p>
            <p><a @click="showDemo('setDisabledListRow')">setDisabled</a>--设置按钮禁用</p>
            <p><a @click="showDemo('setsetShowOrHideListRow')">setShowOrHide</a>--设置按钮显示</p>
            <p><a @click="showDemo('useCustomDialog')">useCustomDialog</a>--自定义设置弹窗</p>
          </template>
          <template v-if="funcName === 'rowStyle'">
            <p>record--列表行数据，index--列表行下标</p>
            <p>
              <a @click="showDemo('request')">request</a>--异步请求(url,method,data)， <a @click="showDemo('toast')">toast</a>--加载动画，
              <a @click="showDemo('confirm')">confirm</a>--确认弹窗，<a @click="showDemo('refresh')">refresh</a>--刷新页面
            </p>
            <p><a @click="showDemo('searchInfo')">searchInfo</a>--获取menudId和modelId以及搜索框内容</p>
            <p><a @click="showDemo('useCustomDialog')">useCustomDialog</a>--自定义设置弹窗</p>
          </template>
          <template v-if="funcName === 'cellStyle'">
            <p>record--列表行数据，column--列表列数据</p>
            <p>rowIndex--列表行下标</p>
            <p>
              <a @click="showDemo('request')">request</a>--异步请求(url,method,data)， <a @click="showDemo('toast')">toast</a>--加载动画，
              <a @click="showDemo('confirm')">confirm</a>--确认弹窗，<a @click="showDemo('refresh')">refresh</a>--刷新页面
            </p>
            <p><a @click="showDemo('searchInfo')">searchInfo</a>--获取menudId和modelId以及搜索框内容</p>
            <p><a @click="showDemo('useCustomDialog')">useCustomDialog</a>--自定义设置弹窗</p>
          </template>
        </div>
      </div>
    </div>
    <CommonHelp @register="registerHelpModal" />
  </BasicModal>
</template>
<script lang="ts" setup>
  import { ref, reactive } from 'vue';
  import { BasicModal, useModalInner, useModal } from '/@/components/Modal';
  import { MonacoEditor } from '/@/components/CodeEditor';
  import CommonHelp from '/@/components/ScriptApiDoc/commonHelp.vue';
  import type { languages } from 'monaco-editor/esm/vs/editor/editor.api';

  const emit = defineEmits(['register', 'confirm']);
  const [registerModal, { closeModal }] = useModalInner(init);
  const [registerHelpModal, { openModal: openHelpModal }] = useModal();
  const editorRef = ref(null);
  const text = ref('');
  const funcName = ref('');
  const title = ref('');
  const isOK = ref(false);

  const provideHover = reactive<languages.HoverProvider>({
    // @ts-expect-error
    provideHover(model, position) {
      // const lineword = model.getLineContent(position.lineNumber);
      const word = model.getWordAtPosition(position)?.word;
      let returnValue = {};
      switch (word) {
        case 'record':
          returnValue = {
            contents: [
              {
                value: ['列表行数据', `${word}：Record<string, any>`].join('\n\n'),
              },
            ],
          };
          break;
        case 'column':
          returnValue = {
            contents: [
              {
                value: ['列表列数据', `${word}：Record<string, any>`].join('\n\n'),
              },
            ],
          };
          break;
        case 'rowIndex':
          returnValue = {
            contents: [
              {
                value: ['列表行下标', `${word}：number`].join('\n\n'),
              },
            ],
          };
          break;
        case 'refresh':
          returnValue = {
            contents: [
              {
                value: ['刷新列表', `${word}：() => void`].join('\n\n'),
              },
            ],
          };
          break;
        case 'toast':
          returnValue = {
            contents: [
              {
                value: ['加载动画', `${word}：(config: string | number | object) => MessageType`].join('\n\n'),
              },
            ],
          };
          break;
        case 'confirm':
          returnValue = {
            contents: [
              {
                value: ['确认框', `${word}：(options: ModalOptionsEx) => ConfirmOptions`].join('\n\n'),
              },
            ],
          };
          break;
        case 'data':
          returnValue = {
            contents: [
              {
                value: ['当前组件的选中数据', `${word}：Record<string, any>`].join('\n\n'),
              },
            ],
          };
          break;
        case 'tableRef':
          returnValue = {
            contents: [
              {
                value: ['表格DOM元素', `${word}：TableActionType`].join('\n\n'),
              },
            ],
          };
          break;
        case 'searchInfo':
          returnValue = {
            contents: [
              {
                value: [
                  '获取menudId和modelId以及搜索框内容',
                  `searchInfo：{`,
                  `&nbsp;&nbsp;modelId:  string 在线表单modelId`,
                  `&nbsp;&nbsp;menuId:  string 在线表单menuId`,
                  `&nbsp;&nbsp;queryJson:  string 搜索条件JSON字符串`,
                  `&nbsp;&nbsp;superQueryJson:  string 高级搜索JSON字符串`,
                  `}`,
                ].join('\n\n'),
              },
            ],
          };
          break;
        case 'setShowOrHide':
          returnValue = {
            contents: [
              {
                value: ['设置显示或隐藏', `${word}：(btnCode: string, value: boolean, record: Record<string, any>) => void`].join('\n\n'),
              },
            ],
          };
          break;
        case 'setDisabled':
          returnValue = {
            contents: [
              {
                value: ['设置禁用项', `${word}：(btnCode: string, value: boolean, record: Record<string, any>) => void`].join('\n\n'),
              },
            ],
          };
          break;
        case 'request':
          returnValue = {
            contents: [
              {
                value: ['异步请求', `${word}：(url: string, method: 'post' | 'get', data: Record<string, any>) => VAxios`].join('\n\n'),
              },
            ],
          };
          break;
        case 'useCustomDialog':
          returnValue = {
            contents: [
              {
                value: [
                  '自定义设置弹窗',
                  `useCustomDialog：() => {`,
                  `&nbsp;&nbsp;addDialog: (option: Partial<CustomDialogParams&gt;) => string`,
                  `&nbsp;&nbsp;getDialog: (id: string) => CustomDialogParams`,
                  `&nbsp;&nbsp;closeDialog: (id: string) => string`,
                  `}`,
                ].join('\n\n'),
              },
              {
                value: [
                  `interface CustomDialogParams {`,
                  `&nbsp;&nbsp;id: string`,
                  `&nbsp;&nbsp;modelId?: string 在线表单id`,
                  `&nbsp;&nbsp;type?: 1 | 2 | 3 类型`,
                  `&nbsp;&nbsp;title?: string 弹窗标题`,
                  `&nbsp;&nbsp;params?: Record<string, any> 父页面给弹窗页面传递的参数`,
                  `&nbsp;&nbsp;width?: string 弹窗宽度`,
                  `&nbsp;&nbsp;style?: Record<string, any> 弹窗样式`,
                  `&nbsp;&nbsp;formUrl?: string 打开系统表单文件路径（仅限views目录下文件）`,
                  `}`,
                ].join('\n\n'),
              },
            ],
          };
          break;

        default:
          break;
      }
      return returnValue;
    },
  });

  function showDemo(type) {
    openHelpModal(true, type);
    return false;
  }

  function reloadEditor() {
    isOK.value = false;
    setTimeout(() => {
      isOK.value = true;
    }, 100);
  }
  function init(data) {
    text.value = data.text;
    funcName.value = data.funcName;
    title.value = getFuncText(data.funcName);
    reloadEditor();
  }
  function fullscreenChange() {
    reloadEditor();
  }
  function getFuncText(key) {
    let text = '';
    switch (key) {
      case 'afterOnload':
        text = '表格事件';
        break;
      case 'rowStyle':
        text = '表格行设置';
        break;
      case 'cellStyle':
        text = '单元格设置';
        break;
      default:
        text = '';
        break;
    }
    return text;
  }
  function handleSubmit() {
    emit('confirm', text.value);
    closeModal();
  }
</script>
