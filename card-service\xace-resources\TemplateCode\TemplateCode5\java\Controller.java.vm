package ${package.Controller};
    #set($serviceName = "${table.serviceName.substring(0,1).toLowerCase()}${table.serviceName.substring(1)}")
    #set($Name = "${genInfo.className.substring(0,1).toUpperCase()}${genInfo.className.substring(1)}")
    #set($name = "${genInfo.className.substring(0,1).toLowerCase()}${genInfo.className.substring(1)}")
    #set($packName = "${genInfo.className.toLowerCase()}")
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import com.xinghuo.common.base.ActionResult;
import com.xinghuo.common.base.UserInfo;
import com.xinghuo.common.exception.DataException;
import com.xinghuo.example.util.GeneraterSwapUtil;
import com.xinghuo.permission.entity.UserEntity;
#if(${DS})
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
#else
import org.springframework.transaction.annotation.Transactional;
#end
import ${modulePackageName}.model.${packName}.*;
    #foreach($subfield in ${child})
import ${package.Entity}.${subfield.className}Entity;
    #end
    #foreach($cl in  ${columnChildren})
import ${package.Service}.${cl.modelUpName}Service;
import ${package.Entity}.${cl.modelUpName}Entity;
    #end
import com.xinghuo.common.util.*;
import com.xinghuo.common.hutool.*;
import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import ${package.Entity}.${Name}Entity;
import ${package.Service}.${table.serviceName};
    #foreach($tableModel in ${child})
                #set($ChildName="${tableModel.className.substring(0,1).toUpperCase()}${tableModel.className.substring(1)}")
                #set($childName="${tableModel.className.substring(0,1).toLowerCase()}${tableModel.classNames.substring(1)}")
import ${package.Entity}.${ChildName}Entity;
import ${package.Service}.${ChildName}Service;
    #end
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import jakarta.validation.Valid;

#macro(SystemDataChange $system $vo)
    #foreach($field in ${system})
        #set($xhKey="${field.config.xhKey}")
        #set($EntityName =${field.vModel})
        #set($fieldName="${field.vModel.substring(0,1).toUpperCase()}${field.vModel.substring(1)}")
        #if(${xhKey}=='createTime'||${xhKey}=='modifyTime')
						if(${vo}.get${fieldName}()!=null){
            ${vo}.set${fieldName}(${vo}.get${fieldName}());
						}
        #elseif(${xhKey}=='createUser'||${xhKey}=='modifyUser')
            ${vo}.set${fieldName}(generaterSwapUtil.userSelectValue(${vo}.get${fieldName}()));
        #elseif(${xhKey}=='currOrganize')
            #set($showLevel = ${field.showLevel})
            ${vo}.set${fieldName}(generaterSwapUtil.comSelectValue(${vo}.get${fieldName}(), "${showLevel}"));
        #elseif(${xhKey}=='currPosition')
            ${vo}.set${fieldName}(generaterSwapUtil.posSelectValue(${vo}.get${fieldName}()));
        #end
    #end
#end

/**
 *
 * ${genInfo.description}
 * @版本： ${genInfo.version}
 * @版权： ${genInfo.copyright}
 * @作者： ${genInfo.createUser}
 * @日期： ${genInfo.createDate}
 */
@Slf4j
@RestController
@Tag(name = "${genInfo.description}" , description = "${module}")
#if(${isCloud}=="cloud")
@RequestMapping("/${genInfo.className}")
#else
@RequestMapping("/api/${module}/${genInfo.className}")
#end
public class ${table.controllerName} {

    #set($peimaryKeyName = "${pKeyName.substring(0,1).toUpperCase()}${pKeyName.substring(1).toLowerCase()}")
    #set($peimaryKeyname = "${pKeyName.substring(0,1).toLowerCase()}${pKeyName.substring(1).toLowerCase()}")

    @Autowired
    private UserProvider userProvider;

    @Autowired
    private GeneraterSwapUtil generaterSwapUtil;

    @Autowired
    private ${table.serviceName} ${serviceName};

    #foreach($tableModel in ${child})
        #set($ChildName="${tableModel.className.substring(0,1).toUpperCase()}${tableModel.className.substring(1)}")
        #set($childName="${tableModel.className.substring(0,1).toLowerCase()}${tableModel.className.substring(1)}")
    @Autowired
    private ${ChildName}Service ${childName}Service;
    #end

    #foreach($cl in  ${columnChildren})
    @Autowired
    private ${cl.modelUpName}Service ${cl.modelLowName}Service;
    #end

    /**
     * 创建
     *
     * @param ${name}CrForm
     * @return
     */
    @PostMapping("/{id}")
    #if(${DS})
    @DSTransactional
    #else
    @Transactional
    #end
    public ActionResult create(@PathVariable("id") String id,@RequestBody @Valid ${Name}CrForm ${name}CrForm) throws DataException {
		String b = ${serviceName}.checkForm(${name}CrForm,0);
		if (StringUtil.isNotEmpty(b)){
		 return ActionResult.fail(b + "不能重复");
		}
        String mainId =id;

        UserInfo userInfo=userProvider.get();
		UserEntity userEntity = generaterSwapUtil.getUser(userInfo.getUserId());
        #set($peimaryKeyName="${pKeyName.substring(0,1).toUpperCase()}${pKeyName.substring(1)}")
    #foreach($field in ${system})
        #set($model = "${field.vModel.substring(0,1).toUpperCase()}${field.vModel.substring(1)}")
        #set($key = ${field.config.xhKey})
        #if(${key}=='createUser')
        ${name}CrForm.set${model}(userInfo.getUserId());
        #elseif(${key}=='createTime')
        ${name}CrForm.set${model}(DateUtil.getNow());
        #elseif(${key}=='currOrganize')
        ${name}CrForm.set${model}(StringUtil.isEmpty(userInfo.getDepartmentId()) ? userInfo.getOrganizeId() : userInfo.getDepartmentId());
        #elseif(${key}=='currPosition')
            ${name}CrForm.set${model}(userEntity.getPositionId());
        #elseif(${key}=='billRule')
        ${name}CrForm.set${model}(generaterSwapUtil.getBillNumber("${field.config.rule}", false));
        #end
    #end
        ${Name}Entity entity = JsonXhUtil.toBean(${name}CrForm, ${Name}Entity.class);
        #if($snowflake)
            entity.set${peimaryKeyName}(mainId);
        #else
            entity.setFlowtaskid(mainId);
            entity.set${peimaryKeyName}(0);
        #end
                ${name}Service.save(entity);
        #foreach($grid in ${child})
            #set($list = "${grid.className.substring(0,1).toUpperCase()}${grid.className.substring(1).toLowerCase()}")
            #set($serviceName = "${grid.className.substring(0,1).toLowerCase()}${grid.className.substring(1)}")
                List<${grid.className}Entity> ${grid.className}List = JsonXhUtil.getJsonToList(${name}CrForm.get${list}List(),${grid.className}Entity.class);

                for(${grid.className}Entity entitys : ${grid.className}List){
            #set($tableField = "${grid.tableField.substring(0,1).toUpperCase()}${grid.tableField.substring(1)}")
            #set($relationField = "${grid.relationField.substring(0,1).toUpperCase()}${grid.relationField.substring(1)}")
            #set($chidKeyName = "${grid.chidKeyName.substring(0,1).toUpperCase()}${grid.chidKeyName.substring(1)}")
            #if($snowflake)
                entitys.set${chidKeyName}(RandomUtil.snowId());
                entitys.set${tableField}(entity.get${relationField}());
            #else
                entitys.set${chidKeyName}(0);
                entitys.set${tableField}(entity.getFlowtaskid());
            #end
            #foreach($xhkey in ${grid.childList})
               #if(${xhkey.fieLdsModel.vModel} != '')

                #set($key = ${xhkey.fieLdsModel.config.xhKey})
                #set($rule = ${xhkey.fieLdsModel.config.rule})
                #set($model = "${xhkey.fieLdsModel.vModel.substring(0,1).toUpperCase()}${xhkey.fieLdsModel.vModel.substring(1)}")
                #if(${key}=='createUser')
					entitys.set${model}(userInfo.getUserId());
                #elseif(${key}=='createTime')
					entitys.set${model}(DateXhUtil.date());
                #elseif(${key}=='currOrganize')
					entitys.set${model}(StringUtil.isEmpty(userInfo.getDepartmentId()) ? userInfo.getOrganizeId() : userInfo.getDepartmentId());
                #elseif(${key}=='currPosition')
				entitys.set${model}(userEntity.getPositionId());
                #elseif(${key}=='billRule')
				entitys.set${model}(generaterSwapUtil.getBillNumber("${rule}",false));
                #end
               #end
            #end
                ${serviceName}Service.save(entitys);
            }
        #end

#if(${columnChildren.size()}>0)
		//副表
    #foreach($cl in  ${columnChildren})
        #set($mainField = $cl.mainField)
        #set($mainUpId = "${mainField.substring(0,1).toUpperCase()}${mainField.substring(1)}")
               #set($oracleName = "${cl.modelName.substring(0,1).toUpperCase()}${cl.modelName.substring(1).toLowerCase()}")
        ${cl.modelName}Entity  ${cl.tableName}entity = JsonXhUtil.toBean(${name}CrForm.get${oracleName}(), ${cl.modelName}Entity.class);
        ${cl.tableName}entity.set${cl.relationUpField}(entity.get${cl.mainUpKey}());
				//自动生成的字段
        #foreach($clModel in ${cl.fieLdsModelList})
            #set($model = "${clModel.field.substring(0,1).toUpperCase()}${clModel.field.substring(1)}")
            #set($xhkey =  ${clModel.mastTable.fieLdsModel.config.xhKey})
            #if(${xhkey}=='createUser')
                ${cl.tableName}entity.set${model}(userInfo.getUserId());
            #elseif(${xhkey}=='createTime')
                ${cl.tableName}entity.set${model}(DateXhUtil.date());
            #elseif(${xhkey}=='currOrganize')
                ${cl.tableName}entity.set${model}(StringUtil.isEmpty(userInfo.getDepartmentId()) ? userInfo.getOrganizeId() : userInfo.getDepartmentId());
            #elseif(${xhkey}=='currPosition')
                       ${cl.tableName}entity.set${model}(userEntity.getPositionId());
            #elseif(${xhkey}=='billRule')
                #set($rule = ${clModel.mastTable.fieLdsModel.config.rule})
            ${cl.tableName}entity.set${model}(generaterSwapUtil.getBillNumber("$!{rule}",false));
            #end
        #end
               #if($snowflake)
                   ${cl.tableName}entity.set${cl.relationUpField}(entity.get${cl.mainUpKey}());
                   ${cl.tableName}entity.set${mainUpId}(mainId);
               #else
                   ${cl.tableName}entity.set${mainUpId}(0);
                   ${cl.tableName}entity.set${cl.relationUpField}(entity.getFlowtaskid());
               #end
        ${cl.modelLowName}Service.save(${cl.tableName}entity);
    #end
#end
        return ActionResult.success("保存成功");
    }

/**
 * 更新
 *
 * @param id
 * @param ${name}Form
 * @return
 */
@PutMapping("/{id}")
    #if(${DS})
    @DSTransactional
    #else
    @Transactional
    #end
@Operation(summary = "更新")
public ActionResult update(@PathVariable("id") String id, @RequestBody @Valid ${Name}CrForm ${name}Form) throws DataException {
		String b = ${name}Service.checkForm(${name}Form,1);
		if (StringUtil.isNotEmpty(b)){
		return ActionResult.fail(b + "不能重复");
		}
		UserInfo userInfo=userProvider.get();
    ${Name}Entity entity= ${name}Service.getById(id);
		if(entity!=null){
    #foreach($xhkey in ${system})
        #set($model = "${xhkey.vModel.substring(0,1).toUpperCase()}${xhkey.vModel.substring(1)}")
        #set($key = ${xhkey.config.xhKey})
        #set($rule = ${xhkey.config.rule})
        #if(${key}=='modifyUser')
            ${name}Form.set${model}(userInfo.getUserId());
        #elseif(${key}=='modifyTime')
            ${name}Form.set${model}(DateUtil.getNow());
        #elseif(${key}=='currOrganize')
            ${name}Form.set${model}(entity.get${model}());
        #elseif(${key}=='currPosition')
            ${name}Form.set${model}(entity.get${model}());
        #end
    #end
    ${Name}Entity subentity=JsonXhUtil.toBean(${name}Form, ${Name}Entity.class);
    #foreach($xhkey in ${system})
        #set($model = "${xhkey.vModel.substring(0,1).toUpperCase()}${xhkey.vModel.substring(1)}")
        #set($key = ${xhkey.config.xhKey})
        #if(${key}=='createUser')
						subentity.set${model}(entity.get${model}());
        #elseif(${key}=='createTime')
						subentity.set${model}(entity.get${model}());
        #end
    #end
		boolean b1 = ${name}Service.updateById(subentity);
		if (!b1){
		return ActionResult.fail("当前表单原数据已被调整，请重新进入该页面编辑并提交数据");
		}
    #if(${lineEdit})
				boolean	isPc = "pc".equals(ServletUtil.getHeader("xh-origin"));
				if (!isPc){
    #end
    #foreach($grid in ${child})
        #set($tableField = "${grid.tableField.substring(0,1).toUpperCase()}${grid.tableField.substring(1)}")
        #set($relationField = "${grid.relationField.substring(0,1).toUpperCase()}${grid.relationField.substring(1)}")
        #set($chidKeyName = "${grid.chidKeyName.substring(0,1).toUpperCase()}${grid.chidKeyName.substring(1)}")
        #set($serviceName = "${grid.className.substring(0,1).toLowerCase()}${grid.className.substring(1)}")

				QueryWrapper<${grid.className}Entity> ${grid.className}queryWrapper = new QueryWrapper<>();
        #if($snowflake)
            ${grid.className}queryWrapper.lambda().eq(${grid.className}Entity::get${tableField}, entity.get${relationField}());
        #else
            ${grid.className}queryWrapper.lambda().eq(${grid.className}Entity::get${tableField}, entity.getFlowtaskid());
        #end
        ${serviceName}Service.remove(${grid.className}queryWrapper);

        #set($list = "${grid.className.substring(0,1).toUpperCase()}${grid.className.substring(1).toLowerCase()}")
				if (${name}Form.get${list}List()!=null){
				List<${grid.className}Entity> ${grid.className}List = JsonXhUtil.getJsonToList(${name}Form.get${list}List(),${grid.className}Entity.class);
				for(${grid.className}Entity entitys : ${grid.className}List){
        #foreach($xhkey in ${grid.childList})
            #if(${xhkey.fieLdsModel.vModel} !='')

                #set($key = ${xhkey.fieLdsModel.config.xhKey})
                #set($rule = ${xhkey.fieLdsModel.config.rule})
                #set($model = "${xhkey.fieLdsModel.vModel.substring(0,1).toUpperCase()}${xhkey.fieLdsModel.vModel.substring(1)}")
                #if(${key}=='modifyUser')
										entitys.set${model}(userInfo.getUserId());
                #elseif(${key}=='modifyTime')
										entitys.set${model}(DateXhUtil.date());
                #elseif(${xhkey}=='currOrganize' || ${xhkey}=='currPosition')
										entity.set${model}(null);
                #elseif(${key}=='billRule')
										entitys.set${model}(StringUtil.isEmpty(entitys.get${model}())?generaterSwapUtil.getBillNumber("${rule}",false):entitys.get${model}());
                #end
            #end
        #end
        #if($snowflake)
						entitys.set${chidKeyName}(RandomUtil.snowId());
						entitys.set${tableField}(entity.get${relationField}());
        #else
						entitys.set${tableField}(entity.getFlowtaskid());
        #end
        ${serviceName}Service.save(entitys);
				}
				}
    #end
    #if(${lineEdit})
				}
    #end
    #if(${columnChildren.size()}>0)
				//子表数据
        #foreach($cl in  ${columnChildren})
            #set($oracleName ="${cl.TableName.substring(0,1).toUpperCase()}${cl.TableName.substring(1).toLowerCase()}")
						if(ObjectUtil.isNotEmpty(${name}Form.get${oracleName}())){
						QueryWrapper<${cl.modelUpName}Entity> queryWrapper${cl.modelUpName} =new QueryWrapper<>();
            #if($snowflake)
								queryWrapper${cl.modelUpName}.lambda().eq(${cl.modelUpName}Entity::get${cl.relationUpField},entity.get${cl.mainUpKey}());
            #else
								queryWrapper${cl.modelUpName}.lambda().eq(${cl.modelUpName}Entity::get${cl.relationUpField},entity.getFlowtaskid());
            #end
            ${cl.modelUpName}Entity ${cl.tableName}OneEntity= ${cl.modelLowName}Service.getOne(queryWrapper${cl.modelUpName});
            ${cl.modelUpName}Entity  ${cl.tableName}entity=JsonXhUtil.toBean(${name}Form.get${oracleName}(), ${cl.modelName}Entity.class);
            ${cl.tableName}entity.set${cl.mainField}(${cl.tableName}OneEntity.get${cl.mainField}());
            #if($snowflake)
                ${cl.tableName}entity.set${cl.relationUpField}(entity.get${cl.mainUpKey}());
            #else
                ${cl.tableName}entity.set${cl.relationUpField}(entity.getFlowtaskid());
            #end
            #foreach($clModel in ${cl.fieLdsModelList})
                #set($model ="${clModel.field.substring(0,1).toUpperCase()}${clModel.field.substring(1)}")
                #set($xhkey =  ${clModel.mastTable.fieLdsModel.config.xhKey})
                #if(${xhkey}=='modifyUser')
                    ${cl.tableName}entity.set${model}(userInfo.getUserId());
                #elseif(${xhkey}=='modifyTime')
                    ${cl.tableName}entity.set${model}(DateXhUtil.date());
                #elseif(${xhkey}=='currOrganize' || ${xhkey}=='currPosition' || ${xhkey}=='createUser' || ${xhkey}=='createTime')
                    ${cl.tableName}entity.set${model}(null);
                    ##                    #elseif(${xhkey}=='currOrganize')
                    ##                        ${cl.tableName}entity.set${model}(StringUtil.isEmpty(userInfo.getDepartmentId()) ? userInfo.getOrganizeId() : userInfo.getDepartmentId());
                    ##                    #elseif(${xhkey}=='currPosition')
                    ##												if(userInfo.getPositionIds()!=null&&userInfo.getPositionIds().length>0){
                    ##                        ${cl.tableName}entity.set${model}(userInfo.getPositionIds()[0]);
                    ##												}
                #elseif(${xhkey}=='billRule')
                    #set($rule = ${clModel.mastTable.fieLdsModel.config.rule})
                    ${cl.tableName}entity.set${model}(generaterSwapUtil.getBillNumber("$!{rule}",false));
                #end
            #end
            ${cl.modelLowName}Service.updateById(${cl.tableName}entity);
						}
        #end
    #end
		return ActionResult.success("更新成功");
		}else{
		return ActionResult.fail("更新失败，数据不存在");
		}
	}

/**
* 信息
*
* @param id
* @return
*/
@Operation(summary = "信息")
@GetMapping("/{id}")
public ActionResult<${Name}InfoVO> info(@PathVariable("id") String id){
    ${Name}Entity entity= ${name}Service.getById(id);
    ${Name}InfoVO vo=JsonXhUtil.jsonDeepCopy(entity, ${Name}InfoVO.class);
    if(vo==null){ return ActionResult.success(vo);}
#set($currtCount=1)
#set($userCount=1)
#set($vo = 'vo')
#SystemDataChange($system,$vo)

		//子表
#foreach($grid in ${child})
    #set($gridname = "$grid.className.toLowerCase()" +"Entity")
    #if($snowflake)
				List<${grid.className}Entity> ${grid.className}List = ${table.serviceName.substring(0,1).toLowerCase()}${table.serviceName.substring(1)}.get${grid.className}List(id);
    #else
				List<${grid.className}Entity> ${grid.className}List = ${table.serviceName.substring(0,1).toLowerCase()}${table.serviceName.substring(1)}.get${grid.className}List(entity.getFlowtaskid());
    #end
    #set($vo =$gridname)
		for(${grid.className}Entity ${vo} : ${grid.className}List){
    #foreach($field in ${grid.childList})
        #if(${field.fieLdsModel.vModel} != '')
            #set($xhKey = ${field.fieLdsModel.config.xhKey})
            #set($rule = ${field.fieLdsModel.config.rule})
            #set($EntityName =${field.fieLdsModel.vModel})
            #set($fieldName = "${field.fieLdsModel.vModel.substring(0,1).toUpperCase()}${field.fieLdsModel.vModel.substring(1)}")
            #if(${xhKey}=='createTime'||${xhKey}=='modifyTime')
								if(${vo}.get${fieldName}()!=null){
                ${vo}.set${fieldName}(${vo}.get${fieldName}());
								}
            #elseif(${xhKey}=='createUser'||${xhKey}=='modifyUser')
                ${vo}.set${fieldName}(generaterSwapUtil.userSelectValue(${vo}.get${fieldName}()));
            #elseif(${xhKey}=='currOrganize')
                #set($showLevel = ${field.showLevel})
                ${vo}.set${fieldName}(generaterSwapUtil.comSelectValue(${vo}.get${fieldName}(), "${showLevel}"));
            #elseif(${xhKey}=='currPosition')
                ${vo}.set${fieldName}(generaterSwapUtil.posSelectValue(${vo}.get${fieldName}()));
            #end
        #end
    #end
		}
    #set($list = "${grid.className.substring(0,1).toUpperCase()}${grid.className.substring(1)}")
		vo.set${list}List(JsonXhUtil.getJsonToList(${grid.className}List,${grid.className}Model.class ));
#end
		//副表
#foreach($cl in  ${columnChildren})
		QueryWrapper<${cl.modelUpName}Entity> queryWrapper${cl.modelUpName} = new QueryWrapper<>();
    #if($snowflake)
				queryWrapper${cl.modelUpName}.lambda().eq(${cl.modelUpName}Entity::get${cl.relationUpField},entity.get${cl.mainUpKey}());
    #else
				queryWrapper${cl.modelUpName}.lambda().eq(${cl.modelUpName}Entity::get${cl.relationUpField},entity.getFlowtaskid());
    #end
    ${cl.modelUpName}Entity ${cl.tableName}Entity = ${cl.modelLowName}Service.getOne(queryWrapper${cl.modelUpName});
    #foreach($mastModel in  ${cl.fieLdsModelList})
        #set($field = ${mastModel.mastTable.fieLdsModel})
        #set($xhKey = ${field.config.xhKey})
        #set($EntityName=${field.vModel})
        #set($fieldName = "${field.vModel.substring(0,1).toUpperCase()}${field.vModel.substring(1)}")
        #if(${xhKey}=='createTime'||${xhKey}=='modifyTime')
						if(${cl.tableName}Entity.get${fieldName}()!=null){
            ${cl.tableName}Entity.set${fieldName}(${cl.tableName}Entity.get${fieldName}());
						}
        #elseif(${xhKey}=='createUser'||${xhKey}=='modifyUser')
            ${cl.tableName}Entity.set${fieldName}(generaterSwapUtil.userSelectValue(${cl.tableName}Entity.get${fieldName}()));
        #elseif(${xhKey}=='currOrganize')
            #set($showLevel = ${field.showLevel})
            ${cl.tableName}Entity.set${fieldName}(generaterSwapUtil.comSelectValue(${cl.tableName}Entity.get${fieldName}(), "${showLevel}"));
        #elseif(${xhKey}=='currPosition')
            ${cl.tableName}Entity.set${fieldName}(generaterSwapUtil.posSelectValue(${cl.tableName}Entity.get${fieldName}()));
        #end
    #end
    #set($oracleName = "${cl.TableName.substring(0,1).toUpperCase()}${cl.TableName.substring(1).toLowerCase()}")
    ${cl.modelUpName}Model ${cl.modelLowName}Model = JsonXhUtil.toBean(${cl.tableName}Entity, ${cl.modelUpName}Model.class);
    #foreach($mastModel in  ${cl.fieLdsModelList})
        #set($field = ${mastModel.mastTable.fieLdsModel})
        #set($xhKey = ${field.config.xhKey})
        #set($EntityName=${field.vModel})
        #set($fieldName = "${field.vModel.substring(0,1).toUpperCase()}${field.vModel.substring(1)}")
        #if(${xhKey}=='createTime'||${xhKey}=='modifyTime')
						if(${cl.tableName}Entity.get${fieldName}()!=null){
            ${cl.modelLowName}Model.set${fieldName}(DateUtil.dateFormat(${cl.tableName}Entity.get${fieldName}()));
						}
        #end
    #end
		vo.set${oracleName}(${cl.modelLowName}Model);
#end
		return ActionResult.success(vo);
		}


		}
