#set($pKeyName = "${context.pKeyName}")
#set($mastTableList = $context.mastTable)
#set($setPermission = "$setPermission")
#set($menuId = "menuId")
#set($useBtnPermission=${context.useBtnPermission})
#set($useFormPermission=${context.useFormPermission})
#macro(code6 $key $html $formModel $showModel $tableFieldModel)
	#set($model = "${html.vModel}")
	#set($slot = ${html.slot})
	#set($prop="${html.vModel}")
	#set($config = $html.config)
	#set($placeholder = "${html.placeholder}")
	#set($show = ${config.noShow})
	#set($vmodel="${formModel}.${model}")
	#set($modelProps="")
	#set($modelOptions="")
	#set($modelUpload="")
	#set($modelColumnOptions="")
	#set($xhkey="${config.xhKey}")
	#set($tag="")
	#set($list="${model}")
	#set($type=${html.type})
	#set($placeholder = ${html.placeholder})
	#set($showJudge= "")
	#set($showWrite="")
	#set($inputalign="")
	#set($isStorage="${html.isStorage}")
	#if(${html.disabled})
		#set($showWrite="disabled")
	#end
	#set($hasFormP="#if($tableFieldModel)${tableFieldModel}-#end${model}")
	#set($relationField="")
	#set($relationModel = "")
	#set($roleOrgroup = "")
	#set($permission = "${useFormPermission}")
	#set($childoptions="")
	#set($ableRelationIds="")
	#if($xhkey=='userSelect' && ${html.relationField})
		#set($ableRelationIds="${context.formModel}.${html.relationField}")
	#end
	#set($configLabel="${config.label}")
	#if($xhkey=='roleSelect' || $xhkey=='groupSelect')
		#set($roleOrgroup = "vModel='"+${prop}+"'")
	#end
	#if($xhkey=='relationForm' || $xhkey=='popupSelect'|| $xhkey=='popupTableSelect')
		#set($relationField = "relationField='"+${html.relationField}+"'")
		#set($relationModel = "vModel='"+${prop}+"'")
	#end
	#if($xhkey=='popupAttr' || $xhkey=='relationFormAttr')
		#set($relationField = "relationField='"+${html.relationField}+"'")
		#set($showList = "")
		#set($showJudge = "")
		#set($permission = "${useFormPermission}")
		#if($isStorage=='0')
			#set($hasFormP="#if($tableFieldModel)${tableFieldModel}-#end${html.relationField}")
			#set($model = "${html.relationField}")
			#set($vmodel="${formModel}.${html.relationField}_${html.showField}")
		#end
	#end
	#if($key=='table')
		#set($prop="")
		#set($childoptions="${showModel}")
		#if($xhkey=='relationForm' || $xhkey=='popupSelect'|| $xhkey=='popupTableSelect')
			#set($relationModel = ":vModel=""'"+${model}+"'+i""")
		#end
		#if($xhkey=='popupAttr' || $xhkey=='relationFormAttr')
			#set($relationField = ":relationField=""'"+${html.relationField}+"'+i""")
			#set($showList = "")
			#set($permission = "${useFormPermission}")
			#if($isStorage=='0')
				#set($hasFormP="#if($tableFieldModel)${tableFieldModel}-#end${html.relationField}")
				#set($model = "${html.relationField}")
				#set($vmodel="${formModel}.${html.relationField}_${html.showField}")
			#end
		#end
		#if($xhkey=='userSelect' && ${html.relationField})
			#set($ableRelationIds="${context.formModel}.${showModel}List[i].${html.relationField}")
		#end
	#end
	#if($xhkey=='createUser' || $xhkey=='createTime' || $xhkey=='modifyUser' || $xhkey=='modifyTime' || $xhkey=='currOrganize' || $xhkey=='currDept' || $xhkey=='currPosition' || $xhkey=='billRule')
		#set($placeholder = "系统自动生成")
		#set($showWrite="disabled")
	#end
	#if($xhkey=='input' || $xhkey=='textarea' || $xhkey=='modifyUser' || $xhkey=='modifyTime' || $xhkey=='billRule')
		#set($tag = "view")
		#if($xhkey=='textarea')
			#set($type = "textarea")
		#end
		#set($inputalign="right")
	#elseif($xhkey=='createUser' || $xhkey=='createTime' || $xhkey=='currOrganize' || $xhkey=='currDept' || $xhkey=='currPosition' )
		#set($tag = "view")
	#elseif($xhkey=='inputNumber')
		#set($tag = "xh-number-box")
	#elseif($xhkey=='slider')
		#set($tag = "u-slider")
	#elseif($xhkey=='rate')
		#set($tag = "xh-rate")
	#elseif($xhkey=='areaSelect' || $xhkey=='switch' || $xhkey=='checkbox' || $xhkey=='select' || $xhkey=='cascader'||$xhkey=='radio'  )
		#set($tag = "view")
	#elseif($xhkey=='treeSelect')
		#set($tag = "view")
	#elseif($xhkey=='colorPicker')
		#set($tag = "")
	#elseif($xhkey=='editor')
		#set($tag = "view")
		#set($configLabel="")
	#elseif($xhkey=='popupTableSelect')
		#set($tag = "view")
	#elseif($xhkey=='groupSelect')
		#set($tag = "view")
	#elseif($xhkey=='roleSelect')
		#set($tag = "view")
	#elseif($xhkey=='uploadImg')
		#set($tag = "image")
		#set($modelUpload="${vmodel}")
	#elseif($xhkey=='uploadFile')
		#set($tag = "xh-file")
	#elseif($xhkey=='popupSelect')
		#set($tag = "view")
		#set($type = "popup")
	#elseif($xhkey=='relationForm')
		#set($tag = "view")
		#set($type = "relation")
	#elseif($xhkey=='datePicker' || $xhkey=='timePicker')
		#set($tag = "view")
	#elseif($xhkey=='userSelect')
		#set($tag = "view")
	#elseif($xhkey=='usersSelect')
		#set($tag = "view")
	#elseif($xhkey=='organizeSelect' || $xhkey=='depSelect' || $xhkey=='posSelect' )
		#set($tag = "view")
		#if($xhkey=='organizeSelect')
			#set($type = "organize")
			#set($tag = "view")
		#elseif($xhkey=='depSelect')
			#set($type = "department")
		#elseif($xhkey=='posSelect')
			#set($type = "position")
		#end
	#elseif($xhkey=='popupAttr')
		#set($tag = "view")
		#set($type = "popupAttr")
	#elseif($xhkey=='relationFormAttr')
		#set($tag = "view")
		#set($type = "relationFormAttr")
	#end
	#if($xhkey=='checkbox' || $xhkey=='select' || $xhkey=='cascader'|| $xhkey=='radio' || $xhkey=='treeSelect')
		#set($modelProps="${childoptions}${list}Props")
		#set($modelOptions="${childoptions}${list}Options")
	#end
	#if($xhkey=='relationForm' || $xhkey=='popupSelect' || $xhkey=='popupTableSelect')
		#set($modelColumnOptions="${childoptions}${list}ColumnOptions")
	#end

	#set($end ="/"+ ${tag})
	#if($show == false && ${config.app}==true)
		#if($tag)
		<view class="u-p-l-20 u-p-r-20 form-item-box">
		<u-form-item #if($permission) #if($useFormPermission==true) v-if="${setPermission}.hasFormP('${hasFormP}',menuId)"#end#end
			#if($config.showLabel && $config.showLabel == true)
				#if($configLabel) label="${configLabel}"#end
				#if($config.tipLabel && $xhkey!='editor' && $configLabel) left-icon="question-circle-fill" @clickIcon="clickIcon('${configLabel}','${config.tipLabel}')" :left-icon-style="{'color':'#a0acb7'}" #end
				#if($showJudge) ${showJudge}#end
				#if($config.labelWidth) label-width="${config.labelWidth}"#end #else label-width="0"#end>
				#if($xhkey=='uploadImg')
				<view class="preview-image-box">
				#end
				<${tag}
				#set($classText="xh-detail-text")
				#if($xhkey=='uploadImg')
					#set($classText="u-preview-image")
				#elseif($xhkey=='editor')
					#set($classText="editor-box")
				#if($model) v-html="${vmodel}"
				#end
				#end
				class = "${classText}"  disabled
				#if($html.count) :max="${html.count}"#end
				#if($html.max) :max="${html.max}"#end
				#set($showModelList=['rate','slider','uploadFile','inputNumber'])
				#if(${showModelList.contains($xhkey)}) v-model="${vmodel}"  #end
				#if($xhkey=='uploadFile') :list="${vmodel}" :detailed="true"  #end
				#if($xhkey=='uploadImg')
				 v-for="(cItem,ci) in ${vmodel}" :key="ci" :src="define.baseURL+cItem.url"
					  mode="aspectFill" @tap.stop="doPreviewImage(define.baseURL+cItem.url,${vmodel})"
				#end
				#if($xhkey=='inputNumber')	isDetail #end
				#if($xhkey=='relationForm') @click.native="toDetail(${vmodel}_id,'${html.modelId}')" style="color:blue"#end
				#if($slot.prepend) prepend="${slot.prepend}" #end
				#if($slot.append) append="${slot.append}" #end
				#if($html.allowhalf) allow-half #end
				#if($ableRelationIds) :ableRelationIds = "${ableRelationIds}" #end
				#if($html.isStorage || $html.isStorage=='0') isStorage="${html.isStorage}" #end
				#if($inputalign) input-align='right' #end
				#if($html.prefixIcon) prefix-icon="${html.prefixIcon}" #end
				#if($html.suffixIcon) suffix-icon="${html.suffixIcon}" #end
				#if($html.selectType) selectType="${html.selectType}" #end
				#if($html.description) description="${html.description}" #end
				#if($html.closeText) closeText="${html.closeText}" #end
				#if($html.direction) direction="${html.direction}" #end
				#if($html.isAmountChinese) isAmountChinese #end
				#if($html.thousands) thousands #end
				#if($html.addonAfter) addonAfter="${html.addonAfter}" #end
				#if($html.addonBefore) addonBefore="${html.addonBefore}" #end
				#if($html.controls) :controls="${html.controls}" #end
				#if($html.hasPage) hasPage #end
				#if($html.filterable || $html.filterable=='false') :showSearch='${html.filterable}' #end
				#if($html.propsValue) propsValue="${html.propsValue}" #end
				#if($html.popupWidth) popupWidth="${html.popupWidth}" #end
				#if($html.popupTitle) popupTitle="${html.popupTitle}" #end
				#if($html.popupType) popupType="${html.popupType}" #end
				#if($relationField) ${relationField} #end
				#if($relationModel) ${relationModel} #end
				#if($roleOrgroup) ${roleOrgroup} #end
				#if($html.showField) showField="${html.showField}" #end
				#if($modelColumnOptions)  :columnOptions="${modelColumnOptions}"#end
				#if($html.modelId) modelId="${html.modelId}" #end
				#if($html.interfaceId) interfaceId="${html.interfaceId}" #end
				#if($html.pageSize) :pageSize="${html.pageSize}" #end
				#if($html.sizeUnit) sizeUnit="${html.sizeUnit}" #end
				#if($html.accept) accept="${html.accept}" #end
				#if($html.fileSize) :fileSize="${html.fileSize}" #end
				#if($html.limit) :limit="${html.limit}" #end
				#if($html.pathType) pathType="${html.pathType}" #end
                #if($html.isAccount) :isAccount="${html.isAccount}" #end
                #if($html.folder) folder="${html.folder}" #end
				#if($placeholder) placeholder="${placeholder}"#end
				#if($modelProps)  :props="${modelProps}"#end
				#if($modelOptions) :options="${modelOptions}"#end
				#if($modelUpload) :value="${modelUpload}"#end
				#if($html.multiple) :multiple="${html.multiple}"#end
				#if($html.maxlength) :maxlength="${html.maxlength}"#end
				#if($html.min) :min="${html.min}"#end
				#if($html.level || ${html.level}==0) :level="${html.level}" #end
				#if($type) type="${type}"#end
				#if($html.step) :step="${html.step}"#end
				#if($html.textStyle) :textStyle='${html.textStyle}'#end
				#if($html.style) :style='${html.style}'#end
				#if($html.readonly) readonly#end
				#if($html.contentPosition) contentPosition="${html.contentPosition}"#end>
				#if($model && $xhkey!='editor'&& $xhkey!='uploadImg'&& $xhkey!='rate'&& $xhkey!='slider' && $xhkey!='uploadFile')
					{{${vmodel}}}
				#end
			</${tag}>
			#if($xhkey=='uploadImg')
			</view>
			#end
		</u-form-item>
		</view>
		#end
	#end
#end
<template>
    <view class="xh-wrap xh-wrap-form" v-if="!loading">
        <u-form :model="${context.formModel}" :rules="${context.formRules}" ref="${context.formModel}" :errorType="['toast']"
				#set($position='left')
				#if(${context.labelPosition}=='top')
					#set($position='top')
				#end
				#set($align='left')
				#if(${context.labelPosition}=='right')
					#set($align='right')
				#end
                label-position="${position}" label-align="${align}" :label-width="labelwidth" class="xh-form">
            #foreach($fieLdsModel in ${context.form})
                #set($xhkey = "${fieLdsModel.xhKey}")
                #set($isEnd = "${fieLdsModel.isEnd}")
				#set($formModel = ${fieLdsModel.formModel})
				#set($config=$formModel.config)
				#set($span=$config.span)
				#set($outermost = ${formModel.outermost})
				#set($header = ${formModel.header})
                #if($xhkey=='card' || $xhkey=='row')
					#if(${config.app}==true)
                    #if(${isEnd}=='0')
            <view class="xh-card">
						#if($header)
			<view class="xh-card-cap u-line-1">${header}#if(${config.tipLabel})<u-icon name="question-circle-fill" class="u-m-l-10" color="#a0acb7" @click="clickIcon('${header}','${config.tipLabel}')"/>#end</view>
						#end
					#else
			</view>
					#end
					#end
				#elseif($xhkey=='tab')
					#set($tabModel = ${formModel.model})
					#set($tabNum = ${formModel.childNum})
					#if(${config.app}==true)
					#if(${isEnd}=='0')
						#if(${outermost}=='0')
			<view prop="${formModel.model}">
			<u-tabs ref="${formModel.model}Current" :is-scroll="false" :list="${tabModel}Data" name="title" :current="${tabModel}Current-1" @change="${tabModel}"/>
			<view>
						#else
			<view v-show="${tabNum}+1 == ${tabModel}Current">
						#end
					#else
						#if(${outermost}=='0')
			</view>
			</view>
						#else
			</view>
						#end
					#end
					#end
				#elseif($xhkey=='collapse')
					#set($collapse = "u-collapse")
					#if(${outermost}=='1')
						#set($collapse = "u-collapse-item")
					#end
					#if(${config.app}==true)
						#if(${isEnd}=='0')
							#if(${outermost}=='0')
								<${collapse} ref="${formModel.model}Current" :accordion="${formModel.accordion}" v-model="${formModel.model}Current">
							#else
								<${collapse} title="${formModel.title}" :open="${formModel.model}Current.includes('${formModel.name}')" name="${formModel.name}" class="collapse-item">
							#end
						#else
						</${collapse}>
						#end
					#end
				#elseif($xhkey=="groupTitle" || $xhkey=="divider" || $xhkey=='text' || $xhkey=='button' || $xhkey=='link' || $xhkey=='alert')
					#set($defaultName="")
					#set($tag="")
					#set($divider ="")
					#set($itemBox = "form-item-box")
					#if($xhkey=="groupTitle")
						#set($tag = "xh-group")
						#set($itemBox="")
					#elseif($xhkey=="divider")
						#set($divider ="half-width=""200"" height=""80""")
						#set($tag = "u-"+$xhkey)
						#set($defaultName=$formModel.content)
					#elseif($xhkey=='text')
						#set($tag= "xh-text")
					#elseif($xhkey=="button")
						#set($tag= "xh-button")
					#elseif($xhkey=="link")
						#set($tag= "xh-link")
					#elseif($xhkey=="alert")
						#set($tag= "xh-alert-tips")
					#end
					#if(${config.app}==true)
			<view class="u-p-l-20 u-p-r-20 ${itemBox}">
			<u-form-item>
				<${tag} #if($formModel.style) :style='${formModel.style}'#end
				#if($formModel.title || $formModel.title=='') title='${formModel.title}'#end
				#if($formModel.tagIcon) tagIcon='${formModel.tagIcon}'#end
				#if($formModel.showIcon) showIcon #end
				#if($formModel.closable) closable #end
				#if($formModel.target) target='${formModel.target}'#end
				#if($formModel.href) href='${formModel.href}'#end
				#if($formModel.buttonText) buttonText='${formModel.buttonText}'#end
				#if($formModel.align) align='${formModel.align}'#end
				#if($formModel.type) type='${formModel.type}'#end
				#if($formModel.textStyle) :textStyle='${formModel.textStyle}'#end
				#if($config.defaultValue) value="${config.defaultValue}"#end
				#if($formModel.helpMessage && ${formModel.content}) tipLabel="${formModel.helpMessage}" @groupIcon="clickIcon('${formModel.content}','${formModel.helpMessage}')" #end
				#if($divider) ${divider}#end
				#if($formModel.content) value="${formModel.content}" content="${formModel.content}"#end
				#if($formModel.contentPosition) contentPosition="${formModel.contentPosition}" #end>
					#if(${defaultName})
					$!{defaultName}
					#end
				</${tag}>
			</u-form-item>
			</view>
					#end
				#elseif($xhkey=='mastTable')
					#set($mastTableModel = $fieLdsModel.formMastTableModel)
					#set($html = $mastTableModel.mastTable.fieLdsModel)
					#set($formModel="${context.formModel}")
					#set($showModel="")
					#set($tableFieldModel="")
					#code6('mastTable' $html $formModel $showModel $tableFieldModel)
				#elseif($xhkey=='mast')
                    #set($html = $fieLdsModel.formColumnModel.fieLdsModel)
					#set($formModel = "${context.formModel}")
					#set($showModel="")
					#set($tableFieldModel="")
					#code6('mast' $html $formModel $showModel $tableFieldModel)
			    #elseif($xhkey=='table')
                    #set($child = $fieLdsModel.childList)
					#set($tableModel = ${child.tableModel})
					#set($tableFieldModel="${tableModel}")
					#set($childApp=$child.app)
                    #set($className = "")
                    #foreach($children in ${context.children})
                        #if(${children.tableModel}==${child.tableModel})
                            #set($className = "${children.className.toLowerCase()}")
                        #end
                    #end
					#set($showJudge= "")
					#set($showTableWrite="")
					#if($childApp == true)
            <view class="xh-table"#if(${showJudge}) $showJudge#end #if($useFormPermission==true) v-if="${setPermission}.hasFormP('${tableModel}',menuId)"#end>
			<view class="xh-table-item" v-for="(item,i) in ${context.formModel}.${className}List" :key="i">
			<view class="xh-table-item-title u-flex u-row-between">
                        #if($child.showTitle== true)
			<text class="xh-table-item-title-num">${child.label}({{i+1}})#if(${child.tipLabel})<u-icon name="question-circle-fill" class="u-m-l-10" color="#a0acb7" @click="clickIcon('${child.label}','${child.tipLabel}')"/>#end</text>
                        #end
						#if($child.addType==0)
						#set($num="1")
						#else
						#set($num="0")
						#end
			</view>
						#foreach($childListAll in ${child.childList})
							#set($html = $childListAll.fieLdsModel)
							#set($formModel="${context.formModel}.${className}List[i]")
							#set($showModel="${className}")
							#code6('table' $html $formModel $showModel $tableFieldModel)
						#end
			</view>
			#set($showSummary = $child.showSummary)
			#if($showSummary)
			<view class="xh-table-item">
				<view class="xh-table-item-title u-flex u-row-between">
					<text class="xh-table-item-title-num">${child.label}合计</text>
				</view>
				<view class="u-p-l-20 u-p-r-20 form-item-box">
				<u-form-item v-for="(item,i) in ${className}()" :key="i" :label="item.name">
					<p>{{item.val}}</p>
				</u-form-item>
				</view>
			</view>
			#end
            </view>
                	#end
                #end
            #end
        </u-form>
        <view class="buttom-actions" >
            <u-button class="buttom-btn" @click="resetForm">取消</u-button>
            <u-button class="buttom-btn" type="primary" @click.stop="submitForm"  v-if="btnList.includes('btn_edit')">编辑</u-button>
        </view>
		<u-modal v-model="show" :content="content" width='70%' border-radius="16" :content-style="{fontSize: '28rpx',padding: '20rpx',lineHeight: '44rpx',textAlign: 'left'}"
		 :titleStyle="{padding: '20rpx'}" :confirm-style="{height: '80rpx',lineHeight: '80rpx'}" :title="title" confirm-text="确定">
		</u-modal>
    </view>
</template>
#macro(rule $fieLdsModel)
	#set($html = $fieLdsModel)
	#set($vModel = "${html.vModel}")
	#set($config = $html.config)
	#set($label = "${config.label}")
	#set($xhkey = "${config.xhKey}")
	#set($listSize=$!{config.regList})
	#set($defaultValue=${config.defaultValue})
	#set($defaultValueSize=$!{config.defaultValue})
	#set($trigger = ${config.trigger})
	#if(${trigger.substring(0,1)}!='[')
		#set($trigger = "'"+ ${config.trigger}+ "'")
	#end
	#if($vModel)
		#if(!$config.defaultValue && $config.defaultValue==[])
			#set($messages='请至少选择一个')
		#elseif(${config.defaultValue} && (${defaultValueSize} || $defaultValueSize.size()>0))
			#set($messages='请至少选择一个')
		#elseif($html.placeholder)
			#set($messages=${html.placeholder})
		#else
			#set($messages='不能为空')
		#end
		#if($config.required==true|| (${listSize} && $listSize.size()>0))
					${vModel}: [
			#if($config.required==true)
						{
							required: true,
							message: '${label}$!{messages}',
							#if($xhkey=='checkbox' || $xhkey=='timeRange' || $xhkey=='dateRange' || $xhkey=='areaSelect' || $xhkey=='cascader')
							type:'array',
							#elseif($xhkey=='organizeSelect' || $xhkey=='uploadFile' || $xhkey=="uploadImg")
							type:'array',
							#elseif($xhkey=='userSelect' || $xhkey=='select' || $xhkey=='depSelect' || $xhkey=='posSelect' || $xhkey=='popupTableSelect'|| $xhkey=='groupSelect'|| $xhkey=='roleSelect' || $xhkey=='treeSelect')
								#if(${html.multiple}=='true')
							type:'array',
								#end
							#end
						},
				#if($listSize.size()>0)
					#foreach($regList in ${config.regList})
						{
							pattern: ${regList.pattern},
							message: '${label}${regList.message}',
							#if($xhkey=='checkbox' || $xhkey=='timeRange' || $xhkey=='dateRange' || $xhkey=='areaSelect' || $xhkey=='cascader')
							type:'array',
							#elseif($xhkey=='organizeSelect' || $xhkey=='uploadFile' || $xhkey=="uploadImg")
							type:'array',
							#elseif($xhkey=='userSelect' || $xhkey=='select' || $xhkey=='depSelect' || $xhkey=='posSelect' || $xhkey=='popupTableSelect'|| $xhkey=='groupSelect'|| $xhkey=='roleSelect' || $xhkey=='treeSelect')
								#if(${html.multiple}=='true')
							type:'array',
								#end
							#end
						},
					#end
				#end
			#end
					],
		#end
	#end
#end
#macro(list $fieLdsModel,$childList)
	#set($html = $fieLdsModel)
	#set($vModel = "${childList}${html.vModel}")
	#set($config = $html.config)
	#set($xhkey = "$config.xhKey")
	#if(${config.dataType}=='dictionary'||${config.dataType}=='dynamic')
				${vModel}Options:[],
	#elseif(${config.dataType} == "static")
		#if($html.slot.options)
                ${vModel}Options:${html.slot.options},
		#elseif($html.options)
                ${vModel}Options:${html.options},
		#end
	#end
	#if($html.props)
				#set($propsModel = ${html.props.props})
				${vModel}Props:{"label":"${propsModel.label}","value":"${propsModel.value}","multiple":${propsModel.multiple},"children":"${propsModel.children}"},
	#end
	#if($config.props)
				${vModel}Props:{"label":"${config.props.label}","value":"${config.props.value}"},
	#end
	#if($xhkey=='relationForm' || $xhkey=='popupSelect' || $xhkey=='popupTableSelect')
				${vModel}ColumnOptions:[
		#foreach($columnOptions in  ${html.columnOptions})
					{
						"label":"${columnOptions.label}",
						"value":"${columnOptions.value}"
					},
		#end
				],
	#end
#end
#macro(options $fieLdsModel,$childList)
	#set($html = $fieLdsModel)
	#set($vModel = "${childList}${html.vModel}")
	#set($config = $html.config)
	#set($dataType = "${config.dataType}")
	#if(${dataType}=='dictionary' || ${dataType}=='dynamic')
			    this.get${vModel}Options()
	#end
#end
#macro(detailoptionsData $fieLdsModel,$childList)
	#set($html = $fieLdsModel)
	#set($vModel = "${childList}${html.vModel}")
	#set($config = $html.config)
	#set($dataType = "${config.dataType}")
	#if(${dataType}=='dictionary')
            get${vModel}Options() {
                getDictionaryDataSelector('${config.dictionaryType}').then(res => {
                    this.${vModel}Options = res.data.list
                })
            },
	#elseif(${dataType}=='dynamic')
            get${vModel}Options() {
				getDataInterfaceRes('${config.propsUrl}').then(res => {
					let data = res.data
                    this.${vModel}Options = data
                })
            },
	#end
#end
#macro(faceRes $fieLdsModel,$childList)
	#set($html = $fieLdsModel)
	#set($vModel = "${childList}${html.vModel}")
	#set($config = $html.config)
	#set($dataType = "${config.dataType}")
	#if(${config.templateJson})
			${vModel}:[
				#foreach($templateJson in ${config.templateJson})
				{
					fieldName:"${templateJson.fieldName}",
					field:"${templateJson.field}",
					defaultValue:"${templateJson.defaultValue}",
					xhKey:"$!{templateJson.xhKey}",
					dataType:"${templateJson.dataType}",
					id:"${templateJson.id}",
					required:"${templateJson.required}",
					relationField:"${templateJson.relationField}",
				},
				#end
			],
	#end
#end
#macro(detailList $key $fieLdsModel $formModel $tableModel $fieldModel $vModel)
	#set($model="${formModel}.${vModel}")
	#set($field="${formModel}.${fieldModel}")
	#if($key=='mastTable')
		#set($field="${formModel}.${tableModel}.${fieldModel}")
	#end
	#set($html = $fieLdsModel)
	#set($config = $html.config)
	#set($xhkey = "${config.xhKey}")
	#if($xhkey=='checkbox' || $xhkey=='timeRange' || $xhkey=='dateRange' || $xhkey=='areaSelect' || $xhkey=='cascader')
				${field} = Array.isArray(${model})? JSON.stringify(${model}):'[]'
	#elseif($xhkey=='organizeSelect' || $xhkey=='uploadFile' || $xhkey=="uploadImg")
				${field} = Array.isArray(${model})? JSON.stringify(${model}):'[]'
	#elseif($xhkey=='userSelect' || $xhkey=='select' || $xhkey=='depSelect' || $xhkey=='posSelect' || $xhkey=='popupTableSelect'|| $xhkey=='groupSelect'|| $xhkey=='roleSelect' || $xhkey=='treeSelect')
		#if(${html.multiple}=='true')
				${field} = Array.isArray(${model})? JSON.stringify(${model}):'[]'
		#else
				${field} = ${model}
		#end
	#else
		#if($key=='mastTable')
				${field} = ${model}
		#end
	#end
#end
#macro(detailInfo $key $fieLdsModel $formModel $tableModel $fieldModel $vModel)
	#set($model="${formModel}.${vModel}")
	#set($field="${formModel}.${fieldModel}")
	#if($key=='mastTable')
		#set($field="${formModel}.${tableModel}.${fieldModel}")
	#end
	#set($html = $fieLdsModel)
	#set($config = $html.config)
	#set($xhkey = "${config.xhKey}")
	#set($dataType = "${config.dataType}")
	#if($key=='mastTable')
		${model} = ${field}
		#if($xhkey=='relationForm')
		${model}_id = ${field}_id
		#end
	#end
	#if($xhkey=='checkbox' || $xhkey=='cascader')
		#if($dataType=='static')
		${model} = ${field}? JSON.parse(${field}):[]
		#end
	#elseif($xhkey=='select' || $xhkey=='treeSelect')
		#if(${html.multiple}=='true' && $dataType=='static')
			${model} = ${field}? JSON.parse(${field}):[]
		#end
	#elseif($xhkey=='uploadFile' || $xhkey=="uploadImg")
		${model} = ${field}? JSON.parse(${field}):[]
	#end
#end

<script>
    import request from '@/utils/request'

    export default {
        data(){
            return{
                btnLoading: false,
				loading: false,
                text: '提示：测试文本',
                tableKey:'',
				${context.formModel}:{
					${pKeyName}:"",
					#foreach($fieLdsModel in ${context.fields})
						#set($html = $fieLdsModel.formColumnModel.fieLdsModel)
						#set($vModel = "${html.vModel}")
						#set($config = $html.config)
						#set($xhkey = "${config.xhKey}")
						#set($dataType = "${config.dataType}")
						#set($list ='""')
						#if($xhkey=='checkbox' || $xhkey=='cascader')
							#if($dataType=='static')
								#set($list ='[]')
							#end
						#elseif($xhkey=='select' || $xhkey=='treeSelect')
							#if(${html.multiple}=='true' && $dataType=='static')
								#set($list ='[]')
							#end
						#elseif($xhkey=='uploadFile' || $xhkey=="uploadImg")
							#set($list ='[]')
						#end
						#if($vModel)
							#if($!config.valueType=='String')
					$!{vModel} : "$!{config.defaultValue}",
							#elseif($!config.valueType=='undefined')
					$!{vModel} : '',
							#else
					$!{vModel} : ${list},
							#end
						#end
					#end
					#foreach($masetkey in $mastTableList.entrySet())
						#set($tableModel = $masetkey.key)
					$tableModel:{
						#set($fieldsAll = $masetkey.value)
						#foreach($fieLdsModel in ${fieldsAll})
							#set($mastTableModel = $fieLdsModel.formMastTableModel)
							#set($html = $fieLdsModel.formMastTableModel.mastTable.fieLdsModel)
							#set($vModel = "${mastTableModel.field}")
							#set($config = $html.config)
							#set($xhkey = "${config.xhKey}")
							#set($dataType = "${config.dataType}")
							#set($list ='""')
							#if($xhkey=='checkbox' || $xhkey=='cascader')
								#if($dataType=='static')
									#set($list ='[]')
								#end
							#elseif($xhkey=='select' || $xhkey=='treeSelect')
								#if(${html.multiple}=='true' && $dataType=='static')
									#set($list ='[]')
								#end
							#elseif($xhkey=='uploadFile' || $xhkey=="uploadImg")
								#set($list ='[]')
							#end
							#if($vModel)
								#if($!config.valueType=='String')
						$!{vModel} : "$!{config.defaultValue}",
								#elseif($!config.valueType=='undefined')
						$!{vModel} : '',
								#else
						$!{vModel} : $list,
								#end
							#end
						#end
					},
					#set($fieldsAll = $masetkey.value)
						#foreach($fieLdsModel in ${fieldsAll})
							#set($mastTableModel = $fieLdsModel.formMastTableModel)
							#set($html = $fieLdsModel.formMastTableModel.mastTable.fieLdsModel)
							#set($vModel = "${mastTableModel.vModel}")
							#set($config = $html.config)
							#set($xhkey = "${config.xhKey}")
							#set($dataType = "${config.dataType}")
							#set($list ='""')
							#if($xhkey=='checkbox' || $xhkey=='cascader')
								#if($dataType=='static')
									#set($list ='[]')
								#end
							#elseif($xhkey=='select' || $xhkey=='treeSelect')
								#if(${html.multiple}=='true' && $dataType=='static')
									#set($list ='[]')
								#end
							#elseif($xhkey=='uploadFile' || $xhkey=="uploadImg")
								#set($list ='[]')
							#end
							#if($vModel)
								#if($!config.valueType=='String')
					$!{vModel} : "$!{config.defaultValue}",
								#elseif($!config.valueType=='undefined')
					$!{vModel} : '',
								#else
					$!{vModel} : $list,
								#end
							#end
						#end
					#end
					#foreach($child in ${context.children})
						#set($className = "${child.className.toLowerCase()}")
					${className}List:[],
					#end
				},
				${context.formRules}:{
					#foreach($fields in ${context.fields})
						#set($fieLdsModel = $fields.formColumnModel.fieLdsModel)
						#rule($fieLdsModel)
					#end
					#foreach($masetkey in $mastTableList.entrySet())
						#set($fieldsAll = $masetkey.value)
						#foreach($fields in ${fieldsAll})
							#set($fieLdsModel = $fields.formMastTableModel.mastTable.fieLdsModel)
							#rule($fieLdsModel)
						#end
					#end
				},
				#foreach($fields in ${context.fields})
					#set($fieLdsModel = $fields.formColumnModel.fieLdsModel)
					#list($fieLdsModel,'')
				#end
				#foreach($masetkey in $mastTableList.entrySet())
					#set($fieldsAll = $masetkey.value)
					#foreach($fields in ${fieldsAll})
						#set($fieLdsModel = $fields.formMastTableModel.mastTable.fieLdsModel)
						#list($fieLdsModel,'')
					#end
				#end
				#foreach($child in ${context.children})
					#set($className = "${child.className.toLowerCase()}")
					#foreach($childList in ${child.childList})
						#set($fieLdsModel = $childList.fieLdsModel)
						#list($fieLdsModel,$className)
					#end
				#end
				#foreach($fieLdsModel in ${context.form})
					#set($xhkey = "${fieLdsModel.xhKey}")
					#set($isEnd = "${fieLdsModel.isEnd}")
					#set($formModel = ${fieLdsModel.formModel})
					#set($model = "${formModel.model}")
					#set($outermost = ${formModel.outermost})
					#set($children = ${formModel.children})
					#if($xhkey=='tab' || $xhkey=='collapse')
						#if(${isEnd}=='0')
							#if(${outermost}=='0')
				${model}Current:${formModel.active},
				${model}Data:[
								#foreach($childrenList in $children)
					{
						title: "${childrenList.title}"
					},
								#end
				],
							#end
						#end
					#end
				#end
                labelwidth:${context.labelWidth}*1.5,
                menuId:'',
				btnList:[],
				idList:[],
                ruleList:{
				#foreach($child in ${context.children})
					#set($className = "${child.className.toLowerCase()}")
					${className}List:{
						#foreach($html in ${child.childList})
							#set($fieLdsModel = ${html.fieLdsModel})
							#set($vModel = "${fieLdsModel.vModel}")
							#set($config = ${fieLdsModel.config})
							#set($xhkey = "${config.xhKey}")
							#set($label = "${config.label}")
							#if($config.required==true)
								#if($xhkey!='switch' && $xhkey!='inputNumber')
						'${vModel}' : '${label}不能为空',
								#end
							#end
						#end
					},
				#end
                },
				interfaceRes:{
					#foreach($html in ${context.fields})
						#set($fieLdsModel = $html.formColumnModel.fieLdsModel)
						#faceRes($fieLdsModel,'')
					#end
					#foreach($masetkey in $mastTableList.entrySet())
						#set($fieldsAll = $masetkey.value)
						#foreach($html in ${fieldsAll})
							#set($fieLdsModel = $html.formMastTableModel.mastTable.fieLdsModel)
							#faceRes($fieLdsModel,'')
						#end
					#end
					#foreach($child in ${context.children})
						#set($className = "${child.className.toLowerCase()}")
						#foreach($childList in ${child.childList})
							#set($fieLdsModel = $childList.fieLdsModel)
							#faceRes($fieLdsModel,$className)
						#end
					#end
				},
				childIndex:-1,
				formatType:{"YYYY":"yyyy","yyyy-MM":"YYYY-MM","yyyy-MM-dd":"YYYY-MM-DD","yyyy-MM-dd HH:mm":"YYYY-MM-DD HH:mm","YYYY-MM-DD HH:mm:ss":"yyyy-mm-dd hh:MM:ss","HH:mm:ss":"hh:MM:ss","HH:mm":"hh:MM"},
				content:'',
				title:'',
				show:false,
            }
        },
        onLoad(option) {
        	this.menuId=option.menuId
        	this.btnList=option.btnList.split(",")
			uni.setNavigationBarTitle({
				title: '详情'
			})
            this.${context.formModel}.${pKeyName} = option.id || 0
			this.idList = option.idList?option.idList.split(","):[]
            this.dataAll()
            this.initData()
			uni.$on('refresh', () => {
				//执行接口更新数据
				this.initData()
			})
        },
		beforeDestroy() {
			uni.$off('refresh')
		},
        onReady() {
            #set($rulesAll = "this."+${context.formRules})
            this.$refs.${context.formModel}.setRules(${rulesAll});
        },
        watch:{
        	dataForm: {
				handler(val, oldVal) {
				#foreach($child in ${context.children})
					#set($className = "${child.className.toLowerCase()}")
					this.${className}()
				#end
				},
				deep: true
			}
        },
        methods:{
			doPreviewImage(url,imagesList) {
				const images = imagesList.map(item => this.define.baseURL + item.url);
				uni.previewImage({
					urls: images,
					current: url,
					success: () => {},
					fail: () => {
						uni.showToast({
							title: '预览图片失败',
							icon: 'none'
						});
					}
				});
			},
			toDetail(id, modelId) {
				if (!id) return
				let config = {
					modelId: modelId,
					id: id,
					formTitle: '详情',
					noShowBtn: 1
				}
				this.$nextTick(() => {
					const url ='/pages/apply/dynamicModel/detail?config=' + this.base64.encode(JSON.stringify(config),"UTF-8")
					uni.navigateTo({
						url: url
					})
				})
			},
			clickIcon(label,tipLabel) {
				this.content = tipLabel
				this.title = label
				this.show = true
			},
        	checkChildRule() {
				let title = [];
				let _ruleList = this.ruleList
				for (let k in _ruleList) {
					let childData = this.${context.formModel}[k]
					childData.forEach((item, index) => {
						for (let model in _ruleList[k]) {
							if (item[model] instanceof Array) {
								if (item[model].length == 0) {
									title.push(_ruleList[k][model])
								}
							} else if (!item[model]) {
								title.push(_ruleList[k][model])
							}
						}
					})
				}
				if (title.length > 0) {
					return title[0]
				}
			},
			resetForm(){
				uni.navigateBack()
			},
			dataAll(){
			    #foreach($html in ${context.fields})
			        #set($fieLdsModel = $html.formColumnModel.fieLdsModel)
			        #options($fieLdsModel,'')
			    #end
				#foreach($masetkey in $mastTableList.entrySet())
					#set($fieldsAll = $masetkey.value)
					#foreach($html in ${fieldsAll})
						#set($fieLdsModel = $html.formMastTableModel.mastTable.fieLdsModel)
						#options($fieLdsModel,'')
					#end
				#end
			    #foreach($child in ${context.children})
					#set($className = "${child.className.toLowerCase()}")
			        #foreach($childList in ${child.childList})
			            #set($fieLdsModel = $childList.fieLdsModel)
						#options($fieLdsModel,$className)
			        #end
			    #end
			},
            #foreach($fields in ${context.fields})
                #set($fieLdsModel = $fields.formColumnModel.fieLdsModel)
				#detailoptionsData($fieLdsModel,'')
            #end
			#foreach($masetkey in $mastTableList.entrySet())
				#set($fieldsAll = $masetkey.value)
				#foreach($fields in ${fieldsAll})
					#set($fieLdsModel = $fields.formMastTableModel.mastTable.fieLdsModel)
					#detailoptionsData($fieLdsModel,'')
				#end
			#end
            #foreach($child in ${context.children})
				#set($className = "${child.className.toLowerCase()}")
			${className}(){
				let table = this.${context.formModel}.${className}List
				let summaryField =${child.summaryField}
				let summaryFieldName =${child.summaryFieldName}
				let data ={}
				let thousandsField = ${child.thousandsField}
				for (let i in summaryField) {
					let map = {}
					let val = 0
					for (let j = 0; j < table.length; j++) {
						let summary = table[j][summaryField[i]];
						if (summary) {
							let data = isNaN(summary) ? 0 :	Number(summary)
							val += data
						}
					}
					map.id = summaryField[i];
					map.name = summaryFieldName[summaryField[i]];
					map.val = (thousandsField.includes(summaryField[i]))? Number(val).toLocaleString('zh', {maximumFractionDigits: '2'}):  val;
					data[summaryField[i]]=map;
				}
				return data;
			},
                #foreach($childList in ${child.childList})
					#set($className = "${child.className.toLowerCase()}")
                    #set($fieLdsModel = $childList.fieLdsModel)
					#detailoptionsData($fieLdsModel,$className)
                #end
            #end
			#foreach($fieLdsModel in ${context.form})
				#set($xhkey = "${fieLdsModel.xhKey}")
				#set($isEnd = "${fieLdsModel.isEnd}")
				#set($formModel = ${fieLdsModel.formModel})
				#set($outermost = ${formModel.outermost})
				#if($xhkey=='tab')
					#if(${isEnd}=='0')
						#if(${outermost}=='0')
			${formModel.model}(index) {
				this.${formModel.model}Current = index+1;
				this.collapse()
			},
						#end
					#end
				#end
			#end
            initData(){
            	#set($nextTick='$'+"nextTick")
				this.$nextTick(function(){
					if (this.${context.formModel}.${pKeyName}) {
						this.loading = true
						request({
							url: '/api/${context.module}/${context.className}/'+'detail/'+this.${context.formModel}.${pKeyName},
							method: 'get',
						}).then(res => {
							this.dataInfo(res.data)
							this.loading = false
						})
					}
				})
            },
            submitForm(){
				uni.navigateTo({
					url: "./form?menuId=" + this.menuId + "&jurisdictionType=btn_edit&id="+this.${context.formModel}.${pKeyName}+"&idList="+this.idList
				})
            },
            selfInit() {
                #foreach($child in ${context.children})
					#set($className = "${child.className.toLowerCase()}")
					#if($child.addType==0)
				this.add${className}List()
                	#end
                #end
                this.$store.commit('base/UPDATE_RELATION_DATA', {})
            },
            #foreach($child in ${context.children})
				#set($className = "${child.className.toLowerCase()}")
            add${className}List(){
            	this.tableKey = '${className}List';
            	#if($child.addType==1)
            	let _addTableConf =${child.addTableConf}
            	let data = {
					addTableConf: _addTableConf,
					formData: this.${context.formModel}
				}
				uni.navigateTo({
					url: '/pages/apply/tableLinkage/index?data=' + encodeURIComponent(JSON.stringify(data))
				})
				return
            	#end
				let item = {
					#foreach($childData in ${child.childList})
						#set($fieLdsModel = ${childData.fieLdsModel})
						#set($vModel = "${fieLdsModel.vModel}")
						#set($config = ${fieLdsModel.config})
						#if($vModel)
							#if($!config.valueType=='String')
					$!{vModel} : "$!{config.defaultValue}",
							#elseif($!config.valueType=='undefined')
					$!{vModel} : '',
							#else
					$!{vModel} : $!{config.defaultValue},
							#end
						#end
					#end
				}
				this.${context.formModel}.${className}List.push(item)
            },
            del${className}List(index) {
                this.${context.formModel}.${className}List.splice(index, 1);
            },
            #end
			dataList(){

			},
			dataInfo(dataAll){
				let _dataAll =dataAll
				#foreach($child in ${context.children})
				#set($className = "${child.className.toLowerCase()}")
				_dataAll.${className}List  =_dataAll.${child.tableModel}
				#end
				this.${context.formModel}=_dataAll
				this.collapse()
			},
			collapse(){
				setTimeout(()=> {
					#foreach($fieLdsModel in ${context.form})
						#set($xhkey = "${fieLdsModel.xhKey}")
						#set($isEnd = "${fieLdsModel.isEnd}")
						#set($formModel = ${fieLdsModel.formModel})
						#set($outermost = ${formModel.outermost})
						#set($config=$formModel.config)
						#if($xhkey=='collapse' || $xhkey=='tab')
							#if(${config.app}==true)
								#if(${isEnd}=='0')
									#if(${outermost}=='0')
										this.$refs.${formModel.model}Current && this.$refs.${formModel.model}Current.init()
									#end
								#end
							#end
						#end
					#end
				}, 1000);
			},
		},
    }

</script>
<style>
	page{
		background-color: #f0f2f6;
	}
</style>
