<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xinghuo.card.budget.dao.FinBudgetMapper">

    <!-- 预算实体ResultMap -->
    <resultMap id="FinBudgetResult" type="com.xinghuo.card.budget.entity.FinBudgetEntity">
        <id property="id" column="ID"/>
        <result property="userId" column="USER_ID"/>
        <result property="name" column="NAME"/>
        <result property="periodType" column="PERIOD_TYPE"/>
        <result property="startDate" column="START_DATE"/>
        <result property="endDate" column="END_DATE"/>
        <result property="totalBudgetAmount" column="TOTAL_BUDGET_AMOUNT"/>
        <result property="status" column="STATUS"/>
        <result property="createBy" column="CREATE_BY"/>
        <result property="createTime" column="CREATE_TIME"/>
        <result property="updateBy" column="UPDATE_BY"/>
        <result property="updateTime" column="UPDATE_TIME"/>
        <result property="tenantId" column="f_tenantid"/>
        <result property="flowId" column="f_flowid"/>
    </resultMap>

    <!-- 分页查询预算列表 -->
    <select id="getList" resultMap="FinBudgetResult">
        SELECT 
            ID, USER_ID, NAME, PERIOD_TYPE, START_DATE, END_DATE, 
            TOTAL_BUDGET_AMOUNT, STATUS, CREATE_BY, CREATE_TIME, 
            UPDATE_BY, UPDATE_TIME, f_tenantid, f_flowid
        FROM fin_budget
        <where>
            <if test="finBudgetPagination.userId != null and finBudgetPagination.userId != ''">
                AND USER_ID = #{finBudgetPagination.userId}
            </if>
            <if test="finBudgetPagination.name != null and finBudgetPagination.name != ''">
                AND NAME LIKE CONCAT('%', #{finBudgetPagination.name}, '%')
            </if>
            <if test="finBudgetPagination.periodType != null and finBudgetPagination.periodType != ''">
                AND PERIOD_TYPE = #{finBudgetPagination.periodType}
            </if>
            <if test="finBudgetPagination.status != null and finBudgetPagination.status != ''">
                AND STATUS = #{finBudgetPagination.status}
            </if>
            <if test="finBudgetPagination.startDateBegin != null">
                AND START_DATE >= #{finBudgetPagination.startDateBegin}
            </if>
            <if test="finBudgetPagination.startDateEnd != null">
                AND START_DATE <= #{finBudgetPagination.startDateEnd}
            </if>
        </where>
        ORDER BY 
            <choose>
                <when test="finBudgetPagination.sidx != null and finBudgetPagination.sidx != ''">
                    ${finBudgetPagination.sidx} ${finBudgetPagination.sort}
                </when>
                <otherwise>
                    CREATE_TIME DESC
                </otherwise>
            </choose>
    </select>

    <!-- 根据用户ID获取当前激活的预算 -->
    <select id="getActiveBudgetsByUserId" resultMap="FinBudgetResult">
        SELECT 
            ID, USER_ID, NAME, PERIOD_TYPE, START_DATE, END_DATE, 
            TOTAL_BUDGET_AMOUNT, STATUS, CREATE_BY, CREATE_TIME, 
            UPDATE_BY, UPDATE_TIME, f_tenantid, f_flowid
        FROM fin_budget
        WHERE USER_ID = #{userId} 
          AND STATUS = 'ACTIVE'
        ORDER BY START_DATE DESC
    </select>

    <!-- 根据用户ID和状态获取预算列表 -->
    <select id="getBudgetsByUserIdAndStatus" resultMap="FinBudgetResult">
        SELECT 
            ID, USER_ID, NAME, PERIOD_TYPE, START_DATE, END_DATE, 
            TOTAL_BUDGET_AMOUNT, STATUS, CREATE_BY, CREATE_TIME, 
            UPDATE_BY, UPDATE_TIME, f_tenantid, f_flowid
        FROM fin_budget
        WHERE USER_ID = #{userId}
        <if test="status != null and status != ''">
            AND STATUS = #{status}
        </if>
        ORDER BY START_DATE DESC
    </select>

</mapper>