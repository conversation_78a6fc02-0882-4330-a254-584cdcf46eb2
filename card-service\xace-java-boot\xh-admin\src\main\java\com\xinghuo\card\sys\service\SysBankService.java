package com.xinghuo.card.sys.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xinghuo.card.sys.entity.SysBankEntity;
import com.xinghuo.card.sys.model.sysbank.SysBankPagination;
import com.alicp.jetcache.anno.Cached;
import com.alicp.jetcache.anno.CacheInvalidate;
import com.alicp.jetcache.anno.CacheUpdate;
import com.alicp.jetcache.anno.CacheType;
import com.xinghuo.common.base.service.BaseService;

import java.util.List;
import java.util.Map;

/**
 * 银行信息管理服务接口
 * 用于管理银行的基本信息，包括银行名称、图标、网站等
 * 支持银行分类和信用卡账号属性配置
 *
 * <AUTHOR> Assistant
 * @version V1.0.0
 * @date 2024-12-29
 */
public interface SysBankService extends BaseService<SysBankEntity> {

    String CACHE_NAME = "dataSysBank";

    /**
     * 查询分页数据
     *
     * @param sysBankPagination 查询对象
     * @return 查询结果
     */
    List<SysBankEntity> getList(SysBankPagination sysBankPagination);

    /**
     * 查询分页或者不分页列表
     *
     * @param sysBankPagination 查询对象
     * @param dataType          0:分页 1:不分页
     * @return 查询结果
     */
    List<SysBankEntity> getTypeList(SysBankPagination sysBankPagination, String dataType);

    /**
     * 获取银行选择器列表
     * 用于下拉框等选择组件
     *
     * @return 银行列表
     */
    @Cached(name = CACHE_NAME, key = "selectList", cacheType = CacheType.LOCAL, expire = 120)
    List<SysBankEntity> getSelectList();

    /**
     * 根据银行类型获取银行列表
     *
     * @param bankType 银行类型
     * @return 银行列表
     */
    @Cached(name = CACHE_NAME, key = "type:#bankType", cacheType = CacheType.LOCAL, expire = 120)
    List<SysBankEntity> getListByType(String bankType);

    /**
     * 获取DataSysBankEntity详细信息
     *
     * @param id 主键
     * @return 银行详细信息
     */
    @Cached(name = CACHE_NAME, key = "#id", cacheType = CacheType.LOCAL, expire = 360)
    SysBankEntity getInfo(String id);

    /**
     * 根据银行简称查询
     *
     * @param bankKey 银行简称
     * @return 银行信息
     */
    @Cached(name = CACHE_NAME, key = "key:#bankKey", cacheType = CacheType.LOCAL, expire = 360)
    SysBankEntity getInfoByBankKey(String bankKey);

    /**
     * 根据银行名称查询
     *
     * @param bankName 银行名称
     * @return 银行信息
     */
    @Cached(name = CACHE_NAME, key = "name:#bankName", cacheType = CacheType.LOCAL, expire = 360)
    SysBankEntity getInfoByBankName(String bankName);

    /**
     * 删除银行
     *
     * @param entity 删除的对象
     */
    @CacheInvalidate(name = CACHE_NAME, key = "#entity.id")
    void delete(SysBankEntity entity);

    /**
     * 新增保存银行
     *
     * @param entity 新增的对象
     */
    void create(SysBankEntity entity);

    /**
     * 修改保存银行
     *
     * @param id     主键
     * @param entity 修改的对象
     * @return 是否成功
     */
    @CacheUpdate(name = CACHE_NAME, key = "#id", value = "#entity")
    boolean update(String id, SysBankEntity entity);

    /**
     * 检查银行名称是否唯一
     *
     * @param bankName 银行名称
     * @param id       排除的ID（用于更新时检查）
     * @return 是否唯一
     */
    boolean checkBankNameUnique(String bankName, String id);

    /**
     * 检查银行简称是否唯一
     *
     * @param bankKey 银行简称
     * @param id      排除的ID（用于更新时检查）
     * @return 是否唯一
     */
    boolean checkBankKeyUnique(String bankKey, String id);

    /**
     * 批量删除银行
     *
     * @param ids 银行ID列表
     * @return 删除成功的数量
     */
    int batchDelete(List<String> ids);

    /**
     * 更新银行状态
     *
     * @param id     银行ID
     * @param status 新状态
     * @return 是否成功
     */
    boolean updateStatus(String id, String status);

    /**
     * 获取银行统计信息
     *
     * @return 统计信息
     */
    @Cached(name = CACHE_NAME, key = "statistics", cacheType = CacheType.LOCAL, expire = 300)
    Map<String, Object> getBankStatistics();

    /**
     * 获取银行类型分布统计
     *
     * @return 银行类型分布
     */
    @Cached(name = CACHE_NAME, key = "typeDistribution", cacheType = CacheType.LOCAL, expire = 300)
    List<Map<String, Object>> getBankTypeDistribution();

    /**
     * 获取信用卡类型分布统计
     *
     * @return 信用卡类型分布
     */
    @Cached(name = CACHE_NAME, key = "creditTypeDistribution", cacheType = CacheType.LOCAL, expire = 300)
    List<Map<String, Object>> getCreditTypeDistribution();

    /**
     * 验证银行是否可以删除
     * 检查是否有关联的用户银行信息或信用卡账户
     *
     * @param id 银行ID
     * @return 验证结果
     */
    Map<String, Object> validateCanDelete(String id);

    /**
     * 批量验证银行是否可以删除
     *
     * @param ids 银行ID列表
     * @return 验证结果
     */
    Map<String, Object> batchValidateCanDelete(List<String> ids);

    /**
     * 导入银行数据
     *
     * @param bankList 银行数据列表
     * @return 导入结果
     */
    Map<String, Object> importBankData(List<SysBankEntity> bankList);

    /**
     * 导出银行数据
     *
     * @param sysBankPagination 查询条件
     * @return 导出数据
     */
    List<Map<String, Object>> exportBankData(SysBankPagination sysBankPagination);

    /**
     * 复制银行信息
     *
     * @param id      源银行ID
     * @param newName 新银行名称
     * @return 复制的银行信息
     */
    SysBankEntity copyBank(String id, String newName);

    /**
     * 获取银行详细信息（包含关联信息）
     *
     * @param id 银行ID
     * @return 详细信息
     */
    Map<String, Object> getBankDetailWithRelated(String id);
}
