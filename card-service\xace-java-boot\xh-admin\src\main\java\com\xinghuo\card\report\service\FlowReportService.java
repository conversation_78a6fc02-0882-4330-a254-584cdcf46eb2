package com.xinghuo.card.report.service;

import com.xinghuo.card.report.model.ReportStatisticsModel;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 流水报表统计服务接口
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2024-01-01
 */
public interface FlowReportService {

    /**
     * 获取统计概要
     *
     * @param userId 用户ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param accIds 账户ID列表
     * @param flowTypeIds 分类ID列表
     * @param transType 交易类型
     * @param keywords 关键词
     * @param minAmount 最小金额
     * @param maxAmount 最大金额
     * @return 统计概要
     */
    ReportStatisticsModel.StatisticsSummary getStatisticsSummary(
            String userId, Date startDate, Date endDate, List<String> accIds,
            List<String> flowTypeIds, List<Integer> transType, String keywords,
            java.math.BigDecimal minAmount, java.math.BigDecimal maxAmount);

    /**
     * 获取收支统计
     *
     * @param userId 用户ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param groupBy 分组方式：day-按日，month-按月，year-按年
     * @param accIds 账户ID列表
     * @param flowTypeIds 分类ID列表
     * @return 收支统计列表
     */
    List<ReportStatisticsModel.IncomeExpenseItem> getIncomeExpenseStatistics(
            String userId, Date startDate, Date endDate, String groupBy,
            List<String> accIds, List<String> flowTypeIds);

    /**
     * 获取分类统计
     *
     * @param userId 用户ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param transType 交易类型
     * @param accIds 账户ID列表
     * @param keywords 关键词
     * @return 分类统计列表
     */
    List<ReportStatisticsModel.CategoryStatItem> getCategoryStatistics(
            String userId, Date startDate, Date endDate, List<Integer> transType,
            List<String> accIds, String keywords);

    /**
     * 获取趋势分析
     *
     * @param userId 用户ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param groupBy 分组方式
     * @param accIds 账户ID列表
     * @param transType 交易类型
     * @return 趋势分析列表
     */
    List<ReportStatisticsModel.TrendAnalysisItem> getTrendAnalysis(
            String userId, Date startDate, Date endDate, String groupBy,
            List<String> accIds, List<Integer> transType);

    /**
     * 获取卡片统计
     *
     * @param userId 用户ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param accIds 账户ID列表
     * @return 卡片统计列表
     */
    List<ReportStatisticsModel.CardStatItem> getCardStatistics(
            String userId, Date startDate, Date endDate, List<String> accIds);

    /**
     * 获取流水明细
     *
     * @param userId 用户ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param accIds 账户ID列表
     * @param flowTypeIds 分类ID列表
     * @param transType 交易类型
     * @param keywords 关键词
     * @param minAmount 最小金额
     * @param maxAmount 最大金额
     * @param pageNum 页码
     * @param pageSize 页大小
     * @return 流水明细列表
     */
    List<ReportStatisticsModel.FlowDetailItem> getFlowDetails(
            String userId, Date startDate, Date endDate, List<String> accIds,
            List<String> flowTypeIds, List<Integer> transType, String keywords,
            java.math.BigDecimal minAmount, java.math.BigDecimal maxAmount,
            int pageNum, int pageSize);

    /**
     * 获取完整报表数据
     *
     * @param userId 用户ID
     * @param reportType 报表类型
     * @param conditions 查询条件
     * @return 完整报表数据
     */
    ReportStatisticsModel getCompleteReport(String userId, Integer reportType, Map<String, Object> conditions);

    /**
     * 导出报表数据
     *
     * @param userId 用户ID
     * @param reportType 报表类型
     * @param conditions 查询条件
     * @param exportFormat 导出格式：excel, csv, pdf
     * @return 导出文件路径
     */
    String exportReport(String userId, Integer reportType, Map<String, Object> conditions, String exportFormat);

    /**
     * 获取快速统计数据（用于首页展示）
     *
     * @param userId 用户ID
     * @return 快速统计数据
     */
    Map<String, Object> getQuickStatistics(String userId);

    /**
     * 获取热门分类排行
     *
     * @param userId 用户ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param transType 交易类型
     * @param limit 限制数量
     * @return 热门分类列表
     */
    List<ReportStatisticsModel.CategoryStatItem> getTopCategories(
            String userId, Date startDate, Date endDate, List<Integer> transType, int limit);

    /**
     * 获取月度对比数据
     *
     * @param userId 用户ID
     * @param currentMonth 当前月份
     * @param compareMonths 对比月份数
     * @return 月度对比数据
     */
    List<ReportStatisticsModel.IncomeExpenseItem> getMonthlyComparison(
            String userId, Date currentMonth, int compareMonths);
}
