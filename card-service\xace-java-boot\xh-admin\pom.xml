<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.xinghuo.51card</groupId>
        <artifactId>51card-boot</artifactId>
        <version>1.0.0</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <groupId>com.xinghuo.51card</groupId>
    <artifactId>xh-admin</artifactId>
    <packaging>jar</packaging>
    <version>2.1.0</version>
    <!--打包WAR包删除注释-->
    <!--<packaging>war</packaging>-->

    <properties>
        <!--依赖输出目录-->
        <output.dependence.file.path>lib/</output.dependence.file.path>
        <!--jar输出目录-->
        <output.jar.file.path>/</output.jar.file.path>
        <!--配置文件输出目录-->
        <output.resource.file.path>config/</output.resource.file.path>
    </properties>



    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.xinghuo.xace</groupId>
            <artifactId>xh-file</artifactId>
            <version>${xace.version}</version>
        </dependency>

        <dependency>
            <groupId>com.xinghuo.xace</groupId>
            <artifactId>xh-ext-demo</artifactId>
            <version>${xace.version}</version>
        </dependency>

        <dependency>
            <groupId>com.xinghuo.xace</groupId>
            <artifactId>xh-system</artifactId>
            <version>${xace.version}</version>
        </dependency>
        <!--
        <dependency>
            <groupId>com.xinghuo.xace</groupId>
            <artifactId>xh-scheduletask</artifactId>
            <version>${project.version}</version>
        </dependency>
        -->
        <dependency>
            <groupId>com.xinghuo.xace</groupId>
            <artifactId>xh-visualdev-base</artifactId>
            <version>${xace.version}</version>
        </dependency>
        <dependency>
            <groupId>com.xinghuo.xace</groupId>
            <artifactId>xh-visualdev-generater</artifactId>
            <version>${xace.version}</version>
        </dependency>
        <dependency>
            <groupId>com.xinghuo.xace</groupId>
            <artifactId>xh-visualdev-portal</artifactId>
            <version>${xace.version}</version>
        </dependency>
        <!--
        <dependency>
            <groupId>com.xinghuo</groupId>
            <artifactId>xh-visualdata</artifactId>
            <version>${project.version}</version>
        </dependency>
        -->
        <dependency>
            <groupId>com.xinghuo.xace</groupId>
            <artifactId>xh-workflow-engine</artifactId>
            <version>${xace.version}</version>
        </dependency>
        <dependency>
            <groupId>com.xinghuo.xace</groupId>
            <artifactId>xh-oauth-controller</artifactId>
            <version>${xace.version}</version>
        </dependency>




        <!--引入Knife4j的官方start包,该指南选择Spring Boot版本<3.0,开发者需要注意-->
        <dependency>
            <groupId>com.github.xiaoymin</groupId>
            <artifactId>knife4j-openapi3-spring-boot-starter</artifactId>
            <version>${knife4j.version}</version>
        </dependency>




    </dependencies>

    <build>
        <finalName>xh-admin-${project.version}</finalName>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>**/*.*</include>
                </includes>
                <filtering>false</filtering>
            </resource>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                </includes>
                <filtering>false</filtering>
            </resource>
        </resources>
        <plugins>
            <!-- ... -->
            <!--打包WAR包注释插件-->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <mainClass>com.xinghuo.admin.XhAdminApplication</mainClass>
                    <layout>ZIP</layout>
                    <!--这里是填写需要包含进去的jar，如果没有则nothing
                    <includes>
                        <include>
                            <groupId>nothing</groupId>
                            <artifactId>nothing</artifactId>
                        </include>
                    </includes>
                    -->
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven-compiler-plugin.version}</version>
                <configuration>
                    <source>${maven.compiler.source}</source>
                    <target>${maven.compiler.target}</target>
                    <compilerArgs>
                        <arg>-parameters</arg>
                    </compilerArgs>
                </configuration>
            </plugin>
            <!-- ... -->
            <!--打包WAR包删除注释-->
            <!--<plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-war-plugin</artifactId>
                <version>3.3.2</version>
                <configuration>
                    <warSourceExcludes>src/main/resources/**</warSourceExcludes>
                    <failOnMissingWebXml>false</failOnMissingWebXml>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <executions>
                    <execution>
                        <id>copy</id>
                        <phase>package</phase>
                        <goals>
                            <goal>copy-dependencies</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${project.build.directory}/lib</outputDirectory>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        -->
        </plugins>

    </build>
</project>
