package com.xinghuo.card.sys.service.impl;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xinghuo.card.sys.dao.DataAccMapper;
import com.xinghuo.card.sys.entity.DataAccEntity;
import com.xinghuo.card.sys.model.dataacc.DataAccPagination;
import com.xinghuo.card.sys.service.DataAccService;
import com.xinghuo.common.util.UserProvider;
import com.xinghuo.common.util.core.StrXhUtil;
import com.xinghuo.common.util.extra.ServletUtil;
import com.xinghuo.permission.model.authorize.AuthorizeConditionModel;
import com.xinghuo.permission.service.AuthorizeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;

/**
 * 个人账号
 *
 * <AUTHOR>
 * @version V1.0.0
 * date 2022-12-02
 */
@Service
@Slf4j
public class DataAccServiceImpl extends ServiceImpl<DataAccMapper, DataAccEntity> implements DataAccService {

    @Autowired
    private UserProvider userProvider;

    @Autowired
    private AuthorizeService authorizeService;

    @Override
    public List<DataAccEntity> getList(boolean showNormalAcc) {
        QueryWrapper<DataAccEntity> queryWrapper = new QueryWrapper<>();
        if(showNormalAcc) {
            queryWrapper.lambda().eq(DataAccEntity::getHideFlag, 0);
        }
        queryWrapper.lambda().orderByAsc(DataAccEntity::getListOrder)
                .orderByDesc(DataAccEntity::getCreateTime);
        return this.list(queryWrapper);
    }


    @Override
    public List<DataAccEntity> getSelectList(){
        QueryWrapper<DataAccEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("id","name");
        queryWrapper.lambda().eq(DataAccEntity::getHideFlag, 0);
        queryWrapper.lambda().orderByAsc(DataAccEntity::getListOrder);
        return this.list(queryWrapper);
    }



    @Override
    public List<DataAccEntity> getList(DataAccPagination dataAccPagination) {
        return getListByType(dataAccPagination, 0);
    }

    @Override
    public List<DataAccEntity> getTypeList(DataAccPagination dataAccPagination, int dataType) {
        return getListByType(dataAccPagination, dataType);
    }

    private List<DataAccEntity> getListByType(DataAccPagination dataAccPagination, int dataType) {
        List<String> allIdList = new ArrayList();
        int total = 0;
        int dataAccNum = 0;
        QueryWrapper<DataAccEntity> dataAccQueryWrapper = new QueryWrapper<>();
        boolean pcPermission = false;
        boolean appPermission = false;
        boolean isPc = "pc".equals(ServletUtil.getHeader("xh-origin"));
        if (isPc && pcPermission) {
            if (!userProvider.get().getIsAdministrator()) {
                Object dataAccObj = authorizeService.getCondition(new AuthorizeConditionModel(dataAccQueryWrapper, dataAccPagination.getMenuId(), "data_acc"));
                if (ObjectUtil.isEmpty(dataAccObj)) {
                    return new ArrayList<>();
                } else {
                    dataAccQueryWrapper = (QueryWrapper<DataAccEntity>) dataAccObj;
                    dataAccNum++;
                }
            }
        }
        if (!isPc && appPermission) {
            if (!userProvider.get().getIsAdministrator()) {
                Object dataAccObj = authorizeService.getCondition(new AuthorizeConditionModel(dataAccQueryWrapper, dataAccPagination.getMenuId(), "data_acc"));
                if (ObjectUtil.isEmpty(dataAccObj)) {
                    return new ArrayList<>();
                } else {
                    dataAccQueryWrapper = (QueryWrapper<DataAccEntity>) dataAccObj;
                    dataAccNum++;
                }
            }
        }
        if (allIdList.size() > 0) {
            dataAccQueryWrapper.lambda().in(DataAccEntity::getId, allIdList);
        }
        //排序
        if (StrXhUtil.isEmpty(dataAccPagination.getSidx())) {
            dataAccQueryWrapper.lambda().orderByDesc(DataAccEntity::getId);
        } else {
            try {
                DataAccEntity dataAccEntity = new DataAccEntity();
                Field declaredField = dataAccEntity.getClass().getDeclaredField(dataAccPagination.getSidx());
                declaredField.setAccessible(true);
                String value = declaredField.getAnnotation(TableField.class).value();
                dataAccQueryWrapper = "asc".equals(dataAccPagination.getSort().toLowerCase()) ? dataAccQueryWrapper.orderByAsc(value) : dataAccQueryWrapper.orderByDesc(value);
            } catch (NoSuchFieldException e) {
                e.printStackTrace();
            }
        }
        return this.list(dataAccQueryWrapper);

    }

    @Override
    public DataAccEntity getInfo(String id) {
        Assert.notEmpty(id, "id不能为空");
        QueryWrapper<DataAccEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(DataAccEntity::getId, id);
        return this.getOne(queryWrapper);
    }

    @Override
    public void create(DataAccEntity entity) {
        this.save(entity);
    }

    @Override
    public boolean update(String id, DataAccEntity entity) {
        entity.setId(id);
        return this.updateById(entity);
    }

    @Override
    public void delete(DataAccEntity entity) {
        if (entity != null) {
            this.removeById(entity.getId());
        }
    }


    /**
     * 每5分钟定时更新执行总数
     */
    @Scheduled(fixedRate = 5 * 60 * 1000)
    public void updateAccBalance(){
        log.info("定时执行 更新 账户余额功能.....");
        this.getBaseMapper().updateAccBalance();
    }

}
