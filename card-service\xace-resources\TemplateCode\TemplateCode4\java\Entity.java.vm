package ${package.Entity};

#if(${entityLombokModel})
import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
#end


import java.util.Date;
import com.fasterxml.jackson.annotation.JsonProperty;
#foreach($field in ${table.fields})
    #if(${field.propertyType} =='BigDecimal')
import java.math.BigDecimal;
    #elseif(${field.propertyType} == 'BigInteger')
import java.math.BigInteger;
    #elseif(${field.propertyType} == 'LocalTime')
import java.sql.Time;
    #end
#end
/**
 *
 * ${table.comment}
 * @版本： ${genInfo.version}
 * @版权： ${genInfo.copyright}
 * @作者： ${genInfo.createUser}
 * @日期： ${genInfo.createDate}
 */
#if(${entityLombokModel})
@Data
#end
#if(${table.convert})
@TableName("${table.name}")
#end
#if(${activeRecord})
public class ${table.entity} extends Model<${table.entity}> {
#else
public class ${table.entityName}  {
#end
#foreach($field in ${table.fields})
#set($tableField = "${field.name}")
#set($Property = "${field.propertyName}")
#if(${field.keyFlag}=='true')

    @TableId("${tableField.toUpperCase()}")
#else
    @TableField("${tableField.toUpperCase()}")
#end
#if(${field.propertyType} =='LocalDateTime' || ${field.propertyType} =='Date')
    private Date ${Property};
#elseif(${field.propertyType} =='Clob')
    private String ${Property};
#elseif(${field.propertyType} == 'LocalTime')
    private Time ${Property};
#elseif(${field.type} =='datetime')
    private Date ${Property};
#else
    private ${field.propertyType} ${Property};
#end

#end
}
