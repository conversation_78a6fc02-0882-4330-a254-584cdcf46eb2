package com.xinghuo.card.flow.model.dataflow;

import com.xinghuo.common.base.model.Pagination;
import lombok.Data;

import java.util.List;

/**
 * 流水表
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2022-11-26
 */
@Data
public class DataFlowPaginationExportModel extends Pagination {

    /**
     * 选择的key
     */
    private String selectKey;

    /**
     * json
     */
    private String json;

    /**
     * 数据类型
     */
    private String dataType;


    /**
     * 类型
     */
    private String transType;

    /**
     * 收支类型
     */
    private String type;

    /**
     * 日期
     */
    private List<String> flowDate;
}
