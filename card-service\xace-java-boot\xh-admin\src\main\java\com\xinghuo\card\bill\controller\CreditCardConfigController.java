package com.xinghuo.card.bill.controller;

import com.xinghuo.card.bill.entity.CreditCardConfigEntity;
import com.xinghuo.card.bill.service.CreditCardConfigService;
import com.xinghuo.common.base.ActionResult;
import com.xinghuo.common.base.UserInfo;
import com.xinghuo.common.base.vo.ListVO;
import com.xinghuo.common.util.UserProvider;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 信用卡配置控制器
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2024-01-01
 */
@Slf4j
@Tag(name = "信用卡配置管理", description = "CreditCardConfig")
@RestController
@RequestMapping("/api/card/config")
public class CreditCardConfigController {

    @Autowired
    private CreditCardConfigService creditCardConfigService;

    @Autowired
    private UserProvider userProvider;

    /**
     * 获取当前用户的信用卡配置列表
     */
    @Operation(summary = "获取信用卡配置列表")
    @GetMapping
    public ActionResult<ListVO<CreditCardConfigEntity>> getConfigList() {
        UserInfo userInfo = userProvider.get();
        List<CreditCardConfigEntity> configs = creditCardConfigService.getByUserId(userInfo.getUserId());
        
        ListVO<CreditCardConfigEntity> listVO = new ListVO<>();
        listVO.setList(configs);
        return ActionResult.success(listVO);
    }

    /**
     * 根据账户ID获取信用卡配置
     */
    @Operation(summary = "根据账户ID获取配置")
    @GetMapping("/acc/{accId}")
    public ActionResult<CreditCardConfigEntity> getConfigByAccId(@PathVariable("accId") String accId) {
        CreditCardConfigEntity config = creditCardConfigService.getByAccId(accId);
        return ActionResult.success(config);
    }

    /**
     * 获取信用卡配置详情
     */
    @Operation(summary = "获取配置详情")
    @GetMapping("/{configId}")
    public ActionResult<CreditCardConfigEntity> getConfigDetail(@PathVariable("configId") String configId) {
        CreditCardConfigEntity config = creditCardConfigService.getById(configId);
        if (config == null) {
            return ActionResult.fail("配置不存在");
        }
        return ActionResult.success(config);
    }

    /**
     * 创建信用卡配置
     */
    @Operation(summary = "创建信用卡配置")
    @PostMapping
    public ActionResult createConfig(@RequestBody @Valid CreditCardConfigEntity config) {
        UserInfo userInfo = userProvider.get();
        config.setUserId(userInfo.getUserId());
        config.setCreateBy(userInfo.getUserId());

        boolean success = creditCardConfigService.createConfig(config);
        if (success) {
            return ActionResult.success("创建成功");
        } else {
            return ActionResult.fail("创建失败，请检查配置信息");
        }
    }

    /**
     * 更新信用卡配置
     */
    @Operation(summary = "更新信用卡配置")
    @PutMapping("/{configId}")
    public ActionResult updateConfig(
            @PathVariable("configId") String configId,
            @RequestBody @Valid CreditCardConfigEntity config) {
        
        UserInfo userInfo = userProvider.get();
        config.setId(configId);
        config.setUpdateBy(userInfo.getUserId());

        boolean success = creditCardConfigService.updateConfig(config);
        if (success) {
            return ActionResult.success("更新成功");
        } else {
            return ActionResult.fail("更新失败，请检查配置信息");
        }
    }

    /**
     * 删除信用卡配置
     */
    @Operation(summary = "删除信用卡配置")
    @DeleteMapping("/{configId}")
    public ActionResult deleteConfig(@PathVariable("configId") String configId) {
        boolean success = creditCardConfigService.deleteConfig(configId);
        if (success) {
            return ActionResult.success("删除成功");
        } else {
            return ActionResult.fail("删除失败");
        }
    }

    /**
     * 启用/禁用自动生成账单
     */
    @Operation(summary = "切换自动生成账单")
    @PostMapping("/{configId}/toggle-auto-generate")
    public ActionResult toggleAutoGenerate(
            @PathVariable("configId") String configId,
            @RequestParam("enable") boolean enable) {
        
        boolean success = creditCardConfigService.toggleAutoGenerate(configId, enable);
        if (success) {
            String message = enable ? "已启用自动生成账单" : "已禁用自动生成账单";
            return ActionResult.success(message);
        } else {
            return ActionResult.fail("操作失败");
        }
    }

    /**
     * 验证信用卡配置
     */
    @Operation(summary = "验证配置信息")
    @PostMapping("/validate")
    public ActionResult validateConfig(@RequestBody CreditCardConfigEntity config) {
        boolean isValid = creditCardConfigService.validateConfig(config);
        if (isValid) {
            return ActionResult.success("配置验证通过");
        } else {
            return ActionResult.fail("配置验证失败，请检查输入信息");
        }
    }
}
