#parse("PublicMacro/ExcelMarco.vm")
##通用参数
#parse("PublicMacro/ConstantMarco.vm")
#ConstantParams()
#set($moduleName = "${context.genInfo.className.toLowerCase()}")
package ${context.package}.model.${moduleName};

import lombok.Data;
import java.sql.Time;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.alibaba.fastjson.annotation.JSONField;
import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelCollection;
import java.math.BigDecimal;
import java.util.List;
/**
 *
 * ${context.genInfo.description}
 * @版本： ${context.genInfo.version}
 * @版权： ${context.genInfo.copyright}
 * @作者： ${context.genInfo.createUser}
 * @日期： ${context.genInfo.createDate}
 */
@Data
public class $!{context.className}ExcelVO{
#if(${context.isMain})
#foreach($fieLdsModel in ${context.importFields})
    #set($html = $fieLdsModel)
    #set($vModel = "${html.vModel}")
    #set($config = $html.config)
    #set($xhkey = "${config.xhKey}")
##        判断是否子表
    #if(${vModel.contains("tableField")} )
    @JsonProperty("${vModel}")
    @ExcelCollection(name="${html.label}",orderNum = "${html.childList.size()}" #if(${context.nameAgain.contains($vModel)}) ,fixedIndex =${foreach.index} #end)
    private List<${html.aliasUpName}ExcelVO> ${vModel};
    #else
        #CreateExcelFields($html,${foreach.index})
    #end
#end
#else
#foreach($html in ${context.children.childList})
    #set($fieLdsModel = ${html.fieLdsModel})
    #set($config = ${fieLdsModel.config})
    #set($xhkey = ${config.xhKey})
    #set($vModel = "${fieLdsModel.vModel}")
    #set($fieldName=${config.label})
    #if($!vModel && !$DownLoadSkipUint.contains($xhkey))
    #CreateExcelFields($fieLdsModel,${foreach.index})
    #end
#end
#end

}
